{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\r\nimport { twMerge } from \"tailwind-merge\"\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs))\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,IAAA,sQAAO,EAAC,IAAA,gOAAI,EAAC;AACtB", "debugId": null}}, {"offset": {"line": 19, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Card = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\r\n      \"rounded-lg border border-gray-200 dark:border-gray-800 bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 shadow-sm\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nCard.displayName = \"Card\"\r\n\r\nconst CardHeader = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardHeader.displayName = \"CardHeader\"\r\n\r\nconst CardTitle = React.forwardRef<\r\n  HTMLParagraphElement,\r\n  React.HTMLAttributes<HTMLHeadingElement>\r\n>(({ className, ...props }, ref) => (\r\n  <h3\r\n    ref={ref}\r\n    className={cn(\r\n      \"text-2xl font-semibold leading-none tracking-tight\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nCardTitle.displayName = \"CardTitle\"\r\n\r\nconst CardDescription = React.forwardRef<\r\n  HTMLParagraphElement,\r\n  React.HTMLAttributes<HTMLParagraphElement>\r\n>(({ className, ...props }, ref) => (\r\n  <p\r\n    ref={ref}\r\n    className={cn(\"text-sm text-gray-600 dark:text-gray-400\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardDescription.displayName = \"CardDescription\"\r\n\r\nconst CardContent = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\r\n))\r\nCardContent.displayName = \"CardContent\"\r\n\r\nconst CardFooter = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"flex items-center p-6 pt-0\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardFooter.displayName = \"CardFooter\"\r\n\r\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,oWAAgB,CAG3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,+XAAC;QACC,KAAK;QACL,WAAW,IAAA,2JAAE,EACX,+HACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,oWAAgB,CAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,+XAAC;QACC,KAAK;QACL,WAAW,IAAA,2JAAE,EAAC,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,oWAAgB,CAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,+XAA<PERSON>;QACC,KAAK;QACL,WAAW,IAAA,2JAAE,EACX,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,oWAAgB,CAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,+XAAC;QACC,KAAK;QACL,WAAW,IAAA,2JAAE,EAAC,4CAA4C;QACzD,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,oWAAgB,CAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,+XAAC;QAAI,KAAK;QAAK,WAAW,IAAA,2JAAE,EAAC,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,oWAAgB,CAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,+XAAC;QACC,KAAK;QACL,WAAW,IAAA,2JAAE,EAAC,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 104, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst buttonVariants = cva(\r\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: \"bg-blue-600 text-white hover:bg-blue-700 dark:bg-blue-600 dark:hover:bg-blue-700\",\r\n        destructive:\r\n          \"bg-red-600 text-white hover:bg-red-700 dark:bg-red-600 dark:hover:bg-red-700\",\r\n        outline:\r\n          \"border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-900 hover:bg-gray-50 dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-gray-100\",\r\n        secondary:\r\n          \"bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-gray-100 hover:bg-gray-200 dark:hover:bg-gray-700\",\r\n        ghost: \"hover:bg-gray-100 dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-gray-100\",\r\n        link: \"text-blue-600 dark:text-blue-400 underline-offset-4 hover:underline\",\r\n      },\r\n      size: {\r\n        default: \"h-10 px-4 py-2\",\r\n        sm: \"h-9 rounded-md px-3\",\r\n        lg: \"h-11 rounded-md px-8\",\r\n        icon: \"h-10 w-10\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n      size: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nexport interface ButtonProps\r\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\r\n    VariantProps<typeof buttonVariants> {\r\n  asChild?: boolean\r\n}\r\n\r\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\r\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\r\n    const Comp = asChild ? Slot : \"button\"\r\n    return (\r\n      <Comp\r\n        className={cn(buttonVariants({ variant, size, className }))}\r\n        ref={ref}\r\n        {...props}\r\n      />\r\n    )\r\n  }\r\n)\r\nButton.displayName = \"Button\"\r\n\r\nexport { Button, buttonVariants }\r\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;;;;;;AAEA,MAAM,iBAAiB,IAAA,oRAAG,EACxB,uQACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,oWAAgB,CAC7B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,+UAAI,GAAG;IAC9B,qBACE,+XAAC;QACC,WAAW,IAAA,2JAAE,EAAC,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 164, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/components/ui/label.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst labelVariants = cva(\r\n  \"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\"\r\n)\r\n\r\nconst Label = React.forwardRef<\r\n  React.ElementRef<typeof LabelPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root> &\r\n    VariantProps<typeof labelVariants>\r\n>(({ className, ...props }, ref) => (\r\n  <LabelPrimitive.Root\r\n    ref={ref}\r\n    className={cn(labelVariants(), className)}\r\n    {...props}\r\n  />\r\n))\r\nLabel.displayName = LabelPrimitive.Root.displayName\r\n\r\nexport { Label }\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;;;;;;AAEA,MAAM,gBAAgB,IAAA,oRAAG,EACvB;AAGF,MAAM,sBAAQ,oWAAgB,CAI5B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,+XAAC,wTAAmB;QAClB,KAAK;QACL,WAAW,IAAA,2JAAE,EAAC,iBAAiB;QAC9B,GAAG,KAAK;;;;;;AAGb,MAAM,WAAW,GAAG,wTAAmB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 212, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/components/ui/dropdown-menu.tsx"], "sourcesContent": ["'use client'\n\nimport * as React from 'react'\nimport * as DropdownMenuPrimitive from '@radix-ui/react-dropdown-menu'\nimport { Check, ChevronRight, Circle } from 'lucide-react'\nimport { cn } from '@/lib/utils'\n\nconst DropdownMenu = DropdownMenuPrimitive.Root\n\nconst DropdownMenuTrigger = DropdownMenuPrimitive.Trigger\n\nconst DropdownMenuGroup = DropdownMenuPrimitive.Group\n\nconst DropdownMenuPortal = DropdownMenuPrimitive.Portal\n\nconst DropdownMenuSub = DropdownMenuPrimitive.Sub\n\nconst DropdownMenuRadioGroup = DropdownMenuPrimitive.RadioGroup\n\nconst DropdownMenuSubTrigger = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.SubTrigger>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.SubTrigger> & {\n    inset?: boolean\n  }\n>(({ className, inset, children, ...props }, ref) => (\n  <DropdownMenuPrimitive.SubTrigger\n    ref={ref}\n    className={cn(\n      'flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent',\n      inset && 'pl-8',\n      className\n    )}\n    {...props}\n  >\n    {children}\n    <ChevronRight className=\"ml-auto h-4 w-4\" />\n  </DropdownMenuPrimitive.SubTrigger>\n))\nDropdownMenuSubTrigger.displayName = DropdownMenuPrimitive.SubTrigger.displayName\n\nconst DropdownMenuSubContent = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.SubContent>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.SubContent>\n>(({ className, ...props }, ref) => (\n  <DropdownMenuPrimitive.SubContent\n    ref={ref}\n    className={cn(\n      'z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2',\n      className\n    )}\n    {...props}\n  />\n))\nDropdownMenuSubContent.displayName = DropdownMenuPrimitive.SubContent.displayName\n\nconst DropdownMenuContent = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Content>\n>(({ className, sideOffset = 4, ...props }, ref) => (\n  <DropdownMenuPrimitive.Portal>\n    <DropdownMenuPrimitive.Content\n      ref={ref}\n      sideOffset={sideOffset}\n      className={cn(\n        'z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2',\n        className\n      )}\n      {...props}\n    />\n  </DropdownMenuPrimitive.Portal>\n))\nDropdownMenuContent.displayName = DropdownMenuPrimitive.Content.displayName\n\nconst DropdownMenuItem = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Item> & {\n    inset?: boolean\n  }\n>(({ className, inset, ...props }, ref) => (\n  <DropdownMenuPrimitive.Item\n    ref={ref}\n    className={cn(\n      'relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50',\n      inset && 'pl-8',\n      className\n    )}\n    {...props}\n  />\n))\nDropdownMenuItem.displayName = DropdownMenuPrimitive.Item.displayName\n\nconst DropdownMenuCheckboxItem = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.CheckboxItem>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.CheckboxItem>\n>(({ className, children, checked, ...props }, ref) => (\n  <DropdownMenuPrimitive.CheckboxItem\n    ref={ref}\n    className={cn(\n      'relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50',\n      className\n    )}\n    checked={checked}\n    {...props}\n  >\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\n      <DropdownMenuPrimitive.ItemIndicator>\n        <Check className=\"h-4 w-4\" />\n      </DropdownMenuPrimitive.ItemIndicator>\n    </span>\n    {children}\n  </DropdownMenuPrimitive.CheckboxItem>\n))\nDropdownMenuCheckboxItem.displayName = DropdownMenuPrimitive.CheckboxItem.displayName\n\nconst DropdownMenuRadioItem = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.RadioItem>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.RadioItem>\n>(({ className, children, ...props }, ref) => (\n  <DropdownMenuPrimitive.RadioItem\n    ref={ref}\n    className={cn(\n      'relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50',\n      className\n    )}\n    {...props}\n  >\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\n      <DropdownMenuPrimitive.ItemIndicator>\n        <Circle className=\"h-2 w-2 fill-current\" />\n      </DropdownMenuPrimitive.ItemIndicator>\n    </span>\n    {children}\n  </DropdownMenuPrimitive.RadioItem>\n))\nDropdownMenuRadioItem.displayName = DropdownMenuPrimitive.RadioItem.displayName\n\nconst DropdownMenuLabel = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Label>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Label> & {\n    inset?: boolean\n  }\n>(({ className, inset, ...props }, ref) => (\n  <DropdownMenuPrimitive.Label\n    ref={ref}\n    className={cn(\n      'px-2 py-1.5 text-sm font-semibold',\n      inset && 'pl-8',\n      className\n    )}\n    {...props}\n  />\n))\nDropdownMenuLabel.displayName = DropdownMenuPrimitive.Label.displayName\n\nconst DropdownMenuSeparator = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Separator>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Separator>\n>(({ className, ...props }, ref) => (\n  <DropdownMenuPrimitive.Separator\n    ref={ref}\n    className={cn('-mx-1 my-1 h-px bg-muted', className)}\n    {...props}\n  />\n))\nDropdownMenuSeparator.displayName = DropdownMenuPrimitive.Separator.displayName\n\nconst DropdownMenuShortcut = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLSpanElement>) => {\n  return (\n    <span\n      className={cn('ml-auto text-xs tracking-widest opacity-60', className)}\n      {...props}\n    />\n  )\n}\nDropdownMenuShortcut.displayName = 'DropdownMenuShortcut'\n\nexport {\n  DropdownMenu,\n  DropdownMenuTrigger,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuCheckboxItem,\n  DropdownMenuRadioItem,\n  DropdownMenuLabel,\n  DropdownMenuSeparator,\n  DropdownMenuShortcut,\n  DropdownMenuGroup,\n  DropdownMenuPortal,\n  DropdownMenuSub,\n  DropdownMenuSubContent,\n  DropdownMenuSubTrigger,\n  DropdownMenuRadioGroup,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA;AACA;AACA;AAAA;AAAA;AACA;AALA;;;;;;AAOA,MAAM,eAAe,6TAA0B;AAE/C,MAAM,sBAAsB,gUAA6B;AAEzD,MAAM,oBAAoB,8TAA2B;AAErD,MAAM,qBAAqB,+TAA4B;AAEvD,MAAM,kBAAkB,4TAAyB;AAEjD,MAAM,yBAAyB,mUAAgC;AAE/D,MAAM,uCAAyB,oWAAgB,CAK7C,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAC3C,+XAAC,mUAAgC;QAC/B,KAAK;QACL,WAAW,IAAA,2JAAE,EACX,wIACA,SAAS,QACT;QAED,GAAG,KAAK;;YAER;0BACD,+XAAC,4VAAY;gBAAC,WAAU;;;;;;;;;;;;AAG5B,uBAAuB,WAAW,GAAG,mUAAgC,CAAC,WAAW;AAEjF,MAAM,uCAAyB,oWAAgB,CAG7C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,+XAAC,mUAAgC;QAC/B,KAAK;QACL,WAAW,IAAA,2JAAE,EACX,ybACA;QAED,GAAG,KAAK;;;;;;AAGb,uBAAuB,WAAW,GAAG,mUAAgC,CAAC,WAAW;AAEjF,MAAM,oCAAsB,oWAAgB,CAG1C,CAAC,EAAE,SAAS,EAAE,aAAa,CAAC,EAAE,GAAG,OAAO,EAAE,oBAC1C,+XAAC,+TAA4B;kBAC3B,cAAA,+XAAC,gUAA6B;YAC5B,KAAK;YACL,YAAY;YACZ,WAAW,IAAA,2JAAE,EACX,ybACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIf,oBAAoB,WAAW,GAAG,gUAA6B,CAAC,WAAW;AAE3E,MAAM,iCAAmB,oWAAgB,CAKvC,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE,oBACjC,+XAAC,6TAA0B;QACzB,KAAK;QACL,WAAW,IAAA,2JAAE,EACX,mOACA,SAAS,QACT;QAED,GAAG,KAAK;;;;;;AAGb,iBAAiB,WAAW,GAAG,6TAA0B,CAAC,WAAW;AAErE,MAAM,yCAA2B,oWAAgB,CAG/C,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,OAAO,EAAE,oBAC7C,+XAAC,qUAAkC;QACjC,KAAK;QACL,WAAW,IAAA,2JAAE,EACX,wOACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,+XAAC;gBAAK,WAAU;0BACd,cAAA,+XAAC,sUAAmC;8BAClC,cAAA,+XAAC,mUAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGpB;;;;;;;AAGL,yBAAyB,WAAW,GAAG,qUAAkC,CAAC,WAAW;AAErF,MAAM,sCAAwB,oWAAgB,CAG5C,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,+XAAC,kUAA+B;QAC9B,KAAK;QACL,WAAW,IAAA,2JAAE,EACX,wOACA;QAED,GAAG,KAAK;;0BAET,+XAAC;gBAAK,WAAU;0BACd,cAAA,+XAAC,sUAAmC;8BAClC,cAAA,+XAAC,sUAAM;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGrB;;;;;;;AAGL,sBAAsB,WAAW,GAAG,kUAA+B,CAAC,WAAW;AAE/E,MAAM,kCAAoB,oWAAgB,CAKxC,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE,oBACjC,+XAAC,8TAA2B;QAC1B,KAAK;QACL,WAAW,IAAA,2JAAE,EACX,qCACA,SAAS,QACT;QAED,GAAG,KAAK;;;;;;AAGb,kBAAkB,WAAW,GAAG,8TAA2B,CAAC,WAAW;AAEvE,MAAM,sCAAwB,oWAAgB,CAG5C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,+XAAC,kUAA+B;QAC9B,KAAK;QACL,WAAW,IAAA,2JAAE,EAAC,4BAA4B;QACzC,GAAG,KAAK;;;;;;AAGb,sBAAsB,WAAW,GAAG,kUAA+B,CAAC,WAAW;AAE/E,MAAM,uBAAuB,CAAC,EAC5B,SAAS,EACT,GAAG,OACmC;IACtC,qBACE,+XAAC;QACC,WAAW,IAAA,2JAAE,EAAC,8CAA8C;QAC3D,GAAG,KAAK;;;;;;AAGf;AACA,qBAAqB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 423, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/components/ui/theme-toggle.tsx"], "sourcesContent": ["'use client'\n\nimport { <PERSON>, Sun, Monitor } from 'lucide-react'\nimport { Button } from './button'\nimport { useTheme } from '@/components/providers/theme-provider'\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuTrigger,\n} from './dropdown-menu'\n\nexport function ThemeToggle() {\n  const { theme, actualTheme, mounted, setTheme } = useTheme()\n\n  const toggleTheme = () => {\n    if (!mounted) return\n    const next = actualTheme === 'light' ? 'dark' : 'light'\n    setTheme(next)\n  }\n\n  return (\n    <DropdownMenu>\n      <DropdownMenuTrigger asChild>\n        <Button\n          variant=\"ghost\"\n          size=\"icon\"\n          className=\"h-9 w-9\"\n          onClick={toggleTheme}\n          title={`Switch to ${actualTheme === 'light' ? 'dark' : 'light'} mode`}\n        >\n          <Sun className=\"h-4 w-4 rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0\" />\n          <Moon className=\"absolute h-4 w-4 rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100\" />\n          <span className=\"sr-only\">Toggle theme</span>\n        </Button>\n      </DropdownMenuTrigger>\n      <DropdownMenuContent align=\"end\">\n        <DropdownMenuItem onClick={() => setTheme('light')}>\n          <Sun className=\"mr-2 h-4 w-4\" />\n          <span>Light</span>\n        </DropdownMenuItem>\n        <DropdownMenuItem onClick={() => setTheme('dark')}>\n          <Moon className=\"mr-2 h-4 w-4\" />\n          <span>Dark</span>\n        </DropdownMenuItem>\n        <DropdownMenuItem onClick={() => setTheme('system')}>\n          <Monitor className=\"mr-2 h-4 w-4\" />\n          <span>System</span>\n        </DropdownMenuItem>\n      </DropdownMenuContent>\n    </DropdownMenu>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAAA;AAAA;AACA;AACA;AACA;AALA;;;;;;AAYO,SAAS;IACd,MAAM,EAAE,KAAK,EAAE,WAAW,EAAE,OAAO,EAAE,QAAQ,EAAE,GAAG,IAAA,kMAAQ;IAE1D,MAAM,cAAc;QAClB,IAAI,CAAC,SAAS;QACd,MAAM,OAAO,gBAAgB,UAAU,SAAS;QAChD,SAAS;IACX;IAEA,qBACE,+XAAC,8LAAY;;0BACX,+XAAC,qMAAmB;gBAAC,OAAO;0BAC1B,cAAA,+XAAC,8KAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,WAAU;oBACV,SAAS;oBACT,OAAO,CAAC,UAAU,EAAE,gBAAgB,UAAU,SAAS,QAAQ,KAAK,CAAC;;sCAErE,+XAAC,6TAAG;4BAAC,WAAU;;;;;;sCACf,+XAAC,gUAAI;4BAAC,WAAU;;;;;;sCAChB,+XAAC;4BAAK,WAAU;sCAAU;;;;;;;;;;;;;;;;;0BAG9B,+XAAC,qMAAmB;gBAAC,OAAM;;kCACzB,+XAAC,kMAAgB;wBAAC,SAAS,IAAM,SAAS;;0CACxC,+XAAC,6TAAG;gCAAC,WAAU;;;;;;0CACf,+XAAC;0CAAK;;;;;;;;;;;;kCAER,+XAAC,kMAAgB;wBAAC,SAAS,IAAM,SAAS;;0CACxC,+XAAC,gUAAI;gCAAC,WAAU;;;;;;0CAChB,+XAAC;0CAAK;;;;;;;;;;;;kCAER,+XAAC,kMAAgB;wBAAC,SAAS,IAAM,SAAS;;0CACxC,+XAAC,yUAAO;gCAAC,WAAU;;;;;;0CACnB,+XAAC;0CAAK;;;;;;;;;;;;;;;;;;;;;;;;AAKhB", "debugId": null}}, {"offset": {"line": 580, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/components/layout/dashboard-layout.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { useSession, signOut } from 'next-auth/react'\nimport { useRouter } from 'next/navigation'\nimport { Button } from '@/components/ui/button'\n\nimport { ThemeToggle } from '@/components/ui/theme-toggle'\nimport {\n  Menu,\n  X,\n  User,\n  LogOut,\n  Settings,\n  Bell,\n  Search,\n  School,\n  Plus,\n  Upload,\n  Download,\n  Users,\n  BookOpen,\n  GraduationCap,\n  FileText,\n  BarChart3,\n  Calendar,\n  Home,\n  Edit,\n  ClipboardList,\n  Award\n} from 'lucide-react'\n\ninterface DashboardLayoutProps {\n  children: React.ReactNode\n  title: string\n  navigation: {\n    name: string\n    href: string\n    icon: string\n  }[]\n}\n\n// Icon mapping object\nconst iconMap: Record<string, React.ComponentType<{ className?: string }>> = {\n  Plus,\n  Upload,\n  Download,\n  Users,\n  BookOpen,\n  GraduationCap,\n  FileText,\n  BarChart3,\n  Calendar,\n  Home,\n  Settings,\n  Bell,\n  User,\n  Edit,\n  ClipboardList,\n  Award\n}\n\nexport default function DashboardLayout({ children, title, navigation }: DashboardLayoutProps) {\n  const { data: session } = useSession()\n  const router = useRouter()\n  const [sidebarOpen, setSidebarOpen] = useState(false)\n\n  const handleSignOut = async () => {\n    await signOut({ callbackUrl: '/' })\n  }\n\n  const getIcon = (iconName: string) => {\n    const IconComponent = iconMap[iconName]\n    return IconComponent || Home // fallback to Home icon if not found\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 dark:bg-gray-950\">\n      {/* Mobile sidebar */}\n      <div className={`fixed inset-0 z-50 lg:hidden ${sidebarOpen ? 'block' : 'hidden'}`}>\n        <div className=\"fixed inset-0 bg-gray-600 bg-opacity-75\" onClick={() => setSidebarOpen(false)} />\n        <div className=\"fixed inset-y-0 left-0 flex w-64 flex-col bg-white dark:bg-gray-900\">\n          <div className=\"flex h-16 items-center justify-between px-4\">\n            <div className=\"flex items-center\">\n              <School className=\"h-8 w-8 text-blue-600\" />\n              <span className=\"ml-2 text-lg font-semibold\">SMS</span>\n            </div>\n            <Button\n              variant=\"ghost\"\n              size=\"sm\"\n              onClick={() => setSidebarOpen(false)}\n            >\n              <X className=\"h-5 w-5\" />\n            </Button>\n          </div>\n          <nav className=\"flex-1 space-y-1 px-2 py-4\">\n            {navigation.map((item) => {\n              const IconComponent = getIcon(item.icon)\n              return (\n                <Button\n                  key={`mobile-${item.name}`}\n                  variant=\"ghost\"\n                  className=\"w-full justify-start\"\n                  onClick={() => {\n                    router.push(item.href)\n                    setSidebarOpen(false)\n                  }}\n                >\n                  <IconComponent className=\"mr-3 h-5 w-5\" />\n                  {item.name}\n                </Button>\n              )\n            })}\n          </nav>\n        </div>\n      </div>\n\n      {/* Desktop sidebar */}\n      <div className=\"hidden lg:fixed lg:inset-y-0 lg:flex lg:w-64 lg:flex-col\">\n        <div className=\"flex flex-col flex-grow bg-white dark:bg-gray-900 border-r border-gray-200 dark:border-gray-800\">\n          <div className=\"flex h-16 items-center px-4\">\n            <School className=\"h-8 w-8 text-blue-600\" />\n            <span className=\"ml-2 text-lg font-semibold hidden xl:inline\">School Management System</span>\n            <span className=\"ml-2 text-lg font-semibold xl:hidden\">SMS</span>\n          </div>\n          <nav className=\"flex-1 space-y-1 px-2 py-4\">\n            {navigation.map((item) => {\n              const IconComponent = getIcon(item.icon)\n              return (\n                <Button\n                  key={`desktop-${item.name}`}\n                  variant=\"ghost\"\n                  className=\"w-full justify-start\"\n                  onClick={() => router.push(item.href)}\n                >\n                  <IconComponent className=\"mr-3 h-5 w-5\" />\n                  {item.name}\n                </Button>\n              )\n            })}\n          </nav>\n        </div>\n      </div>\n\n      {/* Main content */}\n      <div className=\"lg:pl-64\">\n        {/* Top bar */}\n        <div className=\"sticky top-0 z-40 flex h-16 shrink-0 items-center gap-x-4 border-b border-gray-200 dark:border-gray-800 bg-white dark:bg-gray-900 px-4 shadow-sm sm:gap-x-6 sm:px-6 lg:px-8\">\n          <Button\n            variant=\"ghost\"\n            size=\"sm\"\n            className=\"lg:hidden\"\n            onClick={() => setSidebarOpen(true)}\n          >\n            <Menu className=\"h-5 w-5\" />\n          </Button>\n\n          <div className=\"flex flex-1 gap-x-4 self-stretch lg:gap-x-6\">\n            <div className=\"relative flex flex-1\">\n              <div className=\"pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3\">\n                <Search className=\"h-5 w-5 text-gray-400\" />\n              </div>\n              <input\n                type=\"text\"\n                placeholder=\"Search...\"\n                className=\"block h-full w-full border-0 py-0 pl-10 pr-0 text-gray-900 dark:text-gray-100 placeholder:text-gray-400 dark:placeholder:text-gray-500 focus:ring-0 sm:text-sm bg-transparent\"\n              />\n            </div>\n          </div>\n\n          <div className=\"flex items-center gap-x-4 lg:gap-x-6\">\n            <ThemeToggle />\n\n            <Button variant=\"ghost\" size=\"sm\">\n              <Bell className=\"h-5 w-5\" />\n            </Button>\n\n            <div className=\"relative\">\n              <div className=\"flex items-center gap-x-3\">\n                <div className=\"text-sm hidden sm:block\">\n                  <p className=\"font-medium text-gray-900 dark:text-gray-100\">\n                    {session?.user?.firstName} {session?.user?.lastName}\n                  </p>\n                  <p className=\"text-gray-500 dark:text-gray-400 capitalize\">\n                    {session?.user?.role?.toLowerCase()}\n                  </p>\n                </div>\n                <div className=\"flex items-center gap-x-2\">\n                  <Button\n                    variant=\"ghost\"\n                    size=\"sm\"\n                    onClick={handleSignOut}\n                    className=\"flex items-center gap-2\"\n                  >\n                    <LogOut className=\"h-4 w-4\" />\n                    <span className=\"hidden sm:inline\">Sign Out</span>\n                  </Button>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Page content */}\n        <main className=\"py-6\">\n          <div className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\n            <div className=\"mb-6\">\n              <h1 className=\"text-2xl font-bold text-gray-900 dark:text-gray-100\">{title}</h1>\n            </div>\n            {children}\n          </div>\n        </main>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AARA;;;;;;;;AA0CA,sBAAsB;AACtB,MAAM,UAAuE;IAC3E,MAAA,gUAAI;IACJ,QAAA,sUAAM;IACN,UAAA,4UAAQ;IACR,OAAA,mUAAK;IACL,UAAA,gVAAQ;IACR,eAAA,+VAAa;IACb,UAAA,gVAAQ;IACR,WAAA,qVAAS;IACT,UAAA,4UAAQ;IACR,MAAA,iUAAI;IACJ,UAAA,4UAAQ;IACR,MAAA,gUAAI;IACJ,MAAA,gUAAI;IACJ,MAAA,yUAAI;IACJ,eAAA,+VAAa;IACb,OAAA,mUAAK;AACP;AAEe,SAAS,gBAAgB,EAAE,QAAQ,EAAE,KAAK,EAAE,UAAU,EAAwB;IAC3F,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,IAAA,0SAAU;IACpC,MAAM,SAAS,IAAA,gSAAS;IACxB,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,kWAAQ,EAAC;IAE/C,MAAM,gBAAgB;QACpB,MAAM,IAAA,uSAAO,EAAC;YAAE,aAAa;QAAI;IACnC;IAEA,MAAM,UAAU,CAAC;QACf,MAAM,gBAAgB,OAAO,CAAC,SAAS;QACvC,OAAO,iBAAiB,iUAAI,CAAC,qCAAqC;;IACpE;IAEA,qBACE,+XAAC;QAAI,WAAU;;0BAEb,+XAAC;gBAAI,WAAW,CAAC,6BAA6B,EAAE,cAAc,UAAU,UAAU;;kCAChF,+XAAC;wBAAI,WAAU;wBAA0C,SAAS,IAAM,eAAe;;;;;;kCACvF,+XAAC;wBAAI,WAAU;;0CACb,+XAAC;gCAAI,WAAU;;kDACb,+XAAC;wCAAI,WAAU;;0DACb,+XAAC,sUAAM;gDAAC,WAAU;;;;;;0DAClB,+XAAC;gDAAK,WAAU;0DAA6B;;;;;;;;;;;;kDAE/C,+XAAC,8KAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,eAAe;kDAE9B,cAAA,+XAAC,uTAAC;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAGjB,+XAAC;gCAAI,WAAU;0CACZ,WAAW,GAAG,CAAC,CAAC;oCACf,MAAM,gBAAgB,QAAQ,KAAK,IAAI;oCACvC,qBACE,+XAAC,8KAAM;wCAEL,SAAQ;wCACR,WAAU;wCACV,SAAS;4CACP,OAAO,IAAI,CAAC,KAAK,IAAI;4CACrB,eAAe;wCACjB;;0DAEA,+XAAC;gDAAc,WAAU;;;;;;4CACxB,KAAK,IAAI;;uCATL,CAAC,OAAO,EAAE,KAAK,IAAI,EAAE;;;;;gCAYhC;;;;;;;;;;;;;;;;;;0BAMN,+XAAC;gBAAI,WAAU;0BACb,cAAA,+XAAC;oBAAI,WAAU;;sCACb,+XAAC;4BAAI,WAAU;;8CACb,+XAAC,sUAAM;oCAAC,WAAU;;;;;;8CAClB,+XAAC;oCAAK,WAAU;8CAA8C;;;;;;8CAC9D,+XAAC;oCAAK,WAAU;8CAAuC;;;;;;;;;;;;sCAEzD,+XAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAC;gCACf,MAAM,gBAAgB,QAAQ,KAAK,IAAI;gCACvC,qBACE,+XAAC,8KAAM;oCAEL,SAAQ;oCACR,WAAU;oCACV,SAAS,IAAM,OAAO,IAAI,CAAC,KAAK,IAAI;;sDAEpC,+XAAC;4CAAc,WAAU;;;;;;wCACxB,KAAK,IAAI;;mCANL,CAAC,QAAQ,EAAE,KAAK,IAAI,EAAE;;;;;4BASjC;;;;;;;;;;;;;;;;;0BAMN,+XAAC;gBAAI,WAAU;;kCAEb,+XAAC;wBAAI,WAAU;;0CACb,+XAAC,8KAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,eAAe;0CAE9B,cAAA,+XAAC,gUAAI;oCAAC,WAAU;;;;;;;;;;;0CAGlB,+XAAC;gCAAI,WAAU;0CACb,cAAA,+XAAC;oCAAI,WAAU;;sDACb,+XAAC;4CAAI,WAAU;sDACb,cAAA,+XAAC,sUAAM;gDAAC,WAAU;;;;;;;;;;;sDAEpB,+XAAC;4CACC,MAAK;4CACL,aAAY;4CACZ,WAAU;;;;;;;;;;;;;;;;;0CAKhB,+XAAC;gCAAI,WAAU;;kDACb,+XAAC,4LAAW;;;;;kDAEZ,+XAAC,8KAAM;wCAAC,SAAQ;wCAAQ,MAAK;kDAC3B,cAAA,+XAAC,gUAAI;4CAAC,WAAU;;;;;;;;;;;kDAGlB,+XAAC;wCAAI,WAAU;kDACb,cAAA,+XAAC;4CAAI,WAAU;;8DACb,+XAAC;oDAAI,WAAU;;sEACb,+XAAC;4DAAE,WAAU;;gEACV,SAAS,MAAM;gEAAU;gEAAE,SAAS,MAAM;;;;;;;sEAE7C,+XAAC;4DAAE,WAAU;sEACV,SAAS,MAAM,MAAM;;;;;;;;;;;;8DAG1B,+XAAC;oDAAI,WAAU;8DACb,cAAA,+XAAC,8KAAM;wDACL,SAAQ;wDACR,MAAK;wDACL,SAAS;wDACT,WAAU;;0EAEV,+XAAC,0UAAM;gEAAC,WAAU;;;;;;0EAClB,+XAAC;gEAAK,WAAU;0EAAmB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAS/C,+XAAC;wBAAK,WAAU;kCACd,cAAA,+XAAC;4BAAI,WAAU;;8CACb,+XAAC;oCAAI,WAAU;8CACb,cAAA,+XAAC;wCAAG,WAAU;kDAAuD;;;;;;;;;;;gCAEtE;;;;;;;;;;;;;;;;;;;;;;;;AAMb", "debugId": null}}, {"offset": {"line": 1064, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/lib/grading.ts"], "sourcesContent": ["// Grade mapping configuration\r\nexport interface GradeConfig {\r\n  A_PLUS: number; // >= 90\r\n  A: number;     // >= 80\r\n  B_PLUS: number; // >= 70\r\n  B: number;     // >= 60\r\n  C: number;     // >= 50\r\n  D: number;     // >= 40\r\n  E: number;     // < 40\r\n}\r\n\r\n// Default grade configuration\r\nexport const DEFAULT_GRADE_CONFIG: GradeConfig = {\r\n  A_PLUS: 90,\r\n  A: 80,\r\n  B_PLUS: 70,\r\n  B: 60,\r\n  C: 50,\r\n  D: 40,\r\n  E: 0\r\n}\r\n\r\n/**\r\n * Calculate grade based on percentage\r\n */\r\nexport function calculateGrade(percentage: number, config: GradeConfig = DEFAULT_GRADE_CONFIG): string {\r\n  if (percentage >= config.A_PLUS) return 'A+'\r\n  if (percentage >= config.A) return 'A'\r\n  if (percentage >= config.B_PLUS) return 'B+'\r\n  if (percentage >= config.B) return 'B'\r\n  if (percentage >= config.C) return 'C'\r\n  if (percentage >= config.D) return 'D'\r\n  return 'E'\r\n}\r\n\r\n/**\r\n * Calculate percentage from obtained marks and max marks\r\n */\r\nexport function calculatePercentage(obtainedMarks: number, maxMarks: number): number {\r\n  if (maxMarks === 0) return 0\r\n  return Math.round((obtainedMarks / maxMarks) * 100 * 100) / 100 // Round to 2 decimal places\r\n}\r\n\r\n/**\r\n * Calculate GPA based on grades\r\n */\r\nexport function calculateGPA(grades: string[], config: GradeConfig = DEFAULT_GRADE_CONFIG): number {\r\n  if (grades.length === 0) return 0\r\n\r\n  const gradePoints = grades.map(grade => {\r\n    switch (grade) {\r\n      case 'A+': return 4.0\r\n      case 'A': return 3.7\r\n      case 'B+': return 3.3\r\n      case 'B': return 3.0\r\n      case 'C': return 2.0\r\n      case 'D': return 1.0\r\n      case 'E': return 0.0\r\n      default: return 0.0\r\n    }\r\n  })\r\n\r\n  const totalPoints = gradePoints.reduce((sum, points) => sum + points, 0 as number)\r\n  return Math.round((totalPoints / grades.length) * 100) / 100 // Round to 2 decimal places\r\n}\r\n\r\n/**\r\n * Calculate weighted GPA based on subject credits\r\n */\r\nexport function calculateWeightedGPA(\r\n  grades: string[], \r\n  credits: number[] = [], \r\n  config: GradeConfig = DEFAULT_GRADE_CONFIG\r\n): number {\r\n  if (grades.length === 0) return 0\r\n\r\n  // If no credits provided, use equal weight (default credit = 1)\r\n  const subjectCredits = credits.length === grades.length ? credits : grades.map(() => 1)\r\n\r\n  const gradePoints = grades.map(grade => {\r\n    switch (grade) {\r\n      case 'A+': return 4.0\r\n      case 'A': return 3.7\r\n      case 'B+': return 3.3\r\n      case 'B': return 3.0\r\n      case 'C': return 2.0\r\n      case 'D': return 1.0\r\n      case 'E': return 0.0\r\n      default: return 0.0\r\n    }\r\n  })\r\n\r\n  const totalWeightedPoints = gradePoints.reduce((sum, points, index) => {\r\n    return sum + (points * subjectCredits[index])\r\n  }, 0 as number)\r\n\r\n  const totalCredits = subjectCredits.reduce((sum, credit) => sum + credit, 0)\r\n  \r\n  return Math.round((totalWeightedPoints / totalCredits) * 100) / 100 // Round to 2 decimal places\r\n}\r\n\r\n/**\r\n * Calculate attendance percentage\r\n */\r\nexport function calculateAttendancePercentage(presentDays: number, totalDays: number): number {\r\n  if (totalDays === 0) return 0\r\n  return Math.round((presentDays / totalDays) * 100 * 100) / 100 // Round to 2 decimal places\r\n}\r\n\r\n/**\r\n * Get grade color for UI display (text color)\r\n */\r\nexport function getGradeColor(grade: string): string {\r\n  switch (grade) {\r\n    case 'A+':\r\n    case 'A':\r\n      return 'text-green-600'\r\n    case 'B+':\r\n    case 'B':\r\n      return 'text-blue-600'\r\n    case 'C+':\r\n    case 'C':\r\n      return 'text-yellow-600'\r\n    case 'D':\r\n      return 'text-orange-600'\r\n    case 'E':\r\n    case 'F':\r\n      return 'text-red-600'\r\n    default:\r\n      return 'text-gray-600'\r\n  }\r\n}\r\n\r\n/**\r\n * Get grade badge color for UI display (background + text + border)\r\n */\r\nexport function getGradeBadgeColor(grade: string): string {\r\n  switch (grade) {\r\n    case 'A+':\r\n    case 'A':\r\n      return 'bg-green-100 text-green-800 border-green-200'\r\n    case 'B+':\r\n    case 'B':\r\n      return 'bg-blue-100 text-blue-800 border-blue-200'\r\n    case 'C+':\r\n    case 'C':\r\n      return 'bg-yellow-100 text-yellow-800 border-yellow-200'\r\n    case 'D':\r\n      return 'bg-orange-100 text-orange-800 border-orange-200'\r\n    case 'E':\r\n    case 'F':\r\n      return 'bg-red-100 text-red-800 border-red-200'\r\n    default:\r\n      return 'bg-gray-100 text-gray-800 border-gray-200'\r\n  }\r\n}\r\n\r\n/**\r\n * Get attendance status color for UI display\r\n */\r\nexport function getAttendanceColor(status: 'PRESENT' | 'ABSENT' | 'LATE' | 'HALF_DAY'): string {\r\n  switch (status) {\r\n    case 'PRESENT':\r\n      return 'text-green-600'\r\n    case 'ABSENT':\r\n      return 'text-red-600'\r\n    case 'LATE':\r\n      return 'text-yellow-600'\r\n    case 'HALF_DAY':\r\n      return 'text-orange-600'\r\n    default:\r\n      return 'text-gray-600'\r\n  }\r\n}\r\n"], "names": [], "mappings": "AAAA,8BAA8B;;;;;;;;;;;;;;;;;;;;;AAYvB,MAAM,uBAAoC;IAC/C,QAAQ;IACR,GAAG;IACH,QAAQ;IACR,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;AACL;AAKO,SAAS,eAAe,UAAkB,EAAE,SAAsB,oBAAoB;IAC3F,IAAI,cAAc,OAAO,MAAM,EAAE,OAAO;IACxC,IAAI,cAAc,OAAO,CAAC,EAAE,OAAO;IACnC,IAAI,cAAc,OAAO,MAAM,EAAE,OAAO;IACxC,IAAI,cAAc,OAAO,CAAC,EAAE,OAAO;IACnC,IAAI,cAAc,OAAO,CAAC,EAAE,OAAO;IACnC,IAAI,cAAc,OAAO,CAAC,EAAE,OAAO;IACnC,OAAO;AACT;AAKO,SAAS,oBAAoB,aAAqB,EAAE,QAAgB;IACzE,IAAI,aAAa,GAAG,OAAO;IAC3B,OAAO,KAAK,KAAK,CAAC,AAAC,gBAAgB,WAAY,MAAM,OAAO,IAAI,4BAA4B;;AAC9F;AAKO,SAAS,aAAa,MAAgB,EAAE,SAAsB,oBAAoB;IACvF,IAAI,OAAO,MAAM,KAAK,GAAG,OAAO;IAEhC,MAAM,cAAc,OAAO,GAAG,CAAC,CAAA;QAC7B,OAAQ;YACN,KAAK;gBAAM,OAAO;YAClB,KAAK;gBAAK,OAAO;YACjB,KAAK;gBAAM,OAAO;YAClB,KAAK;gBAAK,OAAO;YACjB,KAAK;gBAAK,OAAO;YACjB,KAAK;gBAAK,OAAO;YACjB,KAAK;gBAAK,OAAO;YACjB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,cAAc,YAAY,MAAM,CAAC,CAAC,KAAK,SAAW,MAAM,QAAQ;IACtE,OAAO,KAAK,KAAK,CAAC,AAAC,cAAc,OAAO,MAAM,GAAI,OAAO,IAAI,4BAA4B;;AAC3F;AAKO,SAAS,qBACd,MAAgB,EAChB,UAAoB,EAAE,EACtB,SAAsB,oBAAoB;IAE1C,IAAI,OAAO,MAAM,KAAK,GAAG,OAAO;IAEhC,gEAAgE;IAChE,MAAM,iBAAiB,QAAQ,MAAM,KAAK,OAAO,MAAM,GAAG,UAAU,OAAO,GAAG,CAAC,IAAM;IAErF,MAAM,cAAc,OAAO,GAAG,CAAC,CAAA;QAC7B,OAAQ;YACN,KAAK;gBAAM,OAAO;YAClB,KAAK;gBAAK,OAAO;YACjB,KAAK;gBAAM,OAAO;YAClB,KAAK;gBAAK,OAAO;YACjB,KAAK;gBAAK,OAAO;YACjB,KAAK;gBAAK,OAAO;YACjB,KAAK;gBAAK,OAAO;YACjB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,sBAAsB,YAAY,MAAM,CAAC,CAAC,KAAK,QAAQ;QAC3D,OAAO,MAAO,SAAS,cAAc,CAAC,MAAM;IAC9C,GAAG;IAEH,MAAM,eAAe,eAAe,MAAM,CAAC,CAAC,KAAK,SAAW,MAAM,QAAQ;IAE1E,OAAO,KAAK,KAAK,CAAC,AAAC,sBAAsB,eAAgB,OAAO,IAAI,4BAA4B;;AAClG;AAKO,SAAS,8BAA8B,WAAmB,EAAE,SAAiB;IAClF,IAAI,cAAc,GAAG,OAAO;IAC5B,OAAO,KAAK,KAAK,CAAC,AAAC,cAAc,YAAa,MAAM,OAAO,IAAI,4BAA4B;;AAC7F;AAKO,SAAS,cAAc,KAAa;IACzC,OAAQ;QACN,KAAK;QACL,KAAK;YACH,OAAO;QACT,KAAK;QACL,KAAK;YACH,OAAO;QACT,KAAK;QACL,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;QACL,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF;AAKO,SAAS,mBAAmB,KAAa;IAC9C,OAAQ;QACN,KAAK;QACL,KAAK;YACH,OAAO;QACT,KAAK;QACL,KAAK;YACH,OAAO;QACT,KAAK;QACL,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;QACL,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF;AAKO,SAAS,mBAAmB,MAAkD;IACnF,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF", "debugId": null}}, {"offset": {"line": 1228, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/lib/navigation.ts"], "sourcesContent": ["// Shared navigation configurations for different user roles\n\nexport const adminNavigation = [\n  { name: 'Dashboard', href: '/admin', icon: 'BarChart3' },\n  { name: 'Students', href: '/admin/students', icon: 'Users' },\n  { name: 'Teachers', href: '/admin/teachers', icon: 'GraduationCap' },\n  { name: 'Classes & Sections', href: '/admin/classes', icon: 'BookOpen' },\n  { name: 'Subjects', href: '/admin/subjects', icon: 'FileText' },\n  { name: 'Terms & Exams', href: '/admin/exams', icon: 'Calendar' },\n  { name: 'Attendance', href: '/admin/attendance', icon: 'ClipboardList' },\n  { name: 'Marks', href: '/admin/marks', icon: 'Award' },\n  { name: 'Reports', href: '/admin/reports', icon: 'Download' },\n  { name: 'Settings', href: '/admin/settings', icon: 'Settings' },\n];\n\nexport const teacherNavigation = [\n  { name: 'Dashboard', href: '/teacher', icon: 'BarChart3' },\n  { name: 'My Classes', href: '/teacher/classes', icon: 'BookOpen' },\n  { name: 'Attendance', href: '/teacher/attendance', icon: 'ClipboardList' },\n  { name: 'Marks', href: '/teacher/marks', icon: 'Award' },\n  { name: 'Students', href: '/teacher/students', icon: 'Users' },\n  { name: 'Reports', href: '/teacher/reports', icon: 'FileText' },\n  { name: 'Profile', href: '/teacher/profile', icon: 'User' },\n];\n\nexport const studentNavigation = [\n  { name: 'Dashboard', href: '/student', icon: 'BarChart3' },\n  { name: 'My Classes', href: '/student/classes', icon: 'BookOpen' },\n  { name: 'Attendance', href: '/student/attendance', icon: 'ClipboardList' },\n  { name: 'Marks', href: '/student/marks', icon: 'Award' },\n  { name: 'Reports', href: '/student/reports', icon: 'FileText' },\n  { name: 'Profile', href: '/student/profile', icon: 'User' },\n];\n\n/**\n * Get the default dashboard URL for a user role\n */\nexport function getRoleDashboardUrl(role: string): string {\n  switch (role) {\n    case 'ADMIN':\n      return '/admin'\n    case 'TEACHER':\n      return '/teacher'\n    case 'STUDENT':\n      return '/student'\n    default:\n      return '/'\n  }\n}\n\n/**\n * Get navigation items for a user role\n */\nexport function getRoleNavigation(role: string) {\n  switch (role) {\n    case 'ADMIN':\n      return adminNavigation\n    case 'TEACHER':\n      return teacherNavigation\n    case 'STUDENT':\n      return studentNavigation\n    default:\n      return []\n  }\n}"], "names": [], "mappings": "AAAA,4DAA4D;;;;;;;;;;;;;AAErD,MAAM,kBAAkB;IAC7B;QAAE,MAAM;QAAa,MAAM;QAAU,MAAM;IAAY;IACvD;QAAE,MAAM;QAAY,MAAM;QAAmB,MAAM;IAAQ;IAC3D;QAAE,MAAM;QAAY,MAAM;QAAmB,MAAM;IAAgB;IACnE;QAAE,MAAM;QAAsB,MAAM;QAAkB,MAAM;IAAW;IACvE;QAAE,MAAM;QAAY,MAAM;QAAmB,MAAM;IAAW;IAC9D;QAAE,MAAM;QAAiB,MAAM;QAAgB,MAAM;IAAW;IAChE;QAAE,MAAM;QAAc,MAAM;QAAqB,MAAM;IAAgB;IACvE;QAAE,MAAM;QAAS,MAAM;QAAgB,MAAM;IAAQ;IACrD;QAAE,MAAM;QAAW,MAAM;QAAkB,MAAM;IAAW;IAC5D;QAAE,MAAM;QAAY,MAAM;QAAmB,MAAM;IAAW;CAC/D;AAEM,MAAM,oBAAoB;IAC/B;QAAE,MAAM;QAAa,MAAM;QAAY,MAAM;IAAY;IACzD;QAAE,MAAM;QAAc,MAAM;QAAoB,MAAM;IAAW;IACjE;QAAE,MAAM;QAAc,MAAM;QAAuB,MAAM;IAAgB;IACzE;QAAE,MAAM;QAAS,MAAM;QAAkB,MAAM;IAAQ;IACvD;QAAE,MAAM;QAAY,MAAM;QAAqB,MAAM;IAAQ;IAC7D;QAAE,MAAM;QAAW,MAAM;QAAoB,MAAM;IAAW;IAC9D;QAAE,MAAM;QAAW,MAAM;QAAoB,MAAM;IAAO;CAC3D;AAEM,MAAM,oBAAoB;IAC/B;QAAE,MAAM;QAAa,MAAM;QAAY,MAAM;IAAY;IACzD;QAAE,MAAM;QAAc,MAAM;QAAoB,MAAM;IAAW;IACjE;QAAE,MAAM;QAAc,MAAM;QAAuB,MAAM;IAAgB;IACzE;QAAE,MAAM;QAAS,MAAM;QAAkB,MAAM;IAAQ;IACvD;QAAE,MAAM;QAAW,MAAM;QAAoB,MAAM;IAAW;IAC9D;QAAE,MAAM;QAAW,MAAM;QAAoB,MAAM;IAAO;CAC3D;AAKM,SAAS,oBAAoB,IAAY;IAC9C,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF;AAKO,SAAS,kBAAkB,IAAY;IAC5C,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO,EAAE;IACb;AACF", "debugId": null}}, {"offset": {"line": 1390, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/app/%28dash%29/admin/marks/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Label } from '@/components/ui/label'\nimport DashboardLayout from '@/components/layout/dashboard-layout'\nimport { getGradeBadgeColor } from '@/lib/grading'\nimport { \n  Calendar, \n  Award, \n  BookOpen, \n  Users, \n  TrendingUp,\n  FileText,\n  Download,\n  Filter,\n  Plus,\n  CheckCircle,\n  XCircle\n} from 'lucide-react'\n\ninterface MarkRecord {\n  id: string\n  studentName: string\n  admissionNo: string\n  examName: string\n  subjectName: string\n  className: string\n  sectionName: string\n  obtainedMarks: number\n  maxMarks: number\n  percentage: number\n  grade: string\n  gradedBy: string\n  date: string\n}\n\nimport { adminNavigation } from '@/lib/navigation';\n\nexport default function MarksPage() {\n  const [markRecords, setMarkRecords] = useState<MarkRecord[]>([])\n  const [selectedExam, setSelectedExam] = useState('all')\n  const [selectedSubject, setSelectedSubject] = useState('all')\n  const [selectedClass, setSelectedClass] = useState('all')\n\n  // Mock data - in real app, this would come from API\n  useEffect(() => {\n    setMarkRecords([\n      {\n        id: '1',\n        studentName: 'John Doe',\n        admissionNo: 'STU001',\n        examName: 'Unit Test 1',\n        subjectName: 'Mathematics',\n        className: 'Grade 8',\n        sectionName: 'A',\n        obtainedMarks: 45,\n        maxMarks: 50,\n        percentage: 90,\n        grade: 'A+',\n        gradedBy: 'Mrs. Smith',\n        date: '2024-10-15'\n      },\n      {\n        id: '2',\n        studentName: 'Jane Smith',\n        admissionNo: 'STU002',\n        examName: 'Unit Test 1',\n        subjectName: 'Mathematics',\n        className: 'Grade 8',\n        sectionName: 'A',\n        obtainedMarks: 38,\n        maxMarks: 50,\n        percentage: 76,\n        grade: 'B+',\n        gradedBy: 'Mrs. Smith',\n        date: '2024-10-15'\n      },\n      {\n        id: '3',\n        studentName: 'Mike Johnson',\n        admissionNo: 'STU003',\n        examName: 'Mid Term Exam',\n        subjectName: 'English',\n        className: 'Grade 8',\n        sectionName: 'A',\n        obtainedMarks: 85,\n        maxMarks: 100,\n        percentage: 85,\n        grade: 'A',\n        gradedBy: 'Mr. Brown',\n        date: '2024-11-20'\n      }\n    ])\n  }, [])\n\n\n\n  const marksStats = {\n    total: markRecords.length,\n    average: markRecords.length > 0 \n      ? Math.round(markRecords.reduce((sum, record) => sum + record.percentage, 0) / markRecords.length)\n      : 0,\n    highest: Math.max(...markRecords.map(r => r.percentage)),\n    lowest: Math.min(...markRecords.map(r => r.percentage)),\n    passed: markRecords.filter(r => r.percentage >= 40).length,\n    failed: markRecords.filter(r => r.percentage < 40).length\n  }\n\n  return (\n    <DashboardLayout title=\"Marks Management\" navigation={adminNavigation}>\n      <div className=\"space-y-6\">\n        {/* Header */}\n        <div className=\"flex justify-between items-center\">\n          <div>\n            <h1 className=\"text-2xl font-bold text-gray-900\">Marks Management</h1>\n            <p className=\"text-gray-600\">Monitor and manage student examination marks</p>\n          </div>\n          <div className=\"flex space-x-2\">\n            <Button variant=\"outline\">\n              <Download className=\"w-4 h-4 mr-2\" />\n              Export Marks\n            </Button>\n            <Button>\n              <Plus className=\"w-4 h-4 mr-2\" />\n              Add Marks\n            </Button>\n          </div>\n        </div>\n\n        {/* Stats Cards */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-6\">\n          <Card key=\"total-marks-records\">\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n              <CardTitle className=\"text-sm font-medium\">Total Records</CardTitle>\n              <FileText className=\"h-4 w-4 text-muted-foreground\" />\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-2xl font-bold\">{marksStats.total}</div>\n            </CardContent>\n          </Card>\n          <Card key=\"average-marks-score\">\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n              <CardTitle className=\"text-sm font-medium\">Average Score</CardTitle>\n              <TrendingUp className=\"h-4 w-4 text-blue-600\" />\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-2xl font-bold text-blue-600\">{marksStats.average}%</div>\n            </CardContent>\n          </Card>\n          <Card key=\"highest-marks-score\">\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n              <CardTitle className=\"text-sm font-medium\">Highest Score</CardTitle>\n              <Award className=\"h-4 w-4 text-green-600\" />\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-2xl font-bold text-green-600\">{marksStats.highest}%</div>\n            </CardContent>\n          </Card>\n          <Card key=\"lowest-marks-score\">\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n              <CardTitle className=\"text-sm font-medium\">Lowest Score</CardTitle>\n              <TrendingUp className=\"h-4 w-4 text-red-600\" />\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-2xl font-bold text-red-600\">{marksStats.lowest}%</div>\n            </CardContent>\n          </Card>\n          <Card key=\"passed-students\">\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n              <CardTitle className=\"text-sm font-medium\">Passed</CardTitle>\n              <CheckCircle className=\"h-4 w-4 text-green-600\" />\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-2xl font-bold text-green-600\">{marksStats.passed}</div>\n            </CardContent>\n          </Card>\n          <Card key=\"failed-students\">\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n              <CardTitle className=\"text-sm font-medium\">Failed</CardTitle>\n              <XCircle className=\"h-4 w-4 text-red-600\" />\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-2xl font-bold text-red-600\">{marksStats.failed}</div>\n            </CardContent>\n          </Card>\n        </div>\n\n        {/* Filters */}\n        <Card>\n          <CardHeader>\n            <CardTitle className=\"flex items-center\">\n              <Filter className=\"w-5 h-5 mr-2\" />\n              Filters\n            </CardTitle>\n          </CardHeader>\n          <CardContent>\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n              <div>\n                <Label htmlFor=\"exam\">Exam</Label>\n                <select\n                  id=\"exam\"\n                  value={selectedExam}\n                  onChange={(e) => setSelectedExam(e.target.value)}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                >\n                  <option value=\"all\">All Exams</option>\n                  <option value=\"unit1\">Unit Test 1</option>\n                  <option value=\"midterm\">Mid Term Exam</option>\n                  <option value=\"final\">Final Exam</option>\n                </select>\n              </div>\n              <div>\n                <Label htmlFor=\"subject\">Subject</Label>\n                <select\n                  id=\"subject\"\n                  value={selectedSubject}\n                  onChange={(e) => setSelectedSubject(e.target.value)}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                >\n                  <option value=\"all\">All Subjects</option>\n                  <option value=\"math\">Mathematics</option>\n                  <option value=\"english\">English</option>\n                  <option value=\"science\">Science</option>\n                </select>\n              </div>\n              <div>\n                <Label htmlFor=\"class\">Class</Label>\n                <select\n                  id=\"class\"\n                  value={selectedClass}\n                  onChange={(e) => setSelectedClass(e.target.value)}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                >\n                  <option value=\"all\">All Classes</option>\n                  <option value=\"grade8\">Grade 8</option>\n                  <option value=\"grade9\">Grade 9</option>\n                  <option value=\"grade10\">Grade 10</option>\n                </select>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n\n        {/* Marks Table */}\n        <Card>\n          <CardHeader>\n            <CardTitle>Marks Records</CardTitle>\n            <CardDescription>\n              Examination marks and grades for all students\n            </CardDescription>\n          </CardHeader>\n          <CardContent>\n            <div className=\"overflow-x-auto\">\n              <table className=\"min-w-full divide-y divide-gray-200\">\n                <thead className=\"bg-gray-50\">\n                  <tr>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      Student\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      Exam\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      Subject\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      Class\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      Marks\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      Percentage\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      Grade\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      Graded By\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      Actions\n                    </th>\n                  </tr>\n                </thead>\n                <tbody className=\"bg-white divide-y divide-gray-200\">\n                  {markRecords.map((record) => (\n                    <tr key={record.id}>\n                      <td className=\"px-6 py-4 whitespace-nowrap\">\n                        <div className=\"flex items-center\">\n                          <div className=\"flex-shrink-0 h-10 w-10\">\n                            <div className=\"h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center\">\n                              <span className=\"text-sm font-medium text-gray-700\">\n                                {record.studentName.split(' ').map(n => n[0]).join('')}\n                              </span>\n                            </div>\n                          </div>\n                          <div className=\"ml-4\">\n                            <div className=\"text-sm font-medium text-gray-900\">{record.studentName}</div>\n                            <div className=\"text-sm text-gray-500\">{record.admissionNo}</div>\n                          </div>\n                        </div>\n                      </td>\n                      <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                        {record.examName}\n                      </td>\n                      <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                        {record.subjectName}\n                      </td>\n                      <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                        {record.className} - {record.sectionName}\n                      </td>\n                      <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                        {record.obtainedMarks}/{record.maxMarks}\n                      </td>\n                      <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                        {record.percentage}%\n                      </td>\n                      <td className=\"px-6 py-4 whitespace-nowrap\">\n                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getGradeBadgeColor(record.grade)}`}>\n                          {record.grade}\n                        </span>\n                      </td>\n                      <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                        {record.gradedBy}\n                      </td>\n                      <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium\">\n                        <div className=\"flex space-x-2\">\n                          <Button variant=\"outline\" size=\"sm\">\n                            Edit\n                          </Button>\n                          <Button variant=\"outline\" size=\"sm\">\n                            View\n                          </Button>\n                        </div>\n                      </td>\n                    </tr>\n                  ))}\n                </tbody>\n              </table>\n            </div>\n          </CardContent>\n        </Card>\n      </div>\n    </DashboardLayout>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA8BA;AAvCA;;;;;;;;;;AAyCe,SAAS;IACtB,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,kWAAQ,EAAe,EAAE;IAC/D,MAAM,CAAC,cAAc,gBAAgB,GAAG,IAAA,kWAAQ,EAAC;IACjD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,IAAA,kWAAQ,EAAC;IACvD,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,kWAAQ,EAAC;IAEnD,oDAAoD;IACpD,IAAA,mWAAS,EAAC;QACR,eAAe;YACb;gBACE,IAAI;gBACJ,aAAa;gBACb,aAAa;gBACb,UAAU;gBACV,aAAa;gBACb,WAAW;gBACX,aAAa;gBACb,eAAe;gBACf,UAAU;gBACV,YAAY;gBACZ,OAAO;gBACP,UAAU;gBACV,MAAM;YACR;YACA;gBACE,IAAI;gBACJ,aAAa;gBACb,aAAa;gBACb,UAAU;gBACV,aAAa;gBACb,WAAW;gBACX,aAAa;gBACb,eAAe;gBACf,UAAU;gBACV,YAAY;gBACZ,OAAO;gBACP,UAAU;gBACV,MAAM;YACR;YACA;gBACE,IAAI;gBACJ,aAAa;gBACb,aAAa;gBACb,UAAU;gBACV,aAAa;gBACb,WAAW;gBACX,aAAa;gBACb,eAAe;gBACf,UAAU;gBACV,YAAY;gBACZ,OAAO;gBACP,UAAU;gBACV,MAAM;YACR;SACD;IACH,GAAG,EAAE;IAIL,MAAM,aAAa;QACjB,OAAO,YAAY,MAAM;QACzB,SAAS,YAAY,MAAM,GAAG,IAC1B,KAAK,KAAK,CAAC,YAAY,MAAM,CAAC,CAAC,KAAK,SAAW,MAAM,OAAO,UAAU,EAAE,KAAK,YAAY,MAAM,IAC/F;QACJ,SAAS,KAAK,GAAG,IAAI,YAAY,GAAG,CAAC,CAAA,IAAK,EAAE,UAAU;QACtD,QAAQ,KAAK,GAAG,IAAI,YAAY,GAAG,CAAC,CAAA,IAAK,EAAE,UAAU;QACrD,QAAQ,YAAY,MAAM,CAAC,CAAA,IAAK,EAAE,UAAU,IAAI,IAAI,MAAM;QAC1D,QAAQ,YAAY,MAAM,CAAC,CAAA,IAAK,EAAE,UAAU,GAAG,IAAI,MAAM;IAC3D;IAEA,qBACE,+XAAC,gMAAe;QAAC,OAAM;QAAmB,YAAY,6KAAe;kBACnE,cAAA,+XAAC;YAAI,WAAU;;8BAEb,+XAAC;oBAAI,WAAU;;sCACb,+XAAC;;8CACC,+XAAC;oCAAG,WAAU;8CAAmC;;;;;;8CACjD,+XAAC;oCAAE,WAAU;8CAAgB;;;;;;;;;;;;sCAE/B,+XAAC;4BAAI,WAAU;;8CACb,+XAAC,8KAAM;oCAAC,SAAQ;;sDACd,+XAAC,4UAAQ;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAGvC,+XAAC,8KAAM;;sDACL,+XAAC,gUAAI;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;;8BAOvC,+XAAC;oBAAI,WAAU;;sCACb,+XAAC,0KAAI;;8CACH,+XAAC,gLAAU;oCAAC,WAAU;;sDACpB,+XAAC,+KAAS;4CAAC,WAAU;sDAAsB;;;;;;sDAC3C,+XAAC,gVAAQ;4CAAC,WAAU;;;;;;;;;;;;8CAEtB,+XAAC,iLAAW;8CACV,cAAA,+XAAC;wCAAI,WAAU;kDAAsB,WAAW,KAAK;;;;;;;;;;;;2BAN/C;;;;;sCASV,+XAAC,0KAAI;;8CACH,+XAAC,gLAAU;oCAAC,WAAU;;sDACpB,+XAAC,+KAAS;4CAAC,WAAU;sDAAsB;;;;;;sDAC3C,+XAAC,sVAAU;4CAAC,WAAU;;;;;;;;;;;;8CAExB,+XAAC,iLAAW;8CACV,cAAA,+XAAC;wCAAI,WAAU;;4CAAoC,WAAW,OAAO;4CAAC;;;;;;;;;;;;;2BANhE;;;;;sCASV,+XAAC,0KAAI;;8CACH,+XAAC,gLAAU;oCAAC,WAAU;;sDACpB,+XAAC,+KAAS;4CAAC,WAAU;sDAAsB;;;;;;sDAC3C,+XAAC,mUAAK;4CAAC,WAAU;;;;;;;;;;;;8CAEnB,+XAAC,iLAAW;8CACV,cAAA,+XAAC;wCAAI,WAAU;;4CAAqC,WAAW,OAAO;4CAAC;;;;;;;;;;;;;2BANjE;;;;;sCASV,+XAAC,0KAAI;;8CACH,+XAAC,gLAAU;oCAAC,WAAU;;sDACpB,+XAAC,+KAAS;4CAAC,WAAU;sDAAsB;;;;;;sDAC3C,+XAAC,sVAAU;4CAAC,WAAU;;;;;;;;;;;;8CAExB,+XAAC,iLAAW;8CACV,cAAA,+XAAC;wCAAI,WAAU;;4CAAmC,WAAW,MAAM;4CAAC;;;;;;;;;;;;;2BAN9D;;;;;sCASV,+XAAC,0KAAI;;8CACH,+XAAC,gLAAU;oCAAC,WAAU;;sDACpB,+XAAC,+KAAS;4CAAC,WAAU;sDAAsB;;;;;;sDAC3C,+XAAC,gWAAW;4CAAC,WAAU;;;;;;;;;;;;8CAEzB,+XAAC,iLAAW;8CACV,cAAA,+XAAC;wCAAI,WAAU;kDAAqC,WAAW,MAAM;;;;;;;;;;;;2BAN/D;;;;;sCASV,+XAAC,0KAAI;;8CACH,+XAAC,gLAAU;oCAAC,WAAU;;sDACpB,+XAAC,+KAAS;4CAAC,WAAU;sDAAsB;;;;;;sDAC3C,+XAAC,6UAAO;4CAAC,WAAU;;;;;;;;;;;;8CAErB,+XAAC,iLAAW;8CACV,cAAA,+XAAC;wCAAI,WAAU;kDAAmC,WAAW,MAAM;;;;;;;;;;;;2BAN7D;;;;;;;;;;;8BAYZ,+XAAC,0KAAI;;sCACH,+XAAC,gLAAU;sCACT,cAAA,+XAAC,+KAAS;gCAAC,WAAU;;kDACnB,+XAAC,sUAAM;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;sCAIvC,+XAAC,iLAAW;sCACV,cAAA,+XAAC;gCAAI,WAAU;;kDACb,+XAAC;;0DACC,+XAAC,4KAAK;gDAAC,SAAQ;0DAAO;;;;;;0DACtB,+XAAC;gDACC,IAAG;gDACH,OAAO;gDACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;gDAC/C,WAAU;;kEAEV,+XAAC;wDAAO,OAAM;kEAAM;;;;;;kEACpB,+XAAC;wDAAO,OAAM;kEAAQ;;;;;;kEACtB,+XAAC;wDAAO,OAAM;kEAAU;;;;;;kEACxB,+XAAC;wDAAO,OAAM;kEAAQ;;;;;;;;;;;;;;;;;;kDAG1B,+XAAC;;0DACC,+XAAC,4KAAK;gDAAC,SAAQ;0DAAU;;;;;;0DACzB,+XAAC;gDACC,IAAG;gDACH,OAAO;gDACP,UAAU,CAAC,IAAM,mBAAmB,EAAE,MAAM,CAAC,KAAK;gDAClD,WAAU;;kEAEV,+XAAC;wDAAO,OAAM;kEAAM;;;;;;kEACpB,+XAAC;wDAAO,OAAM;kEAAO;;;;;;kEACrB,+XAAC;wDAAO,OAAM;kEAAU;;;;;;kEACxB,+XAAC;wDAAO,OAAM;kEAAU;;;;;;;;;;;;;;;;;;kDAG5B,+XAAC;;0DACC,+XAAC,4KAAK;gDAAC,SAAQ;0DAAQ;;;;;;0DACvB,+XAAC;gDACC,IAAG;gDACH,OAAO;gDACP,UAAU,CAAC,IAAM,iBAAiB,EAAE,MAAM,CAAC,KAAK;gDAChD,WAAU;;kEAEV,+XAAC;wDAAO,OAAM;kEAAM;;;;;;kEACpB,+XAAC;wDAAO,OAAM;kEAAS;;;;;;kEACvB,+XAAC;wDAAO,OAAM;kEAAS;;;;;;kEACvB,+XAAC;wDAAO,OAAM;kEAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQlC,+XAAC,0KAAI;;sCACH,+XAAC,gLAAU;;8CACT,+XAAC,+KAAS;8CAAC;;;;;;8CACX,+XAAC,qLAAe;8CAAC;;;;;;;;;;;;sCAInB,+XAAC,iLAAW;sCACV,cAAA,+XAAC;gCAAI,WAAU;0CACb,cAAA,+XAAC;oCAAM,WAAU;;sDACf,+XAAC;4CAAM,WAAU;sDACf,cAAA,+XAAC;;kEACC,+XAAC;wDAAG,WAAU;kEAAiF;;;;;;kEAG/F,+XAAC;wDAAG,WAAU;kEAAiF;;;;;;kEAG/F,+XAAC;wDAAG,WAAU;kEAAiF;;;;;;kEAG/F,+XAAC;wDAAG,WAAU;kEAAiF;;;;;;kEAG/F,+XAAC;wDAAG,WAAU;kEAAiF;;;;;;kEAG/F,+XAAC;wDAAG,WAAU;kEAAiF;;;;;;kEAG/F,+XAAC;wDAAG,WAAU;kEAAiF;;;;;;kEAG/F,+XAAC;wDAAG,WAAU;kEAAiF;;;;;;kEAG/F,+XAAC;wDAAG,WAAU;kEAAiF;;;;;;;;;;;;;;;;;sDAKnG,+XAAC;4CAAM,WAAU;sDACd,YAAY,GAAG,CAAC,CAAC,uBAChB,+XAAC;;sEACC,+XAAC;4DAAG,WAAU;sEACZ,cAAA,+XAAC;gEAAI,WAAU;;kFACb,+XAAC;wEAAI,WAAU;kFACb,cAAA,+XAAC;4EAAI,WAAU;sFACb,cAAA,+XAAC;gFAAK,WAAU;0FACb,OAAO,WAAW,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,IAAK,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC;;;;;;;;;;;;;;;;kFAIzD,+XAAC;wEAAI,WAAU;;0FACb,+XAAC;gFAAI,WAAU;0FAAqC,OAAO,WAAW;;;;;;0FACtE,+XAAC;gFAAI,WAAU;0FAAyB,OAAO,WAAW;;;;;;;;;;;;;;;;;;;;;;;sEAIhE,+XAAC;4DAAG,WAAU;sEACX,OAAO,QAAQ;;;;;;sEAElB,+XAAC;4DAAG,WAAU;sEACX,OAAO,WAAW;;;;;;sEAErB,+XAAC;4DAAG,WAAU;;gEACX,OAAO,SAAS;gEAAC;gEAAI,OAAO,WAAW;;;;;;;sEAE1C,+XAAC;4DAAG,WAAU;;gEACX,OAAO,aAAa;gEAAC;gEAAE,OAAO,QAAQ;;;;;;;sEAEzC,+XAAC;4DAAG,WAAU;;gEACX,OAAO,UAAU;gEAAC;;;;;;;sEAErB,+XAAC;4DAAG,WAAU;sEACZ,cAAA,+XAAC;gEAAK,WAAW,CAAC,yDAAyD,EAAE,IAAA,6KAAkB,EAAC,OAAO,KAAK,GAAG;0EAC5G,OAAO,KAAK;;;;;;;;;;;sEAGjB,+XAAC;4DAAG,WAAU;sEACX,OAAO,QAAQ;;;;;;sEAElB,+XAAC;4DAAG,WAAU;sEACZ,cAAA,+XAAC;gEAAI,WAAU;;kFACb,+XAAC,8KAAM;wEAAC,SAAQ;wEAAU,MAAK;kFAAK;;;;;;kFAGpC,+XAAC,8KAAM;wEAAC,SAAQ;wEAAU,MAAK;kFAAK;;;;;;;;;;;;;;;;;;mDA5CjC,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2DtC", "debugId": null}}]}