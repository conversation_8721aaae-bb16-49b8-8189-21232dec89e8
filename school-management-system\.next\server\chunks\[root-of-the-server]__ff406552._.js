module.exports=[18622,(e,t,r)=>{t.exports=e.x("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js"))},56704,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/work-async-storage.external.js",()=>require("next/dist/server/app-render/work-async-storage.external.js"))},32319,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/work-unit-async-storage.external.js",()=>require("next/dist/server/app-render/work-unit-async-storage.external.js"))},20635,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/action-async-storage.external.js",()=>require("next/dist/server/app-render/action-async-storage.external.js"))},24725,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/after-task-async-storage.external.js",()=>require("next/dist/server/app-render/after-task-async-storage.external.js"))},24361,(e,t,r)=>{t.exports=e.x("util",()=>require("util"))},29173,(e,t,r)=>{t.exports=e.x("@prisma/client",()=>require("@prisma/client"))},70406,(e,t,r)=>{t.exports=e.x("next/dist/compiled/@opentelemetry/api",()=>require("next/dist/compiled/@opentelemetry/api"))},93695,(e,t,r)=>{t.exports=e.x("next/dist/shared/lib/no-fallback-error.external.js",()=>require("next/dist/shared/lib/no-fallback-error.external.js"))},61724,(e,t,r)=>{t.exports=e.x("next/dist/compiled/next-server/app-route-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-route-turbo.runtime.prod.js"))},54799,(e,t,r)=>{t.exports=e.x("crypto",()=>require("crypto"))},92509,(e,t,r)=>{t.exports=e.x("url",()=>require("url"))},21517,(e,t,r)=>{t.exports=e.x("http",()=>require("http"))},49719,(e,t,r)=>{t.exports=e.x("assert",()=>require("assert"))},45706,(e,t,r)=>{t.exports=e.x("querystring",()=>require("querystring"))},874,(e,t,r)=>{t.exports=e.x("buffer",()=>require("buffer"))},6461,(e,t,r)=>{t.exports=e.x("zlib",()=>require("zlib"))},24836,(e,t,r)=>{t.exports=e.x("https",()=>require("https"))},27699,(e,t,r)=>{t.exports=e.x("events",()=>require("events"))},12756,e=>{e.v(JSON.parse('{"name":"openid-client","version":"5.7.1","description":"OpenID Connect Relying Party (RP, Client) implementation for Node.js runtime, supports passportjs","keywords":["auth","authentication","basic","certified","client","connect","dynamic","electron","hybrid","identity","implicit","oauth","oauth2","oidc","openid","passport","relying party","strategy"],"homepage":"https://github.com/panva/openid-client","repository":"panva/openid-client","funding":{"url":"https://github.com/sponsors/panva"},"license":"MIT","author":"Filip Skokan <<EMAIL>>","exports":{"types":"./types/index.d.ts","import":"./lib/index.mjs","require":"./lib/index.js"},"main":"./lib/index.js","types":"./types/index.d.ts","files":["lib","types/index.d.ts"],"scripts":{"format":"npx prettier --loglevel silent --write ./lib ./test ./certification ./types","test":"mocha test/**/*.test.js"},"dependencies":{"jose":"^4.15.9","lru-cache":"^6.0.0","object-hash":"^2.2.0","oidc-token-hash":"^5.0.3"},"devDependencies":{"@types/node":"^16.18.106","@types/passport":"^1.0.16","base64url":"^3.0.1","chai":"^4.5.0","mocha":"^10.7.3","nock":"^13.5.5","prettier":"^2.8.8","readable-mock-req":"^0.2.2","sinon":"^9.2.4","timekeeper":"^2.3.1"},"standard-version":{"scripts":{"postchangelog":"sed -i \'\' -e \'s/### \\\\[/## [/g\' CHANGELOG.md"},"types":[{"type":"feat","section":"Features"},{"type":"fix","section":"Fixes"},{"type":"chore","hidden":true},{"type":"docs","hidden":true},{"type":"style","hidden":true},{"type":"refactor","section":"Refactor","hidden":false},{"type":"perf","section":"Performance","hidden":false},{"type":"test","hidden":true}]}}'))},62349,(e,t,r)=>{},45328,e=>{"use strict";e.s(["handler",()=>N,"patchFetch",()=>A,"routeModule",()=>E,"serverHooks",()=>j,"workAsyncStorage",()=>C,"workUnitAsyncStorage",()=>q],45328);var t=e.i(6137),r=e.i(11365),n=e.i(9638),a=e.i(15243),s=e.i(66378),i=e.i(92101),o=e.i(50012),l=e.i(62885),p=e.i(31409),d=e.i(78448),u=e.i(28015),c=e.i(72721),x=e.i(75714),h=e.i(12634),m=e.i(93695);e.i(74732);var y=e.i(66662);e.s(["GET",()=>b],58664);var v=e.i(2835),f=e.i(58356),R=e.i(43382),g=e.i(31279);async function b(e){try{let t=await (0,f.getServerSession)(R.authOptions);if(!t||"TEACHER"!==t.user.role)return v.NextResponse.json({error:"Unauthorized"},{status:401});if(!await g.prisma.teacher.findUnique({where:{userId:t.user.id}}))return v.NextResponse.json({error:"Teacher not found"},{status:404});let{searchParams:r}=new URL(e.url),n=r.get("subjectId"),a=r.get("termId"),s={};n&&"all"!==n&&(s.subjectId=n),a&&"all"!==a&&(s.termId=a);let i=await g.prisma.exam.findMany({where:s,include:{subject:{include:{class:!0}},term:!0,_count:{select:{marks:!0}}},orderBy:[{date:"desc"},{createdAt:"desc"}]});return v.NextResponse.json(i)}catch(e){return console.error("Error fetching exams:",e),v.NextResponse.json({error:"Internal server error"},{status:500})}}var w=e.i(58664);let E=new t.AppRouteRouteModule({definition:{kind:r.RouteKind.APP_ROUTE,page:"/api/teacher/exams/route",pathname:"/api/teacher/exams",filename:"route",bundlePath:""},distDir:".next",relativeProjectDir:"",resolvedPagePath:"[project]/school-management-system/src/app/api/teacher/exams/route.ts",nextConfigOutput:"",userland:w}),{workAsyncStorage:C,workUnitAsyncStorage:q,serverHooks:j}=E;function A(){return(0,n.patchFetch)({workAsyncStorage:C,workUnitAsyncStorage:q})}async function N(e,t,n){var v;let f="/api/teacher/exams/route";f=f.replace(/\/index$/,"")||"/";let R=await E.prepare(e,t,{srcPage:f,multiZoneDraftMode:!1});if(!R)return t.statusCode=400,t.end("Bad Request"),null==n.waitUntil||n.waitUntil.call(n,Promise.resolve()),null;let{buildId:g,params:b,nextConfig:w,isDraftMode:C,prerenderManifest:q,routerServerContext:j,isOnDemandRevalidate:A,revalidateOnlyGenerated:N,resolvedPathname:k}=R,T=(0,i.normalizeAppPath)(f),O=!!(q.dynamicRoutes[T]||q.routes[k]);if(O&&!C){let e=!!q.routes[k],t=q.dynamicRoutes[T];if(t&&!1===t.fallback&&!e)throw new m.NoFallbackError}let P=null;!O||E.isDev||C||(P="/index"===(P=k)?"/":P);let I=!0===E.isDev||!O,S=O&&!I,_=e.method||"GET",H=(0,s.getTracer)(),U=H.getActiveScopeSpan(),M={params:b,prerenderManifest:q,renderOpts:{experimental:{cacheComponents:!!w.experimental.cacheComponents,authInterrupts:!!w.experimental.authInterrupts},supportsDynamicResponse:I,incrementalCache:(0,a.getRequestMeta)(e,"incrementalCache"),cacheLifeProfiles:null==(v=w.experimental)?void 0:v.cacheLife,isRevalidate:S,waitUntil:n.waitUntil,onClose:e=>{t.on("close",e)},onAfterTaskError:void 0,onInstrumentationRequestError:(t,r,n)=>E.onRequestError(e,t,n,j)},sharedContext:{buildId:g}},D=new o.NodeNextRequest(e),F=new o.NodeNextResponse(t),$=l.NextRequestAdapter.fromNodeNextRequest(D,(0,l.signalFromNodeResponse)(t));try{let i=async r=>E.handle($,M).finally(()=>{if(!r)return;r.setAttributes({"http.status_code":t.statusCode,"next.rsc":!1});let n=H.getRootSpanAttributes();if(!n)return;if(n.get("next.span_type")!==p.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${n.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let a=n.get("next.route");if(a){let e=`${_} ${a}`;r.setAttributes({"next.route":a,"http.route":a,"next.span_name":e}),r.updateName(e)}else r.updateName(`${_} ${e.url}`)}),o=async s=>{var o,l;let p=async({previousCacheEntry:r})=>{try{if(!(0,a.getRequestMeta)(e,"minimalMode")&&A&&N&&!r)return t.statusCode=404,t.setHeader("x-nextjs-cache","REVALIDATED"),t.end("This page could not be found"),null;let o=await i(s);e.fetchMetrics=M.renderOpts.fetchMetrics;let l=M.renderOpts.pendingWaitUntil;l&&n.waitUntil&&(n.waitUntil(l),l=void 0);let p=M.renderOpts.collectedTags;if(!O)return await (0,u.sendResponse)(D,F,o,M.renderOpts.pendingWaitUntil),null;{let e=await o.blob(),t=(0,c.toNodeOutgoingHttpHeaders)(o.headers);p&&(t[h.NEXT_CACHE_TAGS_HEADER]=p),!t["content-type"]&&e.type&&(t["content-type"]=e.type);let r=void 0!==M.renderOpts.collectedRevalidate&&!(M.renderOpts.collectedRevalidate>=h.INFINITE_CACHE)&&M.renderOpts.collectedRevalidate,n=void 0===M.renderOpts.collectedExpire||M.renderOpts.collectedExpire>=h.INFINITE_CACHE?void 0:M.renderOpts.collectedExpire;return{value:{kind:y.CachedRouteKind.APP_ROUTE,status:o.status,body:Buffer.from(await e.arrayBuffer()),headers:t},cacheControl:{revalidate:r,expire:n}}}}catch(t){throw(null==r?void 0:r.isStale)&&await E.onRequestError(e,t,{routerKind:"App Router",routePath:f,routeType:"route",revalidateReason:(0,d.getRevalidateReason)({isRevalidate:S,isOnDemandRevalidate:A})},j),t}},m=await E.handleResponse({req:e,nextConfig:w,cacheKey:P,routeKind:r.RouteKind.APP_ROUTE,isFallback:!1,prerenderManifest:q,isRoutePPREnabled:!1,isOnDemandRevalidate:A,revalidateOnlyGenerated:N,responseGenerator:p,waitUntil:n.waitUntil});if(!O)return null;if((null==m||null==(o=m.value)?void 0:o.kind)!==y.CachedRouteKind.APP_ROUTE)throw Object.defineProperty(Error(`Invariant: app-route received invalid cache entry ${null==m||null==(l=m.value)?void 0:l.kind}`),"__NEXT_ERROR_CODE",{value:"E701",enumerable:!1,configurable:!0});(0,a.getRequestMeta)(e,"minimalMode")||t.setHeader("x-nextjs-cache",A?"REVALIDATED":m.isMiss?"MISS":m.isStale?"STALE":"HIT"),C&&t.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");let v=(0,c.fromNodeOutgoingHttpHeaders)(m.value.headers);return(0,a.getRequestMeta)(e,"minimalMode")&&O||v.delete(h.NEXT_CACHE_TAGS_HEADER),!m.cacheControl||t.getHeader("Cache-Control")||v.get("Cache-Control")||v.set("Cache-Control",(0,x.getCacheControlHeader)(m.cacheControl)),await (0,u.sendResponse)(D,F,new Response(m.value.body,{headers:v,status:m.value.status||200})),null};U?await o(U):await H.withPropagatedContext(e.headers,()=>H.trace(p.BaseServerSpan.handleRequest,{spanName:`${_} ${e.url}`,kind:s.SpanKind.SERVER,attributes:{"http.method":_,"http.target":e.url}},o))}catch(t){if(U||t instanceof m.NoFallbackError||await E.onRequestError(e,t,{routerKind:"App Router",routePath:T,routeType:"route",revalidateReason:(0,d.getRevalidateReason)({isRevalidate:S,isOnDemandRevalidate:A})}),O)throw t;return await (0,u.sendResponse)(D,F,new Response(null,{status:500})),null}}}];

//# sourceMappingURL=%5Broot-of-the-server%5D__ff406552._.js.map