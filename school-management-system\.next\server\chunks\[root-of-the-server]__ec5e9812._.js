module.exports=[18622,(e,t,r)=>{t.exports=e.x("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js"))},56704,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/work-async-storage.external.js",()=>require("next/dist/server/app-render/work-async-storage.external.js"))},32319,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/work-unit-async-storage.external.js",()=>require("next/dist/server/app-render/work-unit-async-storage.external.js"))},20635,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/action-async-storage.external.js",()=>require("next/dist/server/app-render/action-async-storage.external.js"))},24725,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/after-task-async-storage.external.js",()=>require("next/dist/server/app-render/after-task-async-storage.external.js"))},24361,(e,t,r)=>{t.exports=e.x("util",()=>require("util"))},29173,(e,t,r)=>{t.exports=e.x("@prisma/client",()=>require("@prisma/client"))},70406,(e,t,r)=>{t.exports=e.x("next/dist/compiled/@opentelemetry/api",()=>require("next/dist/compiled/@opentelemetry/api"))},93695,(e,t,r)=>{t.exports=e.x("next/dist/shared/lib/no-fallback-error.external.js",()=>require("next/dist/shared/lib/no-fallback-error.external.js"))},61724,(e,t,r)=>{t.exports=e.x("next/dist/compiled/next-server/app-route-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-route-turbo.runtime.prod.js"))},54799,(e,t,r)=>{t.exports=e.x("crypto",()=>require("crypto"))},92509,(e,t,r)=>{t.exports=e.x("url",()=>require("url"))},21517,(e,t,r)=>{t.exports=e.x("http",()=>require("http"))},49719,(e,t,r)=>{t.exports=e.x("assert",()=>require("assert"))},45706,(e,t,r)=>{t.exports=e.x("querystring",()=>require("querystring"))},874,(e,t,r)=>{t.exports=e.x("buffer",()=>require("buffer"))},6461,(e,t,r)=>{t.exports=e.x("zlib",()=>require("zlib"))},24836,(e,t,r)=>{t.exports=e.x("https",()=>require("https"))},27699,(e,t,r)=>{t.exports=e.x("events",()=>require("events"))},12756,e=>{e.v(JSON.parse('{"name":"openid-client","version":"5.7.1","description":"OpenID Connect Relying Party (RP, Client) implementation for Node.js runtime, supports passportjs","keywords":["auth","authentication","basic","certified","client","connect","dynamic","electron","hybrid","identity","implicit","oauth","oauth2","oidc","openid","passport","relying party","strategy"],"homepage":"https://github.com/panva/openid-client","repository":"panva/openid-client","funding":{"url":"https://github.com/sponsors/panva"},"license":"MIT","author":"Filip Skokan <<EMAIL>>","exports":{"types":"./types/index.d.ts","import":"./lib/index.mjs","require":"./lib/index.js"},"main":"./lib/index.js","types":"./types/index.d.ts","files":["lib","types/index.d.ts"],"scripts":{"format":"npx prettier --loglevel silent --write ./lib ./test ./certification ./types","test":"mocha test/**/*.test.js"},"dependencies":{"jose":"^4.15.9","lru-cache":"^6.0.0","object-hash":"^2.2.0","oidc-token-hash":"^5.0.3"},"devDependencies":{"@types/node":"^16.18.106","@types/passport":"^1.0.16","base64url":"^3.0.1","chai":"^4.5.0","mocha":"^10.7.3","nock":"^13.5.5","prettier":"^2.8.8","readable-mock-req":"^0.2.2","sinon":"^9.2.4","timekeeper":"^2.3.1"},"standard-version":{"scripts":{"postchangelog":"sed -i \'\' -e \'s/### \\\\[/## [/g\' CHANGELOG.md"},"types":[{"type":"feat","section":"Features"},{"type":"fix","section":"Fixes"},{"type":"chore","hidden":true},{"type":"docs","hidden":true},{"type":"style","hidden":true},{"type":"refactor","section":"Refactor","hidden":false},{"type":"perf","section":"Performance","hidden":false},{"type":"test","hidden":true}]}}'))},12276,e=>{"use strict";e.s(["hasPermission",()=>r]);let t={ADMIN:["users:read","users:write","users:delete","students:read","students:write","students:delete","teachers:read","teachers:write","teachers:delete","classes:read","classes:write","classes:delete","subjects:read","subjects:write","subjects:delete","attendance:read","attendance:write","marks:read","marks:write","reports:read","reports:write","settings:read","settings:write","audit:read"],TEACHER:["students:read","attendance:read","attendance:write","marks:read","marks:write","reports:read"],STUDENT:["attendance:read","marks:read","reports:read"]};function r(e,r){return t[e]?.includes(r)??!1}},61704,(e,t,r)=>{},99118,e=>{"use strict";e.s(["handler",()=>S,"patchFetch",()=>P,"routeModule",()=>A,"serverHooks",()=>T,"workAsyncStorage",()=>k,"workUnitAsyncStorage",()=>q],99118);var t=e.i(6137),r=e.i(11365),s=e.i(9638),a=e.i(15243),n=e.i(66378),i=e.i(92101),o=e.i(50012),d=e.i(62885),l=e.i(31409),u=e.i(78448),c=e.i(28015),p=e.i(72721),h=e.i(75714),x=e.i(12634),m=e.i(93695);e.i(74732);var f=e.i(66662);e.s(["GET",()=>b,"POST",()=>j,"PUT",()=>C],76915);var R=e.i(2835),y=e.i(58356),g=e.i(43382),w=e.i(31279),v=e.i(12276),E=e.i(47504);let N=E.z.object({classId:E.z.number().min(1,"Class ID is required"),date:E.z.string().min(1,"Date is required"),attendanceRecords:E.z.array(E.z.object({studentId:E.z.number().min(1,"Student ID is required"),status:E.z.enum(["PRESENT","ABSENT","LATE","HALF_DAY"]),remarks:E.z.string().optional()}))});async function b(e){try{let t=await (0,y.getServerSession)(g.authOptions);if(!t?.user||!(0,v.hasPermission)(t.user.role,"attendance:read"))return R.NextResponse.json({error:"Unauthorized"},{status:401});let{searchParams:r}=new URL(e.url),s=r.get("classId"),a=r.get("date"),n=parseInt(r.get("page")||"1"),i=parseInt(r.get("limit")||"10"),o=(n-1)*i,d={};"TEACHER"===t.user.role&&(d.class={teacherId:t.user.teacherId}),s&&(d.classId=parseInt(s)),a&&(d.date=new Date(a));let[l,u]=await Promise.all([w.prisma.attendance.findMany({where:d,skip:o,take:i,orderBy:{date:"desc"},include:{student:{select:{id:!0,firstName:!0,lastName:!0,rollNumber:!0}},class:{select:{id:!0,name:!0,section:{select:{name:!0}}}}}}),w.prisma.attendance.count({where:d})]),c=Math.ceil(u/i);return R.NextResponse.json({attendanceRecords:l,pagination:{page:n,limit:i,total:u,totalPages:c}})}catch(e){return console.error("Error fetching attendance:",e),R.NextResponse.json({error:"Failed to fetch attendance"},{status:500})}}async function j(e){try{let t=await (0,y.getServerSession)(g.authOptions);if(!t?.user||!(0,v.hasPermission)(t.user.role,"attendance:write"))return R.NextResponse.json({error:"Unauthorized"},{status:401});let r=await e.json(),s=N.parse(r),a=await w.prisma.class.findUnique({where:{id:s.classId},include:{teacher:!0,students:!0}});if(!a)return R.NextResponse.json({error:"Class not found"},{status:404});if("TEACHER"===t.user.role&&a.teacherId!==t.user.teacherId)return R.NextResponse.json({error:"You are not authorized to mark attendance for this class"},{status:403});if(await w.prisma.attendance.findFirst({where:{classId:s.classId,date:new Date(s.date)}}))return R.NextResponse.json({error:"Attendance for this date has already been marked"},{status:400});let n=a.students.map(e=>e.id);if(s.attendanceRecords.map(e=>e.studentId).filter(e=>!n.includes(e)).length>0)return R.NextResponse.json({error:"Some students do not belong to this class"},{status:400});let i=await w.prisma.$transaction(s.attendanceRecords.map(e=>w.prisma.attendance.create({data:{studentId:e.studentId,classId:s.classId,date:new Date(s.date),status:e.status,remarks:e.remarks},include:{student:{select:{id:!0,firstName:!0,lastName:!0,rollNumber:!0}}}})));return R.NextResponse.json({message:"Attendance marked successfully",attendanceRecords:i},{status:201})}catch(e){if(e instanceof E.z.ZodError)return R.NextResponse.json({error:"Validation error",details:e.errors},{status:400});return console.error("Error marking attendance:",e),R.NextResponse.json({error:"Failed to mark attendance"},{status:500})}}async function C(e){try{let t=await (0,y.getServerSession)(g.authOptions);if(!t?.user||!(0,v.hasPermission)(t.user.role,"UPDATE_ATTENDANCE"))return R.NextResponse.json({error:"Unauthorized"},{status:401});let{classId:r,date:s,attendanceRecords:a}=await e.json();if(!r||!s||!a)return R.NextResponse.json({error:"Class ID, date, and attendance records are required"},{status:400});let n=await w.prisma.class.findUnique({where:{id:parseInt(r)},include:{teacher:!0}});if(!n)return R.NextResponse.json({error:"Class not found"},{status:404});if("TEACHER"===t.user.role&&n.teacherId!==t.user.teacherId)return R.NextResponse.json({error:"You are not authorized to update attendance for this class"},{status:403});let i=await w.prisma.$transaction(a.map(e=>w.prisma.attendance.updateMany({where:{studentId:e.studentId,classId:parseInt(r),date:new Date(s)},data:{status:e.status,remarks:e.remarks}})));return R.NextResponse.json({message:"Attendance updated successfully",updatedRecords:i})}catch(e){return console.error("Error updating attendance:",e),R.NextResponse.json({error:"Failed to update attendance"},{status:500})}}var I=e.i(76915);let A=new t.AppRouteRouteModule({definition:{kind:r.RouteKind.APP_ROUTE,page:"/api/teacher/attendance/route",pathname:"/api/teacher/attendance",filename:"route",bundlePath:""},distDir:".next",relativeProjectDir:"",resolvedPagePath:"[project]/school-management-system/src/app/api/teacher/attendance/route.ts",nextConfigOutput:"",userland:I}),{workAsyncStorage:k,workUnitAsyncStorage:q,serverHooks:T}=A;function P(){return(0,s.patchFetch)({workAsyncStorage:k,workUnitAsyncStorage:q})}async function S(e,t,s){var R;let y="/api/teacher/attendance/route";y=y.replace(/\/index$/,"")||"/";let g=await A.prepare(e,t,{srcPage:y,multiZoneDraftMode:!1});if(!g)return t.statusCode=400,t.end("Bad Request"),null==s.waitUntil||s.waitUntil.call(s,Promise.resolve()),null;let{buildId:w,params:v,nextConfig:E,isDraftMode:N,prerenderManifest:b,routerServerContext:j,isOnDemandRevalidate:C,revalidateOnlyGenerated:I,resolvedPathname:k}=g,q=(0,i.normalizeAppPath)(y),T=!!(b.dynamicRoutes[q]||b.routes[k]);if(T&&!N){let e=!!b.routes[k],t=b.dynamicRoutes[q];if(t&&!1===t.fallback&&!e)throw new m.NoFallbackError}let P=null;!T||A.isDev||N||(P="/index"===(P=k)?"/":P);let S=!0===A.isDev||!T,O=T&&!S,D=e.method||"GET",U=(0,n.getTracer)(),H=U.getActiveScopeSpan(),_={params:v,prerenderManifest:b,renderOpts:{experimental:{cacheComponents:!!E.experimental.cacheComponents,authInterrupts:!!E.experimental.authInterrupts},supportsDynamicResponse:S,incrementalCache:(0,a.getRequestMeta)(e,"incrementalCache"),cacheLifeProfiles:null==(R=E.experimental)?void 0:R.cacheLife,isRevalidate:O,waitUntil:s.waitUntil,onClose:e=>{t.on("close",e)},onAfterTaskError:void 0,onInstrumentationRequestError:(t,r,s)=>A.onRequestError(e,t,s,j)},sharedContext:{buildId:w}},M=new o.NodeNextRequest(e),z=new o.NodeNextResponse(t),F=d.NextRequestAdapter.fromNodeNextRequest(M,(0,d.signalFromNodeResponse)(t));try{let i=async r=>A.handle(F,_).finally(()=>{if(!r)return;r.setAttributes({"http.status_code":t.statusCode,"next.rsc":!1});let s=U.getRootSpanAttributes();if(!s)return;if(s.get("next.span_type")!==l.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${s.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let a=s.get("next.route");if(a){let e=`${D} ${a}`;r.setAttributes({"next.route":a,"http.route":a,"next.span_name":e}),r.updateName(e)}else r.updateName(`${D} ${e.url}`)}),o=async n=>{var o,d;let l=async({previousCacheEntry:r})=>{try{if(!(0,a.getRequestMeta)(e,"minimalMode")&&C&&I&&!r)return t.statusCode=404,t.setHeader("x-nextjs-cache","REVALIDATED"),t.end("This page could not be found"),null;let o=await i(n);e.fetchMetrics=_.renderOpts.fetchMetrics;let d=_.renderOpts.pendingWaitUntil;d&&s.waitUntil&&(s.waitUntil(d),d=void 0);let l=_.renderOpts.collectedTags;if(!T)return await (0,c.sendResponse)(M,z,o,_.renderOpts.pendingWaitUntil),null;{let e=await o.blob(),t=(0,p.toNodeOutgoingHttpHeaders)(o.headers);l&&(t[x.NEXT_CACHE_TAGS_HEADER]=l),!t["content-type"]&&e.type&&(t["content-type"]=e.type);let r=void 0!==_.renderOpts.collectedRevalidate&&!(_.renderOpts.collectedRevalidate>=x.INFINITE_CACHE)&&_.renderOpts.collectedRevalidate,s=void 0===_.renderOpts.collectedExpire||_.renderOpts.collectedExpire>=x.INFINITE_CACHE?void 0:_.renderOpts.collectedExpire;return{value:{kind:f.CachedRouteKind.APP_ROUTE,status:o.status,body:Buffer.from(await e.arrayBuffer()),headers:t},cacheControl:{revalidate:r,expire:s}}}}catch(t){throw(null==r?void 0:r.isStale)&&await A.onRequestError(e,t,{routerKind:"App Router",routePath:y,routeType:"route",revalidateReason:(0,u.getRevalidateReason)({isRevalidate:O,isOnDemandRevalidate:C})},j),t}},m=await A.handleResponse({req:e,nextConfig:E,cacheKey:P,routeKind:r.RouteKind.APP_ROUTE,isFallback:!1,prerenderManifest:b,isRoutePPREnabled:!1,isOnDemandRevalidate:C,revalidateOnlyGenerated:I,responseGenerator:l,waitUntil:s.waitUntil});if(!T)return null;if((null==m||null==(o=m.value)?void 0:o.kind)!==f.CachedRouteKind.APP_ROUTE)throw Object.defineProperty(Error(`Invariant: app-route received invalid cache entry ${null==m||null==(d=m.value)?void 0:d.kind}`),"__NEXT_ERROR_CODE",{value:"E701",enumerable:!1,configurable:!0});(0,a.getRequestMeta)(e,"minimalMode")||t.setHeader("x-nextjs-cache",C?"REVALIDATED":m.isMiss?"MISS":m.isStale?"STALE":"HIT"),N&&t.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");let R=(0,p.fromNodeOutgoingHttpHeaders)(m.value.headers);return(0,a.getRequestMeta)(e,"minimalMode")&&T||R.delete(x.NEXT_CACHE_TAGS_HEADER),!m.cacheControl||t.getHeader("Cache-Control")||R.get("Cache-Control")||R.set("Cache-Control",(0,h.getCacheControlHeader)(m.cacheControl)),await (0,c.sendResponse)(M,z,new Response(m.value.body,{headers:R,status:m.value.status||200})),null};H?await o(H):await U.withPropagatedContext(e.headers,()=>U.trace(l.BaseServerSpan.handleRequest,{spanName:`${D} ${e.url}`,kind:n.SpanKind.SERVER,attributes:{"http.method":D,"http.target":e.url}},o))}catch(t){if(H||t instanceof m.NoFallbackError||await A.onRequestError(e,t,{routerKind:"App Router",routePath:q,routeType:"route",revalidateReason:(0,u.getRevalidateReason)({isRevalidate:O,isOnDemandRevalidate:C})}),T)throw t;return await (0,c.sendResponse)(M,z,new Response(null,{status:500})),null}}}];

//# sourceMappingURL=%5Broot-of-the-server%5D__ec5e9812._.js.map