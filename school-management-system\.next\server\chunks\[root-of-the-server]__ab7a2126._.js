module.exports=[18622,(e,t,r)=>{t.exports=e.x("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js"))},56704,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/work-async-storage.external.js",()=>require("next/dist/server/app-render/work-async-storage.external.js"))},32319,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/work-unit-async-storage.external.js",()=>require("next/dist/server/app-render/work-unit-async-storage.external.js"))},20635,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/action-async-storage.external.js",()=>require("next/dist/server/app-render/action-async-storage.external.js"))},24725,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/after-task-async-storage.external.js",()=>require("next/dist/server/app-render/after-task-async-storage.external.js"))},24361,(e,t,r)=>{t.exports=e.x("util",()=>require("util"))},29173,(e,t,r)=>{t.exports=e.x("@prisma/client",()=>require("@prisma/client"))},70406,(e,t,r)=>{t.exports=e.x("next/dist/compiled/@opentelemetry/api",()=>require("next/dist/compiled/@opentelemetry/api"))},93695,(e,t,r)=>{t.exports=e.x("next/dist/shared/lib/no-fallback-error.external.js",()=>require("next/dist/shared/lib/no-fallback-error.external.js"))},61724,(e,t,r)=>{t.exports=e.x("next/dist/compiled/next-server/app-route-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-route-turbo.runtime.prod.js"))},54799,(e,t,r)=>{t.exports=e.x("crypto",()=>require("crypto"))},92509,(e,t,r)=>{t.exports=e.x("url",()=>require("url"))},21517,(e,t,r)=>{t.exports=e.x("http",()=>require("http"))},49719,(e,t,r)=>{t.exports=e.x("assert",()=>require("assert"))},45706,(e,t,r)=>{t.exports=e.x("querystring",()=>require("querystring"))},874,(e,t,r)=>{t.exports=e.x("buffer",()=>require("buffer"))},6461,(e,t,r)=>{t.exports=e.x("zlib",()=>require("zlib"))},24836,(e,t,r)=>{t.exports=e.x("https",()=>require("https"))},27699,(e,t,r)=>{t.exports=e.x("events",()=>require("events"))},12756,e=>{e.v(JSON.parse('{"name":"openid-client","version":"5.7.1","description":"OpenID Connect Relying Party (RP, Client) implementation for Node.js runtime, supports passportjs","keywords":["auth","authentication","basic","certified","client","connect","dynamic","electron","hybrid","identity","implicit","oauth","oauth2","oidc","openid","passport","relying party","strategy"],"homepage":"https://github.com/panva/openid-client","repository":"panva/openid-client","funding":{"url":"https://github.com/sponsors/panva"},"license":"MIT","author":"Filip Skokan <<EMAIL>>","exports":{"types":"./types/index.d.ts","import":"./lib/index.mjs","require":"./lib/index.js"},"main":"./lib/index.js","types":"./types/index.d.ts","files":["lib","types/index.d.ts"],"scripts":{"format":"npx prettier --loglevel silent --write ./lib ./test ./certification ./types","test":"mocha test/**/*.test.js"},"dependencies":{"jose":"^4.15.9","lru-cache":"^6.0.0","object-hash":"^2.2.0","oidc-token-hash":"^5.0.3"},"devDependencies":{"@types/node":"^16.18.106","@types/passport":"^1.0.16","base64url":"^3.0.1","chai":"^4.5.0","mocha":"^10.7.3","nock":"^13.5.5","prettier":"^2.8.8","readable-mock-req":"^0.2.2","sinon":"^9.2.4","timekeeper":"^2.3.1"},"standard-version":{"scripts":{"postchangelog":"sed -i \'\' -e \'s/### \\\\[/## [/g\' CHANGELOG.md"},"types":[{"type":"feat","section":"Features"},{"type":"fix","section":"Fixes"},{"type":"chore","hidden":true},{"type":"docs","hidden":true},{"type":"style","hidden":true},{"type":"refactor","section":"Refactor","hidden":false},{"type":"perf","section":"Performance","hidden":false},{"type":"test","hidden":true}]}}'))},12276,e=>{"use strict";e.s(["hasPermission",()=>r]);let t={ADMIN:["users:read","users:write","users:delete","students:read","students:write","students:delete","teachers:read","teachers:write","teachers:delete","classes:read","classes:write","classes:delete","subjects:read","subjects:write","subjects:delete","attendance:read","attendance:write","marks:read","marks:write","reports:read","reports:write","settings:read","settings:write","audit:read"],TEACHER:["students:read","attendance:read","attendance:write","marks:read","marks:write","reports:read"],STUDENT:["attendance:read","marks:read","reports:read"]};function r(e,r){return t[e]?.includes(r)??!1}},76053,(e,t,r)=>{},23831,e=>{"use strict";e.s(["handler",()=>O,"patchFetch",()=>T,"routeModule",()=>A,"serverHooks",()=>P,"workAsyncStorage",()=>S,"workUnitAsyncStorage",()=>k],23831);var t=e.i(6137),r=e.i(11365),s=e.i(9638),n=e.i(15243),a=e.i(66378),i=e.i(92101),o=e.i(50012),d=e.i(62885),l=e.i(31409),p=e.i(78448),u=e.i(28015),c=e.i(72721),x=e.i(75714),h=e.i(12634),m=e.i(93695);e.i(74732);var f=e.i(66662);e.s(["GET",()=>b,"POST",()=>N,"PUT",()=>q],62443);var y=e.i(2835),g=e.i(58356),R=e.i(43382),v=e.i(31279),w=e.i(12276),E=e.i(47504);let j=E.z.object({name:E.z.string().min(1,"Section name is required"),description:E.z.string().optional(),isActive:E.z.boolean().default(!0)});async function b(e){try{let t=await (0,g.getServerSession)(R.authOptions);if(!t?.user||!(0,w.hasPermission)(t.user.role,"sections:read"))return y.NextResponse.json({error:"Unauthorized"},{status:401});let{searchParams:r}=new URL(e.url),s=parseInt(r.get("page")||"1"),n=parseInt(r.get("limit")||"10"),a=r.get("search")||"",i=r.get("isActive"),o=(s-1)*n,d={};a&&(d.OR=[{name:{contains:a,mode:"insensitive"}},{description:{contains:a,mode:"insensitive"}}]),null!==i&&(d.isActive="true"===i);let[l,p]=await Promise.all([v.prisma.section.findMany({where:d,skip:o,take:n,orderBy:{name:"asc"},include:{classes:{select:{id:!0,name:!0,_count:{select:{students:!0}}}},_count:{select:{classes:!0}}}}),v.prisma.section.count({where:d})]),u=Math.ceil(p/n);return y.NextResponse.json({sections:l,pagination:{page:s,limit:n,total:p,totalPages:u}})}catch(e){return console.error("Error fetching sections:",e),y.NextResponse.json({error:"Failed to fetch sections"},{status:500})}}async function N(e){try{let t=await (0,g.getServerSession)(R.authOptions);if(!t?.user||!(0,w.hasPermission)(t.user.role,"sections:write"))return y.NextResponse.json({error:"Unauthorized"},{status:401});let r=await e.json(),s=j.parse(r);if(await v.prisma.section.findFirst({where:{name:s.name}}))return y.NextResponse.json({error:"Section with this name already exists"},{status:400});let n=await v.prisma.section.create({data:s});return y.NextResponse.json({message:"Section created successfully",section:n},{status:201})}catch(e){if(e instanceof E.z.ZodError)return y.NextResponse.json({error:"Validation error",details:e.errors},{status:400});return console.error("Error creating section:",e),y.NextResponse.json({error:"Failed to create section"},{status:500})}}async function q(e){try{let t=await (0,g.getServerSession)(R.authOptions);if(!t?.user||!(0,w.hasPermission)(t.user.role,"UPDATE_SECTION"))return y.NextResponse.json({error:"Unauthorized"},{status:401});let{id:r,...s}=await e.json();if(!r)return y.NextResponse.json({error:"Section ID is required"},{status:400});let n=j.partial().parse(s);if(!await v.prisma.section.findUnique({where:{id:parseInt(r)}}))return y.NextResponse.json({error:"Section not found"},{status:404});let a=await v.prisma.section.update({where:{id:parseInt(r)},data:n});return y.NextResponse.json({message:"Section updated successfully",section:a})}catch(e){if(e instanceof E.z.ZodError)return y.NextResponse.json({error:"Validation error",details:e.errors},{status:400});return console.error("Error updating section:",e),y.NextResponse.json({error:"Failed to update section"},{status:500})}}var C=e.i(62443);let A=new t.AppRouteRouteModule({definition:{kind:r.RouteKind.APP_ROUTE,page:"/api/admin/sections/route",pathname:"/api/admin/sections",filename:"route",bundlePath:""},distDir:".next",relativeProjectDir:"",resolvedPagePath:"[project]/school-management-system/src/app/api/admin/sections/route.ts",nextConfigOutput:"",userland:C}),{workAsyncStorage:S,workUnitAsyncStorage:k,serverHooks:P}=A;function T(){return(0,s.patchFetch)({workAsyncStorage:S,workUnitAsyncStorage:k})}async function O(e,t,s){var y;let g="/api/admin/sections/route";g=g.replace(/\/index$/,"")||"/";let R=await A.prepare(e,t,{srcPage:g,multiZoneDraftMode:!1});if(!R)return t.statusCode=400,t.end("Bad Request"),null==s.waitUntil||s.waitUntil.call(s,Promise.resolve()),null;let{buildId:v,params:w,nextConfig:E,isDraftMode:j,prerenderManifest:b,routerServerContext:N,isOnDemandRevalidate:q,revalidateOnlyGenerated:C,resolvedPathname:S}=R,k=(0,i.normalizeAppPath)(g),P=!!(b.dynamicRoutes[k]||b.routes[S]);if(P&&!j){let e=!!b.routes[S],t=b.dynamicRoutes[k];if(t&&!1===t.fallback&&!e)throw new m.NoFallbackError}let T=null;!P||A.isDev||j||(T="/index"===(T=S)?"/":T);let O=!0===A.isDev||!P,U=P&&!O,I=e.method||"GET",_=(0,a.getTracer)(),H=_.getActiveScopeSpan(),D={params:w,prerenderManifest:b,renderOpts:{experimental:{cacheComponents:!!E.experimental.cacheComponents,authInterrupts:!!E.experimental.authInterrupts},supportsDynamicResponse:O,incrementalCache:(0,n.getRequestMeta)(e,"incrementalCache"),cacheLifeProfiles:null==(y=E.experimental)?void 0:y.cacheLife,isRevalidate:U,waitUntil:s.waitUntil,onClose:e=>{t.on("close",e)},onAfterTaskError:void 0,onInstrumentationRequestError:(t,r,s)=>A.onRequestError(e,t,s,N)},sharedContext:{buildId:v}},M=new o.NodeNextRequest(e),F=new o.NodeNextResponse(t),z=d.NextRequestAdapter.fromNodeNextRequest(M,(0,d.signalFromNodeResponse)(t));try{let i=async r=>A.handle(z,D).finally(()=>{if(!r)return;r.setAttributes({"http.status_code":t.statusCode,"next.rsc":!1});let s=_.getRootSpanAttributes();if(!s)return;if(s.get("next.span_type")!==l.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${s.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let n=s.get("next.route");if(n){let e=`${I} ${n}`;r.setAttributes({"next.route":n,"http.route":n,"next.span_name":e}),r.updateName(e)}else r.updateName(`${I} ${e.url}`)}),o=async a=>{var o,d;let l=async({previousCacheEntry:r})=>{try{if(!(0,n.getRequestMeta)(e,"minimalMode")&&q&&C&&!r)return t.statusCode=404,t.setHeader("x-nextjs-cache","REVALIDATED"),t.end("This page could not be found"),null;let o=await i(a);e.fetchMetrics=D.renderOpts.fetchMetrics;let d=D.renderOpts.pendingWaitUntil;d&&s.waitUntil&&(s.waitUntil(d),d=void 0);let l=D.renderOpts.collectedTags;if(!P)return await (0,u.sendResponse)(M,F,o,D.renderOpts.pendingWaitUntil),null;{let e=await o.blob(),t=(0,c.toNodeOutgoingHttpHeaders)(o.headers);l&&(t[h.NEXT_CACHE_TAGS_HEADER]=l),!t["content-type"]&&e.type&&(t["content-type"]=e.type);let r=void 0!==D.renderOpts.collectedRevalidate&&!(D.renderOpts.collectedRevalidate>=h.INFINITE_CACHE)&&D.renderOpts.collectedRevalidate,s=void 0===D.renderOpts.collectedExpire||D.renderOpts.collectedExpire>=h.INFINITE_CACHE?void 0:D.renderOpts.collectedExpire;return{value:{kind:f.CachedRouteKind.APP_ROUTE,status:o.status,body:Buffer.from(await e.arrayBuffer()),headers:t},cacheControl:{revalidate:r,expire:s}}}}catch(t){throw(null==r?void 0:r.isStale)&&await A.onRequestError(e,t,{routerKind:"App Router",routePath:g,routeType:"route",revalidateReason:(0,p.getRevalidateReason)({isRevalidate:U,isOnDemandRevalidate:q})},N),t}},m=await A.handleResponse({req:e,nextConfig:E,cacheKey:T,routeKind:r.RouteKind.APP_ROUTE,isFallback:!1,prerenderManifest:b,isRoutePPREnabled:!1,isOnDemandRevalidate:q,revalidateOnlyGenerated:C,responseGenerator:l,waitUntil:s.waitUntil});if(!P)return null;if((null==m||null==(o=m.value)?void 0:o.kind)!==f.CachedRouteKind.APP_ROUTE)throw Object.defineProperty(Error(`Invariant: app-route received invalid cache entry ${null==m||null==(d=m.value)?void 0:d.kind}`),"__NEXT_ERROR_CODE",{value:"E701",enumerable:!1,configurable:!0});(0,n.getRequestMeta)(e,"minimalMode")||t.setHeader("x-nextjs-cache",q?"REVALIDATED":m.isMiss?"MISS":m.isStale?"STALE":"HIT"),j&&t.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");let y=(0,c.fromNodeOutgoingHttpHeaders)(m.value.headers);return(0,n.getRequestMeta)(e,"minimalMode")&&P||y.delete(h.NEXT_CACHE_TAGS_HEADER),!m.cacheControl||t.getHeader("Cache-Control")||y.get("Cache-Control")||y.set("Cache-Control",(0,x.getCacheControlHeader)(m.cacheControl)),await (0,u.sendResponse)(M,F,new Response(m.value.body,{headers:y,status:m.value.status||200})),null};H?await o(H):await _.withPropagatedContext(e.headers,()=>_.trace(l.BaseServerSpan.handleRequest,{spanName:`${I} ${e.url}`,kind:a.SpanKind.SERVER,attributes:{"http.method":I,"http.target":e.url}},o))}catch(t){if(H||t instanceof m.NoFallbackError||await A.onRequestError(e,t,{routerKind:"App Router",routePath:k,routeType:"route",revalidateReason:(0,p.getRevalidateReason)({isRevalidate:U,isOnDemandRevalidate:q})}),P)throw t;return await (0,u.sendResponse)(M,F,new Response(null,{status:500})),null}}}];

//# sourceMappingURL=%5Broot-of-the-server%5D__ab7a2126._.js.map