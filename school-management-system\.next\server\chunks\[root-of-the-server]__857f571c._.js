module.exports=[18622,(e,t,r)=>{t.exports=e.x("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js"))},56704,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/work-async-storage.external.js",()=>require("next/dist/server/app-render/work-async-storage.external.js"))},32319,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/work-unit-async-storage.external.js",()=>require("next/dist/server/app-render/work-unit-async-storage.external.js"))},20635,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/action-async-storage.external.js",()=>require("next/dist/server/app-render/action-async-storage.external.js"))},24725,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/after-task-async-storage.external.js",()=>require("next/dist/server/app-render/after-task-async-storage.external.js"))},24361,(e,t,r)=>{t.exports=e.x("util",()=>require("util"))},29173,(e,t,r)=>{t.exports=e.x("@prisma/client",()=>require("@prisma/client"))},70406,(e,t,r)=>{t.exports=e.x("next/dist/compiled/@opentelemetry/api",()=>require("next/dist/compiled/@opentelemetry/api"))},93695,(e,t,r)=>{t.exports=e.x("next/dist/shared/lib/no-fallback-error.external.js",()=>require("next/dist/shared/lib/no-fallback-error.external.js"))},61724,(e,t,r)=>{t.exports=e.x("next/dist/compiled/next-server/app-route-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-route-turbo.runtime.prod.js"))},54799,(e,t,r)=>{t.exports=e.x("crypto",()=>require("crypto"))},92509,(e,t,r)=>{t.exports=e.x("url",()=>require("url"))},21517,(e,t,r)=>{t.exports=e.x("http",()=>require("http"))},49719,(e,t,r)=>{t.exports=e.x("assert",()=>require("assert"))},45706,(e,t,r)=>{t.exports=e.x("querystring",()=>require("querystring"))},874,(e,t,r)=>{t.exports=e.x("buffer",()=>require("buffer"))},6461,(e,t,r)=>{t.exports=e.x("zlib",()=>require("zlib"))},24836,(e,t,r)=>{t.exports=e.x("https",()=>require("https"))},27699,(e,t,r)=>{t.exports=e.x("events",()=>require("events"))},12756,e=>{e.v(JSON.parse('{"name":"openid-client","version":"5.7.1","description":"OpenID Connect Relying Party (RP, Client) implementation for Node.js runtime, supports passportjs","keywords":["auth","authentication","basic","certified","client","connect","dynamic","electron","hybrid","identity","implicit","oauth","oauth2","oidc","openid","passport","relying party","strategy"],"homepage":"https://github.com/panva/openid-client","repository":"panva/openid-client","funding":{"url":"https://github.com/sponsors/panva"},"license":"MIT","author":"Filip Skokan <<EMAIL>>","exports":{"types":"./types/index.d.ts","import":"./lib/index.mjs","require":"./lib/index.js"},"main":"./lib/index.js","types":"./types/index.d.ts","files":["lib","types/index.d.ts"],"scripts":{"format":"npx prettier --loglevel silent --write ./lib ./test ./certification ./types","test":"mocha test/**/*.test.js"},"dependencies":{"jose":"^4.15.9","lru-cache":"^6.0.0","object-hash":"^2.2.0","oidc-token-hash":"^5.0.3"},"devDependencies":{"@types/node":"^16.18.106","@types/passport":"^1.0.16","base64url":"^3.0.1","chai":"^4.5.0","mocha":"^10.7.3","nock":"^13.5.5","prettier":"^2.8.8","readable-mock-req":"^0.2.2","sinon":"^9.2.4","timekeeper":"^2.3.1"},"standard-version":{"scripts":{"postchangelog":"sed -i \'\' -e \'s/### \\\\[/## [/g\' CHANGELOG.md"},"types":[{"type":"feat","section":"Features"},{"type":"fix","section":"Fixes"},{"type":"chore","hidden":true},{"type":"docs","hidden":true},{"type":"style","hidden":true},{"type":"refactor","section":"Refactor","hidden":false},{"type":"perf","section":"Performance","hidden":false},{"type":"test","hidden":true}]}}'))},60435,e=>{"use strict";e.s(["BulkMarkEntrySchema",()=>a,"MarkEntrySchema",()=>r,"VALIDATION_PATTERNS",()=>l,"formatValidationErrors",()=>o,"groupErrorsByStudent",()=>d,"validateBulkMarkEntry",()=>n,"validateGradeCalculation",()=>i,"validateMarkEntry",()=>s,"validatePattern",()=>u]);var t=e.i(47504);let r=t.z.object({studentId:t.z.string().min(1,"Student ID is required"),examId:t.z.string().min(1,"Exam ID is required"),obtainedMarks:t.z.number().min(0,"Marks cannot be negative").max(1e3,"Marks cannot exceed 1000"),remarks:t.z.string().optional()}),a=t.z.object({examId:t.z.string().min(1,"Exam ID is required"),marks:t.z.array(t.z.object({studentId:t.z.string().min(1,"Student ID is required"),obtainedMarks:t.z.number().min(0,"Marks cannot be negative").max(1e3,"Marks cannot exceed 1000"),remarks:t.z.string().optional()})).min(1,"At least one mark entry is required")}),s=(e,t,r,a,s)=>{let n=[];return e&&""!==e.trim()||n.push({field:"studentId",message:"Student ID is required"}),t&&""!==t.trim()||n.push({field:"examId",message:"Exam ID is required"}),r<0&&n.push({field:"obtainedMarks",message:"Marks cannot be negative",studentId:e}),r>a&&n.push({field:"obtainedMarks",message:`Marks cannot exceed maximum marks (${a})`,studentId:e}),(r.toString().split(".")[1]||"").length>2&&n.push({field:"obtainedMarks",message:"Marks can have at most 2 decimal places",studentId:e}),s&&s.length>500&&n.push({field:"remarks",message:"Remarks cannot exceed 500 characters",studentId:e}),{isValid:0===n.length,errors:n}},n=(e,t,r)=>{let a=[];if(e&&""!==e.trim()||a.push({field:"examId",message:"Exam ID is required"}),!r||0===r.length)return a.push({field:"marks",message:"At least one mark entry is required"}),{isValid:!1,errors:a};let n=new Set;return r.forEach((r,i)=>{n.has(r.studentId)?a.push({field:"studentId",message:"Duplicate student ID found",studentId:r.studentId}):n.add(r.studentId);let o=s(r.studentId,e,r.obtainedMarks,t,r.remarks);a.push(...o.errors)}),{isValid:0===a.length,errors:a}},i=(e,t)=>t<=0?{isValid:!1,error:"Maximum marks must be greater than 0"}:e<0?{isValid:!1,error:"Obtained marks cannot be negative"}:e>t?{isValid:!1,error:"Obtained marks cannot exceed maximum marks"}:{isValid:!0,percentage:Math.round(e/t*1e4)/100},o=e=>0===e.length?"":1===e.length?e[0].message:`Multiple errors found:
${e.map(e=>`• ${e.message}`).join("\n")}`,d=e=>e.reduce((e,t)=>{let r=t.studentId||"general";return e[r]||(e[r]=[]),e[r].push(t),e},{}),l={STUDENT_ID:/^[a-zA-Z0-9-_]+$/,EXAM_ID:/^[a-zA-Z0-9-_]+$/,MARKS_FORMAT:/^\d+(\.\d{1,2})?$/},u=(e,t,r)=>t.test(e)?null:{field:r,message:`Invalid ${r} format`}},87633,(e,t,r)=>{},43619,e=>{"use strict";e.s(["handler",()=>N,"patchFetch",()=>A,"routeModule",()=>w,"serverHooks",()=>M,"workAsyncStorage",()=>j,"workUnitAsyncStorage",()=>q],43619);var t=e.i(6137),r=e.i(11365),a=e.i(9638),s=e.i(15243),n=e.i(66378),i=e.i(92101),o=e.i(50012),d=e.i(62885),l=e.i(31409),u=e.i(78448),p=e.i(28015),c=e.i(72721),m=e.i(75714),h=e.i(12634),x=e.i(93695);e.i(74732);var f=e.i(66662);e.s(["GET",()=>b,"POST",()=>E],19876);var g=e.i(2835),k=e.i(58356),y=e.i(43382),v=e.i(31279),R=e.i(60435);async function b(e){try{let t=await (0,k.getServerSession)(y.authOptions);if(!t||"TEACHER"!==t.user.role)return g.NextResponse.json({error:"Unauthorized"},{status:401});let r=await v.prisma.teacher.findUnique({where:{userId:t.user.id}});if(!r)return g.NextResponse.json({error:"Teacher not found"},{status:404});let{searchParams:a}=new URL(e.url),s=a.get("examId"),n=a.get("subjectId"),i=a.get("classId"),o={gradedByTeacherId:r.id};s&&"all"!==s&&(o.examId=s),n&&"all"!==n&&(o.exam={subjectId:n}),i&&"all"!==i&&(o.student={currentClassId:i});let d=await v.prisma.mark.findMany({where:o,include:{student:{include:{user:!0,currentClass:!0,currentSection:!0}},exam:{include:{subject:!0,term:!0}}},orderBy:{createdAt:"desc"}});return g.NextResponse.json(d)}catch(e){return console.error("Error fetching teacher marks:",e),g.NextResponse.json({error:"Internal server error"},{status:500})}}async function E(t){try{let r=await (0,k.getServerSession)(y.authOptions);if(!r||"TEACHER"!==r.user.role)return g.NextResponse.json({error:"Unauthorized"},{status:401});let a=await v.prisma.teacher.findUnique({where:{userId:r.user.id}});if(!a)return g.NextResponse.json({error:"Teacher not found"},{status:404});let s=await t.json();if(s.marks&&Array.isArray(s.marks)){let e=R.BulkMarkEntrySchema.safeParse(s);if(!e.success)return g.NextResponse.json({error:"Validation failed",details:e.error.errors},{status:400});let{examId:t,marks:r}=e.data,n=await v.prisma.exam.findUnique({where:{id:t},include:{subject:!0}});if(!n)return g.NextResponse.json({error:"Exam not found"},{status:404});let i=(0,R.validateBulkMarkEntry)(t,n.maxMarks,r);if(!i.isValid)return g.NextResponse.json({error:"Validation failed",message:(0,R.formatValidationErrors)(i.errors),details:i.errors},{status:400});let o=[];for(let e of r)try{let r,s=await v.prisma.mark.findUnique({where:{studentId_examId:{studentId:e.studentId,examId:t}}});r=s?await v.prisma.mark.update({where:{id:s.id},data:{obtainedMarks:e.obtainedMarks,remarks:e.remarks,gradedByTeacherId:a.id}}):await v.prisma.mark.create({data:{studentId:e.studentId,examId:t,obtainedMarks:e.obtainedMarks,remarks:e.remarks,gradedByTeacherId:a.id}}),o.push({success:!0,mark:r})}catch(t){o.push({success:!1,error:t instanceof Error?t.message:"Unknown error",studentId:e.studentId})}return g.NextResponse.json({results:o})}{let t,r=R.MarkEntrySchema.safeParse(s);if(!r.success)return g.NextResponse.json({error:"Validation failed",details:r.error.errors},{status:400});let{studentId:n,examId:i,obtainedMarks:o,remarks:d}=r.data,l=await v.prisma.exam.findUnique({where:{id:i},include:{subject:!0}});if(!l)return g.NextResponse.json({error:"Exam not found"},{status:404});let{validateMarkEntry:u}=await e.A(11928),p=u(n,i,o,l.maxMarks,d);if(!p.isValid)return g.NextResponse.json({error:"Validation failed",message:(0,R.formatValidationErrors)(p.errors),details:p.errors},{status:400});let c=await v.prisma.mark.findUnique({where:{studentId_examId:{studentId:n,examId:i}}});return t=c?await v.prisma.mark.update({where:{id:c.id},data:{obtainedMarks:o,remarks:d,gradedByTeacherId:a.id}}):await v.prisma.mark.create({data:{studentId:n,examId:i,obtainedMarks:o,remarks:d,gradedByTeacherId:a.id}}),g.NextResponse.json(t)}}catch(e){return console.error("Error creating/updating mark:",e),g.NextResponse.json({error:"Internal server error"},{status:500})}}var I=e.i(19876);let w=new t.AppRouteRouteModule({definition:{kind:r.RouteKind.APP_ROUTE,page:"/api/teacher/marks/route",pathname:"/api/teacher/marks",filename:"route",bundlePath:""},distDir:".next",relativeProjectDir:"",resolvedPagePath:"[project]/school-management-system/src/app/api/teacher/marks/route.ts",nextConfigOutput:"",userland:I}),{workAsyncStorage:j,workUnitAsyncStorage:q,serverHooks:M}=w;function A(){return(0,a.patchFetch)({workAsyncStorage:j,workUnitAsyncStorage:q})}async function N(e,t,a){var g;let k="/api/teacher/marks/route";k=k.replace(/\/index$/,"")||"/";let y=await w.prepare(e,t,{srcPage:k,multiZoneDraftMode:!1});if(!y)return t.statusCode=400,t.end("Bad Request"),null==a.waitUntil||a.waitUntil.call(a,Promise.resolve()),null;let{buildId:v,params:R,nextConfig:b,isDraftMode:E,prerenderManifest:I,routerServerContext:j,isOnDemandRevalidate:q,revalidateOnlyGenerated:M,resolvedPathname:A}=y,N=(0,i.normalizeAppPath)(k),C=!!(I.dynamicRoutes[N]||I.routes[A]);if(C&&!E){let e=!!I.routes[A],t=I.dynamicRoutes[N];if(t&&!1===t.fallback&&!e)throw new x.NoFallbackError}let T=null;!C||w.isDev||E||(T="/index"===(T=A)?"/":T);let S=!0===w.isDev||!C,O=C&&!S,P=e.method||"GET",_=(0,n.getTracer)(),D=_.getActiveScopeSpan(),U={params:R,prerenderManifest:I,renderOpts:{experimental:{cacheComponents:!!b.experimental.cacheComponents,authInterrupts:!!b.experimental.authInterrupts},supportsDynamicResponse:S,incrementalCache:(0,s.getRequestMeta)(e,"incrementalCache"),cacheLifeProfiles:null==(g=b.experimental)?void 0:g.cacheLife,isRevalidate:O,waitUntil:a.waitUntil,onClose:e=>{t.on("close",e)},onAfterTaskError:void 0,onInstrumentationRequestError:(t,r,a)=>w.onRequestError(e,t,a,j)},sharedContext:{buildId:v}},H=new o.NodeNextRequest(e),V=new o.NodeNextResponse(t),z=d.NextRequestAdapter.fromNodeNextRequest(H,(0,d.signalFromNodeResponse)(t));try{let i=async r=>w.handle(z,U).finally(()=>{if(!r)return;r.setAttributes({"http.status_code":t.statusCode,"next.rsc":!1});let a=_.getRootSpanAttributes();if(!a)return;if(a.get("next.span_type")!==l.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${a.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let s=a.get("next.route");if(s){let e=`${P} ${s}`;r.setAttributes({"next.route":s,"http.route":s,"next.span_name":e}),r.updateName(e)}else r.updateName(`${P} ${e.url}`)}),o=async n=>{var o,d;let l=async({previousCacheEntry:r})=>{try{if(!(0,s.getRequestMeta)(e,"minimalMode")&&q&&M&&!r)return t.statusCode=404,t.setHeader("x-nextjs-cache","REVALIDATED"),t.end("This page could not be found"),null;let o=await i(n);e.fetchMetrics=U.renderOpts.fetchMetrics;let d=U.renderOpts.pendingWaitUntil;d&&a.waitUntil&&(a.waitUntil(d),d=void 0);let l=U.renderOpts.collectedTags;if(!C)return await (0,p.sendResponse)(H,V,o,U.renderOpts.pendingWaitUntil),null;{let e=await o.blob(),t=(0,c.toNodeOutgoingHttpHeaders)(o.headers);l&&(t[h.NEXT_CACHE_TAGS_HEADER]=l),!t["content-type"]&&e.type&&(t["content-type"]=e.type);let r=void 0!==U.renderOpts.collectedRevalidate&&!(U.renderOpts.collectedRevalidate>=h.INFINITE_CACHE)&&U.renderOpts.collectedRevalidate,a=void 0===U.renderOpts.collectedExpire||U.renderOpts.collectedExpire>=h.INFINITE_CACHE?void 0:U.renderOpts.collectedExpire;return{value:{kind:f.CachedRouteKind.APP_ROUTE,status:o.status,body:Buffer.from(await e.arrayBuffer()),headers:t},cacheControl:{revalidate:r,expire:a}}}}catch(t){throw(null==r?void 0:r.isStale)&&await w.onRequestError(e,t,{routerKind:"App Router",routePath:k,routeType:"route",revalidateReason:(0,u.getRevalidateReason)({isRevalidate:O,isOnDemandRevalidate:q})},j),t}},x=await w.handleResponse({req:e,nextConfig:b,cacheKey:T,routeKind:r.RouteKind.APP_ROUTE,isFallback:!1,prerenderManifest:I,isRoutePPREnabled:!1,isOnDemandRevalidate:q,revalidateOnlyGenerated:M,responseGenerator:l,waitUntil:a.waitUntil});if(!C)return null;if((null==x||null==(o=x.value)?void 0:o.kind)!==f.CachedRouteKind.APP_ROUTE)throw Object.defineProperty(Error(`Invariant: app-route received invalid cache entry ${null==x||null==(d=x.value)?void 0:d.kind}`),"__NEXT_ERROR_CODE",{value:"E701",enumerable:!1,configurable:!0});(0,s.getRequestMeta)(e,"minimalMode")||t.setHeader("x-nextjs-cache",q?"REVALIDATED":x.isMiss?"MISS":x.isStale?"STALE":"HIT"),E&&t.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");let g=(0,c.fromNodeOutgoingHttpHeaders)(x.value.headers);return(0,s.getRequestMeta)(e,"minimalMode")&&C||g.delete(h.NEXT_CACHE_TAGS_HEADER),!x.cacheControl||t.getHeader("Cache-Control")||g.get("Cache-Control")||g.set("Cache-Control",(0,m.getCacheControlHeader)(x.cacheControl)),await (0,p.sendResponse)(H,V,new Response(x.value.body,{headers:g,status:x.value.status||200})),null};D?await o(D):await _.withPropagatedContext(e.headers,()=>_.trace(l.BaseServerSpan.handleRequest,{spanName:`${P} ${e.url}`,kind:n.SpanKind.SERVER,attributes:{"http.method":P,"http.target":e.url}},o))}catch(t){if(D||t instanceof x.NoFallbackError||await w.onRequestError(e,t,{routerKind:"App Router",routePath:N,routeType:"route",revalidateReason:(0,u.getRevalidateReason)({isRevalidate:O,isOnDemandRevalidate:q})}),C)throw t;return await (0,p.sendResponse)(H,V,new Response(null,{status:500})),null}}},11928,e=>{e.v(e=>Promise.resolve().then(()=>e(60435)))}];

//# sourceMappingURL=%5Broot-of-the-server%5D__857f571c._.js.map