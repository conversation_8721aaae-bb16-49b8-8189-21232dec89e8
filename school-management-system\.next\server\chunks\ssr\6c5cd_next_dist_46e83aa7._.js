module.exports=[62360,(a,b,c)=>{(()=>{"use strict";"undefined"!=typeof __nccwpck_require__&&(__nccwpck_require__.ab="/ROOT/school-management-system/node_modules/.pnpm/next@15.5.2_@babel+core@7.2_1d9756f0f263ecf6e82055b1d95a339c/node_modules/next/dist/compiled/cookie/");var a={};(()=>{a.parse=function(a,c){if("string"!=typeof a)throw TypeError("argument str must be a string");for(var e={},f=a.split(d),g=(c||{}).decode||b,h=0;h<f.length;h++){var i=f[h],j=i.indexOf("=");if(!(j<0)){var k=i.substr(0,j).trim(),l=i.substr(++j,i.length).trim();'"'==l[0]&&(l=l.slice(1,-1)),void 0==e[k]&&(e[k]=function(a,b){try{return b(a)}catch(b){return a}}(l,g))}}return e},a.serialize=function(a,b,d){var f=d||{},g=f.encode||c;if("function"!=typeof g)throw TypeError("option encode is invalid");if(!e.test(a))throw TypeError("argument name is invalid");var h=g(b);if(h&&!e.test(h))throw TypeError("argument val is invalid");var i=a+"="+h;if(null!=f.maxAge){var j=f.maxAge-0;if(isNaN(j)||!isFinite(j))throw TypeError("option maxAge is invalid");i+="; Max-Age="+Math.floor(j)}if(f.domain){if(!e.test(f.domain))throw TypeError("option domain is invalid");i+="; Domain="+f.domain}if(f.path){if(!e.test(f.path))throw TypeError("option path is invalid");i+="; Path="+f.path}if(f.expires){if("function"!=typeof f.expires.toUTCString)throw TypeError("option expires is invalid");i+="; Expires="+f.expires.toUTCString()}if(f.httpOnly&&(i+="; HttpOnly"),f.secure&&(i+="; Secure"),f.sameSite)switch("string"==typeof f.sameSite?f.sameSite.toLowerCase():f.sameSite){case!0:case"strict":i+="; SameSite=Strict";break;case"lax":i+="; SameSite=Lax";break;case"none":i+="; SameSite=None";break;default:throw TypeError("option sameSite is invalid")}return i};var b=decodeURIComponent,c=encodeURIComponent,d=/; */,e=/^[\u0009\u0020-\u007e\u0080-\u00ff]+$/})(),b.exports=a})()},95875,a=>{"use strict";a.s(["RouteKind",()=>b]);var b=function(a){return a.PAGES="PAGES",a.PAGES_API="PAGES_API",a.APP_PAGE="APP_PAGE",a.APP_ROUTE="APP_ROUTE",a.IMAGE="IMAGE",a}({})},45535,a=>{"use strict";a.s(["ReflectAdapter",()=>b]);class b{static get(a,b,c){let d=Reflect.get(a,b,c);return"function"==typeof d?d.bind(a):d}static set(a,b,c,d){return Reflect.set(a,b,c,d)}static has(a,b){return Reflect.has(a,b)}static deleteProperty(a,b){return Reflect.deleteProperty(a,b)}}},33634,a=>{"use strict";a.s(["atLeastOneTask",()=>d,"scheduleImmediate",()=>c,"scheduleOnNextTick",()=>b,"waitAtLeastOneReactRenderTask",()=>e]);let b=a=>{Promise.resolve().then(()=>{process.nextTick(a)})},c=a=>{setImmediate(a)};function d(){return new Promise(a=>c(a))}function e(){return new Promise(a=>setImmediate(a))}},17551,a=>{"use strict";a.s(["InvariantError",()=>b]);class b extends Error{constructor(a,b){super("Invariant: "+(a.endsWith(".")?a:a+".")+" This is a bug in Next.js.",b),this.name="InvariantError"}}},2105,a=>{"use strict";function b(a){return"("===a[0]&&a.endsWith(")")}a.s(["DEFAULT_SEGMENT_KEY",()=>d,"PAGE_SEGMENT_KEY",()=>c,"isGroupSegment",()=>b]);let c="__PAGE__",d="__DEFAULT__"},17868,(a,b,c)=>{(()=>{"use strict";var c={491:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.ContextAPI=void 0;let d=c(223),e=c(172),f=c(930),g="context",h=new d.NoopContextManager;class i{constructor(){}static getInstance(){return this._instance||(this._instance=new i),this._instance}setGlobalContextManager(a){return(0,e.registerGlobal)(g,a,f.DiagAPI.instance())}active(){return this._getContextManager().active()}with(a,b,c,...d){return this._getContextManager().with(a,b,c,...d)}bind(a,b){return this._getContextManager().bind(a,b)}_getContextManager(){return(0,e.getGlobal)(g)||h}disable(){this._getContextManager().disable(),(0,e.unregisterGlobal)(g,f.DiagAPI.instance())}}b.ContextAPI=i},930:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.DiagAPI=void 0;let d=c(56),e=c(912),f=c(957),g=c(172);class h{constructor(){function a(a){return function(...b){let c=(0,g.getGlobal)("diag");if(c)return c[a](...b)}}let b=this;b.setLogger=(a,c={logLevel:f.DiagLogLevel.INFO})=>{var d,h,i;if(a===b){let a=Error("Cannot use diag as the logger for itself. Please use a DiagLogger implementation like ConsoleDiagLogger or a custom implementation");return b.error(null!=(d=a.stack)?d:a.message),!1}"number"==typeof c&&(c={logLevel:c});let j=(0,g.getGlobal)("diag"),k=(0,e.createLogLevelDiagLogger)(null!=(h=c.logLevel)?h:f.DiagLogLevel.INFO,a);if(j&&!c.suppressOverrideMessage){let a=null!=(i=Error().stack)?i:"<failed to generate stacktrace>";j.warn(`Current logger will be overwritten from ${a}`),k.warn(`Current logger will overwrite one already registered from ${a}`)}return(0,g.registerGlobal)("diag",k,b,!0)},b.disable=()=>{(0,g.unregisterGlobal)("diag",b)},b.createComponentLogger=a=>new d.DiagComponentLogger(a),b.verbose=a("verbose"),b.debug=a("debug"),b.info=a("info"),b.warn=a("warn"),b.error=a("error")}static instance(){return this._instance||(this._instance=new h),this._instance}}b.DiagAPI=h},653:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.MetricsAPI=void 0;let d=c(660),e=c(172),f=c(930),g="metrics";class h{constructor(){}static getInstance(){return this._instance||(this._instance=new h),this._instance}setGlobalMeterProvider(a){return(0,e.registerGlobal)(g,a,f.DiagAPI.instance())}getMeterProvider(){return(0,e.getGlobal)(g)||d.NOOP_METER_PROVIDER}getMeter(a,b,c){return this.getMeterProvider().getMeter(a,b,c)}disable(){(0,e.unregisterGlobal)(g,f.DiagAPI.instance())}}b.MetricsAPI=h},181:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.PropagationAPI=void 0;let d=c(172),e=c(874),f=c(194),g=c(277),h=c(369),i=c(930),j="propagation",k=new e.NoopTextMapPropagator;class l{constructor(){this.createBaggage=h.createBaggage,this.getBaggage=g.getBaggage,this.getActiveBaggage=g.getActiveBaggage,this.setBaggage=g.setBaggage,this.deleteBaggage=g.deleteBaggage}static getInstance(){return this._instance||(this._instance=new l),this._instance}setGlobalPropagator(a){return(0,d.registerGlobal)(j,a,i.DiagAPI.instance())}inject(a,b,c=f.defaultTextMapSetter){return this._getGlobalPropagator().inject(a,b,c)}extract(a,b,c=f.defaultTextMapGetter){return this._getGlobalPropagator().extract(a,b,c)}fields(){return this._getGlobalPropagator().fields()}disable(){(0,d.unregisterGlobal)(j,i.DiagAPI.instance())}_getGlobalPropagator(){return(0,d.getGlobal)(j)||k}}b.PropagationAPI=l},997:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.TraceAPI=void 0;let d=c(172),e=c(846),f=c(139),g=c(607),h=c(930),i="trace";class j{constructor(){this._proxyTracerProvider=new e.ProxyTracerProvider,this.wrapSpanContext=f.wrapSpanContext,this.isSpanContextValid=f.isSpanContextValid,this.deleteSpan=g.deleteSpan,this.getSpan=g.getSpan,this.getActiveSpan=g.getActiveSpan,this.getSpanContext=g.getSpanContext,this.setSpan=g.setSpan,this.setSpanContext=g.setSpanContext}static getInstance(){return this._instance||(this._instance=new j),this._instance}setGlobalTracerProvider(a){let b=(0,d.registerGlobal)(i,this._proxyTracerProvider,h.DiagAPI.instance());return b&&this._proxyTracerProvider.setDelegate(a),b}getTracerProvider(){return(0,d.getGlobal)(i)||this._proxyTracerProvider}getTracer(a,b){return this.getTracerProvider().getTracer(a,b)}disable(){(0,d.unregisterGlobal)(i,h.DiagAPI.instance()),this._proxyTracerProvider=new e.ProxyTracerProvider}}b.TraceAPI=j},277:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.deleteBaggage=b.setBaggage=b.getActiveBaggage=b.getBaggage=void 0;let d=c(491),e=(0,c(780).createContextKey)("OpenTelemetry Baggage Key");function f(a){return a.getValue(e)||void 0}b.getBaggage=f,b.getActiveBaggage=function(){return f(d.ContextAPI.getInstance().active())},b.setBaggage=function(a,b){return a.setValue(e,b)},b.deleteBaggage=function(a){return a.deleteValue(e)}},993:(a,b)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.BaggageImpl=void 0;class c{constructor(a){this._entries=a?new Map(a):new Map}getEntry(a){let b=this._entries.get(a);if(b)return Object.assign({},b)}getAllEntries(){return Array.from(this._entries.entries()).map(([a,b])=>[a,b])}setEntry(a,b){let d=new c(this._entries);return d._entries.set(a,b),d}removeEntry(a){let b=new c(this._entries);return b._entries.delete(a),b}removeEntries(...a){let b=new c(this._entries);for(let c of a)b._entries.delete(c);return b}clear(){return new c}}b.BaggageImpl=c},830:(a,b)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.baggageEntryMetadataSymbol=void 0,b.baggageEntryMetadataSymbol=Symbol("BaggageEntryMetadata")},369:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.baggageEntryMetadataFromString=b.createBaggage=void 0;let d=c(930),e=c(993),f=c(830),g=d.DiagAPI.instance();b.createBaggage=function(a={}){return new e.BaggageImpl(new Map(Object.entries(a)))},b.baggageEntryMetadataFromString=function(a){return"string"!=typeof a&&(g.error(`Cannot create baggage metadata from unknown type: ${typeof a}`),a=""),{__TYPE__:f.baggageEntryMetadataSymbol,toString:()=>a}}},67:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.context=void 0,b.context=c(491).ContextAPI.getInstance()},223:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.NoopContextManager=void 0;let d=c(780);b.NoopContextManager=class{active(){return d.ROOT_CONTEXT}with(a,b,c,...d){return b.call(c,...d)}bind(a,b){return b}enable(){return this}disable(){return this}}},780:(a,b)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.ROOT_CONTEXT=b.createContextKey=void 0,b.createContextKey=function(a){return Symbol.for(a)};class c{constructor(a){let b=this;b._currentContext=a?new Map(a):new Map,b.getValue=a=>b._currentContext.get(a),b.setValue=(a,d)=>{let e=new c(b._currentContext);return e._currentContext.set(a,d),e},b.deleteValue=a=>{let d=new c(b._currentContext);return d._currentContext.delete(a),d}}}b.ROOT_CONTEXT=new c},506:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.diag=void 0,b.diag=c(930).DiagAPI.instance()},56:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.DiagComponentLogger=void 0;let d=c(172);function e(a,b,c){let e=(0,d.getGlobal)("diag");if(e)return c.unshift(b),e[a](...c)}b.DiagComponentLogger=class{constructor(a){this._namespace=a.namespace||"DiagComponentLogger"}debug(...a){return e("debug",this._namespace,a)}error(...a){return e("error",this._namespace,a)}info(...a){return e("info",this._namespace,a)}warn(...a){return e("warn",this._namespace,a)}verbose(...a){return e("verbose",this._namespace,a)}}},972:(a,b)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.DiagConsoleLogger=void 0;let c=[{n:"error",c:"error"},{n:"warn",c:"warn"},{n:"info",c:"info"},{n:"debug",c:"debug"},{n:"verbose",c:"trace"}];b.DiagConsoleLogger=class{constructor(){for(let a=0;a<c.length;a++)this[c[a].n]=function(a){return function(...b){if(console){let c=console[a];if("function"!=typeof c&&(c=console.log),"function"==typeof c)return c.apply(console,b)}}}(c[a].c)}}},912:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.createLogLevelDiagLogger=void 0;let d=c(957);b.createLogLevelDiagLogger=function(a,b){function c(c,d){let e=b[c];return"function"==typeof e&&a>=d?e.bind(b):function(){}}return a<d.DiagLogLevel.NONE?a=d.DiagLogLevel.NONE:a>d.DiagLogLevel.ALL&&(a=d.DiagLogLevel.ALL),b=b||{},{error:c("error",d.DiagLogLevel.ERROR),warn:c("warn",d.DiagLogLevel.WARN),info:c("info",d.DiagLogLevel.INFO),debug:c("debug",d.DiagLogLevel.DEBUG),verbose:c("verbose",d.DiagLogLevel.VERBOSE)}}},957:(a,b)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.DiagLogLevel=void 0,function(a){a[a.NONE=0]="NONE",a[a.ERROR=30]="ERROR",a[a.WARN=50]="WARN",a[a.INFO=60]="INFO",a[a.DEBUG=70]="DEBUG",a[a.VERBOSE=80]="VERBOSE",a[a.ALL=9999]="ALL"}(b.DiagLogLevel||(b.DiagLogLevel={}))},172:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.unregisterGlobal=b.getGlobal=b.registerGlobal=void 0;let d=c(200),e=c(521),f=c(130),g=e.VERSION.split(".")[0],h=Symbol.for(`opentelemetry.js.api.${g}`),i=d._globalThis;b.registerGlobal=function(a,b,c,d=!1){var f;let g=i[h]=null!=(f=i[h])?f:{version:e.VERSION};if(!d&&g[a]){let b=Error(`@opentelemetry/api: Attempted duplicate registration of API: ${a}`);return c.error(b.stack||b.message),!1}if(g.version!==e.VERSION){let b=Error(`@opentelemetry/api: Registration of version v${g.version} for ${a} does not match previously registered API v${e.VERSION}`);return c.error(b.stack||b.message),!1}return g[a]=b,c.debug(`@opentelemetry/api: Registered a global for ${a} v${e.VERSION}.`),!0},b.getGlobal=function(a){var b,c;let d=null==(b=i[h])?void 0:b.version;if(d&&(0,f.isCompatible)(d))return null==(c=i[h])?void 0:c[a]},b.unregisterGlobal=function(a,b){b.debug(`@opentelemetry/api: Unregistering a global for ${a} v${e.VERSION}.`);let c=i[h];c&&delete c[a]}},130:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.isCompatible=b._makeCompatibilityCheck=void 0;let d=c(521),e=/^(\d+)\.(\d+)\.(\d+)(-(.+))?$/;function f(a){let b=new Set([a]),c=new Set,d=a.match(e);if(!d)return()=>!1;let f={major:+d[1],minor:+d[2],patch:+d[3],prerelease:d[4]};if(null!=f.prerelease)return function(b){return b===a};function g(a){return c.add(a),!1}return function(a){if(b.has(a))return!0;if(c.has(a))return!1;let d=a.match(e);if(!d)return g(a);let h={major:+d[1],minor:+d[2],patch:+d[3],prerelease:d[4]};if(null!=h.prerelease||f.major!==h.major)return g(a);if(0===f.major)return f.minor===h.minor&&f.patch<=h.patch?(b.add(a),!0):g(a);return f.minor<=h.minor?(b.add(a),!0):g(a)}}b._makeCompatibilityCheck=f,b.isCompatible=f(d.VERSION)},886:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.metrics=void 0,b.metrics=c(653).MetricsAPI.getInstance()},901:(a,b)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.ValueType=void 0,function(a){a[a.INT=0]="INT",a[a.DOUBLE=1]="DOUBLE"}(b.ValueType||(b.ValueType={}))},102:(a,b)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.createNoopMeter=b.NOOP_OBSERVABLE_UP_DOWN_COUNTER_METRIC=b.NOOP_OBSERVABLE_GAUGE_METRIC=b.NOOP_OBSERVABLE_COUNTER_METRIC=b.NOOP_UP_DOWN_COUNTER_METRIC=b.NOOP_HISTOGRAM_METRIC=b.NOOP_COUNTER_METRIC=b.NOOP_METER=b.NoopObservableUpDownCounterMetric=b.NoopObservableGaugeMetric=b.NoopObservableCounterMetric=b.NoopObservableMetric=b.NoopHistogramMetric=b.NoopUpDownCounterMetric=b.NoopCounterMetric=b.NoopMetric=b.NoopMeter=void 0;class c{constructor(){}createHistogram(a,c){return b.NOOP_HISTOGRAM_METRIC}createCounter(a,c){return b.NOOP_COUNTER_METRIC}createUpDownCounter(a,c){return b.NOOP_UP_DOWN_COUNTER_METRIC}createObservableGauge(a,c){return b.NOOP_OBSERVABLE_GAUGE_METRIC}createObservableCounter(a,c){return b.NOOP_OBSERVABLE_COUNTER_METRIC}createObservableUpDownCounter(a,c){return b.NOOP_OBSERVABLE_UP_DOWN_COUNTER_METRIC}addBatchObservableCallback(a,b){}removeBatchObservableCallback(a){}}b.NoopMeter=c;class d{}b.NoopMetric=d;class e extends d{add(a,b){}}b.NoopCounterMetric=e;class f extends d{add(a,b){}}b.NoopUpDownCounterMetric=f;class g extends d{record(a,b){}}b.NoopHistogramMetric=g;class h{addCallback(a){}removeCallback(a){}}b.NoopObservableMetric=h;class i extends h{}b.NoopObservableCounterMetric=i;class j extends h{}b.NoopObservableGaugeMetric=j;class k extends h{}b.NoopObservableUpDownCounterMetric=k,b.NOOP_METER=new c,b.NOOP_COUNTER_METRIC=new e,b.NOOP_HISTOGRAM_METRIC=new g,b.NOOP_UP_DOWN_COUNTER_METRIC=new f,b.NOOP_OBSERVABLE_COUNTER_METRIC=new i,b.NOOP_OBSERVABLE_GAUGE_METRIC=new j,b.NOOP_OBSERVABLE_UP_DOWN_COUNTER_METRIC=new k,b.createNoopMeter=function(){return b.NOOP_METER}},660:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.NOOP_METER_PROVIDER=b.NoopMeterProvider=void 0;let d=c(102);class e{getMeter(a,b,c){return d.NOOP_METER}}b.NoopMeterProvider=e,b.NOOP_METER_PROVIDER=new e},200:function(a,b,c){var d=this&&this.__createBinding||(Object.create?function(a,b,c,d){void 0===d&&(d=c),Object.defineProperty(a,d,{enumerable:!0,get:function(){return b[c]}})}:function(a,b,c,d){void 0===d&&(d=c),a[d]=b[c]}),e=this&&this.__exportStar||function(a,b){for(var c in a)"default"===c||Object.prototype.hasOwnProperty.call(b,c)||d(b,a,c)};Object.defineProperty(b,"__esModule",{value:!0}),e(c(46),b)},651:(b,c)=>{Object.defineProperty(c,"__esModule",{value:!0}),c._globalThis=void 0,c._globalThis="object"==typeof globalThis?globalThis:a.g},46:function(a,b,c){var d=this&&this.__createBinding||(Object.create?function(a,b,c,d){void 0===d&&(d=c),Object.defineProperty(a,d,{enumerable:!0,get:function(){return b[c]}})}:function(a,b,c,d){void 0===d&&(d=c),a[d]=b[c]}),e=this&&this.__exportStar||function(a,b){for(var c in a)"default"===c||Object.prototype.hasOwnProperty.call(b,c)||d(b,a,c)};Object.defineProperty(b,"__esModule",{value:!0}),e(c(651),b)},939:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.propagation=void 0,b.propagation=c(181).PropagationAPI.getInstance()},874:(a,b)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.NoopTextMapPropagator=void 0,b.NoopTextMapPropagator=class{inject(a,b){}extract(a,b){return a}fields(){return[]}}},194:(a,b)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.defaultTextMapSetter=b.defaultTextMapGetter=void 0,b.defaultTextMapGetter={get(a,b){if(null!=a)return a[b]},keys:a=>null==a?[]:Object.keys(a)},b.defaultTextMapSetter={set(a,b,c){null!=a&&(a[b]=c)}}},845:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.trace=void 0,b.trace=c(997).TraceAPI.getInstance()},403:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.NonRecordingSpan=void 0;let d=c(476);b.NonRecordingSpan=class{constructor(a=d.INVALID_SPAN_CONTEXT){this._spanContext=a}spanContext(){return this._spanContext}setAttribute(a,b){return this}setAttributes(a){return this}addEvent(a,b){return this}setStatus(a){return this}updateName(a){return this}end(a){}isRecording(){return!1}recordException(a,b){}}},614:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.NoopTracer=void 0;let d=c(491),e=c(607),f=c(403),g=c(139),h=d.ContextAPI.getInstance();b.NoopTracer=class{startSpan(a,b,c=h.active()){var d;if(null==b?void 0:b.root)return new f.NonRecordingSpan;let i=c&&(0,e.getSpanContext)(c);return"object"==typeof(d=i)&&"string"==typeof d.spanId&&"string"==typeof d.traceId&&"number"==typeof d.traceFlags&&(0,g.isSpanContextValid)(i)?new f.NonRecordingSpan(i):new f.NonRecordingSpan}startActiveSpan(a,b,c,d){let f,g,i;if(arguments.length<2)return;2==arguments.length?i=b:3==arguments.length?(f=b,i=c):(f=b,g=c,i=d);let j=null!=g?g:h.active(),k=this.startSpan(a,f,j),l=(0,e.setSpan)(j,k);return h.with(l,i,void 0,k)}}},124:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.NoopTracerProvider=void 0;let d=c(614);b.NoopTracerProvider=class{getTracer(a,b,c){return new d.NoopTracer}}},125:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.ProxyTracer=void 0;let d=new(c(614)).NoopTracer;b.ProxyTracer=class{constructor(a,b,c,d){this._provider=a,this.name=b,this.version=c,this.options=d}startSpan(a,b,c){return this._getTracer().startSpan(a,b,c)}startActiveSpan(a,b,c,d){let e=this._getTracer();return Reflect.apply(e.startActiveSpan,e,arguments)}_getTracer(){if(this._delegate)return this._delegate;let a=this._provider.getDelegateTracer(this.name,this.version,this.options);return a?(this._delegate=a,this._delegate):d}}},846:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.ProxyTracerProvider=void 0;let d=c(125),e=new(c(124)).NoopTracerProvider;b.ProxyTracerProvider=class{getTracer(a,b,c){var e;return null!=(e=this.getDelegateTracer(a,b,c))?e:new d.ProxyTracer(this,a,b,c)}getDelegate(){var a;return null!=(a=this._delegate)?a:e}setDelegate(a){this._delegate=a}getDelegateTracer(a,b,c){var d;return null==(d=this._delegate)?void 0:d.getTracer(a,b,c)}}},996:(a,b)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.SamplingDecision=void 0,function(a){a[a.NOT_RECORD=0]="NOT_RECORD",a[a.RECORD=1]="RECORD",a[a.RECORD_AND_SAMPLED=2]="RECORD_AND_SAMPLED"}(b.SamplingDecision||(b.SamplingDecision={}))},607:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.getSpanContext=b.setSpanContext=b.deleteSpan=b.setSpan=b.getActiveSpan=b.getSpan=void 0;let d=c(780),e=c(403),f=c(491),g=(0,d.createContextKey)("OpenTelemetry Context Key SPAN");function h(a){return a.getValue(g)||void 0}function i(a,b){return a.setValue(g,b)}b.getSpan=h,b.getActiveSpan=function(){return h(f.ContextAPI.getInstance().active())},b.setSpan=i,b.deleteSpan=function(a){return a.deleteValue(g)},b.setSpanContext=function(a,b){return i(a,new e.NonRecordingSpan(b))},b.getSpanContext=function(a){var b;return null==(b=h(a))?void 0:b.spanContext()}},325:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.TraceStateImpl=void 0;let d=c(564);class e{constructor(a){this._internalState=new Map,a&&this._parse(a)}set(a,b){let c=this._clone();return c._internalState.has(a)&&c._internalState.delete(a),c._internalState.set(a,b),c}unset(a){let b=this._clone();return b._internalState.delete(a),b}get(a){return this._internalState.get(a)}serialize(){return this._keys().reduce((a,b)=>(a.push(b+"="+this.get(b)),a),[]).join(",")}_parse(a){!(a.length>512)&&(this._internalState=a.split(",").reverse().reduce((a,b)=>{let c=b.trim(),e=c.indexOf("=");if(-1!==e){let f=c.slice(0,e),g=c.slice(e+1,b.length);(0,d.validateKey)(f)&&(0,d.validateValue)(g)&&a.set(f,g)}return a},new Map),this._internalState.size>32&&(this._internalState=new Map(Array.from(this._internalState.entries()).reverse().slice(0,32))))}_keys(){return Array.from(this._internalState.keys()).reverse()}_clone(){let a=new e;return a._internalState=new Map(this._internalState),a}}b.TraceStateImpl=e},564:(a,b)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.validateValue=b.validateKey=void 0;let c="[_0-9a-z-*/]",d=`[a-z]${c}{0,255}`,e=`[a-z0-9]${c}{0,240}@[a-z]${c}{0,13}`,f=RegExp(`^(?:${d}|${e})$`),g=/^[ -~]{0,255}[!-~]$/,h=/,|=/;b.validateKey=function(a){return f.test(a)},b.validateValue=function(a){return g.test(a)&&!h.test(a)}},98:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.createTraceState=void 0;let d=c(325);b.createTraceState=function(a){return new d.TraceStateImpl(a)}},476:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.INVALID_SPAN_CONTEXT=b.INVALID_TRACEID=b.INVALID_SPANID=void 0;let d=c(475);b.INVALID_SPANID="0000000000000000",b.INVALID_TRACEID="00000000000000000000000000000000",b.INVALID_SPAN_CONTEXT={traceId:b.INVALID_TRACEID,spanId:b.INVALID_SPANID,traceFlags:d.TraceFlags.NONE}},357:(a,b)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.SpanKind=void 0,function(a){a[a.INTERNAL=0]="INTERNAL",a[a.SERVER=1]="SERVER",a[a.CLIENT=2]="CLIENT",a[a.PRODUCER=3]="PRODUCER",a[a.CONSUMER=4]="CONSUMER"}(b.SpanKind||(b.SpanKind={}))},139:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.wrapSpanContext=b.isSpanContextValid=b.isValidSpanId=b.isValidTraceId=void 0;let d=c(476),e=c(403),f=/^([0-9a-f]{32})$/i,g=/^[0-9a-f]{16}$/i;function h(a){return f.test(a)&&a!==d.INVALID_TRACEID}function i(a){return g.test(a)&&a!==d.INVALID_SPANID}b.isValidTraceId=h,b.isValidSpanId=i,b.isSpanContextValid=function(a){return h(a.traceId)&&i(a.spanId)},b.wrapSpanContext=function(a){return new e.NonRecordingSpan(a)}},847:(a,b)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.SpanStatusCode=void 0,function(a){a[a.UNSET=0]="UNSET",a[a.OK=1]="OK",a[a.ERROR=2]="ERROR"}(b.SpanStatusCode||(b.SpanStatusCode={}))},475:(a,b)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.TraceFlags=void 0,function(a){a[a.NONE=0]="NONE",a[a.SAMPLED=1]="SAMPLED"}(b.TraceFlags||(b.TraceFlags={}))},521:(a,b)=>{Object.defineProperty(b,"__esModule",{value:!0}),b.VERSION=void 0,b.VERSION="1.6.0"}},d={};function e(a){var b=d[a];if(void 0!==b)return b.exports;var f=d[a]={exports:{}},g=!0;try{c[a].call(f.exports,f,f.exports,e),g=!1}finally{g&&delete d[a]}return f.exports}e.ab="/ROOT/school-management-system/node_modules/.pnpm/next@15.5.2_@babel+core@7.2_1d9756f0f263ecf6e82055b1d95a339c/node_modules/next/dist/compiled/@opentelemetry/api/";var f={};(()=>{Object.defineProperty(f,"__esModule",{value:!0}),f.trace=f.propagation=f.metrics=f.diag=f.context=f.INVALID_SPAN_CONTEXT=f.INVALID_TRACEID=f.INVALID_SPANID=f.isValidSpanId=f.isValidTraceId=f.isSpanContextValid=f.createTraceState=f.TraceFlags=f.SpanStatusCode=f.SpanKind=f.SamplingDecision=f.ProxyTracerProvider=f.ProxyTracer=f.defaultTextMapSetter=f.defaultTextMapGetter=f.ValueType=f.createNoopMeter=f.DiagLogLevel=f.DiagConsoleLogger=f.ROOT_CONTEXT=f.createContextKey=f.baggageEntryMetadataFromString=void 0;var a=e(369);Object.defineProperty(f,"baggageEntryMetadataFromString",{enumerable:!0,get:function(){return a.baggageEntryMetadataFromString}});var b=e(780);Object.defineProperty(f,"createContextKey",{enumerable:!0,get:function(){return b.createContextKey}}),Object.defineProperty(f,"ROOT_CONTEXT",{enumerable:!0,get:function(){return b.ROOT_CONTEXT}});var c=e(972);Object.defineProperty(f,"DiagConsoleLogger",{enumerable:!0,get:function(){return c.DiagConsoleLogger}});var d=e(957);Object.defineProperty(f,"DiagLogLevel",{enumerable:!0,get:function(){return d.DiagLogLevel}});var g=e(102);Object.defineProperty(f,"createNoopMeter",{enumerable:!0,get:function(){return g.createNoopMeter}});var h=e(901);Object.defineProperty(f,"ValueType",{enumerable:!0,get:function(){return h.ValueType}});var i=e(194);Object.defineProperty(f,"defaultTextMapGetter",{enumerable:!0,get:function(){return i.defaultTextMapGetter}}),Object.defineProperty(f,"defaultTextMapSetter",{enumerable:!0,get:function(){return i.defaultTextMapSetter}});var j=e(125);Object.defineProperty(f,"ProxyTracer",{enumerable:!0,get:function(){return j.ProxyTracer}});var k=e(846);Object.defineProperty(f,"ProxyTracerProvider",{enumerable:!0,get:function(){return k.ProxyTracerProvider}});var l=e(996);Object.defineProperty(f,"SamplingDecision",{enumerable:!0,get:function(){return l.SamplingDecision}});var m=e(357);Object.defineProperty(f,"SpanKind",{enumerable:!0,get:function(){return m.SpanKind}});var n=e(847);Object.defineProperty(f,"SpanStatusCode",{enumerable:!0,get:function(){return n.SpanStatusCode}});var o=e(475);Object.defineProperty(f,"TraceFlags",{enumerable:!0,get:function(){return o.TraceFlags}});var p=e(98);Object.defineProperty(f,"createTraceState",{enumerable:!0,get:function(){return p.createTraceState}});var q=e(139);Object.defineProperty(f,"isSpanContextValid",{enumerable:!0,get:function(){return q.isSpanContextValid}}),Object.defineProperty(f,"isValidTraceId",{enumerable:!0,get:function(){return q.isValidTraceId}}),Object.defineProperty(f,"isValidSpanId",{enumerable:!0,get:function(){return q.isValidSpanId}});var r=e(476);Object.defineProperty(f,"INVALID_SPANID",{enumerable:!0,get:function(){return r.INVALID_SPANID}}),Object.defineProperty(f,"INVALID_TRACEID",{enumerable:!0,get:function(){return r.INVALID_TRACEID}}),Object.defineProperty(f,"INVALID_SPAN_CONTEXT",{enumerable:!0,get:function(){return r.INVALID_SPAN_CONTEXT}});let s=e(67);Object.defineProperty(f,"context",{enumerable:!0,get:function(){return s.context}});let t=e(506);Object.defineProperty(f,"diag",{enumerable:!0,get:function(){return t.diag}});let u=e(886);Object.defineProperty(f,"metrics",{enumerable:!0,get:function(){return u.metrics}});let v=e(939);Object.defineProperty(f,"propagation",{enumerable:!0,get:function(){return v.propagation}});let w=e(845);Object.defineProperty(f,"trace",{enumerable:!0,get:function(){return w.trace}}),f.default={context:s.context,diag:t.diag,metrics:u.metrics,propagation:v.propagation,trace:w.trace}})(),b.exports=f})()},741,98468,a=>{"use strict";let b;a.s(["SpanKind",()=>u,"SpanStatusCode",()=>t,"getTracer",()=>D],741),a.s(["AppRenderSpan",()=>i,"BaseServerSpan",()=>c,"LogSpanAllowList",()=>p,"NextNodeServerSpan",()=>f,"NextVanillaSpanAllowlist",()=>o,"NodeSpan",()=>k,"ResolveMetadataSpan",()=>m],98468);var c=function(a){return a.handleRequest="BaseServer.handleRequest",a.run="BaseServer.run",a.pipe="BaseServer.pipe",a.getStaticHTML="BaseServer.getStaticHTML",a.render="BaseServer.render",a.renderToResponseWithComponents="BaseServer.renderToResponseWithComponents",a.renderToResponse="BaseServer.renderToResponse",a.renderToHTML="BaseServer.renderToHTML",a.renderError="BaseServer.renderError",a.renderErrorToResponse="BaseServer.renderErrorToResponse",a.renderErrorToHTML="BaseServer.renderErrorToHTML",a.render404="BaseServer.render404",a}(c||{}),d=function(a){return a.loadDefaultErrorComponents="LoadComponents.loadDefaultErrorComponents",a.loadComponents="LoadComponents.loadComponents",a}(d||{}),e=function(a){return a.getRequestHandler="NextServer.getRequestHandler",a.getServer="NextServer.getServer",a.getServerRequestHandler="NextServer.getServerRequestHandler",a.createServer="createServer.createServer",a}(e||{}),f=function(a){return a.compression="NextNodeServer.compression",a.getBuildId="NextNodeServer.getBuildId",a.createComponentTree="NextNodeServer.createComponentTree",a.clientComponentLoading="NextNodeServer.clientComponentLoading",a.getLayoutOrPageModule="NextNodeServer.getLayoutOrPageModule",a.generateStaticRoutes="NextNodeServer.generateStaticRoutes",a.generateFsStaticRoutes="NextNodeServer.generateFsStaticRoutes",a.generatePublicRoutes="NextNodeServer.generatePublicRoutes",a.generateImageRoutes="NextNodeServer.generateImageRoutes.route",a.sendRenderResult="NextNodeServer.sendRenderResult",a.proxyRequest="NextNodeServer.proxyRequest",a.runApi="NextNodeServer.runApi",a.render="NextNodeServer.render",a.renderHTML="NextNodeServer.renderHTML",a.imageOptimizer="NextNodeServer.imageOptimizer",a.getPagePath="NextNodeServer.getPagePath",a.getRoutesManifest="NextNodeServer.getRoutesManifest",a.findPageComponents="NextNodeServer.findPageComponents",a.getFontManifest="NextNodeServer.getFontManifest",a.getServerComponentManifest="NextNodeServer.getServerComponentManifest",a.getRequestHandler="NextNodeServer.getRequestHandler",a.renderToHTML="NextNodeServer.renderToHTML",a.renderError="NextNodeServer.renderError",a.renderErrorToHTML="NextNodeServer.renderErrorToHTML",a.render404="NextNodeServer.render404",a.startResponse="NextNodeServer.startResponse",a.route="route",a.onProxyReq="onProxyReq",a.apiResolver="apiResolver",a.internalFetch="internalFetch",a}(f||{}),g=function(a){return a.startServer="startServer.startServer",a}(g||{}),h=function(a){return a.getServerSideProps="Render.getServerSideProps",a.getStaticProps="Render.getStaticProps",a.renderToString="Render.renderToString",a.renderDocument="Render.renderDocument",a.createBodyResult="Render.createBodyResult",a}(h||{}),i=function(a){return a.renderToString="AppRender.renderToString",a.renderToReadableStream="AppRender.renderToReadableStream",a.getBodyResult="AppRender.getBodyResult",a.fetch="AppRender.fetch",a}(i||{}),j=function(a){return a.executeRoute="Router.executeRoute",a}(j||{}),k=function(a){return a.runHandler="Node.runHandler",a}(k||{}),l=function(a){return a.runHandler="AppRouteRouteHandlers.runHandler",a}(l||{}),m=function(a){return a.generateMetadata="ResolveMetadata.generateMetadata",a.generateViewport="ResolveMetadata.generateViewport",a}(m||{}),n=function(a){return a.execute="Middleware.execute",a}(n||{});let o=["Middleware.execute","BaseServer.handleRequest","Render.getServerSideProps","Render.getStaticProps","AppRender.fetch","AppRender.getBodyResult","Render.renderDocument","Node.runHandler","AppRouteRouteHandlers.runHandler","ResolveMetadata.generateMetadata","ResolveMetadata.generateViewport","NextNodeServer.createComponentTree","NextNodeServer.findPageComponents","NextNodeServer.getLayoutOrPageModule","NextNodeServer.startResponse","NextNodeServer.clientComponentLoading"],p=["NextNodeServer.findPageComponents","NextNodeServer.createComponentTree","NextNodeServer.clientComponentLoading"];try{b=a.r(70406)}catch(c){b=a.r(17868)}let{context:q,propagation:r,trace:s,SpanStatusCode:t,SpanKind:u,ROOT_CONTEXT:v}=b;class w extends Error{constructor(a,b){super(),this.bubble=a,this.result=b}}let x=(a,b)=>{(function(a){return"object"==typeof a&&null!==a&&a instanceof w})(b)&&b.bubble?a.setAttribute("next.bubble",!0):(b&&(a.recordException(b),a.setAttribute("error.type",b.name)),a.setStatus({code:t.ERROR,message:null==b?void 0:b.message})),a.end()},y=new Map,z=b.createContextKey("next.rootSpanId"),A=0,B={set(a,b,c){a.push({key:b,value:c})}};class C{getTracerInstance(){return s.getTracer("next.js","0.0.1")}getContext(){return q}getTracePropagationData(){let a=q.active(),b=[];return r.inject(a,b,B),b}getActiveScopeSpan(){return s.getSpan(null==q?void 0:q.active())}withPropagatedContext(a,b,c){let d=q.active();if(s.getSpanContext(d))return b();let e=r.extract(d,a,c);return q.with(e,b)}trace(...a){var b;let[c,d,e]=a,{fn:f,options:g}="function"==typeof d?{fn:d,options:{}}:{fn:e,options:{...d}},h=g.spanName??c;if(!o.includes(c)&&"1"!==process.env.NEXT_OTEL_VERBOSE||g.hideSpan)return f();let i=this.getSpanContext((null==g?void 0:g.parentSpan)??this.getActiveScopeSpan()),j=!1;i?(null==(b=s.getSpanContext(i))?void 0:b.isRemote)&&(j=!0):(i=(null==q?void 0:q.active())??v,j=!0);let k=A++;return g.attributes={"next.span_name":h,"next.span_type":c,...g.attributes},q.with(i.setValue(z,k),()=>this.getTracerInstance().startActiveSpan(h,g,a=>{let b="performance"in globalThis&&"measure"in performance?globalThis.performance.now():void 0,d=()=>{y.delete(k),b&&process.env.NEXT_OTEL_PERFORMANCE_PREFIX&&p.includes(c||"")&&performance.measure(`${process.env.NEXT_OTEL_PERFORMANCE_PREFIX}:next-${(c.split(".").pop()||"").replace(/[A-Z]/g,a=>"-"+a.toLowerCase())}`,{start:b,end:performance.now()})};j&&y.set(k,new Map(Object.entries(g.attributes??{})));try{if(f.length>1)return f(a,b=>x(a,b));let b=f(a);if(null!==b&&"object"==typeof b&&"then"in b&&"function"==typeof b.then)return b.then(b=>(a.end(),b)).catch(b=>{throw x(a,b),b}).finally(d);return a.end(),d(),b}catch(b){throw x(a,b),d(),b}}))}wrap(...a){let b=this,[c,d,e]=3===a.length?a:[a[0],{},a[1]];return o.includes(c)||"1"===process.env.NEXT_OTEL_VERBOSE?function(){let a=d;"function"==typeof a&&"function"==typeof e&&(a=a.apply(this,arguments));let f=arguments.length-1,g=arguments[f];if("function"!=typeof g)return b.trace(c,a,()=>e.apply(this,arguments));{let d=b.getContext().bind(q.active(),g);return b.trace(c,a,(a,b)=>(arguments[f]=function(a){return null==b||b(a),d.apply(this,arguments)},e.apply(this,arguments)))}}:e}startSpan(...a){let[b,c]=a,d=this.getSpanContext((null==c?void 0:c.parentSpan)??this.getActiveScopeSpan());return this.getTracerInstance().startSpan(b,c,d)}getSpanContext(a){return a?s.setSpan(q.active(),a):void 0}getRootSpanAttributes(){let a=q.active().getValue(z);return y.get(a)}setRootSpanAttribute(a,b){let c=q.active().getValue(z),d=y.get(c);d&&d.set(a,b)}}let D=(()=>{let a=new C;return()=>a})()},72999,a=>{"use strict";a.s(["DetachedPromise",()=>b]);class b{constructor(){let a,b;this.promise=new Promise((c,d)=>{a=c,b=d}),this.resolve=a,this.reject=b}}},44371,37984,a=>{"use strict";a.s(["chainStreams",()=>e,"streamFromBuffer",()=>g,"streamFromString",()=>f,"streamToBuffer",()=>h,"streamToString",()=>i],44371),a.i(741),a.i(98468),a.i(72999),a.i(33634),a.s(["ENCODED_TAGS",()=>b],37984);let b={OPENING:{HTML:new Uint8Array([60,104,116,109,108]),BODY:new Uint8Array([60,98,111,100,121])},CLOSED:{HEAD:new Uint8Array([60,47,104,101,97,100,62]),BODY:new Uint8Array([60,47,98,111,100,121,62]),HTML:new Uint8Array([60,47,104,116,109,108,62]),BODY_AND_HTML:new Uint8Array([60,47,98,111,100,121,62,60,47,104,116,109,108,62])},META:{ICON_MARK:new Uint8Array([60,109,101,116,97,32,110,97,109,101,61,34,194,171,110,120,116,45,105,99,111,110,194,187,34])}};function c(){}let d=new TextEncoder;function e(...a){if(0===a.length)return new ReadableStream({start(a){a.close()}});if(1===a.length)return a[0];let{readable:b,writable:d}=new TransformStream,f=a[0].pipeTo(d,{preventClose:!0}),g=1;for(;g<a.length-1;g++){let b=a[g];f=f.then(()=>b.pipeTo(d,{preventClose:!0}))}let h=a[g];return(f=f.then(()=>h.pipeTo(d))).catch(c),b}function f(a){return new ReadableStream({start(b){b.enqueue(d.encode(a)),b.close()}})}function g(a){return new ReadableStream({start(b){b.enqueue(a),b.close()}})}async function h(a){let b=a.getReader(),c=[];for(;;){let{done:a,value:d}=await b.read();if(a)break;c.push(d)}return Buffer.concat(c)}async function i(a,b){let c=new TextDecoder("utf-8",{fatal:!0}),d="";for await(let e of a){if(null==b?void 0:b.aborted)return d;d+=c.decode(e,{stream:!0})}return d+c.decode()}},26974,a=>{"use strict";a.s(["NEXT_REQUEST_META",()=>b,"getRequestMeta",()=>c]);let b=Symbol.for("NextInternalRequestMeta");function c(a,c){let d=a[b]||{};return"string"==typeof c?d[c]:d}},20463,a=>{"use strict";a.s(["CACHE_ONE_YEAR",()=>j,"HTML_CONTENT_TYPE_HEADER",()=>b,"INFINITE_CACHE",()=>k,"NEXT_CACHE_TAGS_HEADER",()=>g,"NEXT_CACHE_TAG_MAX_ITEMS",()=>h,"NEXT_CACHE_TAG_MAX_LENGTH",()=>i,"NEXT_INTERCEPTION_MARKER_PREFIX",()=>d,"NEXT_QUERY_PARAM_PREFIX",()=>c,"PRERENDER_REVALIDATE_HEADER",()=>e,"PRERENDER_REVALIDATE_ONLY_GENERATED_HEADER",()=>f]);let b="text/html; charset=utf-8",c="nxtP",d="nxtI",e="x-prerender-revalidate",f="x-prerender-revalidate-if-generated",g="x-next-cache-tags",h=128,i=256,j=31536e3,k=0xfffffffe,l={shared:"shared",reactServerComponents:"rsc",serverSideRendering:"ssr",actionBrowser:"action-browser",apiNode:"api-node",apiEdge:"api-edge",middleware:"middleware",instrument:"instrument",edgeAsset:"edge-asset",appPagesBrowser:"app-pages-browser",pagesDirBrowser:"pages-dir-browser",pagesDirEdge:"pages-dir-edge",pagesDirNode:"pages-dir-node"};({...l,GROUP:{builtinReact:[l.reactServerComponents,l.actionBrowser],serverOnly:[l.reactServerComponents,l.actionBrowser,l.instrument,l.middleware],neutralTarget:[l.apiNode,l.apiEdge],clientOnly:[l.serverSideRendering,l.appPagesBrowser],bundled:[l.reactServerComponents,l.actionBrowser,l.serverSideRendering,l.appPagesBrowser,l.shared,l.instrument,l.middleware],appPages:[l.reactServerComponents,l.serverSideRendering,l.appPagesBrowser,l.actionBrowser]}})},11055,53841,a=>{"use strict";function b(a){let b=new Headers;for(let[c,d]of Object.entries(a))for(let a of Array.isArray(d)?d:[d])void 0!==a&&("number"==typeof a&&(a=a.toString()),b.append(c,a));return b}function c(a){var b,c,d,e,f,g=[],h=0;function i(){for(;h<a.length&&/\s/.test(a.charAt(h));)h+=1;return h<a.length}for(;h<a.length;){for(b=h,f=!1;i();)if(","===(c=a.charAt(h))){for(d=h,h+=1,i(),e=h;h<a.length&&"="!==(c=a.charAt(h))&&";"!==c&&","!==c;)h+=1;h<a.length&&"="===a.charAt(h)?(f=!0,h=e,g.push(a.substring(b,d)),b=h):h=d+1}else h+=1;(!f||h>=a.length)&&g.push(a.substring(b,a.length))}return g}function d(a){let b={},d=[];if(a)for(let[e,f]of a.entries())"set-cookie"===e.toLowerCase()?(d.push(...c(f)),b[e]=1===d.length?d[0]:d):b[e]=f;return b}function e(a){try{return String(new URL(String(a)))}catch(b){throw Object.defineProperty(Error(`URL is malformed "${String(a)}". Please use only absolute URLs - https://nextjs.org/docs/messages/middleware-relative-urls`,{cause:b}),"__NEXT_ERROR_CODE",{value:"E61",enumerable:!1,configurable:!0})}}function f(a,b,c){if(a)for(let f of(c&&(c=c.toLowerCase()),a)){var d,e;if(b===(null==(d=f.domain)?void 0:d.split(":",1)[0].toLowerCase())||c===f.defaultLocale.toLowerCase()||(null==(e=f.locales)?void 0:e.some(a=>a.toLowerCase()===c)))return f}}a.s(["fromNodeOutgoingHttpHeaders",()=>b,"splitCookiesString",()=>c,"toNodeOutgoingHttpHeaders",()=>d,"validateURL",()=>e],11055),a.i(20463),a.s(["detectDomainLocale",()=>f],53841)},51096,a=>{"use strict";function b(a){return a.replace(/\/$/,"")||"/"}a.s(["removeTrailingSlash",()=>b])},53054,a=>{"use strict";function b(a){let b=a.indexOf("#"),c=a.indexOf("?"),d=c>-1&&(b<0||c<b);return d||b>-1?{pathname:a.substring(0,d?c:b),query:d?a.substring(c,b>-1?b:void 0):"",hash:b>-1?a.slice(b):""}:{pathname:a,query:"",hash:""}}a.s(["parsePath",()=>b])},27675,57647,a=>{"use strict";a.s(["addPathPrefix",()=>c],27675);var b=a.i(53054);function c(a,c){if(!a.startsWith("/")||!c)return a;let{pathname:d,query:e,hash:f}=(0,b.parsePath)(a);return""+c+d+e+f}function d(a,c){if(!a.startsWith("/")||!c)return a;let{pathname:d,query:e,hash:f}=(0,b.parsePath)(a);return""+d+c+e+f}a.s(["addPathSuffix",()=>d],57647)},1518,a=>{"use strict";a.s(["pathHasPrefix",()=>c]);var b=a.i(53054);function c(a,c){if("string"!=typeof a)return!1;let{pathname:d}=(0,b.parsePath)(a);return d===c||d.startsWith(c+"/")}},458,12323,91548,a=>{"use strict";a.s(["formatNextPathnameInfo",()=>f],458);var b=a.i(51096),c=a.i(27675),d=a.i(57647),e=a.i(1518);function f(a){let f=function(a,b,d,f){if(!b||b===d)return a;let g=a.toLowerCase();return!f&&((0,e.pathHasPrefix)(g,"/api")||(0,e.pathHasPrefix)(g,"/"+b.toLowerCase()))?a:(0,c.addPathPrefix)(a,"/"+b)}(a.pathname,a.locale,a.buildId?void 0:a.defaultLocale,a.ignorePrefix);return(a.buildId||!a.trailingSlash)&&(f=(0,b.removeTrailingSlash)(f)),a.buildId&&(f=(0,d.addPathSuffix)((0,c.addPathPrefix)(f,"/_next/data/"+a.buildId),"/"===a.pathname?"index.json":".json")),f=(0,c.addPathPrefix)(f,a.basePath),!a.buildId&&a.trailingSlash?f.endsWith("/")?f:(0,d.addPathSuffix)(f,"/"):(0,b.removeTrailingSlash)(f)}function g(a,b){let c;if((null==b?void 0:b.host)&&!Array.isArray(b.host))c=b.host.toString().split(":",1)[0];else{if(!a.hostname)return;c=a.hostname}return c.toLowerCase()}a.s(["getHostname",()=>g],12323),a.s(["normalizeLocalePath",()=>i],91548);let h=new WeakMap;function i(a,b){let c;if(!b)return{pathname:a};let d=h.get(b);d||(d=b.map(a=>a.toLowerCase()),h.set(b,d));let e=a.split("/",2);if(!e[1])return{pathname:a};let f=e[1].toLowerCase(),g=d.indexOf(f);return g<0?{pathname:a}:(c=b[g],{pathname:a=a.slice(c.length+1)||"/",detectedLocale:c})}},48056,a=>{"use strict";a.s(["removePathPrefix",()=>c]);var b=a.i(1518);function c(a,c){if(!(0,b.pathHasPrefix)(a,c))return a;let d=a.slice(c.length);return d.startsWith("/")?d:"/"+d}},90111,26409,45056,a=>{"use strict";a.s(["isAbortError",()=>G,"pipeToNodeResponse",()=>H],90111),a.s(["NextRequestAdapter",()=>z,"ResponseAbortedName",()=>v,"createAbortController",()=>x,"signalFromNodeResponse",()=>y],45056);var b=a.i(26974),c=a.i(11055),d=a.i(53841),e=a.i(458),f=a.i(12323),g=a.i(91548),h=a.i(48056),i=a.i(1518);let j=/(?!^https?:\/\/)(127(?:\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)){3}|\[::1\]|localhost)/;function k(a,b){return new URL(String(a).replace(j,"localhost"),b&&String(b).replace(j,"localhost"))}let l=Symbol("NextURLInternal");class m{constructor(a,b,c){let d,e;"object"==typeof b&&"pathname"in b||"string"==typeof b?(d=b,e=c||{}):e=c||b||{},this[l]={url:k(a,d??e.base),options:e,basePath:""},this.analyze()}analyze(){var a,b,c,e,j;let k=function(a,b){var c,d;let{basePath:e,i18n:f,trailingSlash:j}=null!=(c=b.nextConfig)?c:{},k={pathname:a,trailingSlash:"/"!==a?a.endsWith("/"):j};e&&(0,i.pathHasPrefix)(k.pathname,e)&&(k.pathname=(0,h.removePathPrefix)(k.pathname,e),k.basePath=e);let l=k.pathname;if(k.pathname.startsWith("/_next/data/")&&k.pathname.endsWith(".json")){let a=k.pathname.replace(/^\/_next\/data\//,"").replace(/\.json$/,"").split("/");k.buildId=a[0],l="index"!==a[1]?"/"+a.slice(1).join("/"):"/",!0===b.parseData&&(k.pathname=l)}if(f){let a=b.i18nProvider?b.i18nProvider.analyze(k.pathname):(0,g.normalizeLocalePath)(k.pathname,f.locales);k.locale=a.detectedLocale,k.pathname=null!=(d=a.pathname)?d:k.pathname,!a.detectedLocale&&k.buildId&&(a=b.i18nProvider?b.i18nProvider.analyze(l):(0,g.normalizeLocalePath)(l,f.locales)).detectedLocale&&(k.locale=a.detectedLocale)}return k}(this[l].url.pathname,{nextConfig:this[l].options.nextConfig,parseData:!0,i18nProvider:this[l].options.i18nProvider}),m=(0,f.getHostname)(this[l].url,this[l].options.headers);this[l].domainLocale=this[l].options.i18nProvider?this[l].options.i18nProvider.detectDomainLocale(m):(0,d.detectDomainLocale)(null==(b=this[l].options.nextConfig)||null==(a=b.i18n)?void 0:a.domains,m);let n=(null==(c=this[l].domainLocale)?void 0:c.defaultLocale)||(null==(j=this[l].options.nextConfig)||null==(e=j.i18n)?void 0:e.defaultLocale);this[l].url.pathname=k.pathname,this[l].defaultLocale=n,this[l].basePath=k.basePath??"",this[l].buildId=k.buildId,this[l].locale=k.locale??n,this[l].trailingSlash=k.trailingSlash}formatPathname(){return(0,e.formatNextPathnameInfo)({basePath:this[l].basePath,buildId:this[l].buildId,defaultLocale:this[l].options.forceLocale?void 0:this[l].defaultLocale,locale:this[l].locale,pathname:this[l].url.pathname,trailingSlash:this[l].trailingSlash})}formatSearch(){return this[l].url.search}get buildId(){return this[l].buildId}set buildId(a){this[l].buildId=a}get locale(){return this[l].locale??""}set locale(a){var b,c;if(!this[l].locale||!(null==(c=this[l].options.nextConfig)||null==(b=c.i18n)?void 0:b.locales.includes(a)))throw Object.defineProperty(TypeError(`The NextURL configuration includes no locale "${a}"`),"__NEXT_ERROR_CODE",{value:"E597",enumerable:!1,configurable:!0});this[l].locale=a}get defaultLocale(){return this[l].defaultLocale}get domainLocale(){return this[l].domainLocale}get searchParams(){return this[l].url.searchParams}get host(){return this[l].url.host}set host(a){this[l].url.host=a}get hostname(){return this[l].url.hostname}set hostname(a){this[l].url.hostname=a}get port(){return this[l].url.port}set port(a){this[l].url.port=a}get protocol(){return this[l].url.protocol}set protocol(a){this[l].url.protocol=a}get href(){let a=this.formatPathname(),b=this.formatSearch();return`${this.protocol}//${this.host}${a}${b}${this.hash}`}set href(a){this[l].url=k(a),this.analyze()}get origin(){return this[l].url.origin}get pathname(){return this[l].url.pathname}set pathname(a){this[l].url.pathname=a}get hash(){return this[l].url.hash}set hash(a){this[l].url.hash=a}get search(){return this[l].url.search}set search(a){this[l].url.search=a}get password(){return this[l].url.password}set password(a){this[l].url.password=a}get username(){return this[l].url.username}set username(a){this[l].url.username=a}get basePath(){return this[l].basePath}set basePath(a){this[l].basePath=a.startsWith("/")?a:`/${a}`}toString(){return this.href}toJSON(){return this.href}[Symbol.for("edge-runtime.inspect.custom")](){return{href:this.href,origin:this.origin,protocol:this.protocol,username:this.username,password:this.password,host:this.host,hostname:this.hostname,port:this.port,pathname:this.pathname,search:this.search,searchParams:this.searchParams,hash:this.hash}}clone(){return new m(String(this),this[l].options)}}class n extends Error{constructor(){super(`The request.page has been deprecated in favour of \`URLPattern\`.
  Read more: https://nextjs.org/docs/messages/middleware-request-page
  `)}}class o extends Error{constructor(){super(`The request.ua has been removed in favour of \`userAgent\` function.
  Read more: https://nextjs.org/docs/messages/middleware-parse-user-agent
  `)}}var p=a.i(90019);let q=Symbol("internal request");class r extends Request{constructor(a,b={}){let d="string"!=typeof a&&"url"in a?a.url:String(a);(0,c.validateURL)(d),b.body&&"half"!==b.duplex&&(b.duplex="half"),a instanceof Request?super(a,b):super(d,b);let e=new m(d,{headers:(0,c.toNodeOutgoingHttpHeaders)(this.headers),nextConfig:b.nextConfig});this[q]={cookies:new p.RequestCookies(this.headers),nextUrl:e,url:e.toString()}}[Symbol.for("edge-runtime.inspect.custom")](){return{cookies:this.cookies,nextUrl:this.nextUrl,url:this.url,bodyUsed:this.bodyUsed,cache:this.cache,credentials:this.credentials,destination:this.destination,headers:Object.fromEntries(this.headers),integrity:this.integrity,keepalive:this.keepalive,method:this.method,mode:this.mode,redirect:this.redirect,referrer:this.referrer,referrerPolicy:this.referrerPolicy,signal:this.signal}}get cookies(){return this[q].cookies}get nextUrl(){return this[q].nextUrl}get page(){throw new n}get ua(){throw new o}get url(){return this[q].url}}a.s(["isNodeNextRequest",()=>t,"isNodeNextResponse",()=>u,"isWebNextRequest",()=>s],26409);let s=a=>!1,t=a=>!0,u=a=>!0,v="ResponseAborted";class w extends Error{constructor(...a){super(...a),this.name=v}}function x(a){let b=new AbortController;return a.once("close",()=>{a.writableFinished||b.abort(new w)}),b}function y(a){let{errored:b,destroyed:c}=a;if(b||c)return AbortSignal.abort(b??new w);let{signal:d}=x(a);return d}class z{static fromBaseNextRequest(a,b){if(t(a))return z.fromNodeNextRequest(a,b);throw Object.defineProperty(Error("Invariant: Unsupported NextRequest type"),"__NEXT_ERROR_CODE",{value:"E345",enumerable:!1,configurable:!0})}static fromNodeNextRequest(a,d){let e,f=null;if("GET"!==a.method&&"HEAD"!==a.method&&a.body&&(f=a.body),a.url.startsWith("http"))e=new URL(a.url);else{let c=(0,b.getRequestMeta)(a,"initURL");e=c&&c.startsWith("http")?new URL(a.url,c):new URL(a.url,"http://n")}return new r(e,{method:a.method,headers:(0,c.fromNodeOutgoingHttpHeaders)(a.headers),duplex:"half",signal:d,...d.aborted?{}:{body:f}})}static fromWebNextRequest(a){let b=null;return"GET"!==a.method&&"HEAD"!==a.method&&(b=a.body),new r(a.url,{method:a.method,headers:(0,c.fromNodeOutgoingHttpHeaders)(a.headers),duplex:"half",signal:a.request.signal,...a.request.signal.aborted?{}:{body:b}})}}var A=a.i(72999),B=a.i(741),C=a.i(98468);let D=0,E=0,F=0;function G(a){return(null==a?void 0:a.name)==="AbortError"||(null==a?void 0:a.name)===v}async function H(a,b,c){try{let{errored:d,destroyed:e}=b;if(d||e)return;let f=x(b),g=function(a,b){let c=!1,d=new A.DetachedPromise;function e(){d.resolve()}a.on("drain",e),a.once("close",()=>{a.off("drain",e),d.resolve()});let f=new A.DetachedPromise;return a.once("finish",()=>{f.resolve()}),new WritableStream({write:async b=>{if(!c){if(c=!0,"performance"in globalThis&&process.env.NEXT_OTEL_PERFORMANCE_PREFIX){let a=function(a={}){let b=0===D?void 0:{clientComponentLoadStart:D,clientComponentLoadTimes:E,clientComponentLoadCount:F};return a.reset&&(D=0,E=0,F=0),b}();a&&performance.measure(`${process.env.NEXT_OTEL_PERFORMANCE_PREFIX}:next-client-component-loading`,{start:a.clientComponentLoadStart,end:a.clientComponentLoadStart+a.clientComponentLoadTimes})}a.flushHeaders(),(0,B.getTracer)().trace(C.NextNodeServerSpan.startResponse,{spanName:"start response"},()=>void 0)}try{let c=a.write(b);"flush"in a&&"function"==typeof a.flush&&a.flush(),c||(await d.promise,d=new A.DetachedPromise)}catch(b){throw a.end(),Object.defineProperty(Error("failed to write chunk to response",{cause:b}),"__NEXT_ERROR_CODE",{value:"E321",enumerable:!1,configurable:!0})}},abort:b=>{a.writableFinished||a.destroy(b)},close:async()=>{if(b&&await b,!a.writableFinished)return a.end(),f.promise}})}(b,c);await a.pipeTo(g,{signal:f.signal})}catch(a){if(G(a))return;throw Object.defineProperty(Error("failed to pipe response",{cause:a}),"__NEXT_ERROR_CODE",{value:"E180",enumerable:!1,configurable:!0})}}},60358,a=>{"use strict";a.s(["RedirectStatusCode",()=>b]);var b=function(a){return a[a.SeeOther=303]="SeeOther",a[a.TemporaryRedirect=307]="TemporaryRedirect",a[a.PermanentRedirect=308]="PermanentRedirect",a}({})},8469,a=>{"use strict";a.s(["Batcher",()=>c]);var b=a.i(72999);class c{constructor(a,b=a=>a()){this.cacheKeyFn=a,this.schedulerFn=b,this.pending=new Map}static create(a){return new c(null==a?void 0:a.cacheKeyFn,null==a?void 0:a.schedulerFn)}async batch(a,c){let d=this.cacheKeyFn?await this.cacheKeyFn(a):a;if(null===d)return c(d,Promise.resolve);let e=this.pending.get(d);if(e)return e;let{promise:f,resolve:g,reject:h}=new b.DetachedPromise;return this.pending.set(d,f),this.schedulerFn(async()=>{try{let a=await c(d,g);g(a)}catch(a){h(a)}finally{this.pending.delete(d)}}),f}}},53055,a=>{"use strict";a.s(["CachedRouteKind",()=>b,"IncrementalCacheKind",()=>c]);var b=function(a){return a.APP_PAGE="APP_PAGE",a.APP_ROUTE="APP_ROUTE",a.PAGES="PAGES",a.FETCH="FETCH",a.REDIRECT="REDIRECT",a.IMAGE="IMAGE",a}({}),c=function(a){return a.APP_PAGE="APP_PAGE",a.APP_ROUTE="APP_ROUTE",a.PAGES="PAGES",a.FETCH="FETCH",a.IMAGE="IMAGE",a}({})},43731,74019,a=>{"use strict";a.s([],43731),a.i(8469),a.i(33634),a.i(53055),a.s(["default",()=>e],74019);var b=a.i(44371),c=a.i(90111),d=a.i(17551);class e{static #a=this.EMPTY=new e(null,{metadata:{},contentType:null});static fromStatic(a,b){return new e(a,{metadata:{},contentType:b})}constructor(a,{contentType:b,waitUntil:c,metadata:d}){this.response=a,this.contentType=b,this.metadata=d,this.waitUntil=c}assignMetadata(a){Object.assign(this.metadata,a)}get isNull(){return null===this.response}get isDynamic(){return"string"!=typeof this.response}toUnchunkedString(a=!1){if(null===this.response)return"";if("string"!=typeof this.response){if(!a)throw Object.defineProperty(new d.InvariantError("dynamic responses cannot be unchunked. This is a bug in Next.js"),"__NEXT_ERROR_CODE",{value:"E732",enumerable:!1,configurable:!0});return(0,b.streamToString)(this.readable)}return this.response}get readable(){return null===this.response?new ReadableStream({start(a){a.close()}}):"string"==typeof this.response?(0,b.streamFromString)(this.response):Buffer.isBuffer(this.response)?(0,b.streamFromBuffer)(this.response):Array.isArray(this.response)?(0,b.chainStreams)(...this.response):this.response}coerce(){return null===this.response?[]:"string"==typeof this.response?[(0,b.streamFromString)(this.response)]:Array.isArray(this.response)?this.response:Buffer.isBuffer(this.response)?[(0,b.streamFromBuffer)(this.response)]:[this.response]}unshift(a){this.response=this.coerce(),this.response.unshift(a)}push(a){this.response=this.coerce(),this.response.push(a)}async pipeTo(a){try{await this.readable.pipeTo(a,{preventClose:!0}),this.waitUntil&&await this.waitUntil,await a.close()}catch(b){if((0,c.isAbortError)(b))return void await a.abort(b);throw b}}async pipeToNodeResponse(a){await (0,c.pipeToNodeResponse)(this.readable,a,this.waitUntil)}}a.i(95875),a.i(20463)}];

//# sourceMappingURL=6c5cd_next_dist_46e83aa7._.js.map