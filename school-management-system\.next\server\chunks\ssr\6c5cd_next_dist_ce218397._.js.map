{"version": 3, "sources": ["turbopack:///[project]/school-management-system/node_modules/.pnpm/next@15.5.2_@babel+core@7.2_1d9756f0f263ecf6e82055b1d95a339c/node_modules/next/src/server/dynamic-rendering-utils.ts", "turbopack:///[project]/school-management-system/node_modules/.pnpm/next@15.5.2_@babel+core@7.2_1d9756f0f263ecf6e82055b1d95a339c/node_modules/next/src/shared/lib/lazy-dynamic/bailout-to-csr.ts", "turbopack:///[project]/school-management-system/node_modules/.pnpm/next@15.5.2_@babel+core@7.2_1d9756f0f263ecf6e82055b1d95a339c/node_modules/next/src/client/components/hooks-server-context.ts", "turbopack:///[project]/school-management-system/node_modules/.pnpm/next@15.5.2_@babel+core@7.2_1d9756f0f263ecf6e82055b1d95a339c/node_modules/next/src/client/components/static-generation-bailout.ts", "turbopack:///[project]/school-management-system/node_modules/.pnpm/next@15.5.2_@babel+core@7.2_1d9756f0f263ecf6e82055b1d95a339c/node_modules/next/src/lib/framework/boundary-constants.tsx", "turbopack:///[project]/school-management-system/node_modules/.pnpm/next@15.5.2_@babel+core@7.2_1d9756f0f263ecf6e82055b1d95a339c/node_modules/next/src/lib/scheduler.ts", "turbopack:///[project]/school-management-system/node_modules/.pnpm/next@15.5.2_@babel+core@7.2_1d9756f0f263ecf6e82055b1d95a339c/node_modules/next/src/shared/lib/invariant-error.ts", "turbopack:///[project]/school-management-system/node_modules/.pnpm/next@15.5.2_@babel+core@7.2_1d9756f0f263ecf6e82055b1d95a339c/node_modules/next/src/server/app-render/dynamic-rendering.ts"], "sourcesContent": ["export function isHangingPromiseRejectionError(\n  err: unknown\n): err is HangingPromiseRejectionError {\n  if (typeof err !== 'object' || err === null || !('digest' in err)) {\n    return false\n  }\n\n  return err.digest === HANGING_PROMISE_REJECTION\n}\n\nconst HANGING_PROMISE_REJECTION = 'HANGING_PROMISE_REJECTION'\n\nclass HangingPromiseRejectionError extends Error {\n  public readonly digest = HANGING_PROMISE_REJECTION\n\n  constructor(\n    public readonly route: string,\n    public readonly expression: string\n  ) {\n    super(\n      `During prerendering, ${expression} rejects when the prerender is complete. Typically these errors are handled by React but if you move ${expression} to a different context by using \\`setTimeout\\`, \\`after\\`, or similar functions you may observe this error and you should handle it in that context. This occurred at route \"${route}\".`\n    )\n  }\n}\n\ntype AbortListeners = Array<(err: unknown) => void>\nconst abortListenersBySignal = new WeakMap<AbortSignal, AbortListeners>()\n\n/**\n * This function constructs a promise that will never resolve. This is primarily\n * useful for cacheComponents where we use promise resolution timing to determine which\n * parts of a render can be included in a prerender.\n *\n * @internal\n */\nexport function makeHangingPromise<T>(\n  signal: AbortSignal,\n  route: string,\n  expression: string\n): Promise<T> {\n  if (signal.aborted) {\n    return Promise.reject(new HangingPromiseRejectionError(route, expression))\n  } else {\n    const hangingPromise = new Promise<T>((_, reject) => {\n      const boundRejection = reject.bind(\n        null,\n        new HangingPromiseRejectionError(route, expression)\n      )\n      let currentListeners = abortListenersBySignal.get(signal)\n      if (currentListeners) {\n        currentListeners.push(boundRejection)\n      } else {\n        const listeners = [boundRejection]\n        abortListenersBySignal.set(signal, listeners)\n        signal.addEventListener(\n          'abort',\n          () => {\n            for (let i = 0; i < listeners.length; i++) {\n              listeners[i]()\n            }\n          },\n          { once: true }\n        )\n      }\n    })\n    // We are fine if no one actually awaits this promise. We shouldn't consider this an unhandled rejection so\n    // we attach a noop catch handler here to suppress this warning. If you actually await somewhere or construct\n    // your own promise out of it you'll need to ensure you handle the error when it rejects.\n    hangingPromise.catch(ignoreReject)\n    return hangingPromise\n  }\n}\n\nfunction ignoreReject() {}\n\nexport function makeDevtoolsIOAwarePromise<T>(underlying: T): Promise<T> {\n  // in React DevTools if we resolve in a setTimeout we will observe\n  // the promise resolution as something that can suspend a boundary or root.\n  return new Promise<T>((resolve) => {\n    // Must use setTimeout to be considered IO React DevTools. setImmediate will not work.\n    setTimeout(() => {\n      resolve(underlying)\n    }, 0)\n  })\n}\n", "// This has to be a shared module which is shared between client component error boundary and dynamic component\nconst BAILOUT_TO_CSR = 'BAILOUT_TO_CLIENT_SIDE_RENDERING'\n\n/** An error that should be thrown when we want to bail out to client-side rendering. */\nexport class BailoutToCSRError extends Error {\n  public readonly digest = BAILOUT_TO_CSR\n\n  constructor(public readonly reason: string) {\n    super(`Bail out to client-side rendering: ${reason}`)\n  }\n}\n\n/** Checks if a passed argument is an error that is thrown if we want to bail out to client-side rendering. */\nexport function isBailoutToCSRError(err: unknown): err is BailoutToCSRError {\n  if (typeof err !== 'object' || err === null || !('digest' in err)) {\n    return false\n  }\n\n  return err.digest === BAILOUT_TO_CSR\n}\n", "const DYNAMIC_ERROR_CODE = 'DYNAMIC_SERVER_USAGE'\n\nexport class DynamicServerError extends Error {\n  digest: typeof DYNAMIC_ERROR_CODE = DYNAMIC_ERROR_CODE\n\n  constructor(public readonly description: string) {\n    super(`Dynamic server usage: ${description}`)\n  }\n}\n\nexport function isDynamicServerError(err: unknown): err is DynamicServerError {\n  if (\n    typeof err !== 'object' ||\n    err === null ||\n    !('digest' in err) ||\n    typeof err.digest !== 'string'\n  ) {\n    return false\n  }\n\n  return err.digest === DYNAMIC_ERROR_CODE\n}\n", "const NEXT_STATIC_GEN_BAILOUT = 'NEXT_STATIC_GEN_BAILOUT'\n\nexport class StaticGenBailoutError extends Error {\n  public readonly code = NEXT_STATIC_GEN_BAILOUT\n}\n\nexport function isStaticGenBailoutError(\n  error: unknown\n): error is StaticGenBailoutError {\n  if (typeof error !== 'object' || error === null || !('code' in error)) {\n    return false\n  }\n\n  return error.code === NEXT_STATIC_GEN_BAILOUT\n}\n", "export const METADATA_BOUNDARY_NAME = '__next_metadata_boundary__'\nexport const VIEWPORT_BOUNDARY_NAME = '__next_viewport_boundary__'\nexport const OUTLET_BOUNDARY_NAME = '__next_outlet_boundary__'\nexport const ROOT_LAYOUT_BOUNDARY_NAME = '__next_root_layout_boundary__'\n", "export type ScheduledFn<T = void> = () => T | PromiseLike<T>\nexport type SchedulerFn<T = void> = (cb: ScheduledFn<T>) => void\n\n/**\n * Schedules a function to be called on the next tick after the other promises\n * have been resolved.\n *\n * @param cb the function to schedule\n */\nexport const scheduleOnNextTick = (cb: ScheduledFn<void>) => {\n  // We use Promise.resolve().then() here so that the operation is scheduled at\n  // the end of the promise job queue, we then add it to the next process tick\n  // to ensure it's evaluated afterwards.\n  //\n  // This was inspired by the implementation of the DataLoader interface: https://github.com/graphql/dataloader/blob/d336bd15282664e0be4b4a657cb796f09bafbc6b/src/index.js#L213-L255\n  //\n  Promise.resolve().then(() => {\n    if (process.env.NEXT_RUNTIME === 'edge') {\n      setTimeout(cb, 0)\n    } else {\n      process.nextTick(cb)\n    }\n  })\n}\n\n/**\n * Schedules a function to be called using `setImmediate` or `setTimeout` if\n * `setImmediate` is not available (like in the Edge runtime).\n *\n * @param cb the function to schedule\n */\nexport const scheduleImmediate = (cb: ScheduledFn<void>): void => {\n  if (process.env.NEXT_RUNTIME === 'edge') {\n    setTimeout(cb, 0)\n  } else {\n    setImmediate(cb)\n  }\n}\n\n/**\n * returns a promise than resolves in a future task. There is no guarantee that the task it resolves in\n * will be the next task but if you await it you can at least be sure that the current task is over and\n * most usefully that the entire microtask queue of the current task has been emptied.\n */\nexport function atLeastOneTask() {\n  return new Promise<void>((resolve) => scheduleImmediate(resolve))\n}\n\n/**\n * This utility function is extracted to make it easier to find places where we are doing\n * specific timing tricks to try to schedule work after React has rendered. This is especially\n * important at the moment because Next.js uses the edge builds of React which use setTimeout to\n * schedule work when you might expect that something like setImmediate would do the trick.\n *\n * Long term we should switch to the node versions of React rendering when possible and then\n * update this to use setImmediate rather than setTimeout\n */\nexport function waitAtLeastOneReactRenderTask(): Promise<void> {\n  if (process.env.NEXT_RUNTIME === 'edge') {\n    return new Promise((r) => setTimeout(r, 0))\n  } else {\n    return new Promise((r) => setImmediate(r))\n  }\n}\n", "export class InvariantError extends Error {\n  constructor(message: string, options?: ErrorOptions) {\n    super(\n      `Invariant: ${message.endsWith('.') ? message : message + '.'} This is a bug in Next.js.`,\n      options\n    )\n    this.name = 'InvariantError'\n  }\n}\n", "/**\n * The functions provided by this module are used to communicate certain properties\n * about the currently running code so that Next.js can make decisions on how to handle\n * the current execution in different rendering modes such as pre-rendering, resuming, and SSR.\n *\n * Today Next.js treats all code as potentially static. Certain APIs may only make sense when dynamically rendering.\n * Traditionally this meant deopting the entire render to dynamic however with PPR we can now deopt parts\n * of a React tree as dynamic while still keeping other parts static. There are really two different kinds of\n * Dynamic indications.\n *\n * The first is simply an intention to be dynamic. unstable_noStore is an example of this where\n * the currently executing code simply declares that the current scope is dynamic but if you use it\n * inside unstable_cache it can still be cached. This type of indication can be removed if we ever\n * make the default dynamic to begin with because the only way you would ever be static is inside\n * a cache scope which this indication does not affect.\n *\n * The second is an indication that a dynamic data source was read. This is a stronger form of dynamic\n * because it means that it is inappropriate to cache this at all. using a dynamic data source inside\n * unstable_cache should error. If you want to use some dynamic data inside unstable_cache you should\n * read that data outside the cache and pass it in as an argument to the cached function.\n */\n\nimport type { WorkStore } from '../app-render/work-async-storage.external'\nimport type {\n  WorkUnitStore,\n  RequestStore,\n  PrerenderStoreLegacy,\n  PrerenderStoreModern,\n  PrerenderStoreModernRuntime,\n} from '../app-render/work-unit-async-storage.external'\n\n// Once postpone is in stable we should switch to importing the postpone export directly\nimport React from 'react'\n\nimport { DynamicServerError } from '../../client/components/hooks-server-context'\nimport { StaticGenBailoutError } from '../../client/components/static-generation-bailout'\nimport {\n  getRuntimeStagePromise,\n  workUnitAsyncStorage,\n} from './work-unit-async-storage.external'\nimport { workAsyncStorage } from '../app-render/work-async-storage.external'\nimport { makeHangingPromise } from '../dynamic-rendering-utils'\nimport {\n  METADATA_BOUNDARY_NAME,\n  VIEWPORT_BOUNDARY_NAME,\n  OUTLET_BOUNDARY_NAME,\n  ROOT_LAYOUT_BOUNDARY_NAME,\n} from '../../lib/framework/boundary-constants'\nimport { scheduleOnNextTick } from '../../lib/scheduler'\nimport { BailoutToCSRError } from '../../shared/lib/lazy-dynamic/bailout-to-csr'\nimport { InvariantError } from '../../shared/lib/invariant-error'\n\nconst hasPostpone = typeof React.unstable_postpone === 'function'\n\nexport type DynamicAccess = {\n  /**\n   * If debugging, this will contain the stack trace of where the dynamic access\n   * occurred. This is used to provide more information to the user about why\n   * their page is being rendered dynamically.\n   */\n  stack?: string\n\n  /**\n   * The expression that was accessed dynamically.\n   */\n  expression: string\n}\n\n// Stores dynamic reasons used during an RSC render.\nexport type DynamicTrackingState = {\n  /**\n   * When true, stack information will also be tracked during dynamic access.\n   */\n  readonly isDebugDynamicAccesses: boolean | undefined\n\n  /**\n   * The dynamic accesses that occurred during the render.\n   */\n  readonly dynamicAccesses: Array<DynamicAccess>\n\n  syncDynamicErrorWithStack: null | Error\n}\n\n// Stores dynamic reasons used during an SSR render.\nexport type DynamicValidationState = {\n  hasSuspenseAboveBody: boolean\n  hasDynamicMetadata: boolean\n  hasDynamicViewport: boolean\n  hasAllowedDynamic: boolean\n  dynamicErrors: Array<Error>\n}\n\nexport function createDynamicTrackingState(\n  isDebugDynamicAccesses: boolean | undefined\n): DynamicTrackingState {\n  return {\n    isDebugDynamicAccesses,\n    dynamicAccesses: [],\n    syncDynamicErrorWithStack: null,\n  }\n}\n\nexport function createDynamicValidationState(): DynamicValidationState {\n  return {\n    hasSuspenseAboveBody: false,\n    hasDynamicMetadata: false,\n    hasDynamicViewport: false,\n    hasAllowedDynamic: false,\n    dynamicErrors: [],\n  }\n}\n\nexport function getFirstDynamicReason(\n  trackingState: DynamicTrackingState\n): undefined | string {\n  return trackingState.dynamicAccesses[0]?.expression\n}\n\n/**\n * This function communicates that the current scope should be treated as dynamic.\n *\n * In most cases this function is a no-op but if called during\n * a PPR prerender it will postpone the current sub-tree and calling\n * it during a normal prerender will cause the entire prerender to abort\n */\nexport function markCurrentScopeAsDynamic(\n  store: WorkStore,\n  workUnitStore: undefined | Exclude<WorkUnitStore, PrerenderStoreModern>,\n  expression: string\n): void {\n  if (workUnitStore) {\n    switch (workUnitStore.type) {\n      case 'cache':\n      case 'unstable-cache':\n        // Inside cache scopes, marking a scope as dynamic has no effect,\n        // because the outer cache scope creates a cache boundary. This is\n        // subtly different from reading a dynamic data source, which is\n        // forbidden inside a cache scope.\n        return\n      case 'private-cache':\n        // A private cache scope is already dynamic by definition.\n        return\n      case 'prerender-legacy':\n      case 'prerender-ppr':\n      case 'request':\n        break\n      default:\n        workUnitStore satisfies never\n    }\n  }\n\n  // If we're forcing dynamic rendering or we're forcing static rendering, we\n  // don't need to do anything here because the entire page is already dynamic\n  // or it's static and it should not throw or postpone here.\n  if (store.forceDynamic || store.forceStatic) return\n\n  if (store.dynamicShouldError) {\n    throw new StaticGenBailoutError(\n      `Route ${store.route} with \\`dynamic = \"error\"\\` couldn't be rendered statically because it used \\`${expression}\\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`\n    )\n  }\n\n  if (workUnitStore) {\n    switch (workUnitStore.type) {\n      case 'prerender-ppr':\n        return postponeWithTracking(\n          store.route,\n          expression,\n          workUnitStore.dynamicTracking\n        )\n      case 'prerender-legacy':\n        workUnitStore.revalidate = 0\n\n        // We aren't prerendering, but we are generating a static page. We need\n        // to bail out of static generation.\n        const err = new DynamicServerError(\n          `Route ${store.route} couldn't be rendered statically because it used ${expression}. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`\n        )\n        store.dynamicUsageDescription = expression\n        store.dynamicUsageStack = err.stack\n\n        throw err\n      case 'request':\n        if (process.env.NODE_ENV !== 'production') {\n          workUnitStore.usedDynamic = true\n        }\n        break\n      default:\n        workUnitStore satisfies never\n    }\n  }\n}\n\n/**\n * This function is meant to be used when prerendering without cacheComponents or PPR.\n * When called during a build it will cause Next.js to consider the route as dynamic.\n *\n * @internal\n */\nexport function throwToInterruptStaticGeneration(\n  expression: string,\n  store: WorkStore,\n  prerenderStore: PrerenderStoreLegacy\n): never {\n  // We aren't prerendering but we are generating a static page. We need to bail out of static generation\n  const err = new DynamicServerError(\n    `Route ${store.route} couldn't be rendered statically because it used \\`${expression}\\`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`\n  )\n\n  prerenderStore.revalidate = 0\n\n  store.dynamicUsageDescription = expression\n  store.dynamicUsageStack = err.stack\n\n  throw err\n}\n\n/**\n * This function should be used to track whether something dynamic happened even when\n * we are in a dynamic render. This is useful for Dev where all renders are dynamic but\n * we still track whether dynamic APIs were accessed for helpful messaging\n *\n * @internal\n */\nexport function trackDynamicDataInDynamicRender(workUnitStore: WorkUnitStore) {\n  switch (workUnitStore.type) {\n    case 'cache':\n    case 'unstable-cache':\n      // Inside cache scopes, marking a scope as dynamic has no effect,\n      // because the outer cache scope creates a cache boundary. This is\n      // subtly different from reading a dynamic data source, which is\n      // forbidden inside a cache scope.\n      return\n    case 'private-cache':\n      // A private cache scope is already dynamic by definition.\n      return\n    case 'prerender':\n    case 'prerender-runtime':\n    case 'prerender-legacy':\n    case 'prerender-ppr':\n    case 'prerender-client':\n      break\n    case 'request':\n      if (process.env.NODE_ENV !== 'production') {\n        workUnitStore.usedDynamic = true\n      }\n      break\n    default:\n      workUnitStore satisfies never\n  }\n}\n\nfunction abortOnSynchronousDynamicDataAccess(\n  route: string,\n  expression: string,\n  prerenderStore: PrerenderStoreModern\n): void {\n  const reason = `Route ${route} needs to bail out of prerendering at this point because it used ${expression}.`\n\n  const error = createPrerenderInterruptedError(reason)\n\n  prerenderStore.controller.abort(error)\n\n  const dynamicTracking = prerenderStore.dynamicTracking\n  if (dynamicTracking) {\n    dynamicTracking.dynamicAccesses.push({\n      // When we aren't debugging, we don't need to create another error for the\n      // stack trace.\n      stack: dynamicTracking.isDebugDynamicAccesses\n        ? new Error().stack\n        : undefined,\n      expression,\n    })\n  }\n}\n\nexport function abortOnSynchronousPlatformIOAccess(\n  route: string,\n  expression: string,\n  errorWithStack: Error,\n  prerenderStore: PrerenderStoreModern\n): void {\n  const dynamicTracking = prerenderStore.dynamicTracking\n  abortOnSynchronousDynamicDataAccess(route, expression, prerenderStore)\n  // It is important that we set this tracking value after aborting. Aborts are executed\n  // synchronously except for the case where you abort during render itself. By setting this\n  // value late we can use it to determine if any of the aborted tasks are the task that\n  // called the sync IO expression in the first place.\n  if (dynamicTracking) {\n    if (dynamicTracking.syncDynamicErrorWithStack === null) {\n      dynamicTracking.syncDynamicErrorWithStack = errorWithStack\n    }\n  }\n}\n\nexport function trackSynchronousPlatformIOAccessInDev(\n  requestStore: RequestStore\n): void {\n  // We don't actually have a controller to abort but we do the semantic equivalent by\n  // advancing the request store out of prerender mode\n  requestStore.prerenderPhase = false\n}\n\n/**\n * use this function when prerendering with cacheComponents. If we are doing a\n * prospective prerender we don't actually abort because we want to discover\n * all caches for the shell. If this is the actual prerender we do abort.\n *\n * This function accepts a prerenderStore but the caller should ensure we're\n * actually running in cacheComponents mode.\n *\n * @internal\n */\nexport function abortAndThrowOnSynchronousRequestDataAccess(\n  route: string,\n  expression: string,\n  errorWithStack: Error,\n  prerenderStore: PrerenderStoreModern\n): never {\n  const prerenderSignal = prerenderStore.controller.signal\n  if (prerenderSignal.aborted === false) {\n    // TODO it would be better to move this aborted check into the callsite so we can avoid making\n    // the error object when it isn't relevant to the aborting of the prerender however\n    // since we need the throw semantics regardless of whether we abort it is easier to land\n    // this way. See how this was handled with `abortOnSynchronousPlatformIOAccess` for a closer\n    // to ideal implementation\n    abortOnSynchronousDynamicDataAccess(route, expression, prerenderStore)\n    // It is important that we set this tracking value after aborting. Aborts are executed\n    // synchronously except for the case where you abort during render itself. By setting this\n    // value late we can use it to determine if any of the aborted tasks are the task that\n    // called the sync IO expression in the first place.\n    const dynamicTracking = prerenderStore.dynamicTracking\n    if (dynamicTracking) {\n      if (dynamicTracking.syncDynamicErrorWithStack === null) {\n        dynamicTracking.syncDynamicErrorWithStack = errorWithStack\n      }\n    }\n  }\n  throw createPrerenderInterruptedError(\n    `Route ${route} needs to bail out of prerendering at this point because it used ${expression}.`\n  )\n}\n\n/**\n * Use this function when dynamically prerendering with dynamicIO.\n * We don't want to error, because it's better to return something\n * (and we've already aborted the render at the point where the sync dynamic error occured),\n * but we should log an error server-side.\n * @internal\n */\nexport function warnOnSyncDynamicError(dynamicTracking: DynamicTrackingState) {\n  if (dynamicTracking.syncDynamicErrorWithStack) {\n    // the server did something sync dynamic, likely\n    // leading to an early termination of the prerender.\n    console.error(dynamicTracking.syncDynamicErrorWithStack)\n  }\n}\n\n// For now these implementations are the same so we just reexport\nexport const trackSynchronousRequestDataAccessInDev =\n  trackSynchronousPlatformIOAccessInDev\n\n/**\n * This component will call `React.postpone` that throws the postponed error.\n */\ntype PostponeProps = {\n  reason: string\n  route: string\n}\nexport function Postpone({ reason, route }: PostponeProps): never {\n  const prerenderStore = workUnitAsyncStorage.getStore()\n  const dynamicTracking =\n    prerenderStore && prerenderStore.type === 'prerender-ppr'\n      ? prerenderStore.dynamicTracking\n      : null\n  postponeWithTracking(route, reason, dynamicTracking)\n}\n\nexport function postponeWithTracking(\n  route: string,\n  expression: string,\n  dynamicTracking: null | DynamicTrackingState\n): never {\n  assertPostpone()\n  if (dynamicTracking) {\n    dynamicTracking.dynamicAccesses.push({\n      // When we aren't debugging, we don't need to create another error for the\n      // stack trace.\n      stack: dynamicTracking.isDebugDynamicAccesses\n        ? new Error().stack\n        : undefined,\n      expression,\n    })\n  }\n\n  React.unstable_postpone(createPostponeReason(route, expression))\n}\n\nfunction createPostponeReason(route: string, expression: string) {\n  return (\n    `Route ${route} needs to bail out of prerendering at this point because it used ${expression}. ` +\n    `React throws this special object to indicate where. It should not be caught by ` +\n    `your own try/catch. Learn more: https://nextjs.org/docs/messages/ppr-caught-error`\n  )\n}\n\nexport function isDynamicPostpone(err: unknown) {\n  if (\n    typeof err === 'object' &&\n    err !== null &&\n    typeof (err as any).message === 'string'\n  ) {\n    return isDynamicPostponeReason((err as any).message)\n  }\n  return false\n}\n\nfunction isDynamicPostponeReason(reason: string) {\n  return (\n    reason.includes(\n      'needs to bail out of prerendering at this point because it used'\n    ) &&\n    reason.includes(\n      'Learn more: https://nextjs.org/docs/messages/ppr-caught-error'\n    )\n  )\n}\n\nif (isDynamicPostponeReason(createPostponeReason('%%%', '^^^')) === false) {\n  throw new Error(\n    'Invariant: isDynamicPostpone misidentified a postpone reason. This is a bug in Next.js'\n  )\n}\n\nconst NEXT_PRERENDER_INTERRUPTED = 'NEXT_PRERENDER_INTERRUPTED'\n\nfunction createPrerenderInterruptedError(message: string): Error {\n  const error = new Error(message)\n  ;(error as any).digest = NEXT_PRERENDER_INTERRUPTED\n  return error\n}\n\ntype DigestError = Error & {\n  digest: string\n}\n\nexport function isPrerenderInterruptedError(\n  error: unknown\n): error is DigestError {\n  return (\n    typeof error === 'object' &&\n    error !== null &&\n    (error as any).digest === NEXT_PRERENDER_INTERRUPTED &&\n    'name' in error &&\n    'message' in error &&\n    error instanceof Error\n  )\n}\n\nexport function accessedDynamicData(\n  dynamicAccesses: Array<DynamicAccess>\n): boolean {\n  return dynamicAccesses.length > 0\n}\n\nexport function consumeDynamicAccess(\n  serverDynamic: DynamicTrackingState,\n  clientDynamic: DynamicTrackingState\n): DynamicTrackingState['dynamicAccesses'] {\n  // We mutate because we only call this once we are no longer writing\n  // to the dynamicTrackingState and it's more efficient than creating a new\n  // array.\n  serverDynamic.dynamicAccesses.push(...clientDynamic.dynamicAccesses)\n  return serverDynamic.dynamicAccesses\n}\n\nexport function formatDynamicAPIAccesses(\n  dynamicAccesses: Array<DynamicAccess>\n): string[] {\n  return dynamicAccesses\n    .filter(\n      (access): access is Required<DynamicAccess> =>\n        typeof access.stack === 'string' && access.stack.length > 0\n    )\n    .map(({ expression, stack }) => {\n      stack = stack\n        .split('\\n')\n        // Remove the \"Error: \" prefix from the first line of the stack trace as\n        // well as the first 4 lines of the stack trace which is the distance\n        // from the user code and the `new Error().stack` call.\n        .slice(4)\n        .filter((line) => {\n          // Exclude Next.js internals from the stack trace.\n          if (line.includes('node_modules/next/')) {\n            return false\n          }\n\n          // Exclude anonymous functions from the stack trace.\n          if (line.includes(' (<anonymous>)')) {\n            return false\n          }\n\n          // Exclude Node.js internals from the stack trace.\n          if (line.includes(' (node:')) {\n            return false\n          }\n\n          return true\n        })\n        .join('\\n')\n      return `Dynamic API Usage Debug - ${expression}:\\n${stack}`\n    })\n}\n\nfunction assertPostpone() {\n  if (!hasPostpone) {\n    throw new Error(\n      `Invariant: React.unstable_postpone is not defined. This suggests the wrong version of React was loaded. This is a bug in Next.js`\n    )\n  }\n}\n\n/**\n * This is a bit of a hack to allow us to abort a render using a Postpone instance instead of an Error which changes React's\n * abort semantics slightly.\n */\nexport function createRenderInBrowserAbortSignal(): AbortSignal {\n  const controller = new AbortController()\n  controller.abort(new BailoutToCSRError('Render in Browser'))\n  return controller.signal\n}\n\n/**\n * In a prerender, we may end up with hanging Promises as inputs due them\n * stalling on connection() or because they're loading dynamic data. In that\n * case we need to abort the encoding of arguments since they'll never complete.\n */\nexport function createHangingInputAbortSignal(\n  workUnitStore: WorkUnitStore\n): AbortSignal | undefined {\n  switch (workUnitStore.type) {\n    case 'prerender':\n    case 'prerender-runtime':\n      const controller = new AbortController()\n\n      if (workUnitStore.cacheSignal) {\n        // If we have a cacheSignal it means we're in a prospective render. If\n        // the input we're waiting on is coming from another cache, we do want\n        // to wait for it so that we can resolve this cache entry too.\n        workUnitStore.cacheSignal.inputReady().then(() => {\n          controller.abort()\n        })\n      } else {\n        // Otherwise we're in the final render and we should already have all\n        // our caches filled.\n        // If the prerender uses stages, we have wait until the runtime stage,\n        // at which point all runtime inputs will be resolved.\n        // (otherwise, a runtime prerender might consider `cookies()` hanging\n        //  even though they'd resolve in the next task.)\n        //\n        // We might still be waiting on some microtasks so we\n        // wait one tick before giving up. When we give up, we still want to\n        // render the content of this cache as deeply as we can so that we can\n        // suspend as deeply as possible in the tree or not at all if we don't\n        // end up waiting for the input.\n        const runtimeStagePromise = getRuntimeStagePromise(workUnitStore)\n        if (runtimeStagePromise) {\n          runtimeStagePromise.then(() =>\n            scheduleOnNextTick(() => controller.abort())\n          )\n        } else {\n          scheduleOnNextTick(() => controller.abort())\n        }\n      }\n\n      return controller.signal\n    case 'prerender-client':\n    case 'prerender-ppr':\n    case 'prerender-legacy':\n    case 'request':\n    case 'cache':\n    case 'private-cache':\n    case 'unstable-cache':\n      return undefined\n    default:\n      workUnitStore satisfies never\n  }\n}\n\nexport function annotateDynamicAccess(\n  expression: string,\n  prerenderStore: PrerenderStoreModern\n) {\n  const dynamicTracking = prerenderStore.dynamicTracking\n  if (dynamicTracking) {\n    dynamicTracking.dynamicAccesses.push({\n      stack: dynamicTracking.isDebugDynamicAccesses\n        ? new Error().stack\n        : undefined,\n      expression,\n    })\n  }\n}\n\nexport function useDynamicRouteParams(expression: string) {\n  const workStore = workAsyncStorage.getStore()\n  const workUnitStore = workUnitAsyncStorage.getStore()\n  if (workStore && workUnitStore) {\n    switch (workUnitStore.type) {\n      case 'prerender-client':\n      case 'prerender': {\n        const fallbackParams = workUnitStore.fallbackRouteParams\n        if (fallbackParams && fallbackParams.size > 0) {\n          // We are in a prerender with cacheComponents semantics. We are going to\n          // hang here and never resolve. This will cause the currently\n          // rendering component to effectively be a dynamic hole.\n          React.use(\n            makeHangingPromise(\n              workUnitStore.renderSignal,\n              workStore.route,\n              expression\n            )\n          )\n        }\n        break\n      }\n      case 'prerender-ppr': {\n        const fallbackParams = workUnitStore.fallbackRouteParams\n        if (fallbackParams && fallbackParams.size > 0) {\n          return postponeWithTracking(\n            workStore.route,\n            expression,\n            workUnitStore.dynamicTracking\n          )\n        }\n        break\n      }\n      case 'prerender-runtime':\n        throw new InvariantError(\n          `\\`${expression}\\` was called during a runtime prerender. Next.js should be preventing ${expression} from being included in server components statically, but did not in this case.`\n        )\n      case 'cache':\n      case 'private-cache':\n        throw new InvariantError(\n          `\\`${expression}\\` was called inside a cache scope. Next.js should be preventing ${expression} from being included in server components statically, but did not in this case.`\n        )\n      case 'prerender-legacy':\n      case 'request':\n      case 'unstable-cache':\n        break\n      default:\n        workUnitStore satisfies never\n    }\n  }\n}\n\nconst hasSuspenseRegex = /\\n\\s+at Suspense \\(<anonymous>\\)/\n\n// Common implicit body tags that React will treat as body when placed directly in html\nconst bodyAndImplicitTags =\n  'body|div|main|section|article|aside|header|footer|nav|form|p|span|h1|h2|h3|h4|h5|h6'\n\n// Detects when RootLayoutBoundary (our framework marker component) appears\n// after Suspense in the component stack, indicating the root layout is wrapped\n// within a Suspense boundary. Ensures no body/html/implicit-body components are in between.\n//\n// Example matches:\n//   at Suspense (<anonymous>)\n//   at __next_root_layout_boundary__ (<anonymous>)\n//\n// Or with other components in between (but not body/html/implicit-body):\n//   at Suspense (<anonymous>)\n//   at SomeComponent (<anonymous>)\n//   at __next_root_layout_boundary__ (<anonymous>)\nconst hasSuspenseBeforeRootLayoutWithoutBodyOrImplicitBodyRegex = new RegExp(\n  `\\\\n\\\\s+at Suspense \\\\(<anonymous>\\\\)(?:(?!\\\\n\\\\s+at (?:${bodyAndImplicitTags}) \\\\(<anonymous>\\\\))[\\\\s\\\\S])*?\\\\n\\\\s+at ${ROOT_LAYOUT_BOUNDARY_NAME} \\\\([^\\\\n]*\\\\)`\n)\n\nconst hasMetadataRegex = new RegExp(\n  `\\\\n\\\\s+at ${METADATA_BOUNDARY_NAME}[\\\\n\\\\s]`\n)\nconst hasViewportRegex = new RegExp(\n  `\\\\n\\\\s+at ${VIEWPORT_BOUNDARY_NAME}[\\\\n\\\\s]`\n)\nconst hasOutletRegex = new RegExp(`\\\\n\\\\s+at ${OUTLET_BOUNDARY_NAME}[\\\\n\\\\s]`)\n\nexport function trackAllowedDynamicAccess(\n  workStore: WorkStore,\n  componentStack: string,\n  dynamicValidation: DynamicValidationState,\n  clientDynamic: DynamicTrackingState\n) {\n  if (hasOutletRegex.test(componentStack)) {\n    // We don't need to track that this is dynamic. It is only so when something else is also dynamic.\n    return\n  } else if (hasMetadataRegex.test(componentStack)) {\n    dynamicValidation.hasDynamicMetadata = true\n    return\n  } else if (hasViewportRegex.test(componentStack)) {\n    dynamicValidation.hasDynamicViewport = true\n    return\n  } else if (\n    hasSuspenseBeforeRootLayoutWithoutBodyOrImplicitBodyRegex.test(\n      componentStack\n    )\n  ) {\n    // For Suspense within body, the prelude wouldn't be empty so it wouldn't violate the empty static shells rule.\n    // But if you have Suspense above body, the prelude is empty but we allow that because having Suspense\n    // is an explicit signal from the user that they acknowledge the empty shell and want dynamic rendering.\n    dynamicValidation.hasAllowedDynamic = true\n    dynamicValidation.hasSuspenseAboveBody = true\n    return\n  } else if (hasSuspenseRegex.test(componentStack)) {\n    // this error had a Suspense boundary above it so we don't need to report it as a source\n    // of disallowed\n    dynamicValidation.hasAllowedDynamic = true\n    return\n  } else if (clientDynamic.syncDynamicErrorWithStack) {\n    // This task was the task that called the sync error.\n    dynamicValidation.dynamicErrors.push(\n      clientDynamic.syncDynamicErrorWithStack\n    )\n    return\n  } else {\n    const message = `Route \"${workStore.route}\": A component accessed data, headers, params, searchParams, or a short-lived cache without a Suspense boundary nor a \"use cache\" above it. See more info: https://nextjs.org/docs/messages/next-prerender-missing-suspense`\n    const error = createErrorWithComponentOrOwnerStack(message, componentStack)\n    dynamicValidation.dynamicErrors.push(error)\n    return\n  }\n}\n\n/**\n * In dev mode, we prefer using the owner stack, otherwise the provided\n * component stack is used.\n */\nfunction createErrorWithComponentOrOwnerStack(\n  message: string,\n  componentStack: string\n) {\n  const ownerStack =\n    process.env.NODE_ENV !== 'production' && React.captureOwnerStack\n      ? React.captureOwnerStack()\n      : null\n\n  const error = new Error(message)\n  error.stack = error.name + ': ' + message + (ownerStack ?? componentStack)\n  return error\n}\n\nexport enum PreludeState {\n  Full = 0,\n  Empty = 1,\n  Errored = 2,\n}\n\nexport function logDisallowedDynamicError(\n  workStore: WorkStore,\n  error: Error\n): void {\n  console.error(error)\n\n  if (!workStore.dev) {\n    if (workStore.hasReadableErrorStacks) {\n      console.error(\n        `To get a more detailed stack trace and pinpoint the issue, start the app in development mode by running \\`next dev\\`, then open \"${workStore.route}\" in your browser to investigate the error.`\n      )\n    } else {\n      console.error(`To get a more detailed stack trace and pinpoint the issue, try one of the following:\n  - Start the app in development mode by running \\`next dev\\`, then open \"${workStore.route}\" in your browser to investigate the error.\n  - Rerun the production build with \\`next build --debug-prerender\\` to generate better stack traces.`)\n    }\n  }\n}\n\nexport function throwIfDisallowedDynamic(\n  workStore: WorkStore,\n  prelude: PreludeState,\n  dynamicValidation: DynamicValidationState,\n  serverDynamic: DynamicTrackingState\n): void {\n  if (prelude !== PreludeState.Full) {\n    if (dynamicValidation.hasSuspenseAboveBody) {\n      // This route has opted into allowing fully dynamic rendering\n      // by including a Suspense boundary above the body. In this case\n      // a lack of a shell is not considered disallowed so we simply return\n      return\n    }\n\n    if (serverDynamic.syncDynamicErrorWithStack) {\n      // There is no shell and the server did something sync dynamic likely\n      // leading to an early termination of the prerender before the shell\n      // could be completed. We terminate the build/validating render.\n      logDisallowedDynamicError(\n        workStore,\n        serverDynamic.syncDynamicErrorWithStack\n      )\n      throw new StaticGenBailoutError()\n    }\n\n    // We didn't have any sync bailouts but there may be user code which\n    // blocked the root. We would have captured these during the prerender\n    // and can log them here and then terminate the build/validating render\n    const dynamicErrors = dynamicValidation.dynamicErrors\n    if (dynamicErrors.length > 0) {\n      for (let i = 0; i < dynamicErrors.length; i++) {\n        logDisallowedDynamicError(workStore, dynamicErrors[i])\n      }\n\n      throw new StaticGenBailoutError()\n    }\n\n    // If we got this far then the only other thing that could be blocking\n    // the root is dynamic Viewport. If this is dynamic then\n    // you need to opt into that by adding a Suspense boundary above the body\n    // to indicate your are ok with fully dynamic rendering.\n    if (dynamicValidation.hasDynamicViewport) {\n      console.error(\n        `Route \"${workStore.route}\" has a \\`generateViewport\\` that depends on Request data (\\`cookies()\\`, etc...) or uncached external data (\\`fetch(...)\\`, etc...) without explicitly allowing fully dynamic rendering. See more info here: https://nextjs.org/docs/messages/next-prerender-dynamic-viewport`\n      )\n      throw new StaticGenBailoutError()\n    }\n\n    if (prelude === PreludeState.Empty) {\n      // If we ever get this far then we messed up the tracking of invalid dynamic.\n      // We still adhere to the constraint that you must produce a shell but invite the\n      // user to report this as a bug in Next.js.\n      console.error(\n        `Route \"${workStore.route}\" did not produce a static shell and Next.js was unable to determine a reason. This is a bug in Next.js.`\n      )\n      throw new StaticGenBailoutError()\n    }\n  } else {\n    if (\n      dynamicValidation.hasAllowedDynamic === false &&\n      dynamicValidation.hasDynamicMetadata\n    ) {\n      console.error(\n        `Route \"${workStore.route}\" has a \\`generateMetadata\\` that depends on Request data (\\`cookies()\\`, etc...) or uncached external data (\\`fetch(...)\\`, etc...) when the rest of the route does not. See more info here: https://nextjs.org/docs/messages/next-prerender-dynamic-metadata`\n      )\n      throw new StaticGenBailoutError()\n    }\n  }\n}\n\nexport function delayUntilRuntimeStage<T>(\n  prerenderStore: PrerenderStoreModernRuntime,\n  result: Promise<T>\n): Promise<T> {\n  if (prerenderStore.runtimeStagePromise) {\n    return prerenderStore.runtimeStagePromise.then(() => result)\n  }\n  return result\n}\n"], "names": ["isHangingPromiseRejectionError", "makeDevtoolsIOAwarePromise", "makeHangingPromise", "err", "digest", "HANGING_PROMISE_REJECTION", "HangingPromiseRejectionError", "Error", "constructor", "route", "expression", "abortListenersBySignal", "WeakMap", "signal", "aborted", "Promise", "reject", "hanging<PERSON>romise", "_", "boundRejection", "bind", "currentListeners", "get", "push", "listeners", "set", "addEventListener", "i", "length", "once", "catch", "ignoreReject", "underlying", "resolve", "setTimeout", "BailoutToCSRError", "isBailoutToCSRError", "BAILOUT_TO_CSR", "reason", "DynamicServerError", "isDynamicServerError", "DYNAMIC_ERROR_CODE", "description", "StaticGenBailoutError", "isStaticGenBailoutError", "NEXT_STATIC_GEN_BAILOUT", "code", "error", "METADATA_BOUNDARY_NAME", "OUTLET_BOUNDARY_NAME", "ROOT_LAYOUT_BOUNDARY_NAME", "VIEWPORT_BOUNDARY_NAME", "atLeastOneTask", "scheduleImmediate", "scheduleOnNextTick", "waitAtLeastOneReactRenderTask", "cb", "then", "process", "env", "NEXT_RUNTIME", "nextTick", "setImmediate", "r", "InvariantError", "message", "options", "endsWith", "name", "Postpone", "PreludeState", "abortAndThrowOnSynchronousRequestDataAccess", "abortOnSynchronousPlatformIOAccess", "accessedDynamicData", "annotateDynamicAccess", "consumeDynamicAccess", "createDynamicTrackingState", "createDynamicValidationState", "createHangingInputAbortSignal", "createRenderInBrowserAbortSignal", "delayUntilRuntimeStage", "formatDynamicAPIAccesses", "getFirstDynamicReason", "isDynamicPostpone", "isPrerenderInterruptedError", "logDisallowedDynamicError", "markCurrentScopeAsDynamic", "postponeWithTracking", "throwIfDisallowedDynamic", "throwToInterruptStaticGeneration", "trackAllowedDynamicAccess", "trackDynamicDataInDynamicRender", "trackSynchronousPlatformIOAccessInDev", "trackSynchronousRequestDataAccessInDev", "useDynamicRouteParams", "warnOnSyncDynamicError", "hasPostpone", "React", "unstable_postpone", "isDebugDynamicAccesses", "dynamicAccesses", "syncDynamicErrorWithStack", "hasSuspenseAboveBody", "hasDynamicMetadata", "hasDynamicViewport", "hasAllowedDynamic", "dynamicErrors", "trackingState", "store", "workUnitStore", "type", "forceDynamic", "forceStatic", "dynamicShouldError", "dynamicTracking", "revalidate", "dynamicUsageDescription", "dynamicUsageStack", "stack", "NODE_ENV", "usedDynamic", "prerenderStore", "abortOnSynchronousDynamicDataAccess", "createPrerenderInterruptedError", "controller", "abort", "undefined", "errorWithStack", "requestStore", "prerenderPhase", "prerenderSignal", "console", "workUnitAsyncStorage", "getStore", "assertPostpone", "createPostponeReason", "isDynamicPostponeReason", "includes", "NEXT_PRERENDER_INTERRUPTED", "serverDynamic", "clientDynamic", "filter", "access", "map", "split", "slice", "line", "join", "AbortController", "cacheSignal", "inputReady", "runtimeStagePromise", "getRuntimeStagePromise", "workStore", "workAsyncStorage", "fallbackP<PERSON><PERSON>", "fallbackRouteParams", "size", "use", "renderSignal", "hasSuspenseRegex", "bodyAndImplicitTags", "hasSuspenseBeforeRootLayoutWithoutBodyOrImplicitBodyRegex", "RegExp", "hasMetadataRegex", "hasViewportRegex", "hasOutletRegex", "componentStack", "dynamicValidation", "test", "createErrorWithComponentOrOwnerStack", "ownerStack", "captureOwnerStack", "dev", "hasReadableErrorStacks", "prelude", "result"], "mappings": "6CAAO,SAASA,EACdG,CAAY,QAEZ,AAAmB,UAAf,OAAOA,GAAoBA,AAAQ,QAAQ,CAAE,CAAA,WAAYA,GAAE,AAIxDA,EAAIC,CAJwD,KAIlD,GAAKC,CACxB,wIARgBL,8BAA8B,CAAA,kBAA9BA,GA2EAC,0BAA0B,CAAA,kBAA1BA,GAxCAC,kBAAkB,CAAA,kBAAlBA,KAzBhB,IAAMG,EAA4B,2BAElC,OAAMC,UAAqCC,MAGzCC,YACkBC,CAAa,CACbC,CAAkB,CAClC,CACA,KAAK,CACH,CAAC,qBAAqB,EAAEA,EAAW,qGAAqG,EAAEA,EAAW,8KAA8K,EAAED,EAAM,EAAE,CAAC,EAAA,IAAA,CAJhUA,KAAAA,CAAAA,EAAAA,IAAAA,CACAC,UAAAA,CAAAA,EAAAA,IAAAA,CAJFN,MAAAA,CAASC,CASzB,CACF,CAGA,IAAMM,EAAyB,IAAIC,QAS5B,SAASV,EACdW,CAAmB,CACnBJ,CAAa,CACbC,CAAkB,EAElB,GAAIG,EAAOC,OAAO,CAChB,CADkB,MACXC,QAAQC,MAAM,CAAC,IAAIV,EAA6BG,EAAOC,GACzD,EACL,IAAMO,EAAiB,IAAIF,QAAW,CAACG,EAAGF,KACxC,IAAMG,EAAiBH,EAAOI,IAAI,CAChC,KACA,IAAId,EAA6BG,EAAOC,IAEtCW,EAAmBV,EAAuBW,GAAG,CAACT,GAClD,GAAIQ,EACFA,EAAiBE,IAAI,CAACJ,OACjB,CACL,CAHoB,GAGdK,EAAY,CAACL,EAAe,CAClCR,EAAuBc,GAAG,CAACZ,EAAQW,GACnCX,EAAOa,gBAAgB,CACrB,QACA,KACE,IAAK,IAAIC,EAAI,EAAGA,EAAIH,EAAUI,MAAM,CAAED,IAAK,AACzCH,CAAS,CAACG,EAAE,EAEhB,EACA,CAAEE,MAAM,CAAK,EAEjB,CACF,GAKA,OADAZ,EAAea,KAAK,CAACC,GACdd,CACT,CACF,CAEA,SAASc,IAAgB,CAElB,SAAS9B,EAA8B+B,CAAa,EAGzD,OAAO,IAAIjB,QAAW,AAACkB,IAErBC,WAAW,KACTD,EAAQD,EACV,EAAG,EACL,EACF,uKChFaG,iBAAiB,CAAA,kBAAjBA,GASGC,mBAAmB,CAAA,kBAAnBA,KAZhB,IAAMC,EAAiB,kCAGhB,OAAMF,UAA0B5B,MAGrCC,YAA4B8B,CAAc,CAAE,CAC1C,KAAK,CAAE,sCAAqCA,GAAAA,IAAAA,CADlBA,MAAAA,CAAAA,EAAAA,IAAAA,CAFZlC,MAAAA,CAASiC,CAIzB,CACF,CAGO,SAASD,EAAoBjC,CAAY,QAC9C,AAAmB,UAAf,OAAOA,GAA4B,OAARA,CAAgB,CAAE,CAAA,WAAYA,GAItDA,AAJwD,EAIpDC,CAJwD,KAIlD,GAAKiC,CACxB,uKCjBaE,kBAAkB,CAAA,kBAAlBA,GAQGC,oBAAoB,CAAA,kBAApBA,KAVhB,IAAMC,EAAqB,sBAEpB,OAAMF,UAA2BhC,MAGtCC,YAA4BkC,CAAmB,CAAE,CAC/C,KAAK,CAAE,yBAAwBA,GAAAA,IAAAA,CADLA,WAAAA,CAAAA,EAAAA,IAAAA,CAF5BtC,MAAAA,CAAoCqC,CAIpC,CACF,CAEO,SAASD,EAAqBrC,CAAY,QAC/C,AACiB,UAAf,OAAOA,GACC,OAARA,CACA,CAAE,CAAA,WAAYA,GAAE,AACM,UAAtB,AACA,OADOA,EAAIC,MAAM,EAKZD,EAAIC,MAAM,GAAKqC,CACxB,6XCnBaE,qBAAqB,CAAA,kBAArBA,GAIGC,uBAAuB,CAAA,kBAAvBA,KANhB,IAAMC,EAA0B,yBAEzB,OAAMF,UAA8BpC,wBAApC,KAAA,IAAA,GAAA,IAAA,CACWuC,IAAAA,CAAOD,EACzB,CAEO,SAASD,EACdG,CAAc,QAEd,AAAqB,UAAjB,OAAOA,GAAgC,OAAVA,CAAkB,CAAE,CAAA,SAAUA,GAIxDA,EAJ4D,AAItDD,GAJ0D,CAItD,GAAKD,CACxB,6XCdaG,sBAAsB,CAAA,kBAAtBA,GAEAC,oBAAoB,CAAA,kBAApBA,GACAC,yBAAyB,CAAA,kBAAzBA,GAFAC,sBAAsB,CAAA,kBAAtBA,KADN,IAAMH,EAAyB,6BACzBG,EAAyB,6BACzBF,EAAuB,2BACvBC,EAA4B,qMCyCzBE,cAAc,CAAA,kBAAdA,GAbHC,iBAAiB,CAAA,kBAAjBA,GAtBAC,kBAAkB,CAAA,kBAAlBA,GAgDGC,6BAA6B,CAAA,kBAA7BA,KAhDT,IAAMD,EAAqB,AAACE,IAOjCzC,QAAQkB,OAAO,GAAGwB,IAAI,CAAC,KAInBC,QAAQG,QAAQ,CAACL,EAErB,EACF,EAQaH,EAAoB,AAACG,IAI9BM,aAAaN,EAEjB,EAOO,SAASJ,IACd,OAAO,IAAIrC,QAAc,AAACkB,GAAYoB,EAAkBpB,GAC1D,CAWO,SAASsB,IAIZ,OAAO,IAAIxC,QAASgD,AAAD,GAAOD,aAAaC,GAE3C,yGC/DaC,iBAAAA,qCAAAA,IAAN,OAAMA,UAAuBzD,MAClCC,YAAYyD,CAAe,CAAEC,CAAsB,CAAE,CACnD,KAAK,CACF,eAAaD,CAAAA,CAAQE,QAAQ,CAAC,KAAOF,EAAUA,EAAU,GAAA,CAAE,CAAE,6BAC9DC,GAEF,IAAI,CAACE,IAAI,CAAG,gBACd,CACF,gCCYC,OAAA,cAAA,CAAA,EAAA,aAAA,kGA6VeC,QAAQ,CAAA,kBAARA,GA4XJC,YAAY,CAAA,kBAAZA,GApbIC,2CAA2C,CAAA,kBAA3CA,GArCAC,kCAAkC,CAAA,kBAAlCA,GAuLAC,mBAAmB,CAAA,kBAAnBA,GAkIAC,qBAAqB,CAAA,kBAArBA,GA5HAC,oBAAoB,CAAA,kBAApBA,GArXAC,0BAA0B,CAAA,kBAA1BA,GAUAC,4BAA4B,CAAA,kBAA5BA,GAmbAC,6BAA6B,CAAA,kBAA7BA,GAXAC,gCAAgC,CAAA,kBAAhCA,GA8TAC,sBAAsB,CAAA,kBAAtBA,GAhXAC,wBAAwB,CAAA,kBAAxBA,GA5WAC,qBAAqB,CAAA,kBAArBA,GAsSAC,iBAAiB,CAAA,kBAAjBA,GAwCAC,2BAA2B,CAAA,kBAA3BA,GAqTAC,yBAAyB,CAAA,kBAAzBA,GAtnBAC,yBAAyB,CAAA,kBAAzBA,GA6PAC,oBAAoB,CAAA,kBAApBA,GA4YAC,wBAAwB,CAAA,kBAAxBA,GA/jBAC,gCAAgC,CAAA,kBAAhCA,GAueAC,yBAAyB,CAAA,kBAAzBA,GA9cAC,+BAA+B,CAAA,kBAA/BA,GAuEAC,qCAAqC,CAAA,kBAArCA,GAgEHC,sCAAsC,CAAA,kBAAtCA,GAqPGC,qBAAqB,CAAA,kBAArBA,GA9PAC,sBAAsB,CAAA,kBAAtBA,+DA9TE,CAAA,CAAA,IAAA,QAEiB,CAAA,CAAA,IAAA,OACG,CAAA,CAAA,IAAA,OAI/B,CAAA,CAAA,IAAA,OAC0B,CAAA,CAAA,IAAA,OACE,CAAA,CAAA,IAAA,OAM5B,CAAA,CAAA,IAAA,OAC4B,CAAA,CAAA,IAAA,OACD,CAAA,CAAA,IAAA,OACH,CAAA,CAAA,IAAA,GAEzBC,EAAiD,YAAnC,OAAOC,EAAAA,OAAK,CAACC,iBAAiB,CAwC3C,SAAStB,EACduB,CAA2C,EAE3C,MAAO,wBACLA,EACAC,gBAAiB,EAAE,CACnBC,0BAA2B,IAC7B,CACF,CAEO,SAASxB,IACd,MAAO,CACLyB,qBAAsB,GACtBC,mBAAoB,GACpBC,oBAAoB,EACpBC,mBAAmB,EACnBC,cAAe,EAAE,AACnB,CACF,CAEO,SAASxB,EACdyB,CAAmC,MAE5BA,EAAP,OAAA,AAAuC,OAAhCA,EAAAA,EAAcP,eAAe,CAAC,EAAA,AAAE,EAAA,KAAA,EAAhCO,EAAkCjG,UAAU,AACrD,CASO,SAAS4E,EACdsB,CAAgB,CAChBC,CAAuE,CACvEnG,CAAkB,EAElB,GAAImG,EACF,OAAQA,EAAcC,IADL,AACS,EACxB,IAAK,QACL,IAAK,iBAML,IAAK,gBADH,MAUJ,CAMF,IAAIF,EAAMG,YAAY,GAAIH,EAAMI,WAAW,EAAE,AAE7C,GAAIJ,EAAMK,kBAAkB,CAC1B,CAD4B,KACtB,OAAA,cAEL,CAFK,IAAItE,EAAAA,qBAAqB,CAC7B,CAAC,MAAM,EAAEiE,EAAMnG,KAAK,CAAC,8EAA8E,EAAEC,EAAW,4HAA4H,CAAC,EADzO,oBAAA,OAAA,mBAAA,gBAAA,CAEN,GAGF,GAAImG,EACF,OAAQA,EAAcC,IADL,AACS,EACxB,IAAK,gBACH,OAAOvB,EACLqB,EAAMnG,KAAK,CACXC,EACAmG,EAAcK,eAAe,CAEjC,KAAK,mBACHL,EAAcM,UAAU,CAAG,EAI3B,IAAMhH,EAAM,OAAA,cAEX,CAFW,IAAIoC,EAAAA,kBAAkB,CAChC,CAAC,MAAM,EAAEqE,EAAMnG,KAAK,CAAC,iDAAiD,EAAEC,EAAW,2EAA2E,CAAC,EADrJ,oBAAA,OAAA,mBAAA,gBAAA,CAEZ,EAIA,OAHAkG,EAAMQ,uBAAuB,CAAG1G,EAChCkG,EAAMS,iBAAiB,CAAGlH,EAAImH,KAAK,CAE7BnH,CAQV,EAEJ,CAQO,SAASsF,EACd/E,CAAkB,CAClBkG,CAAgB,CAChBa,CAAoC,EAGpC,IAAMtH,EAAM,OAAA,cAEX,CAFW,IAAIoC,EAAAA,kBAAkB,CAChC,CAAC,MAAM,EAAEqE,EAAMnG,KAAK,CAAC,mDAAmD,EAAEC,EAAW,6EAA6E,CAAC,EADzJ,oBAAA,OAAA,mBAAA,eAAA,EAEZ,EAOA,OALA+G,EAAeN,UAAU,CAAG,EAE5BP,EAAMQ,uBAAuB,CAAG1G,EAChCkG,EAAMS,iBAAiB,CAAGlH,EAAImH,KAAK,CAE7BnH,CACR,CASO,SAASwF,EAAgCkB,CAA4B,EAC1E,OAAQA,EAAcC,IAAI,EACxB,IAAK,QACL,IAAK,iBAML,IAAK,gBADH,MAiBJ,CACF,CAEA,SAASY,EACPjH,CAAa,CACbC,CAAkB,CAClB+G,CAAoC,EAIpC,IAAM1E,EAAQ4E,EAFC,CAAC,MAAM,EAAElH,EAAM,mBAEgB6B,8CAFiD,EAAE5B,EAAW,CAAC,CAAC,EAI9G+G,EAAeG,UAAU,CAACC,KAAK,CAAC9E,GAEhC,IAAMmE,EAAkBO,EAAeP,eAAe,CAClDA,GACFA,EAAgBd,YADG,GACY,CAAC7E,IAAI,CAAC,CAGnC+F,MAAOJ,EAAgBf,sBAAsB,CACzC,AAAI5F,QAAQ+G,KAAK,MACjBQ,aACJpH,CACF,EAEJ,CAEO,SAAS8D,EACd/D,CAAa,CACbC,CAAkB,CAClBqH,CAAqB,CACrBN,CAAoC,EAEpC,IAAMP,EAAkBO,EAAeP,eAAe,CACtDQ,EAAoCjH,EAAOC,EAAY+G,GAKnDP,GACgD,MAAM,CAApDA,EAAgBb,KADD,oBAC0B,GAC3Ca,EAAgBb,yBAAyB,CAAG0B,CAAAA,CAGlD,CAEO,SAASnC,EACdoC,CAA0B,EAI1BA,EAAaC,cAAc,CAAG,EAChC,CAYO,SAAS1D,EACd9D,CAAa,CACbC,CAAkB,CAClBqH,CAAqB,CACrBN,CAAoC,EAGpC,IAAgC,IADRA,AACpBS,EADmCN,UAAU,CAAC/G,MAAM,CACpCC,OAAO,CAAY,CAMrC4G,EAAoCjH,EAAOC,EAAY+G,GAKvD,IAAMP,EAAkBO,EAAeP,eAAe,CAClDA,GACEA,AAA8C,MAAM,GAApCb,KADD,oBAC0B,GAC3Ca,EAAgBb,yBAAyB,CAAG0B,CAAAA,CAGlD,CACA,MAAMJ,EACJ,CAAC,MAAM,EAAElH,EAAM,iEAAiE,EAAEC,EAAW,CAAC,CAAC,CAEnG,CASO,SAASqF,EAAuBmB,CAAqC,EACtEA,EAAgBb,yBAAyB,EAAE,AAG7C8B,QAAQpF,KAAK,CAACmE,EAAgBb,yBAAyB,CAE3D,CAGO,IAAMR,EACXD,EASK,SAASvB,EAAS,QAAE/B,CAAM,OAAE7B,CAAK,CAAiB,EACvD,IAAMgH,EAAiBW,EAAAA,oBAAoB,CAACC,QAAQ,GAKpD9C,EAAqB9E,EAAO6B,EAH1BmF,GAA0C,AAAxBA,GAGgBP,iBAHDJ,IAAI,CACjCW,EAAeP,eAAe,CAC9B,KAER,CAEO,SAAS3B,EACd9E,CAAa,CACbC,CAAkB,CAClBwG,CAA4C,EAE5CoB,AAmIF,SAASA,GACP,GAAI,CAACtC,EACH,MAAM,KADU,EACV,cAEL,CAFSzF,AAAJ,MACJ,CAAC,gIAAgI,CAAC,EAD9H,oBAAA,OAAA,mBAAA,gBAAA,CAEN,GAEJ,IAxIM2G,GACFA,EAAgBd,YADG,GACY,CAAC7E,IAAI,CAAC,CAGnC+F,MAAOJ,EAAgBf,sBAAsB,CACrC5F,AAAJ,QAAY+G,KAAK,MACjBQ,aACJpH,CACF,GAGFuF,EAAAA,OAAK,CAACC,iBAAiB,CAACqC,EAAqB9H,EAAOC,GACtD,CAEA,SAAS6H,EAAqB9H,CAAa,CAAEC,CAAkB,EAC7D,MACE,CAAC,MAAM,EAAED,EAAM,iEAAiE,EAAEC,EAAW,kKAAE,CAAC,AAIpG,CAEO,EALH,CAAC,MAKWyE,EAAkBhF,CAAY,QAC5C,AACiB,UAAf,OAAOA,GACC,OAARA,GACgC,UAAhC,AACA,OADQA,EAAY8D,OAAO,EAEpBuE,EAAyBrI,EAAY8D,AAXoC,CAAC,GACjF,CAAC,EAUkD,CAGvD,CAEA,SAASuE,EAAwBlG,CAAc,EAC7C,OACEA,EAAOmG,QAAQ,CACb,6CAlBgF,CAAC,sBAoBnFnG,EAAOmG,QAAQ,CACb,gEAGN,CAEA,IAAoE,IAAhED,EAAwBD,CAA+C,CAA1B,MAAO,QACtD,MAAM,OAAA,cAEL,CAFK,AAAIhI,MACR,0FADI,oBAAA,OAAA,mBAAA,gBAAA,CAEN,GAGF,IAAMmI,EAA6B,6BAEnC,SAASf,EAAgC1D,CAAe,EACtD,IAAMlB,EAAQ,OAAA,cAAkB,CAAlB,AAAIxC,MAAM0D,GAAV,oBAAA,OAAA,mBAAA,gBAAA,CAAiB,GAE/B,OADElB,EAAc3C,MAAM,CAAGsI,EAClB3F,CACT,CAMO,SAASqC,EACdrC,CAAc,EAEd,MACmB,UAAjB,OAAOA,GACG,OAAVA,GACCA,EAAc3C,MAAM,GAAKsI,GAC1B,SAAU3F,GACV,YAAaA,GACbA,aAAiBxC,KAErB,CAEO,SAASkE,EACd2B,CAAqC,EAErC,OAAOA,EAAgBxE,MAAM,CAAG,CAClC,CAEO,SAAS+C,EACdgE,CAAmC,CACnCC,CAAmC,EAMnC,OADAD,EAAcvC,eAAe,CAAC7E,IAAI,IAAIqH,EAAcxC,eAAe,EAC5DuC,EAAcvC,eAAe,AACtC,CAEO,SAASnB,EACdmB,CAAqC,EAErC,OAAOA,EACJyC,MAAM,CACJC,AAAD,GAC0B,UAAxB,OAAOA,EAAOxB,KAAK,EAAiBwB,EAAOxB,KAAK,CAAC1F,MAAM,CAAG,GAE7DmH,GAAG,CAAC,CAAC,YAAErI,CAAU,CAAE4G,OAAK,CAAE,IACzBA,EAAQA,EACL0B,KAAK,CAAC,MACP,AAGCC,KAAK,CAAC,GACNJ,MAAM,CAAC,AAACK,KAEHA,EAAKT,QAAQ,CAAC,uBAAuB,AAKrCS,EAAKT,QAAQ,CAAC,MAXoD,aAWjC,AAKjCS,EAAKT,QAAQ,CAAC,YAAY,CAM/BU,IAAI,CAAC,MACD,CAAC,0BAA0B,EAAEzI,EAAW;AAAG,EAAE4G,EAAAA,CAAO,EAEjE,CAcO,SAASvC,IACd,IAAM6C,EAAa,IAAIwB,gBAEvB,OADAxB,EAAWC,KAAK,CAAC,OAAA,cAA0C,CAA1C,IAAI1F,EAAAA,iBAAiB,CAAC,qBAAtB,oBAAA,OAAA,mBAAA,gBAAA,CAAyC,IACnDyF,EAAW/G,MAAM,AAC1B,CAOO,SAASiE,EACd+B,CAA4B,EAE5B,OAAQA,EAAcC,IAAI,EACxB,IAAK,YACL,IAAK,oBACH,IAAMc,EAAa,IAAIwB,gBAEvB,GAAIvC,EAAcwC,WAAW,CAI3BxC,CAJ6B,CAIfwC,WAAW,CAACC,UAAU,GAAG7F,IAAI,CAAC,KAC1CmE,EAAWC,KAAK,EAClB,OACK,CAaL,IAAM0B,EAAsBC,CAAAA,EAAAA,EAAAA,sBAAAA,AAAsB,EAAC3C,GAC/C0C,EACFA,EAAoB9F,IAAI,CAAC,IACvBH,CAAAA,EAAAA,EAAAA,GAFqB,eAErBA,AAAkB,EAAC,IAAMsE,EAAWC,KAAK,KAG3CvE,CAAAA,EAAAA,EAAAA,kBAAAA,AAAkB,EAAC,IAAMsE,EAAWC,KAAK,GAE7C,CAEA,OAAOD,EAAW/G,MACpB,AAD0B,KACrB,mBACL,IAAK,gBACL,IAAK,mBACL,IAAK,UACL,IAAK,QACL,IAAK,gBACL,IAAK,iBACH,MAGJ,CAHWiH,AAIb,CAEO,SAASpD,EACdhE,CAAkB,CAClB+G,CAAoC,EAEpC,IAAMP,EAAkBO,EAAeP,eAAe,CAClDA,GACFA,EAAgBd,YADG,GACY,CAAC7E,IAAI,CAAC,CACnC+F,MAAOJ,EAAgBf,sBAAsB,CACzC,AAAI5F,QAAQ+G,KAAK,MACjBQ,aACJpH,CACF,EAEJ,CAEO,SAASoF,EAAsBpF,CAAkB,EACtD,IAAM+I,EAAYC,EAAAA,gBAAgB,CAACrB,QAAQ,GACrCxB,EAAgBuB,EAAAA,oBAAoB,CAACC,QAAQ,GACnD,GAAIoB,GAAa5C,EACf,OAAQA,EAAcC,IADQ,AACJ,EACxB,IAAK,mBACL,IAAK,YAAa,CAChB,IAAM6C,EAAiB9C,EAAc+C,mBAAmB,CACpDD,GAAkBA,EAAeE,IAAI,CAAG,GAAG,AAI7C5D,EAAAA,OAAK,CAAC6D,GAAG,CACP5J,CAAAA,EAAAA,EAAAA,kBAAAA,AAAkB,EAChB2G,EAAckD,YAAY,CAC1BN,EAAUhJ,KAAK,CACfC,IAIN,KACF,CACA,IAAK,gBAAiB,CACpB,IAAMiJ,EAAiB9C,EAAc+C,mBAAmB,CACxD,GAAID,GAAkBA,EAAeE,IAAI,CAAG,EAC1C,CAD6C,MACtCtE,EACLkE,EAAUhJ,KAAK,CACfC,EACAmG,EAAcK,eAAe,EAGjC,KACF,CACA,IAAK,oBACH,MAAM,OAAA,cAEL,CAFK,IAAIlD,EAAAA,cAAc,CACtB,CAAC,EAAE,EAAEtD,EAAW,uEAAuE,EAAEA,EAAW,+EAA+E,CAAC,EADhL,oBAAA,OAAA,mBAAA,gBAAA,CAEN,EACF,KAAK,QACL,IAAK,gBACH,MAAM,OAAA,cAEL,CAFK,IAAIsD,EAAAA,cAAc,CACtB,CAAC,EAAE,EAAEtD,EAAW,iEAAiE,EAAEA,EAAW,+EAA+E,CAAC,EAD1K,oBAAA,OAAA,mBAAA,gBAAA,CAEN,EAOJ,CAEJ,CAEA,IAAMsJ,EAAmB,mCAkBnBE,EAA4D,AAAIC,OACpE,CAAC,uDAAuD,EAAEF,oBAAoB,yCAAyC,+DAAE/G,EAAAA,yBAAyB,CAAC,cAAc,CAAC,EAG9JkH,EAAmB,AAAID,OAC3B,CAAC,UAAU,EAAEnH,EAAAA,sBAAsB,CAAC,QAAQ,CAAC,EAEzCqH,EAAmB,AAAIF,OAC3B,CAAC,UAAU,EAAEhH,EAAAA,sBAAsB,CAAC,QAAQ,CAAC,EAEzCmH,EAAiB,AAAIH,OAAO,CAAC,UAAU,EAAElH,EAAAA,oBAAoB,CAAC,QAAQ,CAAC,EAEtE,SAASyC,EACd+D,CAAoB,CACpBc,CAAsB,CACtBC,CAAyC,CACzC5B,CAAmC,EAEnC,IAAI0B,EAAeG,IAAI,CAACF,IAGjB,GAAIH,EAAiBK,IAAI,CAACF,GAAiB,AAHT,CAIvCC,EAAkBjE,kBAAkB,EAAG,EACvC,MACF,CAAO,GAAI8D,EAAiBI,IAAI,CAACF,GAAiB,CAChDC,EAAkBhE,kBAAkB,EAAG,EACvC,MACF,CAAO,GACL0D,EAA0DO,IAAI,CAC5DF,GAEF,CAIAC,EAAkB/D,iBAAiB,EAAG,EACtC+D,EAAkBlE,oBAAoB,CAAG,GACzC,MACF,MAAO,GAAI0D,EAAiBS,IAAI,CAACF,GAAiB,CAGhDC,EAAkB/D,iBAAiB,EAAG,EACtC,MACF,KAMO,CANA,GAAImC,EAAcvC,yBAAyB,CAAE,YAElDmE,EAAkB9D,aAAa,CAACnF,IAAI,CAClCqH,EAAcvC,yBAAyB,EAKzC,IAAMtD,EAAQ2H,AAUlB,SAASA,AACPzG,CAAe,CACfsG,CAAsB,EAOtB,IAAMxH,EAAQ,OAAA,UAnBuCkB,IAmBrB,CAAlB,AAAI1D,MAAM0D,GAAV,oBAAA,OAAA,mBAAA,gBAAA,CAAiB,GAE/B,OADAlB,EAAMuE,KAAK,CAAGvE,EAAMqB,IAAI,CAAG,KAAOH,EAAyBsG,EACpDxH,CACT,EAvBoB,CAAC,EAqB0B4H,CAAAA,IArBnB,CAqB8C,CArB5ClB,EAAUhJ,KAAK,CAAC,2NAA2N,CAAC,CAC1M8J,eAC5DC,EAAkB9D,aAAa,CAACnF,IAAI,CAACwB,EAEvC,EACF,CAoBO,IAAKuB,EAAAA,SAAAA,CAAAA,GAAAA,WAAAA,4DAAAA,OAML,SAASe,EACdoE,CAAoB,CACpB1G,CAAY,EAEZoF,QAAQpF,KAAK,CAACA,GAET0G,EAAUoB,GAAG,EAAE,CACdpB,EAAUqB,sBAAsB,CAClC3C,CADoC,OAC5BpF,KAAK,CACX,CAAC,iIAAiI,EAAE0G,EAAUhJ,KAAK,CAAC,2CAA2C,CAAC,EAGlM0H,QAAQpF,KAAK,CAAC,CAAC;0EACqD,EAAE0G,EAAUhJ,KAAK,CAAC;qGACS,CAAC,EAGtG,CAEO,SAAS+E,EACdiE,CAAoB,CACpBsB,CAAqB,CACrBP,CAAyC,CACzC7B,CAAmC,EAEnC,GAAIoC,IAAAA,EAA+B,CACjC,GAAIP,EAAkBlE,oBAAoB,CAIxC,CAJ0C,MAO5C,GAAIqC,EAActC,yBAAyB,CAQzC,CAR2C,KAI3ChB,EACEoE,EACAd,EAActC,yBAAyB,EAEnC,IAAI1D,EAAAA,qBAAqB,CAMjC,IAAM+D,EAAgB8D,EAAkB9D,aAAa,CACrD,GAAIA,EAAc9E,MAAM,CAAG,EAAG,CAC5B,IAAK,IAAID,EAAI,EAAGA,EAAI+E,EAAc9E,MAAM,CAAED,IAAK,AAC7C0D,EAA0BoE,EAAW/C,CAAa,CAAC/E,EAAE,CAGvD,OAAM,IAAIgB,EAAAA,qBAAqB,AACjC,CAMA,GAAI6H,EAAkBhE,kBAAkB,CAItC,CAJwC,KACxC2B,QAAQpF,KAAK,CACX,CAAC,OAAO,EAAE0G,EAAUhJ,KAAK,CAAC,8QAA8Q,CAAC,EAErS,IAAIkC,EAAAA,qBAAqB,CAGjC,GAAIoI,GAAgC,CAAhCA,EAOF,MAHA5C,QAAQpF,KAAK,CACX,CAAC,OAAO,EAAE0G,EAAUhJ,KAAK,CAAC,wGAAwG,CAAC,EAE/H,IAAIkC,EAAAA,qBAAqB,AAEnC,MACE,CADK,GAEqC,IAAxC6H,EAAkB/D,iBAAiB,EACnC+D,EAAkBjE,kBAAkB,CAKpC,CAJA,KACA4B,QAAQpF,KAAK,CACX,CAAC,OAAO,EAAE0G,EAAUhJ,KAAK,CAAC,8PAA8P,CAAC,EAErR,IAAIkC,EAAAA,qBAAqB,AAGrC,CAEO,SAASqC,EACdyC,CAA2C,CAC3CuD,CAAkB,SAElB,AAAIvD,EAAe8B,mBAAmB,CAC7B9B,CAD+B,CAChB8B,mBAAmB,CAAC9F,IAAI,CAAC,IAAMuH,GAEhDA,CACT", "ignoreList": [0, 1, 2, 3, 4, 5, 6, 7]}