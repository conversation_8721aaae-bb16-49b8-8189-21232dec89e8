module.exports=[93695,(a,b,c)=>{b.exports=a.x("next/dist/shared/lib/no-fallback-error.external.js",()=>require("next/dist/shared/lib/no-fallback-error.external.js"))},61146,a=>{"use strict";function b(a){return a.isOnDemandRevalidate?"on-demand":a.isRevalidate?"stale":void 0}a.s(["getRevalidateReason",()=>b])},34057,a=>{"use strict";a.s(["NodeNextRequest",()=>k,"NodeNextResponse",()=>l],34057);var b,c=a.i(45535);class d extends Error{constructor(){super("Headers cannot be modified. Read more: https://nextjs.org/docs/app/api-reference/functions/headers")}static callable(){throw new d}}class e extends Headers{constructor(a){super(),this.headers=new Proxy(a,{get(b,d,e){if("symbol"==typeof d)return c.ReflectAdapter.get(b,d,e);let f=d.toLowerCase(),g=Object.keys(a).find(a=>a.toLowerCase()===f);if(void 0!==g)return c.ReflectAdapter.get(b,g,e)},set(b,d,e,f){if("symbol"==typeof d)return c.ReflectAdapter.set(b,d,e,f);let g=d.toLowerCase(),h=Object.keys(a).find(a=>a.toLowerCase()===g);return c.ReflectAdapter.set(b,h??d,e,f)},has(b,d){if("symbol"==typeof d)return c.ReflectAdapter.has(b,d);let e=d.toLowerCase(),f=Object.keys(a).find(a=>a.toLowerCase()===e);return void 0!==f&&c.ReflectAdapter.has(b,f)},deleteProperty(b,d){if("symbol"==typeof d)return c.ReflectAdapter.deleteProperty(b,d);let e=d.toLowerCase(),f=Object.keys(a).find(a=>a.toLowerCase()===e);return void 0===f||c.ReflectAdapter.deleteProperty(b,f)}})}static seal(a){return new Proxy(a,{get(a,b,e){switch(b){case"append":case"delete":case"set":return d.callable;default:return c.ReflectAdapter.get(a,b,e)}}})}merge(a){return Array.isArray(a)?a.join(", "):a}static from(a){return a instanceof Headers?a:new e(a)}append(a,b){let c=this.headers[a];"string"==typeof c?this.headers[a]=[c,b]:Array.isArray(c)?c.push(b):this.headers[a]=b}delete(a){delete this.headers[a]}get(a){let b=this.headers[a];return void 0!==b?this.merge(b):null}has(a){return void 0!==this.headers[a]}set(a,b){this.headers[a]=b}forEach(a,b){for(let[c,d]of this.entries())a.call(b,d,c,this)}*entries(){for(let a of Object.keys(this.headers)){let b=a.toLowerCase(),c=this.get(b);yield[b,c]}}*keys(){for(let a of Object.keys(this.headers)){let b=a.toLowerCase();yield b}}*values(){for(let a of Object.keys(this.headers)){let b=this.get(a);yield b}}[Symbol.iterator](){return this.entries()}}a.i(20463),a.i(741),a.i(98468),Symbol("__next_preview_data");let f=Symbol("__prerender_bypass");var g=a.i(26974),h=a.i(60358);class i{constructor(a,b,c){this.method=a,this.url=b,this.body=c}get cookies(){var b;return this._cookies?this._cookies:this._cookies=(b=this.headers,function(){let{cookie:c}=b;if(!c)return{};let{parse:d}=a.r(62360);return d(Array.isArray(c)?c.join("; "):c)})()}}class j{constructor(a){this.destination=a}redirect(a,b){return this.setHeader("Location",a),this.statusCode=b,b===h.RedirectStatusCode.PermanentRedirect&&this.setHeader("Refresh",`0;url=${a}`),this}}class k extends i{static #a=b=g.NEXT_REQUEST_META;constructor(a){var c;super(a.method.toUpperCase(),a.url,a),this._req=a,this.headers=this._req.headers,this.fetchMetrics=null==(c=this._req)?void 0:c.fetchMetrics,this[b]=this._req[g.NEXT_REQUEST_META]||{},this.streaming=!1}get originalRequest(){return this._req[g.NEXT_REQUEST_META]=this[g.NEXT_REQUEST_META],this._req.url=this.url,this._req.cookies=this.cookies,this._req}set originalRequest(a){this._req=a}stream(){if(this.streaming)throw Object.defineProperty(Error("Invariant: NodeNextRequest.stream() can only be called once"),"__NEXT_ERROR_CODE",{value:"E467",enumerable:!1,configurable:!0});return this.streaming=!0,new ReadableStream({start:a=>{this._req.on("data",b=>{a.enqueue(new Uint8Array(b))}),this._req.on("end",()=>{a.close()}),this._req.on("error",b=>{a.error(b)})}})}}class l extends j{get originalResponse(){return f in this&&(this._res[f]=this[f]),this._res}constructor(a){super(a),this._res=a,this.textBody=void 0}get sent(){return this._res.finished||this._res.headersSent}get statusCode(){return this._res.statusCode}set statusCode(a){this._res.statusCode=a}get statusMessage(){return this._res.statusMessage}set statusMessage(a){this._res.statusMessage=a}setHeader(a,b){return this._res.setHeader(a,b),this}removeHeader(a){return this._res.removeHeader(a),this}getHeaderValues(a){let b=this._res.getHeader(a);if(void 0!==b)return(Array.isArray(b)?b:[b]).map(a=>a.toString())}hasHeader(a){return this._res.hasHeader(a)}getHeader(a){let b=this.getHeaderValues(a);return Array.isArray(b)?b.join(","):void 0}getHeaders(){return this._res.getHeaders()}appendHeader(a,b){let c=this.getHeaderValues(a)??[];return c.includes(b)||this._res.setHeader(a,[...c,b]),this}body(a){return this.textBody=a,this}send(){this._res.end(this.textBody)}onClose(a){this.originalResponse.on("close",a)}}},91795,a=>{"use strict";a.s(["normalizeAppPath",()=>c],91795);var b=a.i(2105);function c(a){var c;return(c=a.split("/").reduce((a,c,d,e)=>!c||(0,b.isGroupSegment)(c)||"@"===c[0]||("page"===c||"route"===c)&&d===e.length-1?a:a+"/"+c,"")).startsWith("/")?c:"/"+c}},24478,a=>{"use strict";a.s(["getCacheControlHeader",()=>c]);var b=a.i(20463);function c({revalidate:a,expire:c}){let d="number"==typeof a&&void 0!==c&&a<c?`, stale-while-revalidate=${c-a}`:"";return 0===a?"private, no-cache, no-store, max-age=0, must-revalidate":"number"==typeof a?`s-maxage=${a}${d}`:`s-maxage=${b.CACHE_ONE_YEAR}${d}`}},3727,a=>{a.n(a.i(14188))},29173,(a,b,c)=>{b.exports=a.x("@prisma/client",()=>require("@prisma/client"))},46112,a=>{"use strict";a.s(["default",()=>b]);let b=(0,a.i(58730).registerClientReference)(function(){throw Error("Attempted to call the default export of [project]/school-management-system/src/components/layout/dashboard-layout.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/school-management-system/src/components/layout/dashboard-layout.tsx <module evaluation>","default")},36631,a=>{"use strict";a.s(["default",()=>b]);let b=(0,a.i(58730).registerClientReference)(function(){throw Error("Attempted to call the default export of [project]/school-management-system/src/components/layout/dashboard-layout.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/school-management-system/src/components/layout/dashboard-layout.tsx","default")},75031,a=>{"use strict";a.i(46112);var b=a.i(36631);a.n(b)},80975,a=>{"use strict";a.s(["adminNavigation",()=>b]);let b=[{name:"Dashboard",href:"/admin",icon:"BarChart3"},{name:"Students",href:"/admin/students",icon:"Users"},{name:"Teachers",href:"/admin/teachers",icon:"GraduationCap"},{name:"Classes & Sections",href:"/admin/classes",icon:"BookOpen"},{name:"Subjects",href:"/admin/subjects",icon:"FileText"},{name:"Terms & Exams",href:"/admin/exams",icon:"Calendar"},{name:"Attendance",href:"/admin/attendance",icon:"ClipboardList"},{name:"Marks",href:"/admin/marks",icon:"Award"},{name:"Reports",href:"/admin/reports",icon:"Download"},{name:"Settings",href:"/admin/settings",icon:"Settings"}]},97854,a=>{"use strict";a.s(["prisma",()=>c]);var b=a.i(29173);let c=globalThis.prisma??new b.PrismaClient},839,(a,b,c)=>{},28213,a=>{"use strict";a.s(["StudentTable",()=>b]);let b=(0,a.i(58730).registerClientReference)(function(){throw Error("Attempted to call StudentTable() from the server but StudentTable is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/school-management-system/src/components/students/student-table.tsx <module evaluation>","StudentTable")},37053,a=>{"use strict";a.s(["StudentTable",()=>b]);let b=(0,a.i(58730).registerClientReference)(function(){throw Error("Attempted to call StudentTable() from the server but StudentTable is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/school-management-system/src/components/students/student-table.tsx","StudentTable")},14974,a=>{"use strict";a.i(28213);var b=a.i(37053);a.n(b)},8776,a=>{"use strict";a.s(["default",()=>q],8776);var b=a.i(18042),c=a.i(18866),d=a.i(97854),e=a.i(14974),f=a.i(75031),g=a.i(80975),h=a.i(37532),i=a.i(8265),j=a.i(51251);let k=(0,j.default)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]),l=(0,j.default)("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]]);var m=a.i(46711);let n=(0,j.default)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]]);async function o({searchParams:a}){let{search:c,classId:f,gender:g,page:h="1"}=await a,i=parseInt(h),j={};c&&(j.OR=[{user:{firstName:{contains:c,mode:"insensitive"}}},{user:{lastName:{contains:c,mode:"insensitive"}}},{user:{email:{contains:c,mode:"insensitive"}}}]),f&&(j.currentClassId=f),g&&(j.gender=g);let[k,l,m]=await Promise.all([d.prisma.student.findMany({where:j,include:{user:!0,currentClass:!0,currentSection:!0},skip:(i-1)*10,take:10,orderBy:[{user:{lastName:"asc"}},{user:{firstName:"asc"}}]}),d.prisma.student.count({where:j}),d.prisma.class.findMany({include:{sections:!0},orderBy:[{name:"asc"}]})]),n=Math.ceil(l/10);return(0,b.jsx)(e.StudentTable,{students:k,classes:m,pagination:{page:i,limit:10,totalCount:l,totalPages:n,hasNextPage:i<n,hasPrevPage:i>1}})}function p(){return(0,b.jsx)(h.Card,{children:(0,b.jsx)(h.CardContent,{className:"flex items-center justify-center py-12",children:(0,b.jsxs)("div",{className:"flex items-center gap-2",children:[(0,b.jsx)(n,{className:"w-6 h-6 animate-spin"}),(0,b.jsx)("span",{children:"Loading students..."})]})})})}function q({searchParams:a}){return(0,b.jsx)(f.default,{title:"Students",navigation:g.adminNavigation,children:(0,b.jsxs)("div",{className:"space-y-6",children:[(0,b.jsxs)("div",{className:"flex flex-col space-y-4 sm:flex-row sm:items-center sm:justify-between sm:space-y-0",children:[(0,b.jsxs)("div",{children:[(0,b.jsx)("h1",{className:"text-2xl sm:text-3xl font-bold tracking-tight",children:"Students"}),(0,b.jsx)("p",{className:"text-muted-foreground",children:"Manage student information and records"})]}),(0,b.jsxs)("div",{className:"flex flex-col space-y-2 sm:flex-row sm:space-y-0 sm:space-x-2",children:[(0,b.jsx)(m.default,{href:"/admin/students/new",className:"w-full sm:w-auto",children:(0,b.jsxs)(i.Button,{className:"w-full sm:w-auto",children:[(0,b.jsx)(k,{className:"w-4 h-4 mr-2"}),(0,b.jsx)("span",{className:"sm:hidden",children:"Add Student"}),(0,b.jsx)("span",{className:"hidden sm:inline",children:"Add Student"})]})}),(0,b.jsx)(m.default,{href:"/admin/students/bulk",className:"w-full sm:w-auto",children:(0,b.jsxs)(i.Button,{variant:"outline",className:"w-full sm:w-auto",children:[(0,b.jsx)(l,{className:"w-4 h-4 mr-2"}),(0,b.jsx)("span",{className:"sm:hidden",children:"Import"}),(0,b.jsx)("span",{className:"hidden sm:inline",children:"Bulk Import"})]})})]})]}),(0,b.jsx)(c.Suspense,{fallback:(0,b.jsx)(p,{}),children:(0,b.jsx)(o,{searchParams:a})})]})})}}];

//# sourceMappingURL=%5Broot-of-the-server%5D__3cea4563._.js.map