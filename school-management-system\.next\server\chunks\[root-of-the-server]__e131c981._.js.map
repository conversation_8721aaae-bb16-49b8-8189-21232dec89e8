{"version": 3, "sources": ["turbopack:///[project]/school-management-system/node_modules/.pnpm/next@15.5.2_@babel+core@7.2_1d9756f0f263ecf6e82055b1d95a339c/node_modules/next/dist/esm/build/templates/app-route.js", "turbopack:///[project]/school-management-system/src/app/api/admin/settings/route.ts"], "sourcesContent": ["import { AppRouteRouteModule } from \"next/dist/esm/server/route-modules/app-route/module.compiled\";\nimport { RouteKind } from \"next/dist/esm/server/route-kind\";\nimport { patchFetch as _patchFetch } from \"next/dist/esm/server/lib/patch-fetch\";\nimport { getRequestMeta } from \"next/dist/esm/server/request-meta\";\nimport { getTracer, SpanKind } from \"next/dist/esm/server/lib/trace/tracer\";\nimport { normalizeAppPath } from \"next/dist/esm/shared/lib/router/utils/app-paths\";\nimport { NodeNextRequest, NodeNextResponse } from \"next/dist/esm/server/base-http/node\";\nimport { NextRequestAdapter, signalFromNodeResponse } from \"next/dist/esm/server/web/spec-extension/adapters/next-request\";\nimport { BaseServerSpan } from \"next/dist/esm/server/lib/trace/constants\";\nimport { getRevalidateReason } from \"next/dist/esm/server/instrumentation/utils\";\nimport { sendResponse } from \"next/dist/esm/server/send-response\";\nimport { fromNodeOutgoingHttpHeaders, toNodeOutgoingHttpHeaders } from \"next/dist/esm/server/web/utils\";\nimport { getCacheControlHeader } from \"next/dist/esm/server/lib/cache-control\";\nimport { INFINITE_CACHE, NEXT_CACHE_TAGS_HEADER } from \"next/dist/esm/lib/constants\";\nimport { NoFallbackError } from \"next/dist/esm/shared/lib/no-fallback-error.external\";\nimport { CachedRouteKind } from \"next/dist/esm/server/response-cache\";\nimport * as userland from \"INNER_APP_ROUTE\";\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new AppRouteRouteModule({\n    definition: {\n        kind: RouteKind.APP_ROUTE,\n        page: \"/api/admin/settings/route\",\n        pathname: \"/api/admin/settings\",\n        filename: \"route\",\n        bundlePath: \"\"\n    },\n    distDir: process.env.__NEXT_RELATIVE_DIST_DIR || '',\n    relativeProjectDir: process.env.__NEXT_RELATIVE_PROJECT_DIR || '',\n    resolvedPagePath: \"[project]/school-management-system/src/app/api/admin/settings/route.ts\",\n    nextConfigOutput,\n    userland\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return _patchFetch({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\nexport { routeModule, workAsyncStorage, workUnitAsyncStorage, serverHooks, patchFetch,  };\nexport async function handler(req, res, ctx) {\n    var _nextConfig_experimental;\n    let srcPage = \"/api/admin/settings/route\";\n    // turbopack doesn't normalize `/index` in the page name\n    // so we need to to process dynamic routes properly\n    // TODO: fix turbopack providing differing value from webpack\n    if (process.env.TURBOPACK) {\n        srcPage = srcPage.replace(/\\/index$/, '') || '/';\n    } else if (srcPage === '/index') {\n        // we always normalize /index specifically\n        srcPage = '/';\n    }\n    const multiZoneDraftMode = process.env.__NEXT_MULTI_ZONE_DRAFT_MODE;\n    const prepareResult = await routeModule.prepare(req, res, {\n        srcPage,\n        multiZoneDraftMode\n    });\n    if (!prepareResult) {\n        res.statusCode = 400;\n        res.end('Bad Request');\n        ctx.waitUntil == null ? void 0 : ctx.waitUntil.call(ctx, Promise.resolve());\n        return null;\n    }\n    const { buildId, params, nextConfig, isDraftMode, prerenderManifest, routerServerContext, isOnDemandRevalidate, revalidateOnlyGenerated, resolvedPathname } = prepareResult;\n    const normalizedSrcPage = normalizeAppPath(srcPage);\n    let isIsr = Boolean(prerenderManifest.dynamicRoutes[normalizedSrcPage] || prerenderManifest.routes[resolvedPathname]);\n    if (isIsr && !isDraftMode) {\n        const isPrerendered = Boolean(prerenderManifest.routes[resolvedPathname]);\n        const prerenderInfo = prerenderManifest.dynamicRoutes[normalizedSrcPage];\n        if (prerenderInfo) {\n            if (prerenderInfo.fallback === false && !isPrerendered) {\n                throw new NoFallbackError();\n            }\n        }\n    }\n    let cacheKey = null;\n    if (isIsr && !routeModule.isDev && !isDraftMode) {\n        cacheKey = resolvedPathname;\n        // ensure /index and / is normalized to one key\n        cacheKey = cacheKey === '/index' ? '/' : cacheKey;\n    }\n    const supportsDynamicResponse = // If we're in development, we always support dynamic HTML\n    routeModule.isDev === true || // If this is not SSG or does not have static paths, then it supports\n    // dynamic HTML.\n    !isIsr;\n    // This is a revalidation request if the request is for a static\n    // page and it is not being resumed from a postponed render and\n    // it is not a dynamic RSC request then it is a revalidation\n    // request.\n    const isRevalidate = isIsr && !supportsDynamicResponse;\n    const method = req.method || 'GET';\n    const tracer = getTracer();\n    const activeSpan = tracer.getActiveScopeSpan();\n    const context = {\n        params,\n        prerenderManifest,\n        renderOpts: {\n            experimental: {\n                cacheComponents: Boolean(nextConfig.experimental.cacheComponents),\n                authInterrupts: Boolean(nextConfig.experimental.authInterrupts)\n            },\n            supportsDynamicResponse,\n            incrementalCache: getRequestMeta(req, 'incrementalCache'),\n            cacheLifeProfiles: (_nextConfig_experimental = nextConfig.experimental) == null ? void 0 : _nextConfig_experimental.cacheLife,\n            isRevalidate,\n            waitUntil: ctx.waitUntil,\n            onClose: (cb)=>{\n                res.on('close', cb);\n            },\n            onAfterTaskError: undefined,\n            onInstrumentationRequestError: (error, _request, errorContext)=>routeModule.onRequestError(req, error, errorContext, routerServerContext)\n        },\n        sharedContext: {\n            buildId\n        }\n    };\n    const nodeNextReq = new NodeNextRequest(req);\n    const nodeNextRes = new NodeNextResponse(res);\n    const nextReq = NextRequestAdapter.fromNodeNextRequest(nodeNextReq, signalFromNodeResponse(res));\n    try {\n        const invokeRouteModule = async (span)=>{\n            return routeModule.handle(nextReq, context).finally(()=>{\n                if (!span) return;\n                span.setAttributes({\n                    'http.status_code': res.statusCode,\n                    'next.rsc': false\n                });\n                const rootSpanAttributes = tracer.getRootSpanAttributes();\n                // We were unable to get attributes, probably OTEL is not enabled\n                if (!rootSpanAttributes) {\n                    return;\n                }\n                if (rootSpanAttributes.get('next.span_type') !== BaseServerSpan.handleRequest) {\n                    console.warn(`Unexpected root span type '${rootSpanAttributes.get('next.span_type')}'. Please report this Next.js issue https://github.com/vercel/next.js`);\n                    return;\n                }\n                const route = rootSpanAttributes.get('next.route');\n                if (route) {\n                    const name = `${method} ${route}`;\n                    span.setAttributes({\n                        'next.route': route,\n                        'http.route': route,\n                        'next.span_name': name\n                    });\n                    span.updateName(name);\n                } else {\n                    span.updateName(`${method} ${req.url}`);\n                }\n            });\n        };\n        const handleResponse = async (currentSpan)=>{\n            var _cacheEntry_value;\n            const responseGenerator = async ({ previousCacheEntry })=>{\n                try {\n                    if (!getRequestMeta(req, 'minimalMode') && isOnDemandRevalidate && revalidateOnlyGenerated && !previousCacheEntry) {\n                        res.statusCode = 404;\n                        // on-demand revalidate always sets this header\n                        res.setHeader('x-nextjs-cache', 'REVALIDATED');\n                        res.end('This page could not be found');\n                        return null;\n                    }\n                    const response = await invokeRouteModule(currentSpan);\n                    req.fetchMetrics = context.renderOpts.fetchMetrics;\n                    let pendingWaitUntil = context.renderOpts.pendingWaitUntil;\n                    // Attempt using provided waitUntil if available\n                    // if it's not we fallback to sendResponse's handling\n                    if (pendingWaitUntil) {\n                        if (ctx.waitUntil) {\n                            ctx.waitUntil(pendingWaitUntil);\n                            pendingWaitUntil = undefined;\n                        }\n                    }\n                    const cacheTags = context.renderOpts.collectedTags;\n                    // If the request is for a static response, we can cache it so long\n                    // as it's not edge.\n                    if (isIsr) {\n                        const blob = await response.blob();\n                        // Copy the headers from the response.\n                        const headers = toNodeOutgoingHttpHeaders(response.headers);\n                        if (cacheTags) {\n                            headers[NEXT_CACHE_TAGS_HEADER] = cacheTags;\n                        }\n                        if (!headers['content-type'] && blob.type) {\n                            headers['content-type'] = blob.type;\n                        }\n                        const revalidate = typeof context.renderOpts.collectedRevalidate === 'undefined' || context.renderOpts.collectedRevalidate >= INFINITE_CACHE ? false : context.renderOpts.collectedRevalidate;\n                        const expire = typeof context.renderOpts.collectedExpire === 'undefined' || context.renderOpts.collectedExpire >= INFINITE_CACHE ? undefined : context.renderOpts.collectedExpire;\n                        // Create the cache entry for the response.\n                        const cacheEntry = {\n                            value: {\n                                kind: CachedRouteKind.APP_ROUTE,\n                                status: response.status,\n                                body: Buffer.from(await blob.arrayBuffer()),\n                                headers\n                            },\n                            cacheControl: {\n                                revalidate,\n                                expire\n                            }\n                        };\n                        return cacheEntry;\n                    } else {\n                        // send response without caching if not ISR\n                        await sendResponse(nodeNextReq, nodeNextRes, response, context.renderOpts.pendingWaitUntil);\n                        return null;\n                    }\n                } catch (err) {\n                    // if this is a background revalidate we need to report\n                    // the request error here as it won't be bubbled\n                    if (previousCacheEntry == null ? void 0 : previousCacheEntry.isStale) {\n                        await routeModule.onRequestError(req, err, {\n                            routerKind: 'App Router',\n                            routePath: srcPage,\n                            routeType: 'route',\n                            revalidateReason: getRevalidateReason({\n                                isRevalidate,\n                                isOnDemandRevalidate\n                            })\n                        }, routerServerContext);\n                    }\n                    throw err;\n                }\n            };\n            const cacheEntry = await routeModule.handleResponse({\n                req,\n                nextConfig,\n                cacheKey,\n                routeKind: RouteKind.APP_ROUTE,\n                isFallback: false,\n                prerenderManifest,\n                isRoutePPREnabled: false,\n                isOnDemandRevalidate,\n                revalidateOnlyGenerated,\n                responseGenerator,\n                waitUntil: ctx.waitUntil\n            });\n            // we don't create a cacheEntry for ISR\n            if (!isIsr) {\n                return null;\n            }\n            if ((cacheEntry == null ? void 0 : (_cacheEntry_value = cacheEntry.value) == null ? void 0 : _cacheEntry_value.kind) !== CachedRouteKind.APP_ROUTE) {\n                var _cacheEntry_value1;\n                throw Object.defineProperty(new Error(`Invariant: app-route received invalid cache entry ${cacheEntry == null ? void 0 : (_cacheEntry_value1 = cacheEntry.value) == null ? void 0 : _cacheEntry_value1.kind}`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E701\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n            if (!getRequestMeta(req, 'minimalMode')) {\n                res.setHeader('x-nextjs-cache', isOnDemandRevalidate ? 'REVALIDATED' : cacheEntry.isMiss ? 'MISS' : cacheEntry.isStale ? 'STALE' : 'HIT');\n            }\n            // Draft mode should never be cached\n            if (isDraftMode) {\n                res.setHeader('Cache-Control', 'private, no-cache, no-store, max-age=0, must-revalidate');\n            }\n            const headers = fromNodeOutgoingHttpHeaders(cacheEntry.value.headers);\n            if (!(getRequestMeta(req, 'minimalMode') && isIsr)) {\n                headers.delete(NEXT_CACHE_TAGS_HEADER);\n            }\n            // If cache control is already set on the response we don't\n            // override it to allow users to customize it via next.config\n            if (cacheEntry.cacheControl && !res.getHeader('Cache-Control') && !headers.get('Cache-Control')) {\n                headers.set('Cache-Control', getCacheControlHeader(cacheEntry.cacheControl));\n            }\n            await sendResponse(nodeNextReq, nodeNextRes, new Response(cacheEntry.value.body, {\n                headers,\n                status: cacheEntry.value.status || 200\n            }));\n            return null;\n        };\n        // TODO: activeSpan code path is for when wrapped by\n        // next-server can be removed when this is no longer used\n        if (activeSpan) {\n            await handleResponse(activeSpan);\n        } else {\n            await tracer.withPropagatedContext(req.headers, ()=>tracer.trace(BaseServerSpan.handleRequest, {\n                    spanName: `${method} ${req.url}`,\n                    kind: SpanKind.SERVER,\n                    attributes: {\n                        'http.method': method,\n                        'http.target': req.url\n                    }\n                }, handleResponse));\n        }\n    } catch (err) {\n        // if we aren't wrapped by base-server handle here\n        if (!activeSpan && !(err instanceof NoFallbackError)) {\n            await routeModule.onRequestError(req, err, {\n                routerKind: 'App Router',\n                routePath: normalizedSrcPage,\n                routeType: 'route',\n                revalidateReason: getRevalidateReason({\n                    isRevalidate,\n                    isOnDemandRevalidate\n                })\n            });\n        }\n        // rethrow so that we can handle serving error page\n        // If this is during static generation, throw the error again.\n        if (isIsr) throw err;\n        // Otherwise, send a 500 response.\n        await sendResponse(nodeNextReq, nodeNextRes, new Response(null, {\n            status: 500\n        }));\n        return null;\n    }\n}\n\n//# sourceMappingURL=app-route.js.map\n", "import { NextRequest, NextResponse } from 'next/server'\r\nimport { getServerSession } from 'next-auth'\r\nimport { authOptions } from '@/lib/auth'\r\nimport { prisma } from '@/lib/db'\r\n\r\n// Small helper to build the list of settings to upsert. Exported for unit tests.\r\nexport function buildSettingsUpdates(type: string, data: Record<string, unknown>): Array<{ key: string; value: string }> {\r\n  const settingsToUpdate: Array<{ key: string; value: string }> = []\r\n\r\n  switch (type) {\r\n    case 'general':\r\n      settingsToUpdate.push(\r\n        { key: 'school_name', value: data.name },\r\n        { key: 'school_address', value: data.address },\r\n        { key: 'school_phone', value: data.phone },\r\n        { key: 'school_email', value: data.email },\r\n        { key: 'school_website', value: data.website },\r\n        { key: 'school_principal', value: data.principal },\r\n        { key: 'school_established_year', value: data.establishedYear }\r\n      )\r\n      break\r\n\r\n    case 'academic':\r\n      settingsToUpdate.push(\r\n        { key: 'academic_year', value: data.academicYear },\r\n        { key: 'current_term', value: data.currentTerm },\r\n        { key: 'grading_system', value: data.gradingSystem },\r\n        { key: 'pass_percentage', value: data.passPercentage?.toString() },\r\n        { key: 'max_attendance_percentage', value: data.maxAttendancePercentage?.toString() }\r\n      )\r\n      break\r\n\r\n    case 'notifications':\r\n      settingsToUpdate.push(\r\n        { key: 'attendance_alerts', value: data.attendanceAlerts?.toString() },\r\n        { key: 'exam_results', value: data.examResults?.toString() },\r\n        { key: 'report_card_generation', value: data.reportCardGeneration?.toString() },\r\n        { key: 'system_updates', value: data.systemUpdates?.toString() }\r\n      )\r\n      break\r\n\r\n    case 'security':\r\n      settingsToUpdate.push(\r\n        { key: 'session_timeout', value: data.sessionTimeout?.toString() },\r\n        { key: 'password_policy', value: data.passwordPolicy },\r\n        { key: 'two_factor_auth', value: data.twoFactorAuth?.toString() },\r\n        { key: 'login_attempts', value: data.loginAttempts?.toString() }\r\n      )\r\n      break\r\n\r\n    default:\r\n      throw new Error('Invalid settings type')\r\n  }\r\n\r\n  // Ensure no undefined slips through which would cause Prisma validation error\r\n  for (const s of settingsToUpdate) {\r\n    if (s.value === undefined) {\r\n      throw new Error(`Missing value for setting key: ${s.key}`)\r\n    }\r\n  }\r\n\r\n  return settingsToUpdate\r\n}\r\n\r\nexport async function GET(request: NextRequest) {\r\n  try {\r\n    const session = await getServerSession(authOptions)\r\n\r\n    if (!session || session.user.role !== 'ADMIN') {\r\n      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })\r\n    }\r\n\r\n    const { searchParams } = new URL(request.url)\r\n    const type = searchParams.get('type')\r\n\r\n    // Get all settings\r\n    const settings = await prisma.setting.findMany()\r\n\r\n    // Convert settings array to object for easier access\r\n    const settingsObject = settings.reduce((acc, setting) => {\r\n      acc[setting.key] = setting.value\r\n      return acc\r\n    }, {} as Record<string, string>)\r\n\r\n    // Return different data based on type parameter\r\n    switch (type) {\r\n      case 'general':\r\n        return NextResponse.json({\r\n          schoolName: settingsObject['school_name'] || 'Advance School',\r\n          address: settingsObject['school_address'] || '123 Education Street, City, State 12345',\r\n          phone: settingsObject['school_phone'] || '+****************',\r\n          email: settingsObject['school_email'] || '<EMAIL>',\r\n          website: settingsObject['school_website'] || 'www.advanceschool.edu',\r\n          principal: settingsObject['school_principal'] || 'Dr. John Smith',\r\n          establishedYear: settingsObject['school_established_year'] || '1995'\r\n        })\r\n\r\n      case 'academic':\r\n        return NextResponse.json({\r\n          academicYear: settingsObject['academic_year'] || '2024-2025',\r\n          currentTerm: settingsObject['current_term'] || 'Term 1',\r\n          gradingSystem: settingsObject['grading_system'] || 'LETTER',\r\n          passPercentage: parseInt(settingsObject['pass_percentage']) || 40,\r\n          maxAttendancePercentage: parseInt(settingsObject['max_attendance_percentage']) || 75\r\n        })\r\n\r\n      case 'notifications':\r\n        return NextResponse.json({\r\n          attendanceAlerts: settingsObject['attendance_alerts'] === 'true',\r\n          examResults: settingsObject['exam_results'] === 'true',\r\n          reportCardGeneration: settingsObject['report_card_generation'] === 'true',\r\n          systemUpdates: settingsObject['system_updates'] === 'true'\r\n        })\r\n\r\n      case 'security':\r\n        return NextResponse.json({\r\n          sessionTimeout: parseInt(settingsObject['session_timeout']) || 30,\r\n          passwordPolicy: settingsObject['password_policy'] || 'strong',\r\n          twoFactorAuth: settingsObject['two_factor_auth'] === 'true',\r\n          loginAttempts: settingsObject['login_attempts'] === 'true'\r\n        })\r\n\r\n      default:\r\n        // Return all settings\r\n        return NextResponse.json({\r\n          general: {\r\n            schoolName: settingsObject['school_name'] || 'Advance School',\r\n            address: settingsObject['school_address'] || '123 Education Street, City, State 12345',\r\n            phone: settingsObject['school_phone'] || '+****************',\r\n            email: settingsObject['school_email'] || '<EMAIL>',\r\n            website: settingsObject['school_website'] || 'www.advanceschool.edu',\r\n            principal: settingsObject['school_principal'] || 'Dr. John Smith',\r\n            establishedYear: settingsObject['school_established_year'] || '1995'\r\n          },\r\n          academic: {\r\n            academicYear: settingsObject['academic_year'] || '2024-2025',\r\n            currentTerm: settingsObject['current_term'] || 'Term 1',\r\n            gradingSystem: settingsObject['grading_system'] || 'LETTER',\r\n            passPercentage: parseInt(settingsObject['pass_percentage']) || 40,\r\n            maxAttendancePercentage: parseInt(settingsObject['max_attendance_percentage']) || 75\r\n          },\r\n          notifications: {\r\n            attendanceAlerts: settingsObject['attendance_alerts'] === 'true',\r\n            examResults: settingsObject['exam_results'] === 'true',\r\n            reportCardGeneration: settingsObject['report_card_generation'] === 'true',\r\n            systemUpdates: settingsObject['system_updates'] === 'true'\r\n          },\r\n          security: {\r\n            sessionTimeout: parseInt(settingsObject['session_timeout']) || 30,\r\n            passwordPolicy: settingsObject['password_policy'] || 'strong',\r\n            twoFactorAuth: settingsObject['two_factor_auth'] === 'true',\r\n            loginAttempts: settingsObject['login_attempts'] === 'true'\r\n          }\r\n        })\r\n    }\r\n  } catch (error) {\r\n    console.error('Error fetching settings:', error)\r\n    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })\r\n  }\r\n}\r\n\r\nexport async function POST(request: NextRequest) {\r\n  try {\r\n    const session = await getServerSession(authOptions)\r\n\r\n    if (!session || session.user.role !== 'ADMIN') {\r\n      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })\r\n    }\r\n\r\n    const body = await request.json()\r\n    const { type, data } = body\r\n\r\n    let settingsToUpdate: Array<{ key: string; value: string }>\r\n    try {\r\n      settingsToUpdate = buildSettingsUpdates(type, data)\r\n    } catch (e) {\r\n      return NextResponse.json({ error: e instanceof Error ? e.message : 'Invalid payload' }, { status: 400 })\r\n    }\r\n\r\n    // Update or create settings\r\n    for (const setting of settingsToUpdate) {\r\n      await prisma.setting.upsert({\r\n        where: { key: setting.key },\r\n        update: { value: setting.value },\r\n        create: {\r\n          key: setting.key,\r\n          value: setting.value,\r\n        }\r\n      })\r\n    }\r\n\r\n    // Log the settings update\r\n    await prisma.auditLog.create({\r\n      data: {\r\n        action: 'SETTINGS_UPDATE',\r\n        entity: 'SETTING',\r\n        entityId: type,\r\n        userId: session.user.id,\r\n        meta: {\r\n          details: `Updated ${type} settings`,\r\n          ipAddress: request.headers.get('x-forwarded-for') || 'unknown'\r\n        }\r\n      }\r\n    })\r\n\r\n    return NextResponse.json({\r\n      message: `${type} settings updated successfully`,\r\n      type,\r\n      data\r\n    })\r\n  } catch (error) {\r\n    console.error('Error updating settings:', error)\r\n    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })\r\n  }\r\n}\r\n"], "names": [], "mappings": "+xHAAA,IAAA,EAAA,EAAA,CAAA,CAAA,MACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,MACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,CAAA,CAAA,OAAA,IAAA,EAAA,EAAA,CAAA,CAAA,0ECfA,IAAA,EAAA,EAAA,CAAA,CAAA,MACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OAGO,SAAS,EAAqB,CAAY,CAAE,CAA6B,EAC9E,IAAM,EAA0D,EAAE,CAElE,OAAQ,GACN,IAAK,UACH,EAAiB,IAAI,CACnB,CAAE,IAAK,cAAe,MAAO,EAAK,IAAI,AAAC,EACvC,CAAE,IAAK,iBAAkB,MAAO,EAAK,OAAO,AAAC,EAC7C,CAAE,IAAK,eAAgB,MAAO,EAAK,KAAK,AAAC,EACzC,CAAE,IAAK,eAAgB,MAAO,EAAK,KAAK,AAAC,EACzC,CAAE,IAAK,iBAAkB,MAAO,EAAK,OAAO,AAAC,EAC7C,CAAE,IAAK,mBAAoB,MAAO,EAAK,SAAS,AAAC,EACjD,CAAE,IAAK,0BAA2B,MAAO,EAAK,eAAgB,AAAD,GAE/D,KAEF,KAAK,WACH,EAAiB,IAAI,CACnB,CAAE,IAAK,gBAAiB,MAAO,EAAK,YAAY,AAAC,EACjD,CAAE,IAAK,eAAgB,MAAO,EAAK,WAAW,AAAC,EAC/C,CAAE,IAAK,iBAAkB,MAAO,EAAK,aAAa,AAAC,EACnD,CAAE,IAAK,kBAAmB,MAAO,EAAK,cAAc,EAAE,UAAW,EACjE,CAAE,IAAK,4BAA6B,MAAO,EAAK,uBAAuB,EAAE,UAAW,GAEtF,KAEF,KAAK,gBACH,EAAiB,IAAI,CACnB,CAAE,IAAK,oBAAqB,MAAO,EAAK,gBAAgB,EAAE,UAAW,EACrE,CAAE,IAAK,eAAgB,MAAO,EAAK,WAAW,EAAE,UAAW,EAC3D,CAAE,IAAK,yBAA0B,MAAO,EAAK,oBAAoB,EAAE,UAAW,EAC9E,CAAE,IAAK,iBAAkB,MAAO,EAAK,aAAa,EAAE,UAAW,GAEjE,KAEF,KAAK,WACH,EAAiB,IAAI,CACnB,CAAE,IAAK,kBAAmB,MAAO,EAAK,cAAc,EAAE,UAAW,EACjE,CAAE,IAAK,kBAAmB,MAAO,EAAK,cAAc,AAAC,EACrD,CAAE,IAAK,kBAAmB,MAAO,EAAK,aAAa,EAAE,UAAW,EAChE,CAAE,IAAK,iBAAkB,MAAO,EAAK,aAAa,EAAE,UAAW,GAEjE,KAEF,SACE,MAAM,AAAI,MAAM,wBACpB,CAGA,IAAK,IAAM,KAAK,EACd,QAAgB,IAAZ,EAAE,CAD0B,IACL,AAAhB,CACT,MAAM,AAAI,MAAM,CAAC,+BAA+B,EAAE,EAAE,GAAG,CAAA,CAAE,EAI7D,OAAO,CACT,CAEO,eAAe,EAAI,CAAoB,EAC5C,GAAI,CACF,IAAM,EAAU,MAAM,CAAA,EAAA,EAAA,gBAAA,AAAgB,EAAC,EAAA,WAAW,EAElD,GAAI,CAAC,GAAiC,SAAS,CAA/B,EAAQ,IAAI,CAAC,IAAI,CAC/B,OAAO,EAAA,YAAY,CAAC,IAAI,CAAC,CAAE,MAAO,cAAe,EAAG,CAAE,OAAQ,GAAI,GAGpE,GAAM,cAAE,CAAY,CAAE,CAAG,IAAI,IAAI,EAAQ,GAAG,EACtC,EAAO,EAAa,GAAG,CAAC,QAMxB,EAAiB,CAHN,MAAM,EAAA,MAAM,CAAC,OAAO,CAAC,QAAQ,EAAA,EAGd,MAAM,CAAC,CAAC,EAAK,KAC3C,CAAG,CAAC,EAAQ,GAAG,CAAC,CAAG,EAAQ,KAAK,CACzB,GACN,CAAC,GAGJ,OAAQ,GACN,IAAK,UACH,OAAO,EAAA,YAAY,CAAC,IAAI,CAAC,CACvB,WAAY,EAAe,WAAc,CAAf,CAAmB,iBAC7C,QAAS,EAAe,YAAD,EAAkB,EAAI,0CAC7C,MAAO,EAAe,YAAD,AAAgB,EAAI,oBACzC,MAAO,EAAe,YAAD,AAAgB,EAAI,yBACzC,QAAS,EAAe,YAAD,EAAkB,EAAI,wBAC7C,UAAW,EAAe,YAAD,IAAoB,EAAI,iBACjD,gBAAiB,EAAe,YAAD,WAA2B,EAAI,MAChE,EAEF,KAAK,WACH,OAAO,EAAA,YAAY,CAAC,IAAI,CAAC,CACvB,aAAc,EAAe,YAAD,CAAiB,EAAI,YACjD,YAAa,EAAe,YAAD,AAAgB,EAAI,SAC/C,cAAe,EAAe,YAAD,EAAkB,EAAI,SACnD,eAAgB,SAAS,EAAe,YAAD,GAAmB,GAAK,GAC/D,wBAAyB,SAAS,EAAe,YAAD,aAA6B,GAAK,EACpF,EAEF,KAAK,gBACH,OAAO,EAAA,YAAY,CAAC,IAAI,CAAC,CACvB,iBAAkB,AAAwC,WAAzB,GAAD,cAAqB,CACrD,YAAgD,SAAnC,EAAe,YAAD,AAAgB,CAC3C,qBAAmE,SAA7C,EAAe,YAAD,UAA0B,CAC9D,cAAoD,SAArC,EAAe,YAAD,EAAkB,AACjD,EAEF,KAAK,WACH,OAAO,EAAA,YAAY,CAAC,IAAI,CAAC,CACvB,eAAgB,SAAS,EAAe,YAAD,GAAmB,GAAK,GAC/D,eAAgB,EAAe,YAAD,GAAmB,EAAI,SACrD,cAAqD,SAAtC,EAAe,YAAD,GAAmB,CAChD,cAAoD,SAArC,EAAe,YAAD,EAAkB,AACjD,EAEF,SAEE,OAAO,EAAA,YAAY,CAAC,IAAI,CAAC,CACvB,QAAS,CACP,WAAY,EAAe,WAAc,CAAf,CAAmB,iBAC7C,QAAS,EAAe,YAAD,EAAkB,EAAI,0CAC7C,MAAO,EAAe,YAAD,AAAgB,EAAI,oBACzC,MAAO,EAAe,YAAD,AAAgB,EAAI,yBACzC,QAAS,EAAe,YAAD,EAAkB,EAAI,wBAC7C,UAAW,EAAe,YAAD,IAAoB,EAAI,iBACjD,gBAAiB,EAAe,YAAD,WAA2B,EAAI,MAChE,EACA,SAAU,CACR,aAAc,EAAe,YAAD,CAAiB,EAAI,YACjD,YAAa,EAAe,YAAD,AAAgB,EAAI,SAC/C,cAAe,EAAe,YAAD,EAAkB,EAAI,SACnD,eAAgB,SAAS,EAAe,YAAD,GAAmB,GAAK,GAC/D,wBAAyB,SAAS,EAAe,YAAD,aAA6B,GAAK,EACpF,EACA,cAAe,CACb,iBAA0D,SAAxC,EAAe,YAAD,KAAqB,CACrD,YAAgD,SAAnC,EAAe,YAAD,AAAgB,CAC3C,qBAAmE,SAA7C,EAAe,YAAD,UAA0B,CAC9D,cAAoD,SAArC,EAAe,YAAD,EAAkB,AACjD,EACA,SAAU,CACR,eAAgB,SAAS,EAAe,YAAD,GAAmB,GAAK,GAC/D,eAAgB,EAAe,YAAD,GAAmB,EAAI,SACrD,cAAqD,SAAtC,EAAe,YAAD,GAAmB,CAChD,cAAoD,SAArC,EAAe,YAAD,EAC/B,AADiD,CAEnD,EACJ,CACF,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,2BAA4B,GACnC,EAAA,YAAY,CAAC,IAAI,CAAC,CAAE,MAAO,uBAAwB,EAAG,CAAE,OAAQ,GAAI,EAC7E,CACF,CAEO,eAAe,EAAK,CAAoB,EAC7C,GAAI,CACF,IASI,EATE,EAAU,MAAM,CAAA,EAAA,EAAA,gBAAgB,AAAhB,EAAiB,EAAA,WAAW,EAElD,GAAI,CAAC,GAAiC,SAAS,CAA/B,EAAQ,IAAI,CAAC,IAAI,CAC/B,OAAO,EAAA,YAAY,CAAC,IAAI,CAAC,CAAE,MAAO,cAAe,EAAG,CAAE,OAAQ,GAAI,GAIpE,GAAM,MAAE,CAAI,MAAE,CAAI,CAAE,CADP,EACU,IADJ,EAAQ,IAAI,GAI/B,GAAI,CACF,EAAmB,EAAqB,EAAM,EAChD,CAAE,MAAO,EAAG,CACV,OAAO,EAAA,YAAY,CAAC,IAAI,CAAC,CAAE,MAAO,aAAa,MAAQ,EAAE,OAAO,CAAG,iBAAkB,EAAG,CAAE,OAAQ,GAAI,EACxG,CAGA,IAAK,IAAM,KAAW,EACpB,MAAM,EAAA,MAAM,CAD0B,AACzB,OAAO,CAAC,MAAM,CAAC,CAC1B,MAAO,CAAE,IAAK,EAAQ,GAAG,AAAC,EAC1B,OAAQ,CAAE,MAAO,EAAQ,KAAK,AAAC,EAC/B,OAAQ,CACN,IAAK,EAAQ,GAAG,CAChB,MAAO,EAAQ,KAAK,AACtB,CACF,GAiBF,OAbA,MAAM,EAAA,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAC3B,KAAM,CACJ,OAAQ,kBACR,OAAQ,UACR,SAAU,EACV,OAAQ,EAAQ,IAAI,CAAC,EAAE,CACvB,KAAM,CACJ,QAAS,CAAC,QAAQ,EAAE,EAAK,SAAS,CAAC,CACnC,UAAW,EAAQ,OAAO,CAAC,GAAG,CAAC,oBAAsB,SACvD,CACF,CACF,GAEO,EAAA,YAAY,CAAC,IAAI,CAAC,CACvB,QAAS,CAAA,EAAG,EAAK,8BAA8B,CAAC,MAChD,OACA,CACF,EACF,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,2BAA4B,GACnC,EAAA,YAAY,CAAC,IAAI,CAAC,CAAE,MAAO,uBAAwB,EAAG,CAAE,OAAQ,GAAI,EAC7E,CACF,CDtMA,IAAA,EAAA,EAAA,CAAA,CAAA,OAIA,IAAM,EAAc,IAAI,EAAA,mBAAmB,CAAC,CACxC,WAAY,CACR,KAAM,EAAA,SAAS,CAAC,SAAS,CACzB,KAAM,4BACN,SAAU,sBACV,SAAU,QACV,WAAY,EAChB,EACA,QAAS,CAAA,OACT,IADiD,eACc,CAA3C,EACpB,iBAAkB,yEAClB,iBAZqB,GAarB,SAAA,CACJ,GAIM,kBAAE,CAAgB,sBAAE,CAAoB,aAAE,CAAW,CAAE,CAAG,EAChE,SAAS,IACL,MAAO,CAAA,EAAA,EAAA,UAAA,AAAW,EAAC,kBACf,EACA,sBACJ,EACJ,CAEO,eAAe,EAAQ,CAAG,CAAE,CAAG,CAAE,CAAG,EACvC,IAAI,EACJ,IAAI,EAAU,4BAKV,EAAU,EAAQ,OAAO,CAAC,WAAY,KAAO,IAMjD,IAAM,EAAgB,MAAM,EAAY,OAAO,CAAC,EAAK,EAAK,SACtD,EACA,mBAHE,CAAA,CAIN,GACA,GAAI,CAAC,EAID,OAHA,EAAI,IADY,MACF,CAAG,IACjB,EAAI,GAAG,CAAC,eACS,MAAjB,CAAwB,CAApB,IAAyB,KAAhB,EAAoB,EAAI,SAAS,CAAC,IAAI,CAAC,EAAK,QAAQ,OAAO,IACjE,KAEX,GAAM,SAAE,CAAO,QAAE,CAAM,YAAE,CAAU,aAAE,CAAW,mBAAE,CAAiB,qBAAE,CAAmB,CAAE,sBAAoB,yBAAE,CAAuB,kBAAE,CAAgB,CAAE,CAAG,EACxJ,EAAoB,CAAA,EAAA,EAAA,gBAAA,AAAgB,EAAC,GACvC,GAAQ,CAAQ,GAAkB,aAAa,CAAC,EAAkB,EAAI,EAAkB,MAAM,CAAC,EAAA,AAAiB,EACpH,GAAI,GAAS,CAAC,EAAa,CACvB,IAAM,GAAgB,CAAQ,EAAkB,MAAM,CAAC,EAAiB,CAClE,EAAgB,EAAkB,aAAa,CAAC,EAAkB,CACxE,GAAI,IAC+B,IAA3B,EAAc,KADH,GACW,EAAc,CAAC,EACrC,MAAM,IAAI,EAAA,CAD0C,cAC3B,AAGrC,CACA,IAAI,EAAW,MACX,GAAU,EAAY,IAAb,CAAkB,EAAK,EAAD,EAG/B,EAAW,AAAa,OAHqB,KAC7C,EAAW,CAAA,EAEwB,IAAM,CAAA,EAE7C,IAAM,EACN,CAAsB,MAAV,EAAkB,GAAb,EAEjB,CAAC,EAKK,EAAe,GAAS,CAAC,EACzB,EAAS,EAAI,MAAM,EAAI,MACvB,EAAS,CAAA,EAAA,EAAA,SAAA,AAAS,IAClB,EAAa,EAAO,WAVyE,OAUvD,GACtC,EAAU,QACZ,oBACA,EACA,WAAY,CACR,aAAc,CACV,iBAAiB,CAAQ,EAAW,YAAY,CAAC,eAAe,CAChE,eAAgB,EAAQ,EAAW,YAAY,CAAC,cAAc,AAClE,0BACA,EACA,iBAAkB,CAAA,EAAA,EAAA,cAAA,AAAc,EAAC,EAAK,oBACtC,kBAAmB,AAAwD,MAAvD,GAA2B,EAAW,YAAA,AAAY,EAAY,KAAK,EAAI,EAAyB,SAAS,cAC7H,EACA,UAAW,EAAI,SAAS,CACxB,QAAS,AAAC,IACN,EAAI,EAAE,CAAC,QAAS,EACpB,EACA,sBAAkB,EAClB,8BAA+B,CAAC,EAAO,EAAU,IAAe,EAAY,cAAc,CAAC,EAAK,EAAO,EAAc,EACzH,EACA,cAAe,SACX,CACJ,CACJ,EACM,EAAc,IAAI,EAAA,eAAe,CAAC,GAClC,EAAc,IAAI,EAAA,gBAAgB,CAAC,GACnC,EAAU,EAAA,kBAAkB,CAAC,mBAAmB,CAAC,EAAa,CAAA,EAAA,EAAA,sBAAA,AAAsB,EAAC,IAC3F,GAAI,CACA,IAAM,EAAoB,MAAO,GACtB,EAAY,MAAM,CAAC,EAAS,GAAS,OAAO,CAAC,KAChD,GAAI,CAAC,EAAM,OACX,EAAK,aAAa,CAAC,CACf,mBAAoB,EAAI,UAAU,CAClC,WAAY,EAChB,GACA,IAAM,EAAqB,EAAO,qBAAqB,GAEvD,GAAI,CAAC,EACD,OAEJ,GAAI,EAAmB,GAAG,CAAC,EAHF,kBAGwB,EAAA,cAAc,CAAC,aAAa,CAAE,YAC3E,QAAQ,IAAI,CAAC,CAAC,2BAA2B,EAAE,EAAmB,GAAG,CAAC,kBAAkB,qEAAqE,CAAC,EAG9J,IAAM,EAAQ,EAAmB,GAAG,CAAC,cACrC,GAAI,EAAO,CACP,IAAM,EAAO,CAAA,EAAG,EAAO,CAAC,EAAE,EAAA,CAAO,CACjC,EAAK,aAAa,CAAC,CACf,aAAc,EACd,aAAc,EACd,iBAAkB,CACtB,GACA,EAAK,UAAU,CAAC,EACpB,MACI,CADG,CACE,UAAU,CAAC,CAAA,EAAG,EAAO,CAAC,EAAE,EAAI,GAAG,CAAA,CAAE,CAE9C,GAEE,EAAiB,MAAO,QACtB,EA0FI,EAzFR,IAAM,EAAoB,MAAO,oBAAE,CAAkB,CAAE,IACnD,GAAI,CACA,GAAI,CAAC,CAAA,EAAA,EAAA,cAAA,AAAc,EAAC,EAAK,gBAAkB,GAAwB,GAA2B,CAAC,EAK3F,OAJA,EAAI,SAD2G,CACjG,CAAG,IAEjB,EAAI,SAAS,CAAC,iBAAkB,eAChC,EAAI,GAAG,CAAC,gCACD,KAEX,IAAM,EAAW,MAAM,EAAkB,GACzC,EAAI,YAAY,CAAG,EAAQ,UAAU,CAAC,YAAY,CAClD,IAAI,EAAmB,EAAQ,UAAU,CAAC,gBAAgB,CAGtD,GACI,EAAI,SAAS,EAAE,CACf,CAFc,CAEV,SAAS,CAAC,GACd,OAAmB,GAG3B,IAAM,EAAY,EAAQ,UAAU,CAAC,aAAa,CAGlD,IAAI,EA6BA,OADA,MAAM,CAAA,EAAA,EAAA,YAAA,AAAY,EAAC,EAAa,EAAa,EAAU,EAAQ,UAAU,CAAC,gBAAgB,EACnF,IA7BA,EACP,IAAM,EAAO,MAAM,EAAS,IAAI,GAE1B,EAAU,CAAA,EAAA,EAAA,yBAAyB,AAAzB,EAA0B,EAAS,OAAO,EACtD,IACA,CAAO,CAAC,EAAA,GADG,mBACmB,CAAC,CAAG,CAAA,EAElC,CAAC,CAAO,CAAC,eAAe,EAAI,EAAK,IAAI,EAAE,CACvC,CAAO,CAAC,eAAe,CAAG,EAAK,IAAA,AAAI,EAEvC,IAAM,EAAa,KAAkD,IAA3C,EAAQ,UAAU,CAAC,mBAAmB,IAAoB,EAAQ,UAAU,CAAC,mBAAmB,EAAI,EAAA,cAAA,AAAc,GAAG,AAAQ,EAAQ,UAAU,CAAC,mBAAmB,CACvL,EAAS,KAA8C,IAAvC,EAAQ,UAAU,CAAC,eAAe,EAAoB,EAAQ,UAAU,CAAC,eAAe,EAAI,EAAA,cAAc,MAAG,EAAY,EAAQ,UAAU,CAAC,eAAe,CAcjL,MAZmB,CAYZ,AAXH,MAAO,CACH,KAAM,EAAA,eAAe,CAAC,SAAS,CAC/B,OAAQ,EAAS,MAAM,CACvB,KAAM,OAAO,IAAI,CAAC,MAAM,EAAK,WAAW,YACxC,CACJ,EACA,aAAc,CACV,oBACA,CACJ,CACJ,CAEJ,CAKJ,CAAE,KALS,CAKF,EAAK,CAcV,MAX0B,MAAtB,EAA6B,KAAK,EAAI,EAAmB,OAAA,AAAO,EAAE,CAClE,MAAM,EAAY,cAAc,CAAC,EAAK,EAAK,CACvC,WAAY,aACZ,UAAW,EACX,UAAW,QACX,iBAAkB,CAAA,EAAA,EAAA,mBAAA,AAAmB,EAAC,CAClC,oCACA,CACJ,EACJ,EAAG,GAED,CACV,CACJ,EACM,EAAa,MAAM,EAAY,cAAc,CAAC,KAChD,aACA,WACA,EACA,UAAW,EAAA,SAAS,CAAC,SAAS,CAC9B,YAAY,EACZ,oBACA,mBAAmB,uBACnB,0BACA,oBACA,EACA,UAAW,EAAI,SAAS,AAC5B,GAEA,GAAI,CAAC,EACD,KADQ,EACD,KAEX,GAAI,CAAe,MAAd,CAAqB,EAAS,AAA0C,GAA9C,IAAK,EAAoB,EAAW,KAAK,AAAL,EAAiB,KAAK,EAAI,EAAkB,IAAI,IAAM,EAAA,eAAe,CAAC,SAAS,CAE9I,CAFgJ,KAE1I,OAAO,cAAc,CAAC,AAAI,MAAM,CAAC,kDAAkD,EAAgB,MAAd,CAAqB,EAAS,AAA2C,GAA/C,IAAK,EAAqB,EAAW,KAAA,AAAK,EAAY,KAAK,EAAI,EAAmB,IAAI,CAAA,CAAE,EAAG,oBAAqB,CACjO,MAAO,OACP,YAAY,EACZ,cAAc,CAClB,EAEA,CAAC,CAAA,EAAA,EAAA,cAAA,AAAc,EAAC,EAAK,gBAAgB,AACrC,EAAI,SAAS,CAAC,iBAAkB,EAAuB,cAAgB,EAAW,MAAM,CAAG,OAAS,EAAW,OAAO,CAAG,QAAU,OAGnI,GACA,EAAI,QADS,CACA,CAAC,gBAAiB,2DAEnC,IAAM,EAAU,CAAA,EAAA,EAAA,2BAAA,AAA2B,EAAC,EAAW,KAAK,CAAC,OAAO,EAapE,MAZI,AAAE,CAAD,AAAC,EAAA,EAAA,cAAA,AAAc,EAAC,EAAK,gBAAkB,GACxC,EAD6C,AACrC,GADwC,GAClC,CAAC,EAAA,sBAAsB,GAIrC,EAAW,YAAY,EAAK,EAAD,AAAK,SAAS,CAAC,kBAAqB,EAAD,AAAS,GAAG,CAAC,kBAC3E,AAD6F,EACrF,GAAG,CAAC,gBAAiB,CAAA,EAAA,EAAA,qBAAA,AAAqB,EAAC,EAAW,YAAY,GAE9E,MAAM,CAAA,EAAA,EAAA,YAAA,AAAY,EAAC,EAAa,EAAa,IAAI,SAAS,EAAW,KAAK,CAAC,IAAI,CAAE,SAC7E,EACA,OAAQ,EAAW,KAAK,CAAC,MAAM,EAAI,GACvC,IACO,IACX,EAGI,EACA,MAAM,EAAe,EADT,CAGZ,MAAM,EAAO,qBAAqB,CAAC,EAAI,OAAO,CAAE,IAAI,EAAO,KAAK,CAAC,EAAA,cAAc,CAAC,aAAa,CAAE,CACvF,SAAU,CAAA,EAAG,EAAO,CAAC,EAAE,EAAI,GAAG,CAAA,CAAE,CAChC,KAAM,EAAA,QAAQ,CAAC,MAAM,CACrB,WAAY,CACR,cAAe,EACf,cAAe,EAAI,GAAG,AAC1B,CACJ,EAAG,GAEf,CAAE,MAAO,EAAK,CAeV,GAbI,AAAC,GAAgB,WAAF,CAAC,CAAgB,EAAA,eAAe,EAC/C,CADkD,KAC5C,EAAY,cAAc,CAAC,EAAK,EAAK,CACvC,WAAY,aACZ,UAAW,EACX,UAAW,QACX,iBAAkB,CAAA,EAAA,EAAA,mBAAA,AAAmB,EAAC,cAClC,uBACA,CACJ,EACJ,GAIA,EAAO,MAAM,EAKjB,OAHA,MAAM,CAAA,EAAA,EAAA,YAAA,AAAY,EAAC,EAAa,EAAa,IAAI,SAAS,KAAM,CAC5D,OAAQ,GACZ,IACO,IACX,CACJ,EAEA,qCAAqC", "ignoreList": [0]}