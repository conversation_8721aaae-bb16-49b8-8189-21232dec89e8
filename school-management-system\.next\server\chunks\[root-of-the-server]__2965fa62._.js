module.exports=[18622,(e,t,r)=>{t.exports=e.x("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js"))},56704,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/work-async-storage.external.js",()=>require("next/dist/server/app-render/work-async-storage.external.js"))},32319,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/work-unit-async-storage.external.js",()=>require("next/dist/server/app-render/work-unit-async-storage.external.js"))},20635,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/action-async-storage.external.js",()=>require("next/dist/server/app-render/action-async-storage.external.js"))},24725,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/after-task-async-storage.external.js",()=>require("next/dist/server/app-render/after-task-async-storage.external.js"))},29173,(e,t,r)=>{t.exports=e.x("@prisma/client",()=>require("@prisma/client"))},70406,(e,t,r)=>{t.exports=e.x("next/dist/compiled/@opentelemetry/api",()=>require("next/dist/compiled/@opentelemetry/api"))},93695,(e,t,r)=>{t.exports=e.x("next/dist/shared/lib/no-fallback-error.external.js",()=>require("next/dist/shared/lib/no-fallback-error.external.js"))},61724,(e,t,r)=>{t.exports=e.x("next/dist/compiled/next-server/app-route-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-route-turbo.runtime.prod.js"))},18108,(e,t,r)=>{},46534,e=>{"use strict";e.s(["handler",()=>q,"patchFetch",()=>b,"routeModule",()=>C,"serverHooks",()=>A,"workAsyncStorage",()=>N,"workUnitAsyncStorage",()=>j],46534);var t=e.i(6137),r=e.i(11365),s=e.i(9638),a=e.i(15243),n=e.i(66378),o=e.i(92101),i=e.i(50012),l=e.i(62885),d=e.i(31409),u=e.i(78448),c=e.i(28015),p=e.i(72721),x=e.i(75714),h=e.i(12634),m=e.i(93695);e.i(74732);var R=e.i(66662);e.s(["DELETE",()=>E,"GET",()=>w,"PUT",()=>g],57951);var f=e.i(2835),v=e.i(31279);async function w(e,{params:t}){try{let e=(await t).id,r=await v.prisma.class.findUnique({where:{id:e},include:{sections:!0,teacher:{select:{id:!0,firstName:!0,lastName:!0,email:!0}},students:{select:{id:!0,dob:!0,gender:!0,user:{select:{firstName:!0,lastName:!0,email:!0}}}},_count:{select:{students:!0}}}});if(!r)return f.NextResponse.json({error:"Class not found"},{status:404});return f.NextResponse.json({class:r})}catch(e){return console.error("Error fetching class:",e),f.NextResponse.json({error:"Failed to fetch class"},{status:500})}}async function g(e,{params:t}){try{let r=(await t).id,s=await e.json();if(!await v.prisma.class.findUnique({where:{id:r}}))return f.NextResponse.json({error:"Class not found"},{status:404});let a=await v.prisma.class.update({where:{id:r},data:s,include:{section:!0,teacher:{select:{id:!0,firstName:!0,lastName:!0,email:!0}}}});return f.NextResponse.json({message:"Class updated successfully",class:a})}catch(e){return console.error("Error updating class:",e),f.NextResponse.json({error:"Failed to update class"},{status:500})}}async function E(e,{params:t}){try{let e=(await t).id,r=await v.prisma.class.findUnique({where:{id:e},include:{students:!0}});if(!r)return f.NextResponse.json({error:"Class not found"},{status:404});if(r.students.length>0)return f.NextResponse.json({error:"Cannot delete class with enrolled students. Please transfer or remove students first."},{status:400});return await v.prisma.class.delete({where:{id:e}}),f.NextResponse.json({message:"Class deleted successfully"})}catch(e){return console.error("Error deleting class:",e),f.NextResponse.json({error:"Failed to delete class"},{status:500})}}var y=e.i(57951);let C=new t.AppRouteRouteModule({definition:{kind:r.RouteKind.APP_ROUTE,page:"/api/admin/classes/[id]/route",pathname:"/api/admin/classes/[id]",filename:"route",bundlePath:""},distDir:".next",relativeProjectDir:"",resolvedPagePath:"[project]/school-management-system/src/app/api/admin/classes/[id]/route.ts",nextConfigOutput:"",userland:y}),{workAsyncStorage:N,workUnitAsyncStorage:j,serverHooks:A}=C;function b(){return(0,s.patchFetch)({workAsyncStorage:N,workUnitAsyncStorage:j})}async function q(e,t,s){var f;let v="/api/admin/classes/[id]/route";v=v.replace(/\/index$/,"")||"/";let w=await C.prepare(e,t,{srcPage:v,multiZoneDraftMode:!1});if(!w)return t.statusCode=400,t.end("Bad Request"),null==s.waitUntil||s.waitUntil.call(s,Promise.resolve()),null;let{buildId:g,params:E,nextConfig:y,isDraftMode:N,prerenderManifest:j,routerServerContext:A,isOnDemandRevalidate:b,revalidateOnlyGenerated:q,resolvedPathname:T}=w,P=(0,o.normalizeAppPath)(v),k=!!(j.dynamicRoutes[P]||j.routes[T]);if(k&&!N){let e=!!j.routes[T],t=j.dynamicRoutes[P];if(t&&!1===t.fallback&&!e)throw new m.NoFallbackError}let O=null;!k||C.isDev||N||(O="/index"===(O=T)?"/":O);let _=!0===C.isDev||!k,U=k&&!_,S=e.method||"GET",H=(0,n.getTracer)(),I=H.getActiveScopeSpan(),M={params:E,prerenderManifest:j,renderOpts:{experimental:{cacheComponents:!!y.experimental.cacheComponents,authInterrupts:!!y.experimental.authInterrupts},supportsDynamicResponse:_,incrementalCache:(0,a.getRequestMeta)(e,"incrementalCache"),cacheLifeProfiles:null==(f=y.experimental)?void 0:f.cacheLife,isRevalidate:U,waitUntil:s.waitUntil,onClose:e=>{t.on("close",e)},onAfterTaskError:void 0,onInstrumentationRequestError:(t,r,s)=>C.onRequestError(e,t,s,A)},sharedContext:{buildId:g}},D=new i.NodeNextRequest(e),F=new i.NodeNextResponse(t),$=l.NextRequestAdapter.fromNodeNextRequest(D,(0,l.signalFromNodeResponse)(t));try{let o=async r=>C.handle($,M).finally(()=>{if(!r)return;r.setAttributes({"http.status_code":t.statusCode,"next.rsc":!1});let s=H.getRootSpanAttributes();if(!s)return;if(s.get("next.span_type")!==d.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${s.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let a=s.get("next.route");if(a){let e=`${S} ${a}`;r.setAttributes({"next.route":a,"http.route":a,"next.span_name":e}),r.updateName(e)}else r.updateName(`${S} ${e.url}`)}),i=async n=>{var i,l;let d=async({previousCacheEntry:r})=>{try{if(!(0,a.getRequestMeta)(e,"minimalMode")&&b&&q&&!r)return t.statusCode=404,t.setHeader("x-nextjs-cache","REVALIDATED"),t.end("This page could not be found"),null;let i=await o(n);e.fetchMetrics=M.renderOpts.fetchMetrics;let l=M.renderOpts.pendingWaitUntil;l&&s.waitUntil&&(s.waitUntil(l),l=void 0);let d=M.renderOpts.collectedTags;if(!k)return await (0,c.sendResponse)(D,F,i,M.renderOpts.pendingWaitUntil),null;{let e=await i.blob(),t=(0,p.toNodeOutgoingHttpHeaders)(i.headers);d&&(t[h.NEXT_CACHE_TAGS_HEADER]=d),!t["content-type"]&&e.type&&(t["content-type"]=e.type);let r=void 0!==M.renderOpts.collectedRevalidate&&!(M.renderOpts.collectedRevalidate>=h.INFINITE_CACHE)&&M.renderOpts.collectedRevalidate,s=void 0===M.renderOpts.collectedExpire||M.renderOpts.collectedExpire>=h.INFINITE_CACHE?void 0:M.renderOpts.collectedExpire;return{value:{kind:R.CachedRouteKind.APP_ROUTE,status:i.status,body:Buffer.from(await e.arrayBuffer()),headers:t},cacheControl:{revalidate:r,expire:s}}}}catch(t){throw(null==r?void 0:r.isStale)&&await C.onRequestError(e,t,{routerKind:"App Router",routePath:v,routeType:"route",revalidateReason:(0,u.getRevalidateReason)({isRevalidate:U,isOnDemandRevalidate:b})},A),t}},m=await C.handleResponse({req:e,nextConfig:y,cacheKey:O,routeKind:r.RouteKind.APP_ROUTE,isFallback:!1,prerenderManifest:j,isRoutePPREnabled:!1,isOnDemandRevalidate:b,revalidateOnlyGenerated:q,responseGenerator:d,waitUntil:s.waitUntil});if(!k)return null;if((null==m||null==(i=m.value)?void 0:i.kind)!==R.CachedRouteKind.APP_ROUTE)throw Object.defineProperty(Error(`Invariant: app-route received invalid cache entry ${null==m||null==(l=m.value)?void 0:l.kind}`),"__NEXT_ERROR_CODE",{value:"E701",enumerable:!1,configurable:!0});(0,a.getRequestMeta)(e,"minimalMode")||t.setHeader("x-nextjs-cache",b?"REVALIDATED":m.isMiss?"MISS":m.isStale?"STALE":"HIT"),N&&t.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");let f=(0,p.fromNodeOutgoingHttpHeaders)(m.value.headers);return(0,a.getRequestMeta)(e,"minimalMode")&&k||f.delete(h.NEXT_CACHE_TAGS_HEADER),!m.cacheControl||t.getHeader("Cache-Control")||f.get("Cache-Control")||f.set("Cache-Control",(0,x.getCacheControlHeader)(m.cacheControl)),await (0,c.sendResponse)(D,F,new Response(m.value.body,{headers:f,status:m.value.status||200})),null};I?await i(I):await H.withPropagatedContext(e.headers,()=>H.trace(d.BaseServerSpan.handleRequest,{spanName:`${S} ${e.url}`,kind:n.SpanKind.SERVER,attributes:{"http.method":S,"http.target":e.url}},i))}catch(t){if(I||t instanceof m.NoFallbackError||await C.onRequestError(e,t,{routerKind:"App Router",routePath:P,routeType:"route",revalidateReason:(0,u.getRevalidateReason)({isRevalidate:U,isOnDemandRevalidate:b})}),k)throw t;return await (0,c.sendResponse)(D,F,new Response(null,{status:500})),null}}}];

//# sourceMappingURL=%5Broot-of-the-server%5D__2965fa62._.js.map