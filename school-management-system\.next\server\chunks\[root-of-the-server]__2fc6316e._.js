module.exports=[18622,(e,t,r)=>{t.exports=e.x("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js"))},56704,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/work-async-storage.external.js",()=>require("next/dist/server/app-render/work-async-storage.external.js"))},32319,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/work-unit-async-storage.external.js",()=>require("next/dist/server/app-render/work-unit-async-storage.external.js"))},20635,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/action-async-storage.external.js",()=>require("next/dist/server/app-render/action-async-storage.external.js"))},24725,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/after-task-async-storage.external.js",()=>require("next/dist/server/app-render/after-task-async-storage.external.js"))},24361,(e,t,r)=>{t.exports=e.x("util",()=>require("util"))},29173,(e,t,r)=>{t.exports=e.x("@prisma/client",()=>require("@prisma/client"))},70406,(e,t,r)=>{t.exports=e.x("next/dist/compiled/@opentelemetry/api",()=>require("next/dist/compiled/@opentelemetry/api"))},93695,(e,t,r)=>{t.exports=e.x("next/dist/shared/lib/no-fallback-error.external.js",()=>require("next/dist/shared/lib/no-fallback-error.external.js"))},61724,(e,t,r)=>{t.exports=e.x("next/dist/compiled/next-server/app-route-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-route-turbo.runtime.prod.js"))},54799,(e,t,r)=>{t.exports=e.x("crypto",()=>require("crypto"))},92509,(e,t,r)=>{t.exports=e.x("url",()=>require("url"))},21517,(e,t,r)=>{t.exports=e.x("http",()=>require("http"))},49719,(e,t,r)=>{t.exports=e.x("assert",()=>require("assert"))},45706,(e,t,r)=>{t.exports=e.x("querystring",()=>require("querystring"))},874,(e,t,r)=>{t.exports=e.x("buffer",()=>require("buffer"))},6461,(e,t,r)=>{t.exports=e.x("zlib",()=>require("zlib"))},24836,(e,t,r)=>{t.exports=e.x("https",()=>require("https"))},27699,(e,t,r)=>{t.exports=e.x("events",()=>require("events"))},12756,e=>{e.v(JSON.parse('{"name":"openid-client","version":"5.7.1","description":"OpenID Connect Relying Party (RP, Client) implementation for Node.js runtime, supports passportjs","keywords":["auth","authentication","basic","certified","client","connect","dynamic","electron","hybrid","identity","implicit","oauth","oauth2","oidc","openid","passport","relying party","strategy"],"homepage":"https://github.com/panva/openid-client","repository":"panva/openid-client","funding":{"url":"https://github.com/sponsors/panva"},"license":"MIT","author":"Filip Skokan <<EMAIL>>","exports":{"types":"./types/index.d.ts","import":"./lib/index.mjs","require":"./lib/index.js"},"main":"./lib/index.js","types":"./types/index.d.ts","files":["lib","types/index.d.ts"],"scripts":{"format":"npx prettier --loglevel silent --write ./lib ./test ./certification ./types","test":"mocha test/**/*.test.js"},"dependencies":{"jose":"^4.15.9","lru-cache":"^6.0.0","object-hash":"^2.2.0","oidc-token-hash":"^5.0.3"},"devDependencies":{"@types/node":"^16.18.106","@types/passport":"^1.0.16","base64url":"^3.0.1","chai":"^4.5.0","mocha":"^10.7.3","nock":"^13.5.5","prettier":"^2.8.8","readable-mock-req":"^0.2.2","sinon":"^9.2.4","timekeeper":"^2.3.1"},"standard-version":{"scripts":{"postchangelog":"sed -i \'\' -e \'s/### \\\\[/## [/g\' CHANGELOG.md"},"types":[{"type":"feat","section":"Features"},{"type":"fix","section":"Fixes"},{"type":"chore","hidden":true},{"type":"docs","hidden":true},{"type":"style","hidden":true},{"type":"refactor","section":"Refactor","hidden":false},{"type":"perf","section":"Performance","hidden":false},{"type":"test","hidden":true}]}}'))},12276,e=>{"use strict";e.s(["hasPermission",()=>r]);let t={ADMIN:["users:read","users:write","users:delete","students:read","students:write","students:delete","teachers:read","teachers:write","teachers:delete","classes:read","classes:write","classes:delete","subjects:read","subjects:write","subjects:delete","attendance:read","attendance:write","marks:read","marks:write","reports:read","reports:write","settings:read","settings:write","audit:read"],TEACHER:["students:read","attendance:read","attendance:write","marks:read","marks:write","reports:read"],STUDENT:["attendance:read","marks:read","reports:read"]};function r(e,r){return t[e]?.includes(r)??!1}},54759,(e,t,r)=>{},72621,e=>{"use strict";e.s(["handler",()=>P,"patchFetch",()=>T,"routeModule",()=>q,"serverHooks",()=>A,"workAsyncStorage",()=>C,"workUnitAsyncStorage",()=>k],72621);var t=e.i(6137),r=e.i(11365),s=e.i(9638),a=e.i(15243),n=e.i(66378),i=e.i(92101),o=e.i(50012),d=e.i(62885),l=e.i(31409),u=e.i(78448),p=e.i(28015),c=e.i(72721),h=e.i(75714),x=e.i(12634),m=e.i(93695);e.i(74732);var f=e.i(66662);e.s(["DELETE",()=>b,"GET",()=>E,"PUT",()=>j],99024);var y=e.i(2835),v=e.i(58356),R=e.i(43382),g=e.i(31279),w=e.i(12276);async function E(e,{params:t}){try{let e=await (0,v.getServerSession)(R.authOptions);if(!e?.user||!(0,w.hasPermission)(e.user.role,"teachers:read"))return y.NextResponse.json({error:"Unauthorized"},{status:401});let r=(await t).id,s=await g.prisma.teacher.findUnique({where:{id:r},include:{user:{select:{id:!0,email:!0,role:!0,firstName:!0,lastName:!0,phone:!0}},attendances:!0,marks:!0}});if(!s)return y.NextResponse.json({error:"Teacher not found"},{status:404});let a={id:s.id,userId:s.userId,employeeCode:s.employeeCode,qualification:s.qualification,phoneAlt:s.phoneAlt,joinedOn:s.joinedOn,createdAt:s.createdAt,updatedAt:s.updatedAt,firstName:s.user.firstName,lastName:s.user.lastName,email:s.user.email,phone:s.user.phone,role:s.user.role,isActive:!0,gender:null,experience:null,classes:[],subjects:[],user:s.user,attendances:s.attendances,marks:s.marks};return y.NextResponse.json({teacher:a})}catch(e){return console.error("Error fetching teacher:",e),y.NextResponse.json({error:"Failed to fetch teacher"},{status:500})}}async function j(e,{params:t}){try{let r=await (0,v.getServerSession)(R.authOptions);if(!r?.user||!(0,w.hasPermission)(r.user.role,"teachers:write"))return y.NextResponse.json({error:"Unauthorized"},{status:401});let s=(await t).id,a=await e.json();if(!await g.prisma.teacher.findUnique({where:{id:s}}))return y.NextResponse.json({error:"Teacher not found"},{status:404});let n=await g.prisma.teacher.update({where:{id:s},data:a,include:{user:{select:{id:!0,email:!0,role:!0}},attendances:!0,marks:!0}});return y.NextResponse.json({message:"Teacher updated successfully",teacher:n})}catch(e){return console.error("Error updating teacher:",e),y.NextResponse.json({error:"Failed to update teacher"},{status:500})}}async function b(e,{params:t}){try{let e=await (0,v.getServerSession)(R.authOptions);if(!e?.user||!(0,w.hasPermission)(e.user.role,"teachers:delete"))return y.NextResponse.json({error:"Unauthorized"},{status:401});let r=(await t).id,s=await g.prisma.teacher.findUnique({where:{id:r},include:{user:!0,attendances:!0,marks:!0}});if(!s)return y.NextResponse.json({error:"Teacher not found"},{status:404});if(s.attendances.length>0||s.marks.length>0)return y.NextResponse.json({error:"Cannot delete teacher with active records. Please reassign or deactivate instead."},{status:400});return await g.prisma.$transaction([g.prisma.teacher.delete({where:{id:r}}),g.prisma.user.delete({where:{id:s.user.id}})]),y.NextResponse.json({message:"Teacher deleted successfully"})}catch(e){return console.error("Error deleting teacher:",e),y.NextResponse.json({error:"Failed to delete teacher"},{status:500})}}var N=e.i(99024);let q=new t.AppRouteRouteModule({definition:{kind:r.RouteKind.APP_ROUTE,page:"/api/admin/teachers/[id]/route",pathname:"/api/admin/teachers/[id]",filename:"route",bundlePath:""},distDir:".next",relativeProjectDir:"",resolvedPagePath:"[project]/school-management-system/src/app/api/admin/teachers/[id]/route.ts",nextConfigOutput:"",userland:N}),{workAsyncStorage:C,workUnitAsyncStorage:k,serverHooks:A}=q;function T(){return(0,s.patchFetch)({workAsyncStorage:C,workUnitAsyncStorage:k})}async function P(e,t,s){var y;let v="/api/admin/teachers/[id]/route";v=v.replace(/\/index$/,"")||"/";let R=await q.prepare(e,t,{srcPage:v,multiZoneDraftMode:!1});if(!R)return t.statusCode=400,t.end("Bad Request"),null==s.waitUntil||s.waitUntil.call(s,Promise.resolve()),null;let{buildId:g,params:w,nextConfig:E,isDraftMode:j,prerenderManifest:b,routerServerContext:N,isOnDemandRevalidate:C,revalidateOnlyGenerated:k,resolvedPathname:A}=R,T=(0,i.normalizeAppPath)(v),P=!!(b.dynamicRoutes[T]||b.routes[A]);if(P&&!j){let e=!!b.routes[A],t=b.dynamicRoutes[T];if(t&&!1===t.fallback&&!e)throw new m.NoFallbackError}let O=null;!P||q.isDev||j||(O="/index"===(O=A)?"/":O);let S=!0===q.isDev||!P,U=P&&!S,_=e.method||"GET",H=(0,n.getTracer)(),I=H.getActiveScopeSpan(),D={params:w,prerenderManifest:b,renderOpts:{experimental:{cacheComponents:!!E.experimental.cacheComponents,authInterrupts:!!E.experimental.authInterrupts},supportsDynamicResponse:S,incrementalCache:(0,a.getRequestMeta)(e,"incrementalCache"),cacheLifeProfiles:null==(y=E.experimental)?void 0:y.cacheLife,isRevalidate:U,waitUntil:s.waitUntil,onClose:e=>{t.on("close",e)},onAfterTaskError:void 0,onInstrumentationRequestError:(t,r,s)=>q.onRequestError(e,t,s,N)},sharedContext:{buildId:g}},M=new o.NodeNextRequest(e),F=new o.NodeNextResponse(t),$=d.NextRequestAdapter.fromNodeNextRequest(M,(0,d.signalFromNodeResponse)(t));try{let i=async r=>q.handle($,D).finally(()=>{if(!r)return;r.setAttributes({"http.status_code":t.statusCode,"next.rsc":!1});let s=H.getRootSpanAttributes();if(!s)return;if(s.get("next.span_type")!==l.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${s.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let a=s.get("next.route");if(a){let e=`${_} ${a}`;r.setAttributes({"next.route":a,"http.route":a,"next.span_name":e}),r.updateName(e)}else r.updateName(`${_} ${e.url}`)}),o=async n=>{var o,d;let l=async({previousCacheEntry:r})=>{try{if(!(0,a.getRequestMeta)(e,"minimalMode")&&C&&k&&!r)return t.statusCode=404,t.setHeader("x-nextjs-cache","REVALIDATED"),t.end("This page could not be found"),null;let o=await i(n);e.fetchMetrics=D.renderOpts.fetchMetrics;let d=D.renderOpts.pendingWaitUntil;d&&s.waitUntil&&(s.waitUntil(d),d=void 0);let l=D.renderOpts.collectedTags;if(!P)return await (0,p.sendResponse)(M,F,o,D.renderOpts.pendingWaitUntil),null;{let e=await o.blob(),t=(0,c.toNodeOutgoingHttpHeaders)(o.headers);l&&(t[x.NEXT_CACHE_TAGS_HEADER]=l),!t["content-type"]&&e.type&&(t["content-type"]=e.type);let r=void 0!==D.renderOpts.collectedRevalidate&&!(D.renderOpts.collectedRevalidate>=x.INFINITE_CACHE)&&D.renderOpts.collectedRevalidate,s=void 0===D.renderOpts.collectedExpire||D.renderOpts.collectedExpire>=x.INFINITE_CACHE?void 0:D.renderOpts.collectedExpire;return{value:{kind:f.CachedRouteKind.APP_ROUTE,status:o.status,body:Buffer.from(await e.arrayBuffer()),headers:t},cacheControl:{revalidate:r,expire:s}}}}catch(t){throw(null==r?void 0:r.isStale)&&await q.onRequestError(e,t,{routerKind:"App Router",routePath:v,routeType:"route",revalidateReason:(0,u.getRevalidateReason)({isRevalidate:U,isOnDemandRevalidate:C})},N),t}},m=await q.handleResponse({req:e,nextConfig:E,cacheKey:O,routeKind:r.RouteKind.APP_ROUTE,isFallback:!1,prerenderManifest:b,isRoutePPREnabled:!1,isOnDemandRevalidate:C,revalidateOnlyGenerated:k,responseGenerator:l,waitUntil:s.waitUntil});if(!P)return null;if((null==m||null==(o=m.value)?void 0:o.kind)!==f.CachedRouteKind.APP_ROUTE)throw Object.defineProperty(Error(`Invariant: app-route received invalid cache entry ${null==m||null==(d=m.value)?void 0:d.kind}`),"__NEXT_ERROR_CODE",{value:"E701",enumerable:!1,configurable:!0});(0,a.getRequestMeta)(e,"minimalMode")||t.setHeader("x-nextjs-cache",C?"REVALIDATED":m.isMiss?"MISS":m.isStale?"STALE":"HIT"),j&&t.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");let y=(0,c.fromNodeOutgoingHttpHeaders)(m.value.headers);return(0,a.getRequestMeta)(e,"minimalMode")&&P||y.delete(x.NEXT_CACHE_TAGS_HEADER),!m.cacheControl||t.getHeader("Cache-Control")||y.get("Cache-Control")||y.set("Cache-Control",(0,h.getCacheControlHeader)(m.cacheControl)),await (0,p.sendResponse)(M,F,new Response(m.value.body,{headers:y,status:m.value.status||200})),null};I?await o(I):await H.withPropagatedContext(e.headers,()=>H.trace(l.BaseServerSpan.handleRequest,{spanName:`${_} ${e.url}`,kind:n.SpanKind.SERVER,attributes:{"http.method":_,"http.target":e.url}},o))}catch(t){if(I||t instanceof m.NoFallbackError||await q.onRequestError(e,t,{routerKind:"App Router",routePath:T,routeType:"route",revalidateReason:(0,u.getRevalidateReason)({isRevalidate:U,isOnDemandRevalidate:C})}),P)throw t;return await (0,p.sendResponse)(M,F,new Response(null,{status:500})),null}}}];

//# sourceMappingURL=%5Broot-of-the-server%5D__2fc6316e._.js.map