module.exports=[18622,(e,t,r)=>{t.exports=e.x("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js"))},56704,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/work-async-storage.external.js",()=>require("next/dist/server/app-render/work-async-storage.external.js"))},32319,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/work-unit-async-storage.external.js",()=>require("next/dist/server/app-render/work-unit-async-storage.external.js"))},20635,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/action-async-storage.external.js",()=>require("next/dist/server/app-render/action-async-storage.external.js"))},24725,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/after-task-async-storage.external.js",()=>require("next/dist/server/app-render/after-task-async-storage.external.js"))},24361,(e,t,r)=>{t.exports=e.x("util",()=>require("util"))},14747,(e,t,r)=>{t.exports=e.x("path",()=>require("path"))},29173,(e,t,r)=>{t.exports=e.x("@prisma/client",()=>require("@prisma/client"))},70406,(e,t,r)=>{t.exports=e.x("next/dist/compiled/@opentelemetry/api",()=>require("next/dist/compiled/@opentelemetry/api"))},93695,(e,t,r)=>{t.exports=e.x("next/dist/shared/lib/no-fallback-error.external.js",()=>require("next/dist/shared/lib/no-fallback-error.external.js"))},61724,(e,t,r)=>{t.exports=e.x("next/dist/compiled/next-server/app-route-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-route-turbo.runtime.prod.js"))},54799,(e,t,r)=>{t.exports=e.x("crypto",()=>require("crypto"))},92509,(e,t,r)=>{t.exports=e.x("url",()=>require("url"))},21517,(e,t,r)=>{t.exports=e.x("http",()=>require("http"))},49719,(e,t,r)=>{t.exports=e.x("assert",()=>require("assert"))},45706,(e,t,r)=>{t.exports=e.x("querystring",()=>require("querystring"))},874,(e,t,r)=>{t.exports=e.x("buffer",()=>require("buffer"))},6461,(e,t,r)=>{t.exports=e.x("zlib",()=>require("zlib"))},24836,(e,t,r)=>{t.exports=e.x("https",()=>require("https"))},27699,(e,t,r)=>{t.exports=e.x("events",()=>require("events"))},12756,e=>{e.v(JSON.parse('{"name":"openid-client","version":"5.7.1","description":"OpenID Connect Relying Party (RP, Client) implementation for Node.js runtime, supports passportjs","keywords":["auth","authentication","basic","certified","client","connect","dynamic","electron","hybrid","identity","implicit","oauth","oauth2","oidc","openid","passport","relying party","strategy"],"homepage":"https://github.com/panva/openid-client","repository":"panva/openid-client","funding":{"url":"https://github.com/sponsors/panva"},"license":"MIT","author":"Filip Skokan <<EMAIL>>","exports":{"types":"./types/index.d.ts","import":"./lib/index.mjs","require":"./lib/index.js"},"main":"./lib/index.js","types":"./types/index.d.ts","files":["lib","types/index.d.ts"],"scripts":{"format":"npx prettier --loglevel silent --write ./lib ./test ./certification ./types","test":"mocha test/**/*.test.js"},"dependencies":{"jose":"^4.15.9","lru-cache":"^6.0.0","object-hash":"^2.2.0","oidc-token-hash":"^5.0.3"},"devDependencies":{"@types/node":"^16.18.106","@types/passport":"^1.0.16","base64url":"^3.0.1","chai":"^4.5.0","mocha":"^10.7.3","nock":"^13.5.5","prettier":"^2.8.8","readable-mock-req":"^0.2.2","sinon":"^9.2.4","timekeeper":"^2.3.1"},"standard-version":{"scripts":{"postchangelog":"sed -i \'\' -e \'s/### \\\\[/## [/g\' CHANGELOG.md"},"types":[{"type":"feat","section":"Features"},{"type":"fix","section":"Fixes"},{"type":"chore","hidden":true},{"type":"docs","hidden":true},{"type":"style","hidden":true},{"type":"refactor","section":"Refactor","hidden":false},{"type":"perf","section":"Performance","hidden":false},{"type":"test","hidden":true}]}}'))},12276,e=>{"use strict";e.s(["hasPermission",()=>r]);let t={ADMIN:["users:read","users:write","users:delete","students:read","students:write","students:delete","teachers:read","teachers:write","teachers:delete","classes:read","classes:write","classes:delete","subjects:read","subjects:write","subjects:delete","attendance:read","attendance:write","marks:read","marks:write","reports:read","reports:write","settings:read","settings:write","audit:read"],TEACHER:["students:read","attendance:read","attendance:write","marks:read","marks:write","reports:read"],STUDENT:["attendance:read","marks:read","reports:read"]};function r(e,r){return t[e]?.includes(r)??!1}},75359,(e,t,r)=>{},22734,(e,t,r)=>{t.exports=e.x("fs",()=>require("fs"))},60900,e=>{"use strict";e.s(["handler",()=>T,"patchFetch",()=>A,"routeModule",()=>q,"serverHooks",()=>N,"workAsyncStorage",()=>j,"workUnitAsyncStorage",()=>k],60900);var t=e.i(6137),r=e.i(11365),s=e.i(9638),n=e.i(15243),a=e.i(66378),i=e.i(92101),o=e.i(50012),d=e.i(62885),p=e.i(31409),l=e.i(78448),u=e.i(28015),c=e.i(72721),x=e.i(75714),h=e.i(12634),m=e.i(93695);e.i(74732);var v=e.i(66662);e.s(["GET",()=>E],88485);var y=e.i(2835),f=e.i(58356),g=e.i(43382),R=e.i(12276),w=e.i(22734),b=e.i(14747);async function E(e){try{let t,r,s,n=await (0,f.getServerSession)(g.authOptions);if(!n?.user)return y.NextResponse.json({error:"Unauthorized"},{status:401});if(!(0,R.hasPermission)(n.user.role,"students:read"))return y.NextResponse.json({error:"Forbidden"},{status:403});let{searchParams:a}=new URL(e.url),i=a.get("type")||"xlsx";if("csv"===i?(t=b.join(process.cwd(),"templates","student-import-template.csv"),r="text/csv",s="student-import-template.csv"):(t=b.join(process.cwd(),"templates","student-import-template.xlsx"),r="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",s="student-import-template.xlsx"),!w.existsSync(t))return y.NextResponse.json({error:"Template file not found"},{status:404});let o=w.readFileSync(t);return new y.NextResponse(o,{headers:{"Content-Type":r,"Content-Disposition":`attachment; filename="${s}"`,"Content-Length":o.length.toString()}})}catch(e){return console.error("Error downloading template:",e),y.NextResponse.json({error:"Internal server error"},{status:500})}}var C=e.i(88485);let q=new t.AppRouteRouteModule({definition:{kind:r.RouteKind.APP_ROUTE,page:"/api/admin/students/template/route",pathname:"/api/admin/students/template",filename:"route",bundlePath:""},distDir:".next",relativeProjectDir:"",resolvedPagePath:"[project]/school-management-system/src/app/api/admin/students/template/route.ts",nextConfigOutput:"",userland:C}),{workAsyncStorage:j,workUnitAsyncStorage:k,serverHooks:N}=q;function A(){return(0,s.patchFetch)({workAsyncStorage:j,workUnitAsyncStorage:k})}async function T(e,t,s){var y;let f="/api/admin/students/template/route";f=f.replace(/\/index$/,"")||"/";let g=await q.prepare(e,t,{srcPage:f,multiZoneDraftMode:!1});if(!g)return t.statusCode=400,t.end("Bad Request"),null==s.waitUntil||s.waitUntil.call(s,Promise.resolve()),null;let{buildId:R,params:w,nextConfig:b,isDraftMode:E,prerenderManifest:C,routerServerContext:j,isOnDemandRevalidate:k,revalidateOnlyGenerated:N,resolvedPathname:A}=g,T=(0,i.normalizeAppPath)(f),P=!!(C.dynamicRoutes[T]||C.routes[A]);if(P&&!E){let e=!!C.routes[A],t=C.dynamicRoutes[T];if(t&&!1===t.fallback&&!e)throw new m.NoFallbackError}let S=null;!P||q.isDev||E||(S="/index"===(S=A)?"/":S);let O=!0===q.isDev||!P,_=P&&!O,H=e.method||"GET",U=(0,a.getTracer)(),I=U.getActiveScopeSpan(),D={params:w,prerenderManifest:C,renderOpts:{experimental:{cacheComponents:!!b.experimental.cacheComponents,authInterrupts:!!b.experimental.authInterrupts},supportsDynamicResponse:O,incrementalCache:(0,n.getRequestMeta)(e,"incrementalCache"),cacheLifeProfiles:null==(y=b.experimental)?void 0:y.cacheLife,isRevalidate:_,waitUntil:s.waitUntil,onClose:e=>{t.on("close",e)},onAfterTaskError:void 0,onInstrumentationRequestError:(t,r,s)=>q.onRequestError(e,t,s,j)},sharedContext:{buildId:R}},M=new o.NodeNextRequest(e),F=new o.NodeNextResponse(t),$=d.NextRequestAdapter.fromNodeNextRequest(M,(0,d.signalFromNodeResponse)(t));try{let i=async r=>q.handle($,D).finally(()=>{if(!r)return;r.setAttributes({"http.status_code":t.statusCode,"next.rsc":!1});let s=U.getRootSpanAttributes();if(!s)return;if(s.get("next.span_type")!==p.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${s.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let n=s.get("next.route");if(n){let e=`${H} ${n}`;r.setAttributes({"next.route":n,"http.route":n,"next.span_name":e}),r.updateName(e)}else r.updateName(`${H} ${e.url}`)}),o=async a=>{var o,d;let p=async({previousCacheEntry:r})=>{try{if(!(0,n.getRequestMeta)(e,"minimalMode")&&k&&N&&!r)return t.statusCode=404,t.setHeader("x-nextjs-cache","REVALIDATED"),t.end("This page could not be found"),null;let o=await i(a);e.fetchMetrics=D.renderOpts.fetchMetrics;let d=D.renderOpts.pendingWaitUntil;d&&s.waitUntil&&(s.waitUntil(d),d=void 0);let p=D.renderOpts.collectedTags;if(!P)return await (0,u.sendResponse)(M,F,o,D.renderOpts.pendingWaitUntil),null;{let e=await o.blob(),t=(0,c.toNodeOutgoingHttpHeaders)(o.headers);p&&(t[h.NEXT_CACHE_TAGS_HEADER]=p),!t["content-type"]&&e.type&&(t["content-type"]=e.type);let r=void 0!==D.renderOpts.collectedRevalidate&&!(D.renderOpts.collectedRevalidate>=h.INFINITE_CACHE)&&D.renderOpts.collectedRevalidate,s=void 0===D.renderOpts.collectedExpire||D.renderOpts.collectedExpire>=h.INFINITE_CACHE?void 0:D.renderOpts.collectedExpire;return{value:{kind:v.CachedRouteKind.APP_ROUTE,status:o.status,body:Buffer.from(await e.arrayBuffer()),headers:t},cacheControl:{revalidate:r,expire:s}}}}catch(t){throw(null==r?void 0:r.isStale)&&await q.onRequestError(e,t,{routerKind:"App Router",routePath:f,routeType:"route",revalidateReason:(0,l.getRevalidateReason)({isRevalidate:_,isOnDemandRevalidate:k})},j),t}},m=await q.handleResponse({req:e,nextConfig:b,cacheKey:S,routeKind:r.RouteKind.APP_ROUTE,isFallback:!1,prerenderManifest:C,isRoutePPREnabled:!1,isOnDemandRevalidate:k,revalidateOnlyGenerated:N,responseGenerator:p,waitUntil:s.waitUntil});if(!P)return null;if((null==m||null==(o=m.value)?void 0:o.kind)!==v.CachedRouteKind.APP_ROUTE)throw Object.defineProperty(Error(`Invariant: app-route received invalid cache entry ${null==m||null==(d=m.value)?void 0:d.kind}`),"__NEXT_ERROR_CODE",{value:"E701",enumerable:!1,configurable:!0});(0,n.getRequestMeta)(e,"minimalMode")||t.setHeader("x-nextjs-cache",k?"REVALIDATED":m.isMiss?"MISS":m.isStale?"STALE":"HIT"),E&&t.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");let y=(0,c.fromNodeOutgoingHttpHeaders)(m.value.headers);return(0,n.getRequestMeta)(e,"minimalMode")&&P||y.delete(h.NEXT_CACHE_TAGS_HEADER),!m.cacheControl||t.getHeader("Cache-Control")||y.get("Cache-Control")||y.set("Cache-Control",(0,x.getCacheControlHeader)(m.cacheControl)),await (0,u.sendResponse)(M,F,new Response(m.value.body,{headers:y,status:m.value.status||200})),null};I?await o(I):await U.withPropagatedContext(e.headers,()=>U.trace(p.BaseServerSpan.handleRequest,{spanName:`${H} ${e.url}`,kind:a.SpanKind.SERVER,attributes:{"http.method":H,"http.target":e.url}},o))}catch(t){if(I||t instanceof m.NoFallbackError||await q.onRequestError(e,t,{routerKind:"App Router",routePath:T,routeType:"route",revalidateReason:(0,l.getRevalidateReason)({isRevalidate:_,isOnDemandRevalidate:k})}),P)throw t;return await (0,u.sendResponse)(M,F,new Response(null,{status:500})),null}}}];

//# sourceMappingURL=%5Broot-of-the-server%5D__02f87545._.js.map