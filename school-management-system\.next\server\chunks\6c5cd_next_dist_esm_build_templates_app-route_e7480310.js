module.exports=[82612,e=>{"use strict";let r;e.s(["handler",()=>s4,"patchFetch",()=>s2,"routeModule",()=>sZ,"serverHooks",()=>s0,"workAsyncStorage",()=>sQ,"workUnitAsyncStorage",()=>s1],82612);var t,a,n,s=e.i(6137),i=e.i(11365),c=e.i(9638),o=e.i(15243),l=e.i(66378),f=e.i(92101),h=e.i(50012),u=e.i(62885),d=e.i(31409),p=e.i(78448),m=e.i(28015),g=e.i(72721),v=e.i(75714),T=e.i(12634),b=e.i(93695);e.i(74732);var E=e.i(66662);e.s(["GET",()=>sX,"POST",()=>sq],82131);var w=e.i(2835),A=e.i(58356),S=e.i(43382),k=e.i(31279),y=e.i(12276),C=e.i(24638),R={};R.version="0.18.5";var O=1200,x=1252,_=[874,932,936,949,950,1250,1251,1252,1253,1254,1255,1256,1257,1258,1e4],I={0:1252,1:65001,2:65001,77:1e4,128:932,129:949,130:1361,134:936,136:950,161:1253,162:1254,163:1258,177:1255,178:1256,186:1257,204:1251,222:874,238:1250,255:1252,69:6969},N=function(e){-1!=_.indexOf(e)&&(x=I[0]=e)},D=function(e){O=e,N(e)};function F(){D(1200),N(1252)}function P(e){for(var r=[],t=0,a=e.length;t<a;++t)r[t]=e.charCodeAt(t);return r}function L(e){for(var r=[],t=0;t<e.length>>1;++t)r[t]=String.fromCharCode(e.charCodeAt(2*t+1)+(e.charCodeAt(2*t)<<8));return r.join("")}var M=function(e){var r=e.charCodeAt(0),t=e.charCodeAt(1);if(255==r&&254==t){for(var a=e.slice(2),n=[],s=0;s<a.length>>1;++s)n[s]=String.fromCharCode(a.charCodeAt(2*s)+(a.charCodeAt(2*s+1)<<8));return n.join("")}return 254==r&&255==t?L(e.slice(2)):65279==r?e.slice(1):e},U=function(e){return String.fromCharCode(e)},B=function(e){return String.fromCharCode(e)},H="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";function W(e){for(var r="",t=0,a=0,n=0,s=0,i=0,c=0,o=0,l=0;l<e.length;)s=(t=e.charCodeAt(l++))>>2,i=(3&t)<<4|(a=e.charCodeAt(l++))>>4,c=(15&a)<<2|(n=e.charCodeAt(l++))>>6,o=63&n,isNaN(a)?c=o=64:isNaN(n)&&(o=64),r+=H.charAt(s)+H.charAt(i)+H.charAt(c)+H.charAt(o);return r}function V(e){var r="",t=0,a=0,n=0,s=0,i=0,c=0;e=e.replace(/[^\w\+\/\=]/g,"");for(var o=0;o<e.length;)r+=String.fromCharCode((n=H.indexOf(e.charAt(o++)))<<2|(s=H.indexOf(e.charAt(o++)))>>4),t=(15&s)<<4|(i=H.indexOf(e.charAt(o++)))>>2,64!==i&&(r+=String.fromCharCode(t)),a=(3&i)<<6|(c=H.indexOf(e.charAt(o++))),64!==c&&(r+=String.fromCharCode(a));return r}var G="undefined"!=typeof Buffer&&"undefined"!=typeof process&&void 0!==process.versions&&!!process.versions.node,z=function(){if("undefined"!=typeof Buffer){var e=!Buffer.from;if(!e)try{Buffer.from("foo","utf8")}catch(r){e=!0}return e?function(e,r){return r?new Buffer(e,r):new Buffer(e)}:Buffer.from.bind(Buffer)}return function(){}}();function Y(e){return G?Buffer.alloc?Buffer.alloc(e):new Buffer(e):"undefined"!=typeof Uint8Array?new Uint8Array(e):Array(e)}function j(e){return G?Buffer.allocUnsafe?Buffer.allocUnsafe(e):new Buffer(e):"undefined"!=typeof Uint8Array?new Uint8Array(e):Array(e)}var K=function(e){return G?z(e,"binary"):e.split("").map(function(e){return 255&e.charCodeAt(0)})};function X(e){if(Array.isArray(e))return e.map(function(e){return String.fromCharCode(e)}).join("");for(var r=[],t=0;t<e.length;++t)r[t]=String.fromCharCode(e[t]);return r.join("")}function q(e){if("undefined"==typeof ArrayBuffer)throw Error("Unsupported");if(e instanceof ArrayBuffer)return q(new Uint8Array(e));for(var r=Array(e.length),t=0;t<e.length;++t)r[t]=e[t];return r}var J=G?function(e){return Buffer.concat(e.map(function(e){return Buffer.isBuffer(e)?e:z(e)}))}:function(e){if("undefined"!=typeof Uint8Array){var r=0,t=0;for(r=0;r<e.length;++r)t+=e[r].length;var a=new Uint8Array(t),n=0;for(r=0,t=0;r<e.length;t+=n,++r)if(n=e[r].length,e[r]instanceof Uint8Array)a.set(e[r],t);else if("string"==typeof e[r])throw"wtf";else a.set(new Uint8Array(e[r]),t);return a}return[].concat.apply([],e.map(function(e){return Array.isArray(e)?e:[].slice.call(e)}))},Z=/\u0000/g,Q=/[\u0001-\u0006]/g;function ee(e){for(var r="",t=e.length-1;t>=0;)r+=e.charAt(t--);return r}function er(e,r){var t=""+e;return t.length>=r?t:e0("0",r-t.length)+t}function et(e,r){var t=""+e;return t.length>=r?t:e0(" ",r-t.length)+t}function ea(e,r){var t=""+e;return t.length>=r?t:t+e0(" ",r-t.length)}function en(e,r){var t,a;return e>0x100000000||e<-0x100000000?(t=""+Math.round(e)).length>=r?t:e0("0",r-t.length)+t:(a=""+Math.round(e)).length>=r?a:e0("0",r-a.length)+a}function es(e,r){return r=r||0,e.length>=7+r&&(32|e.charCodeAt(r))==103&&(32|e.charCodeAt(r+1))==101&&(32|e.charCodeAt(r+2))==110&&(32|e.charCodeAt(r+3))==101&&(32|e.charCodeAt(r+4))==114&&(32|e.charCodeAt(r+5))==97&&(32|e.charCodeAt(r+6))==108}var ei=[["Sun","Sunday"],["Mon","Monday"],["Tue","Tuesday"],["Wed","Wednesday"],["Thu","Thursday"],["Fri","Friday"],["Sat","Saturday"]],ec=[["J","Jan","January"],["F","Feb","February"],["M","Mar","March"],["A","Apr","April"],["M","May","May"],["J","Jun","June"],["J","Jul","July"],["A","Aug","August"],["S","Sep","September"],["O","Oct","October"],["N","Nov","November"],["D","Dec","December"]],eo={0:"General",1:"0",2:"0.00",3:"#,##0",4:"#,##0.00",9:"0%",10:"0.00%",11:"0.00E+00",12:"# ?/?",13:"# ??/??",14:"m/d/yy",15:"d-mmm-yy",16:"d-mmm",17:"mmm-yy",18:"h:mm AM/PM",19:"h:mm:ss AM/PM",20:"h:mm",21:"h:mm:ss",22:"m/d/yy h:mm",37:"#,##0 ;(#,##0)",38:"#,##0 ;[Red](#,##0)",39:"#,##0.00;(#,##0.00)",40:"#,##0.00;[Red](#,##0.00)",45:"mm:ss",46:"[h]:mm:ss",47:"mmss.0",48:"##0.0E+0",49:"@",56:'"上午/下午 "hh"時"mm"分"ss"秒 "'},el={5:37,6:38,7:39,8:40,23:0,24:0,25:0,26:0,27:14,28:14,29:14,30:14,31:14,50:14,51:14,52:14,53:14,54:14,55:14,56:14,57:14,58:14,59:1,60:2,61:3,62:4,67:9,68:10,69:12,70:13,71:14,72:14,73:15,74:16,75:17,76:20,77:21,78:22,79:45,80:46,81:47,82:0},ef={5:'"$"#,##0_);\\("$"#,##0\\)',63:'"$"#,##0_);\\("$"#,##0\\)',6:'"$"#,##0_);[Red]\\("$"#,##0\\)',64:'"$"#,##0_);[Red]\\("$"#,##0\\)',7:'"$"#,##0.00_);\\("$"#,##0.00\\)',65:'"$"#,##0.00_);\\("$"#,##0.00\\)',8:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',66:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',41:'_(* #,##0_);_(* \\(#,##0\\);_(* "-"_);_(@_)',42:'_("$"* #,##0_);_("$"* \\(#,##0\\);_("$"* "-"_);_(@_)',43:'_(* #,##0.00_);_(* \\(#,##0.00\\);_(* "-"??_);_(@_)',44:'_("$"* #,##0.00_);_("$"* \\(#,##0.00\\);_("$"* "-"??_);_(@_)'};function eh(e,r,t){for(var a=e<0?-1:1,n=e*a,s=0,i=1,c=0,o=1,l=0,f=0,h=Math.floor(n);l<r&&(c=(h=Math.floor(n))*i+s,f=h*l+o,!(n-h<5e-8));)n=1/(n-h),s=i,i=c,o=l,l=f;if(f>r&&(l>r?(f=o,c=s):(f=l,c=i)),!t)return[0,a*c,f];var u=Math.floor(a*c/f);return[u,a*c-u*f,f]}function eu(e,r,t){if(e>2958465||e<0)return null;var a=0|e,n=Math.floor(86400*(e-a)),s=0,i=[],c={D:a,T:n,u:86400*(e-a)-n,y:0,m:0,d:0,H:0,M:0,S:0,q:0};if(1e-6>Math.abs(c.u)&&(c.u=0),r&&r.date1904&&(a+=1462),c.u>.9999&&(c.u=0,86400==++n&&(c.T=n=0,++a,++c.D)),60===a)i=t?[1317,10,29]:[1900,2,29],s=3;else if(0===a)i=t?[1317,8,29]:[1900,1,0],s=6;else{a>60&&--a;var o,l,f,h=new Date(1900,0,1);h.setDate(h.getDate()+a-1),i=[h.getFullYear(),h.getMonth()+1,h.getDate()],s=h.getDay(),a<60&&(s=(s+6)%7),t&&(o=h,l=i,l[0]-=581,f=o.getDay(),o<60&&(f=(f+6)%7),s=f)}return c.y=i[0],c.m=i[1],c.d=i[2],c.S=n%60,c.M=(n=Math.floor(n/60))%60,c.H=n=Math.floor(n/60),c.q=s,c}var ed=new Date(1899,11,31,0,0,0),ep=ed.getTime(),em=new Date(1900,2,1,0,0,0);function eg(e,r){var t=e.getTime();return r?t-=1262304e5:e>=em&&(t+=864e5),(t-(ep+(e.getTimezoneOffset()-ed.getTimezoneOffset())*6e4))/864e5}function ev(e){return -1==e.indexOf(".")?e:e.replace(/(?:\.0*|(\.\d*[1-9])0+)$/,"$1")}function eT(e){var r,t,a,n,s,i=Math.floor(Math.log(Math.abs(e))*Math.LOG10E);return i>=-4&&i<=-1?s=e.toPrecision(10+i):9>=Math.abs(i)?(r=e<0?12:11,s=(t=ev(e.toFixed(12))).length<=r||(t=e.toPrecision(10)).length<=r?t:e.toExponential(5)):s=10===i?e.toFixed(10).substr(0,12):(a=ev(e.toFixed(11))).length>(e<0?12:11)||"0"===a||"-0"===a?e.toPrecision(6):a,ev(-1==(n=s.toUpperCase()).indexOf("E")?n:n.replace(/(?:\.0*|(\.\d*[1-9])0+)[Ee]/,"$1E").replace(/(E[+-])(\d)$/,"$10$2"))}function eb(e,r){switch(typeof e){case"string":return e;case"boolean":return e?"TRUE":"FALSE";case"number":return(0|e)===e?e.toString(10):eT(e);case"undefined":return"";case"object":if(null==e)return"";if(e instanceof Date)return eF(14,eg(e,r&&r.date1904),r)}throw Error("unsupported value in General format: "+e)}function eE(e){if(e.length<=3)return e;for(var r=e.length%3,t=e.substr(0,r);r!=e.length;r+=3)t+=(t.length>0?",":"")+e.substr(r,3);return t}var ew=/%/g,eA=/# (\?+)( ?)\/( ?)(\d+)/,eS=/^#*0*\.([0#]+)/,ek=/\).*[0#]/,ey=/\(###\) ###\\?-####/;function eC(e){for(var r,t="",a=0;a!=e.length;++a)switch(r=e.charCodeAt(a)){case 35:break;case 63:t+=" ";break;case 48:t+="0";break;default:t+=String.fromCharCode(r)}return t}function eR(e,r){var t=Math.pow(10,r);return""+Math.round(e*t)/t}function eO(e,r){var t=e-Math.floor(e),a=Math.pow(10,r);return r<(""+Math.round(t*a)).length?0:Math.round(t*a)}function ex(e,r,t){return(0|t)===t?function e(r,t,a){if(40===r.charCodeAt(0)&&!t.match(ek)){var n,s=t.replace(/\( */,"").replace(/ \)/,"").replace(/\)/,"");return a>=0?e("n",s,a):"("+e("n",s,-a)+")"}if(44===t.charCodeAt(t.length-1)){for(var i=t,c=i.length-1;44===i.charCodeAt(c-1);)--c;return ex(r,i.substr(0,c),a/Math.pow(10,3*(i.length-c)))}if(-1!==t.indexOf("%"))return l=(o=t).replace(ew,""),f=o.length-l.length,ex(r,l,a*Math.pow(10,2*f))+e0("%",f);if(-1!==t.indexOf("E"))return function e(r,t){var a,n=r.indexOf("E")-r.indexOf(".")-1;if(r.match(/^#+0.0E\+0$/)){if(0==t)return"0.0E+0";if(t<0)return"-"+e(r,-t);var s=r.indexOf(".");-1===s&&(s=r.indexOf("E"));var i=Math.floor(Math.log(t)*Math.LOG10E)%s;if(i<0&&(i+=s),!(a=(t/Math.pow(10,i)).toPrecision(n+1+(s+i)%s)).match(/[Ee]/)){var c=Math.floor(Math.log(t)*Math.LOG10E);-1===a.indexOf(".")?a=a.charAt(0)+"."+a.substr(1)+"E+"+(c-a.length+i):a+="E+"+(c-i),a=a.replace(/\+-/,"-")}a=a.replace(/^([+-]?)(\d*)\.(\d*)[Ee]/,function(e,r,t,a){return r+t+a.substr(0,(s+i)%s)+"."+a.substr(i)+"E"})}else a=t.toExponential(n);return r.match(/E\+00$/)&&a.match(/e[+-]\d$/)&&(a=a.substr(0,a.length-1)+"0"+a.charAt(a.length-1)),r.match(/E\-/)&&a.match(/e\+/)&&(a=a.replace(/e\+/,"e")),a.replace("e","E")}(t,a);if(36===t.charCodeAt(0))return"$"+e(r,t.substr(" "==t.charAt(1)?2:1),a);var o,l,f,h,u,d,p,m=Math.abs(a),g=a<0?"-":"";if(t.match(/^00+$/))return g+er(m,t.length);if(t.match(/^[#?]+$/))return h=""+a,0===a&&(h=""),h.length>t.length?h:eC(t.substr(0,t.length-h.length))+h;if(u=t.match(eA))return g+(0===m?"":""+m)+e0(" ",(n=u)[1].length+2+n[4].length);if(t.match(/^#+0+$/))return g+er(m,t.length-t.indexOf("0"));if(u=t.match(eS))return h=(h=(""+a).replace(/^([^\.]+)$/,"$1."+eC(u[1])).replace(/\.$/,"."+eC(u[1]))).replace(/\.(\d*)$/,function(e,r){return"."+r+e0("0",eC(u[1]).length-r.length)}),-1!==t.indexOf("0.")?h:h.replace(/^0\./,".");if(u=(t=t.replace(/^#+([0.])/,"$1")).match(/^(0*)\.(#*)$/))return g+(""+m).replace(/\.(\d*[1-9])0*$/,".$1").replace(/^(-?\d*)$/,"$1.").replace(/^0\./,u[1].length?"0.":".");if(u=t.match(/^#{1,3},##0(\.?)$/))return g+eE(""+m);if(u=t.match(/^#,##0\.([#0]*0)$/))return a<0?"-"+e(r,t,-a):eE(""+a)+"."+e0("0",u[1].length);if(u=t.match(/^#,#*,#0/))return e(r,t.replace(/^#,#*,/,""),a);if(u=t.match(/^([0#]+)(\\?-([0#]+))+$/))return h=ee(e(r,t.replace(/[\\-]/g,""),a)),d=0,ee(ee(t.replace(/\\/g,"")).replace(/[0#]/g,function(e){return d<h.length?h.charAt(d++):"0"===e?"0":""}));if(t.match(ey))return"("+(h=e(r,"##########",a)).substr(0,3)+") "+h.substr(3,3)+"-"+h.substr(6);var v="";if(u=t.match(/^([#0?]+)( ?)\/( ?)([#0?]+)/))return p=eh(m,Math.pow(10,d=Math.min(u[4].length,7))-1,!1),h=""+g," "==(v=ex("n",u[1],p[1])).charAt(v.length-1)&&(v=v.substr(0,v.length-1)+"0"),h+=v+u[2]+"/"+u[3],(v=ea(p[2],d)).length<u[4].length&&(v=eC(u[4].substr(u[4].length-v.length))+v),h+=v;if(u=t.match(/^# ([#0?]+)( ?)\/( ?)([#0?]+)/))return g+((p=eh(m,Math.pow(10,d=Math.min(Math.max(u[1].length,u[4].length),7))-1,!0))[0]||(p[1]?"":"0"))+" "+(p[1]?et(p[1],d)+u[2]+"/"+u[3]+ea(p[2],d):e0(" ",2*d+1+u[2].length+u[3].length));if(u=t.match(/^[#0?]+$/))return(h=""+a,t.length<=h.length)?h:eC(t.substr(0,t.length-h.length))+h;if(u=t.match(/^([#0]+)\.([#0]+)$/)){d=(h=""+a.toFixed(Math.min(u[2].length,10)).replace(/([^0])0+$/,"$1")).indexOf(".");var T=t.indexOf(".")-d,b=t.length-h.length-T;return eC(t.substr(0,T)+h+t.substr(t.length-b))}if(u=t.match(/^00,000\.([#0]*0)$/))return a<0?"-"+e(r,t,-a):eE(""+a).replace(/^\d,\d{3}$/,"0$&").replace(/^\d*$/,function(e){return"00,"+(e.length<3?er(0,3-e.length):"")+e})+"."+er(0,u[1].length);switch(t){case"###,###":case"##,###":case"#,###":var E=eE(""+m);return"0"!==E?g+E:"";default:if(t.match(/\.[0#?]*$/))return e(r,t.slice(0,t.lastIndexOf(".")),a)+eC(t.slice(t.lastIndexOf(".")))}throw Error("unsupported format |"+t+"|")}(e,r,t):function e(r,t,a){if(40===r.charCodeAt(0)&&!t.match(ek)){var n,s,i,c,o,l,f=t.replace(/\( */,"").replace(/ \)/,"").replace(/\)/,"");return a>=0?e("n",f,a):"("+e("n",f,-a)+")"}if(44===t.charCodeAt(t.length-1)){for(var h=t,u=h.length-1;44===h.charCodeAt(u-1);)--u;return ex(r,h.substr(0,u),a/Math.pow(10,3*(h.length-u)))}if(-1!==t.indexOf("%"))return p=(d=t).replace(ew,""),m=d.length-p.length,ex(r,p,a*Math.pow(10,2*m))+e0("%",m);if(-1!==t.indexOf("E"))return function e(r,t){var a,n=r.indexOf("E")-r.indexOf(".")-1;if(r.match(/^#+0.0E\+0$/)){if(0==t)return"0.0E+0";if(t<0)return"-"+e(r,-t);var s=r.indexOf(".");-1===s&&(s=r.indexOf("E"));var i=Math.floor(Math.log(t)*Math.LOG10E)%s;if(i<0&&(i+=s),-1===(a=(t/Math.pow(10,i)).toPrecision(n+1+(s+i)%s)).indexOf("e")){var c=Math.floor(Math.log(t)*Math.LOG10E);for(-1===a.indexOf(".")?a=a.charAt(0)+"."+a.substr(1)+"E+"+(c-a.length+i):a+="E+"+(c-i);"0."===a.substr(0,2);)a=(a=a.charAt(0)+a.substr(2,s)+"."+a.substr(2+s)).replace(/^0+([1-9])/,"$1").replace(/^0+\./,"0.");a=a.replace(/\+-/,"-")}a=a.replace(/^([+-]?)(\d*)\.(\d*)[Ee]/,function(e,r,t,a){return r+t+a.substr(0,(s+i)%s)+"."+a.substr(i)+"E"})}else a=t.toExponential(n);return r.match(/E\+00$/)&&a.match(/e[+-]\d$/)&&(a=a.substr(0,a.length-1)+"0"+a.charAt(a.length-1)),r.match(/E\-/)&&a.match(/e\+/)&&(a=a.replace(/e\+/,"e")),a.replace("e","E")}(t,a);if(36===t.charCodeAt(0))return"$"+e(r,t.substr(" "==t.charAt(1)?2:1),a);var d,p,m,g,v,T,b,E=Math.abs(a),w=a<0?"-":"";if(t.match(/^00+$/))return w+en(E,t.length);if(t.match(/^[#?]+$/))return"0"===(g=en(a,0))&&(g=""),g.length>t.length?g:eC(t.substr(0,t.length-g.length))+g;if(v=t.match(eA))return c=Math.floor((i=Math.round(E*(s=parseInt((n=v)[4],10))))/s),o=i-c*s,w+(0===c?"":""+c)+" "+(0===o?e0(" ",n[1].length+1+n[4].length):et(o,n[1].length)+n[2]+"/"+n[3]+er(s,n[4].length));if(t.match(/^#+0+$/))return w+en(E,t.length-t.indexOf("0"));if(v=t.match(eS))return g=eR(a,v[1].length).replace(/^([^\.]+)$/,"$1."+eC(v[1])).replace(/\.$/,"."+eC(v[1])).replace(/\.(\d*)$/,function(e,r){return"."+r+e0("0",eC(v[1]).length-r.length)}),-1!==t.indexOf("0.")?g:g.replace(/^0\./,".");if(v=(t=t.replace(/^#+([0.])/,"$1")).match(/^(0*)\.(#*)$/))return w+eR(E,v[2].length).replace(/\.(\d*[1-9])0*$/,".$1").replace(/^(-?\d*)$/,"$1.").replace(/^0\./,v[1].length?"0.":".");if(v=t.match(/^#{1,3},##0(\.?)$/))return w+eE(en(E,0));if(v=t.match(/^#,##0\.([#0]*0)$/))return a<0?"-"+e(r,t,-a):eE(""+(Math.floor(a)+ +((l=v[1].length)<(""+Math.round((a-Math.floor(a))*Math.pow(10,l))).length)))+"."+er(eO(a,v[1].length),v[1].length);if(v=t.match(/^#,#*,#0/))return e(r,t.replace(/^#,#*,/,""),a);if(v=t.match(/^([0#]+)(\\?-([0#]+))+$/))return g=ee(e(r,t.replace(/[\\-]/g,""),a)),T=0,ee(ee(t.replace(/\\/g,"")).replace(/[0#]/g,function(e){return T<g.length?g.charAt(T++):"0"===e?"0":""}));if(t.match(ey))return"("+(g=e(r,"##########",a)).substr(0,3)+") "+g.substr(3,3)+"-"+g.substr(6);var A="";if(v=t.match(/^([#0?]+)( ?)\/( ?)([#0?]+)/))return b=eh(E,Math.pow(10,T=Math.min(v[4].length,7))-1,!1),g=""+w," "==(A=ex("n",v[1],b[1])).charAt(A.length-1)&&(A=A.substr(0,A.length-1)+"0"),g+=A+v[2]+"/"+v[3],(A=ea(b[2],T)).length<v[4].length&&(A=eC(v[4].substr(v[4].length-A.length))+A),g+=A;if(v=t.match(/^# ([#0?]+)( ?)\/( ?)([#0?]+)/))return w+((b=eh(E,Math.pow(10,T=Math.min(Math.max(v[1].length,v[4].length),7))-1,!0))[0]||(b[1]?"":"0"))+" "+(b[1]?et(b[1],T)+v[2]+"/"+v[3]+ea(b[2],T):e0(" ",2*T+1+v[2].length+v[3].length));if(v=t.match(/^[#0?]+$/))return(g=en(a,0),t.length<=g.length)?g:eC(t.substr(0,t.length-g.length))+g;if(v=t.match(/^([#0?]+)\.([#0]+)$/)){T=(g=""+a.toFixed(Math.min(v[2].length,10)).replace(/([^0])0+$/,"$1")).indexOf(".");var S=t.indexOf(".")-T,k=t.length-g.length-S;return eC(t.substr(0,S)+g+t.substr(t.length-k))}if(v=t.match(/^00,000\.([#0]*0)$/))return T=eO(a,v[1].length),a<0?"-"+e(r,t,-a):eE(a<0x7fffffff&&a>-0x80000000?""+(a>=0?0|a:a-1|0):""+Math.floor(a)).replace(/^\d,\d{3}$/,"0$&").replace(/^\d*$/,function(e){return"00,"+(e.length<3?er(0,3-e.length):"")+e})+"."+er(T,v[1].length);switch(t){case"###,##0.00":return e(r,"#,##0.00",a);case"###,###":case"##,###":case"#,###":var y=eE(en(E,0));return"0"!==y?w+y:"";case"###,###.00":return e(r,"###,##0.00",a).replace(/^0\./,".");case"#,###.00":return e(r,"#,##0.00",a).replace(/^0\./,".")}throw Error("unsupported format |"+t+"|")}(e,r,t)}var e_=/\[[HhMmSs\u0E0A\u0E19\u0E17]*\]/;function eI(e){for(var r=0,t="",a="";r<e.length;)switch(t=e.charAt(r)){case"G":es(e,r)&&(r+=6),r++;break;case'"':for(;34!==e.charCodeAt(++r)&&r<e.length;);++r;break;case"\\":case"_":r+=2;break;case"@":++r;break;case"B":case"b":if("1"===e.charAt(r+1)||"2"===e.charAt(r+1))return!0;case"M":case"D":case"Y":case"H":case"S":case"E":case"m":case"d":case"y":case"h":case"s":case"e":case"g":return!0;case"A":case"a":case"上":if("A/P"===e.substr(r,3).toUpperCase()||"AM/PM"===e.substr(r,5).toUpperCase()||"上午/下午"===e.substr(r,5).toUpperCase())return!0;++r;break;case"[":for(a=t;"]"!==e.charAt(r++)&&r<e.length;)a+=e.charAt(r);if(a.match(e_))return!0;break;case".":case"0":case"#":for(;r<e.length&&("0#?.,E+-%".indexOf(t=e.charAt(++r))>-1||"\\"==t&&"-"==e.charAt(r+1)&&"0#".indexOf(e.charAt(r+2))>-1););break;case"?":for(;e.charAt(++r)===t;);break;case"*":++r,(" "==e.charAt(r)||"*"==e.charAt(r))&&++r;break;case"(":case")":case" ":default:++r;break;case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":for(;r<e.length&&"0123456789".indexOf(e.charAt(++r))>-1;);}return!1}var eN=/\[(=|>[=]?|<[>=]?)(-?\d+(?:\.\d*)?)\]/;function eD(e,r){if(null==r)return!1;var t=parseFloat(r[2]);switch(r[1]){case"=":if(e==t)return!0;break;case">":if(e>t)return!0;break;case"<":if(e<t)return!0;break;case"<>":if(e!=t)return!0;break;case">=":if(e>=t)return!0;break;case"<=":if(e<=t)return!0}return!1}function eF(e,r,t){null==t&&(t={});var a="";switch(typeof e){case"string":a="m/d/yy"==e&&t.dateNF?t.dateNF:e;break;case"number":null==(a=14==e&&t.dateNF?t.dateNF:(null!=t.table?t.table:eo)[e])&&(a=t.table&&t.table[el[e]]||eo[el[e]]),null==a&&(a=ef[e]||"General")}if(es(a,0))return eb(r,t);r instanceof Date&&(r=eg(r,t.date1904));var n=function(e,r){var t=function(e){for(var r=[],t=!1,a=0,n=0;a<e.length;++a)switch(e.charCodeAt(a)){case 34:t=!t;break;case 95:case 42:case 92:++a;break;case 59:r[r.length]=e.substr(n,a-n),n=a+1}if(r[r.length]=e.substr(n),!0===t)throw Error("Format |"+e+"| unterminated string ");return r}(e),a=t.length,n=t[a-1].indexOf("@");if(a<4&&n>-1&&--a,t.length>4)throw Error("cannot find right format for |"+t.join("|")+"|");if("number"!=typeof r)return[4,4===t.length||n>-1?t[t.length-1]:"@"];switch(t.length){case 1:t=n>-1?["General","General","General",t[0]]:[t[0],t[0],t[0],"@"];break;case 2:t=n>-1?[t[0],t[0],t[0],t[1]]:[t[0],t[1],t[0],"@"];break;case 3:t=n>-1?[t[0],t[1],t[0],t[2]]:[t[0],t[1],t[2],"@"]}var s=r>0?t[0]:r<0?t[1]:t[2];if(-1===t[0].indexOf("[")&&-1===t[1].indexOf("["))return[a,s];if(null!=t[0].match(/\[[=<>]/)||null!=t[1].match(/\[[=<>]/)){var i=t[0].match(eN),c=t[1].match(eN);return eD(r,i)?[a,t[0]]:eD(r,c)?[a,t[1]]:[a,t[null!=i&&null!=c?2:1]]}return[a,s]}(a,r);if(es(n[1]))return eb(r,t);if(!0===r)r="TRUE";else if(!1===r)r="FALSE";else if(""===r||null==r)return"";return function(e,r,t,a){for(var n,s,i,c=[],o="",l=0,f="",h="t",u="H";l<e.length;)switch(f=e.charAt(l)){case"G":if(!es(e,l))throw Error("unrecognized character "+f+" in "+e);c[c.length]={t:"G",v:"General"},l+=7;break;case'"':for(o="";34!==(i=e.charCodeAt(++l))&&l<e.length;)o+=String.fromCharCode(i);c[c.length]={t:"t",v:o},++l;break;case"\\":var d=e.charAt(++l),p="("===d||")"===d?d:"t";c[c.length]={t:p,v:d},++l;break;case"_":c[c.length]={t:"t",v:" "},l+=2;break;case"@":c[c.length]={t:"T",v:r},++l;break;case"B":case"b":if("1"===e.charAt(l+1)||"2"===e.charAt(l+1)){if(null==n&&null==(n=eu(r,t,"2"===e.charAt(l+1))))return"";c[c.length]={t:"X",v:e.substr(l,2)},h=f,l+=2;break}case"M":case"D":case"Y":case"H":case"S":case"E":f=f.toLowerCase();case"m":case"d":case"y":case"h":case"s":case"e":case"g":if(r<0||null==n&&null==(n=eu(r,t)))return"";for(o=f;++l<e.length&&e.charAt(l).toLowerCase()===f;)o+=f;"m"===f&&"h"===h.toLowerCase()&&(f="M"),"h"===f&&(f=u),c[c.length]={t:f,v:o},h=f;break;case"A":case"a":case"上":var m={t:f,v:f};if(null==n&&(n=eu(r,t)),"A/P"===e.substr(l,3).toUpperCase()?(null!=n&&(m.v=n.H>=12?"P":"A"),m.t="T",u="h",l+=3):"AM/PM"===e.substr(l,5).toUpperCase()?(null!=n&&(m.v=n.H>=12?"PM":"AM"),m.t="T",l+=5,u="h"):"上午/下午"===e.substr(l,5).toUpperCase()?(null!=n&&(m.v=n.H>=12?"下午":"上午"),m.t="T",l+=5,u="h"):(m.t="t",++l),null==n&&"T"===m.t)return"";c[c.length]=m,h=f;break;case"[":for(o=f;"]"!==e.charAt(l++)&&l<e.length;)o+=e.charAt(l);if("]"!==o.slice(-1))throw'unterminated "[" block: |'+o+"|";if(o.match(e_)){if(null==n&&null==(n=eu(r,t)))return"";c[c.length]={t:"Z",v:o.toLowerCase()},h=o.charAt(1)}else o.indexOf("$")>-1&&(o=(o.match(/\$([^-\[\]]*)/)||[])[1]||"$",eI(e)||(c[c.length]={t:"t",v:o}));break;case".":if(null!=n){for(o=f;++l<e.length&&"0"===(f=e.charAt(l));)o+=f;c[c.length]={t:"s",v:o};break}case"0":case"#":for(o=f;++l<e.length&&"0#?.,E+-%".indexOf(f=e.charAt(l))>-1;)o+=f;c[c.length]={t:"n",v:o};break;case"?":for(o=f;e.charAt(++l)===f;)o+=f;c[c.length]={t:f,v:o},h=f;break;case"*":++l,(" "==e.charAt(l)||"*"==e.charAt(l))&&++l;break;case"(":case")":c[c.length]={t:1===a?"t":f,v:f},++l;break;case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":for(o=f;l<e.length&&"0123456789".indexOf(e.charAt(++l))>-1;)o+=e.charAt(l);c[c.length]={t:"D",v:o};break;case" ":c[c.length]={t:f,v:f},++l;break;case"$":c[c.length]={t:"t",v:"$"},++l;break;default:if(-1===",$-+/():!^&'~{}<>=€acfijklopqrtuvwxzP".indexOf(f))throw Error("unrecognized character "+f+" in "+e);c[c.length]={t:"t",v:f},++l}var g,v=0,T=0;for(l=c.length-1,h="t";l>=0;--l)switch(c[l].t){case"h":case"H":c[l].t=u,h="h",v<1&&(v=1);break;case"s":(g=c[l].v.match(/\.0+$/))&&(T=Math.max(T,g[0].length-1)),v<3&&(v=3);case"d":case"y":case"M":case"e":h=c[l].t;break;case"m":"s"===h&&(c[l].t="M",v<2&&(v=2));break;case"X":break;case"Z":v<1&&c[l].v.match(/[Hh]/)&&(v=1),v<2&&c[l].v.match(/[Mm]/)&&(v=2),v<3&&c[l].v.match(/[Ss]/)&&(v=3)}switch(v){case 0:break;case 1:n.u>=.5&&(n.u=0,++n.S),n.S>=60&&(n.S=0,++n.M),n.M>=60&&(n.M=0,++n.H);break;case 2:n.u>=.5&&(n.u=0,++n.S),n.S>=60&&(n.S=0,++n.M)}var b,E="";for(l=0;l<c.length;++l)switch(c[l].t){case"t":case"T":case" ":case"D":break;case"X":c[l].v="",c[l].t=";";break;case"d":case"m":case"y":case"h":case"H":case"M":case"s":case"e":case"b":case"Z":c[l].v=function(e,r,t,a){var n,s="",i=0,c=0,o=t.y,l=0;switch(e){case 98:o=t.y+543;case 121:switch(r.length){case 1:case 2:n=o%100,l=2;break;default:n=o%1e4,l=4}break;case 109:switch(r.length){case 1:case 2:n=t.m,l=r.length;break;case 3:return ec[t.m-1][1];case 5:return ec[t.m-1][0];default:return ec[t.m-1][2]}break;case 100:switch(r.length){case 1:case 2:n=t.d,l=r.length;break;case 3:return ei[t.q][0];default:return ei[t.q][1]}break;case 104:switch(r.length){case 1:case 2:n=1+(t.H+11)%12,l=r.length;break;default:throw"bad hour format: "+r}break;case 72:switch(r.length){case 1:case 2:n=t.H,l=r.length;break;default:throw"bad hour format: "+r}break;case 77:switch(r.length){case 1:case 2:n=t.M,l=r.length;break;default:throw"bad minute format: "+r}break;case 115:if("s"!=r&&"ss"!=r&&".0"!=r&&".00"!=r&&".000"!=r)throw"bad second format: "+r;if(0===t.u&&("s"==r||"ss"==r))return er(t.S,r.length);if((i=Math.round((c=a>=2?3===a?1e3:100:1===a?10:1)*(t.S+t.u)))>=60*c&&(i=0),"s"===r)return 0===i?"0":""+i/c;if(s=er(i,2+a),"ss"===r)return s.substr(0,2);return"."+s.substr(2,r.length-1);case 90:switch(r){case"[h]":case"[hh]":n=24*t.D+t.H;break;case"[m]":case"[mm]":n=(24*t.D+t.H)*60+t.M;break;case"[s]":case"[ss]":n=((24*t.D+t.H)*60+t.M)*60+Math.round(t.S+t.u);break;default:throw"bad abstime format: "+r}l=3===r.length?1:2;break;case 101:n=o,l=1}return l>0?er(n,l):""}(c[l].t.charCodeAt(0),c[l].v,n,T),c[l].t="t";break;case"n":case"?":for(b=l+1;null!=c[b]&&("?"===(f=c[b].t)||"D"===f||(" "===f||"t"===f)&&null!=c[b+1]&&("?"===c[b+1].t||"t"===c[b+1].t&&"/"===c[b+1].v)||"("===c[l].t&&(" "===f||"n"===f||")"===f)||"t"===f&&("/"===c[b].v||" "===c[b].v&&null!=c[b+1]&&"?"==c[b+1].t));)c[l].v+=c[b].v,c[b]={v:"",t:";"},++b;E+=c[l].v,l=b-1;break;case"G":c[l].t="t",c[l].v=eb(r,t)}var w,A,S="";if(E.length>0){40==E.charCodeAt(0)?(w=r<0&&45===E.charCodeAt(0)?-r:r,A=ex("n",E,w)):(A=ex("n",E,w=r<0&&a>1?-r:r),w<0&&c[0]&&"t"==c[0].t&&(A=A.substr(1),c[0].v="-"+c[0].v)),b=A.length-1;var k=c.length;for(l=0;l<c.length;++l)if(null!=c[l]&&"t"!=c[l].t&&c[l].v.indexOf(".")>-1){k=l;break}var y=c.length;if(k===c.length&&-1===A.indexOf("E")){for(l=c.length-1;l>=0;--l)null!=c[l]&&-1!=="n?".indexOf(c[l].t)&&(b>=c[l].v.length-1?(b-=c[l].v.length,c[l].v=A.substr(b+1,c[l].v.length)):b<0?c[l].v="":(c[l].v=A.substr(0,b+1),b=-1),c[l].t="t",y=l);b>=0&&y<c.length&&(c[y].v=A.substr(0,b+1)+c[y].v)}else if(k!==c.length&&-1===A.indexOf("E")){for(b=A.indexOf(".")-1,l=k;l>=0;--l)if(null!=c[l]&&-1!=="n?".indexOf(c[l].t)){for(s=c[l].v.indexOf(".")>-1&&l===k?c[l].v.indexOf(".")-1:c[l].v.length-1,S=c[l].v.substr(s+1);s>=0;--s)b>=0&&("0"===c[l].v.charAt(s)||"#"===c[l].v.charAt(s))&&(S=A.charAt(b--)+S);c[l].v=S,c[l].t="t",y=l}for(b>=0&&y<c.length&&(c[y].v=A.substr(0,b+1)+c[y].v),b=A.indexOf(".")+1,l=k;l<c.length;++l)if(null!=c[l]&&(-1!=="n?(".indexOf(c[l].t)||l===k)){for(s=c[l].v.indexOf(".")>-1&&l===k?c[l].v.indexOf(".")+1:0,S=c[l].v.substr(0,s);s<c[l].v.length;++s)b<A.length&&(S+=A.charAt(b++));c[l].v=S,c[l].t="t",y=l}}}for(l=0;l<c.length;++l)null!=c[l]&&"n?".indexOf(c[l].t)>-1&&(w=a>1&&r<0&&l>0&&"-"===c[l-1].v?-r:r,c[l].v=ex(c[l].t,c[l].v,w),c[l].t="t");var C="";for(l=0;l!==c.length;++l)null!=c[l]&&(C+=c[l].v);return C}(n[1],r,t,n[0])}function eP(e,r){if("number"!=typeof r){r=+r||-1;for(var t=0;t<392;++t){if(void 0==eo[t]){r<0&&(r=t);continue}if(eo[t]==e){r=t;break}}r<0&&(r=391)}return eo[r]=e,r}function eL(){var e;e||(e={}),e[0]="General",e[1]="0",e[2]="0.00",e[3]="#,##0",e[4]="#,##0.00",e[9]="0%",e[10]="0.00%",e[11]="0.00E+00",e[12]="# ?/?",e[13]="# ??/??",e[14]="m/d/yy",e[15]="d-mmm-yy",e[16]="d-mmm",e[17]="mmm-yy",e[18]="h:mm AM/PM",e[19]="h:mm:ss AM/PM",e[20]="h:mm",e[21]="h:mm:ss",e[22]="m/d/yy h:mm",e[37]="#,##0 ;(#,##0)",e[38]="#,##0 ;[Red](#,##0)",e[39]="#,##0.00;(#,##0.00)",e[40]="#,##0.00;[Red](#,##0.00)",e[45]="mm:ss",e[46]="[h]:mm:ss",e[47]="mmss.0",e[48]="##0.0E+0",e[49]="@",e[56]='"上午/下午 "hh"時"mm"分"ss"秒 "',eo=e}var eM={5:'"$"#,##0_);\\("$"#,##0\\)',6:'"$"#,##0_);[Red]\\("$"#,##0\\)',7:'"$"#,##0.00_);\\("$"#,##0.00\\)',8:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',23:"General",24:"General",25:"General",26:"General",27:"m/d/yy",28:"m/d/yy",29:"m/d/yy",30:"m/d/yy",31:"m/d/yy",32:"h:mm:ss",33:"h:mm:ss",34:"h:mm:ss",35:"h:mm:ss",36:"m/d/yy",41:'_(* #,##0_);_(* (#,##0);_(* "-"_);_(@_)',42:'_("$"* #,##0_);_("$"* (#,##0);_("$"* "-"_);_(@_)',43:'_(* #,##0.00_);_(* (#,##0.00);_(* "-"??_);_(@_)',44:'_("$"* #,##0.00_);_("$"* (#,##0.00);_("$"* "-"??_);_(@_)',50:"m/d/yy",51:"m/d/yy",52:"m/d/yy",53:"m/d/yy",54:"m/d/yy",55:"m/d/yy",56:"m/d/yy",57:"m/d/yy",58:"m/d/yy",59:"0",60:"0.00",61:"#,##0",62:"#,##0.00",63:'"$"#,##0_);\\("$"#,##0\\)',64:'"$"#,##0_);[Red]\\("$"#,##0\\)',65:'"$"#,##0.00_);\\("$"#,##0.00\\)',66:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',67:"0%",68:"0.00%",69:"# ?/?",70:"# ??/??",71:"m/d/yy",72:"m/d/yy",73:"d-mmm-yy",74:"d-mmm",75:"mmm-yy",76:"h:mm",77:"h:mm:ss",78:"m/d/yy h:mm",79:"mm:ss",80:"[h]:mm:ss",81:"mmss.0"},eU=/[dD]+|[mM]+|[yYeE]+|[Hh]+|[Ss]+/g,eB=function(){var e={};e.version="1.2.0";var r=function(){for(var e=0,r=Array(256),t=0;256!=t;++t)e=1&(e=1&(e=1&(e=1&(e=1&(e=1&(e=1&(e=1&(e=t)?-0x12477ce0^e>>>1:e>>>1)?-0x12477ce0^e>>>1:e>>>1)?-0x12477ce0^e>>>1:e>>>1)?-0x12477ce0^e>>>1:e>>>1)?-0x12477ce0^e>>>1:e>>>1)?-0x12477ce0^e>>>1:e>>>1)?-0x12477ce0^e>>>1:e>>>1)?-0x12477ce0^e>>>1:e>>>1,r[t]=e;return"undefined"!=typeof Int32Array?new Int32Array(r):r}(),t=function(e){var r=0,t=0,a=0,n="undefined"!=typeof Int32Array?new Int32Array(4096):Array(4096);for(a=0;256!=a;++a)n[a]=e[a];for(a=0;256!=a;++a)for(t=e[a],r=256+a;r<4096;r+=256)t=n[r]=t>>>8^e[255&t];var s=[];for(a=1;16!=a;++a)s[a-1]="undefined"!=typeof Int32Array?n.subarray(256*a,256*a+256):n.slice(256*a,256*a+256);return s}(r),a=t[0],n=t[1],s=t[2],i=t[3],c=t[4],o=t[5],l=t[6],f=t[7],h=t[8],u=t[9],d=t[10],p=t[11],m=t[12],g=t[13],v=t[14];return e.table=r,e.bstr=function(e,t){for(var a=-1^t,n=0,s=e.length;n<s;)a=a>>>8^r[(a^e.charCodeAt(n++))&255];return~a},e.buf=function(e,t){for(var T=-1^t,b=e.length-15,E=0;E<b;)T=v[e[E++]^255&T]^g[e[E++]^T>>8&255]^m[e[E++]^T>>16&255]^p[e[E++]^T>>>24]^d[e[E++]]^u[e[E++]]^h[e[E++]]^f[e[E++]]^l[e[E++]]^o[e[E++]]^c[e[E++]]^i[e[E++]]^s[e[E++]]^n[e[E++]]^a[e[E++]]^r[e[E++]];for(b+=15;E<b;)T=T>>>8^r[(T^e[E++])&255];return~T},e.str=function(e,t){for(var a=-1^t,n=0,s=e.length,i=0,c=0;n<s;)(i=e.charCodeAt(n++))<128?a=a>>>8^r[(a^i)&255]:i<2048?a=(a=a>>>8^r[(a^(192|i>>6&31))&255])>>>8^r[(a^(128|63&i))&255]:i>=55296&&i<57344?(i=(1023&i)+64,c=1023&e.charCodeAt(n++),a=(a=(a=(a=a>>>8^r[(a^(240|i>>8&7))&255])>>>8^r[(a^(128|i>>2&63))&255])>>>8^r[(a^(128|c>>6&15|(3&i)<<4))&255])>>>8^r[(a^(128|63&c))&255]):a=(a=(a=a>>>8^r[(a^(224|i>>12&15))&255])>>>8^r[(a^(128|i>>6&63))&255])>>>8^r[(a^(128|63&i))&255];return~a},e}(),eH=function(){var e,r,t={};function a(e){if("/"==e.charAt(e.length-1))return -1===e.slice(0,-1).indexOf("/")?e:a(e.slice(0,-1));var r=e.lastIndexOf("/");return -1===r?e:e.slice(0,r+1)}function n(e){if("/"==e.charAt(e.length-1))return n(e.slice(0,-1));var r=e.lastIndexOf("/");return -1===r?e:e.slice(r+1)}function s(e){ts(e,0);for(var r={},t=0;e.l<=e.length-4;){var a=e.read_shift(2),n=e.read_shift(2),s=e.l+n,i={};21589===a&&(1&(t=e.read_shift(1))&&(i.mtime=e.read_shift(4)),n>5&&(2&t&&(i.atime=e.read_shift(4)),4&t&&(i.ctime=e.read_shift(4))),i.mtime&&(i.mt=new Date(1e3*i.mtime))),e.l=s,r[a]=i}return r}function i(){return e||(e={})}function c(e,r){if(80==e[0]&&75==e[1])return ec(e,r);if((32|e[0])==109&&(32|e[1])==105)return function(e,r){if("mime-version:"!=T(e.slice(0,13)).toLowerCase())throw Error("Unsupported MAD header");var t=r&&r.root||"",a=(G&&Buffer.isBuffer(e)?e.toString("binary"):T(e)).split("\r\n"),n=0,s="";for(n=0;n<a.length;++n)if((s=a[n],/^Content-Location:/i.test(s))&&(s=s.slice(s.indexOf("file")),t||(t=s.slice(0,s.lastIndexOf("/")+1)),s.slice(0,t.length)!=t))for(;t.length>0&&(t=(t=t.slice(0,t.length-1)).slice(0,t.lastIndexOf("/")+1),s.slice(0,t.length)!=t););var i=(a[1]||"").match(/boundary="(.*?)"/);if(!i)throw Error("MAD cannot find boundary");var c="--"+(i[1]||""),o={FileIndex:[],FullPaths:[]};l(o);var f,h=0;for(n=0;n<a.length;++n){var u=a[n];(u===c||u===c+"--")&&(h++&&function(e,r,t){for(var a,n="",s="",i="",c=0;c<10;++c){var o=r[c];if(!o||o.match(/^\s*$/))break;var l=o.match(/^(.*?):\s*([^\s].*)$/);if(l)switch(l[1].toLowerCase()){case"content-location":n=l[2].trim();break;case"content-type":i=l[2].trim();break;case"content-transfer-encoding":s=l[2].trim()}}switch(++c,s.toLowerCase()){case"base64":a=K(V(r.slice(c).join("")));break;case"quoted-printable":a=function(e){for(var r=[],t=0;t<e.length;++t){for(var a=e[t];t<=e.length&&"="==a.charAt(a.length-1);)a=a.slice(0,a.length-1)+e[++t];r.push(a)}for(var n=0;n<r.length;++n)r[n]=r[n].replace(/[=][0-9A-Fa-f]{2}/g,function(e){return String.fromCharCode(parseInt(e.slice(1),16))});return K(r.join("\r\n"))}(r.slice(c));break;default:throw Error("Unsupported Content-Transfer-Encoding "+s)}var f=el(e,n.slice(t.length),a,{unsafe:!0});i&&(f.ctype=i)}(o,a.slice(f,n),t),f=n)}return o}(e,r);if(e.length<512)throw Error("CFB file size "+e.length+" < 512");var t=3,a=512,n=0,s=0,i=0,c=0,f=0,h=[],m=e.slice(0,512);ts(m,0);var g=function(e){if(80==e[e.l]&&75==e[e.l+1])return[0,0];e.chk(p,"Header Signature: "),e.l+=16;var r=e.read_shift(2,"u");return[e.read_shift(2,"u"),r]}(m);switch(t=g[0]){case 3:a=512;break;case 4:a=4096;break;case 0:if(0==g[1])return ec(e,r);default:throw Error("Major Version: Expected 3 or 4 saw "+t)}512!==a&&ts(m=e.slice(0,a),28);var v=e.slice(0,a),b=m,E=t,w=9;switch(b.l+=2,w=b.read_shift(2)){case 9:if(3!=E)throw Error("Sector Shift: Expected 9 saw "+w);break;case 12:if(4!=E)throw Error("Sector Shift: Expected 12 saw "+w);break;default:throw Error("Sector Shift: Expected 9 or 12 saw "+w)}b.chk("0600","Mini Sector Shift: "),b.chk("000000000000","Reserved: ");var A=m.read_shift(4,"i");if(3===t&&0!==A)throw Error("# Directory Sectors: Expected 0 saw "+A);m.l+=4,i=m.read_shift(4,"i"),m.l+=4,m.chk("00100000","Mini Stream Cutoff Size: "),c=m.read_shift(4,"i"),n=m.read_shift(4,"i"),f=m.read_shift(4,"i"),s=m.read_shift(4,"i");for(var S=-1,k=0;k<109&&!((S=m.read_shift(4,"i"))<0);++k)h[k]=S;var y=function(e,r){for(var t=Math.ceil(e.length/r)-1,a=[],n=1;n<t;++n)a[n-1]=e.slice(n*r,(n+1)*r);return a[t-1]=e.slice(t*r),a}(e,a);!function e(r,t,a,n,s){var i=d;if(r===d){if(0!==t)throw Error("DIFAT chain shorter than expected")}else if(-1!==r){var c=a[r],o=(n>>>2)-1;if(!c)return;for(var l=0;l<o&&(i=r7(c,4*l))!==d;++l)s.push(i);e(r7(c,n-4),t-1,a,n,s)}}(f,s,y,a,h);var C=function(e,r,t,a){var n=e.length,s=[],i=[],c=[],o=[],l=a-1,f=0,h=0,u=0,d=0;for(f=0;f<n;++f)if(c=[],(u=f+r)>=n&&(u-=n),!i[u]){o=[];var p=[];for(h=u;h>=0;){p[h]=!0,i[h]=!0,c[c.length]=h,o.push(e[h]);var m=t[Math.floor(4*h/a)];if(a<4+(d=4*h&l))throw Error("FAT boundary crossed: "+h+" 4 "+a);if(!e[m]||p[h=r7(e[m],d)])break}s[u]={nodes:c,data:rU([o])}}return s}(y,i,h,a);C[i].name="!Directory",n>0&&c!==d&&(C[c].name="!MiniFAT"),C[h[0]].name="!FAT",C.fat_addrs=h,C.ssz=a;var R=[],O=[],x=[];(function(e,r,t,a,n,s,i,c){for(var l,f=0,h=2*!!a.length,p=r[e].data,m=0,g=0;m<p.length;m+=128){var v=p.slice(m,m+128);ts(v,64),g=v.read_shift(2),l=rH(v,0,g-h),a.push(l);var T={name:l,type:v.read_shift(1),color:v.read_shift(1),L:v.read_shift(4,"i"),R:v.read_shift(4,"i"),C:v.read_shift(4,"i"),clsid:v.read_shift(16),state:v.read_shift(4,"i"),start:0,size:0};0!==v.read_shift(2)+v.read_shift(2)+v.read_shift(2)+v.read_shift(2)&&(T.ct=o(v,v.l-8)),0!==v.read_shift(2)+v.read_shift(2)+v.read_shift(2)+v.read_shift(2)&&(T.mt=o(v,v.l-8)),T.start=v.read_shift(4,"i"),T.size=v.read_shift(4,"i"),T.size<0&&T.start<0&&(T.size=T.type=0,T.start=d,T.name=""),5===T.type?(f=T.start,n>0&&f!==d&&(r[f].name="!StreamData")):T.size>=4096?(T.storage="fat",void 0===r[T.start]&&(r[T.start]=function(e,r,t,a,n){var s=[],i=[];n||(n=[]);var c=a-1,o=0,l=0;for(o=r;o>=0;){n[o]=!0,s[s.length]=o,i.push(e[o]);var f=t[Math.floor(4*o/a)];if(a<4+(l=4*o&c))throw Error("FAT boundary crossed: "+o+" 4 "+a);if(!e[f])break;o=r7(e[f],l)}return{nodes:s,data:rU([i])}}(t,T.start,r.fat_addrs,r.ssz)),r[T.start].name=T.name,T.content=r[T.start].data.slice(0,T.size)):(T.storage="minifat",T.size<0?T.size=0:f!==d&&T.start!==d&&r[f]&&(T.content=function(e,r,t){for(var a=e.start,n=e.size,s=[],i=a;t&&n>0&&i>=0;)s.push(r.slice(i*u,i*u+u)),n-=u,i=r7(t,4*i);return 0===s.length?tc(0):J(s).slice(0,e.size)}(T,r[f].data,(r[c]||{}).data))),T.content&&ts(T.content,0),s[l]=T,i.push(T)}})(i,C,y,R,n,{},O,c),function(e,r,t){for(var a=0,n=0,s=0,i=0,c=0,o=t.length,l=[],f=[];a<o;++a)l[a]=f[a]=a,r[a]=t[a];for(;c<f.length;++c)n=e[a=f[c]].L,s=e[a].R,i=e[a].C,l[a]===a&&(-1!==n&&l[n]!==n&&(l[a]=l[n]),-1!==s&&l[s]!==s&&(l[a]=l[s])),-1!==i&&(l[i]=a),-1!==n&&a!=l[a]&&(l[n]=l[a],f.lastIndexOf(n)<c&&f.push(n)),-1!==s&&a!=l[a]&&(l[s]=l[a],f.lastIndexOf(s)<c&&f.push(s));for(a=1;a<o;++a)l[a]===a&&(-1!==s&&l[s]!==s?l[a]=l[s]:-1!==n&&l[n]!==n&&(l[a]=l[n]));for(a=1;a<o;++a)if(0!==e[a].type){if((c=a)!=l[c])do c=l[c],r[a]=r[c]+"/"+r[a];while(0!==c&&-1!==l[c]&&c!=l[c])l[a]=-1}for(r[0]+="/",a=1;a<o;++a)2!==e[a].type&&(r[a]+="/")}(O,x,R),R.shift();var _={FileIndex:O,FullPaths:x};return r&&r.raw&&(_.raw={header:v,sectors:y}),_}function o(e,r){return new Date((r8(e,r+4)/1e7*0x100000000+r8(e,r)/1e7-0x2b6109100)*1e3)}function l(e,r){var t=r||{},a=t.root||"Root Entry";if(e.FullPaths||(e.FullPaths=[]),e.FileIndex||(e.FileIndex=[]),e.FullPaths.length!==e.FileIndex.length)throw Error("inconsistent CFB structure");0===e.FullPaths.length&&(e.FullPaths[0]=a+"/",e.FileIndex[0]={name:a,type:5}),t.CLSID&&(e.FileIndex[0].clsid=t.CLSID),function(e){var r="\x01Sh33tJ5";if(!eH.find(e,"/"+r)){var t=tc(4);t[0]=55,t[1]=t[3]=50,t[2]=54,e.FileIndex.push({name:r,type:2,content:t,size:4,L:69,R:69,C:69}),e.FullPaths.push(e.FullPaths[0]+r),f(e)}}(e)}function f(e,r){l(e);for(var t=!1,s=!1,i=e.FullPaths.length-1;i>=0;--i){var c=e.FileIndex[i];switch(c.type){case 0:s?t=!0:(e.FileIndex.pop(),e.FullPaths.pop());break;case 1:case 2:case 5:s=!0,isNaN(c.R*c.L*c.C)&&(t=!0),c.R>-1&&c.L>-1&&c.R==c.L&&(t=!0);break;default:t=!0}}if(t||r){var o=new Date(1987,1,19),f=0,h=Object.create?Object.create(null):{},u=[];for(i=0;i<e.FullPaths.length;++i)h[e.FullPaths[i]]=!0,0!==e.FileIndex[i].type&&u.push([e.FullPaths[i],e.FileIndex[i]]);for(i=0;i<u.length;++i){var d=a(u[i][0]);(s=h[d])||(u.push([d,{name:n(d).replace("/",""),type:1,clsid:g,ct:o,mt:o,content:null}]),h[d]=!0)}for(u.sort(function(e,r){return function(e,r){for(var t=e.split("/"),a=r.split("/"),n=0,s=0,i=Math.min(t.length,a.length);n<i;++n){if(s=t[n].length-a[n].length)return s;if(t[n]!=a[n])return t[n]<a[n]?-1:1}return t.length-a.length}(e[0],r[0])}),e.FullPaths=[],e.FileIndex=[],i=0;i<u.length;++i)e.FullPaths[i]=u[i][0],e.FileIndex[i]=u[i][1];for(i=0;i<u.length;++i){var p=e.FileIndex[i],m=e.FullPaths[i];if(p.name=n(m).replace("/",""),p.L=p.R=p.C=-(p.color=1),p.size=p.content?p.content.length:0,p.start=0,p.clsid=p.clsid||g,0===i)p.C=u.length>1?1:-1,p.size=0,p.type=5;else if("/"==m.slice(-1)){for(f=i+1;f<u.length&&a(e.FullPaths[f])!=m;++f);for(p.C=f>=u.length?-1:f,f=i+1;f<u.length&&a(e.FullPaths[f])!=a(m);++f);p.R=f>=u.length?-1:f,p.type=1}else a(e.FullPaths[i+1]||"")==a(m)&&(p.R=i+1),p.type=2}}}function h(e,t){var a=t||{};if("mad"==a.fileType)return function(e,r){for(var t=r||{},a=t.boundary||"SheetJS",n=["MIME-Version: 1.0",'Content-Type: multipart/related; boundary="'+(a="------="+a).slice(2)+'"',"","",""],s=e.FullPaths[0],i=s,c=e.FileIndex[0],o=1;o<e.FullPaths.length;++o)if(i=e.FullPaths[o].slice(s.length),(c=e.FileIndex[o]).size&&c.content&&"\x01Sh33tJ5"!=i){i=i.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7E-\xFF]/g,function(e){return"_x"+e.charCodeAt(0).toString(16)+"_"}).replace(/[\u0080-\uFFFF]/g,function(e){return"_u"+e.charCodeAt(0).toString(16)+"_"});for(var l=c.content,f=G&&Buffer.isBuffer(l)?l.toString("binary"):T(l),h=0,u=Math.min(1024,f.length),d=0,p=0;p<=u;++p)(d=f.charCodeAt(p))>=32&&d<128&&++h;var m=h>=4*u/5;n.push(a),n.push("Content-Location: "+(t.root||"file:///C:/SheetJS/")+i),n.push("Content-Transfer-Encoding: "+(m?"quoted-printable":"base64")),n.push("Content-Type: "+function(e,r){if(e.ctype)return e.ctype;var t=e.name||"",a=t.match(/\.([^\.]+)$/);return a&&eo[a[1]]||r&&(a=(t=r).match(/[\.\\]([^\.\\])+$/))&&eo[a[1]]?eo[a[1]]:"application/octet-stream"}(c,i)),n.push(""),n.push(m?function(e){var r=e.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7E-\xFF=]/g,function(e){var r=e.charCodeAt(0).toString(16).toUpperCase();return"="+(1==r.length?"0"+r:r)});"\n"==(r=r.replace(/ $/mg,"=20").replace(/\t$/mg,"=09")).charAt(0)&&(r="=0D"+r.slice(1)),r=r.replace(/\r(?!\n)/mg,"=0D").replace(/\n\n/mg,"\n=0A").replace(/([^\r\n])\n/mg,"$1=0A");for(var t=[],a=r.split("\r\n"),n=0;n<a.length;++n){var s=a[n];if(0==s.length){t.push("");continue}for(var i=0;i<s.length;){var c=76,o=s.slice(i,i+c);"="==o.charAt(c-1)?c--:"="==o.charAt(c-2)?c-=2:"="==o.charAt(c-3)&&(c-=3),o=s.slice(i,i+c),(i+=c)<s.length&&(o+="="),t.push(o)}}return t.join("\r\n")}(f):function(e){for(var r=W(e),t=[],a=0;a<r.length;a+=76)t.push(r.slice(a,a+76));return t.join("\r\n")+"\r\n"}(f))}return n.push(a+"--\r\n"),n.join("\r\n")}(e,a);if(f(e),"zip"===a.fileType)return function(e,t){var a=[],n=[],s=tc(1),i=8*!!(t||{}).compression,c=0,o=0,l=0,f=0,h=e.FullPaths[0],u=h,d=e.FileIndex[0],p=[],m=0;for(c=1;c<e.FullPaths.length;++c)if(u=e.FullPaths[c].slice(h.length),(d=e.FileIndex[c]).size&&d.content&&"\x01Sh33tJ5"!=u){var g,v=l,T=tc(u.length);for(o=0;o<u.length;++o)T.write_shift(1,127&u.charCodeAt(o));T=T.slice(0,T.l),p[f]=eB.buf(d.content,0);var b=d.content;8==i&&(g=b,b=r?r.deflateRawSync(g):q(g)),(s=tc(30)).write_shift(4,0x4034b50),s.write_shift(2,20),s.write_shift(2,0),s.write_shift(2,i),d.mt?function(e,r){"string"==typeof r&&(r=new Date(r));var t=r.getHours();t=(t=t<<6|r.getMinutes())<<5|r.getSeconds()>>>1,e.write_shift(2,t);var a=r.getFullYear()-1980;a=(a=a<<4|r.getMonth()+1)<<5|r.getDate(),e.write_shift(2,a)}(s,d.mt):s.write_shift(4,0),s.write_shift(-4,(0,p[f])),s.write_shift(4,(0,b.length)),s.write_shift(4,(0,d.content.length)),s.write_shift(2,T.length),s.write_shift(2,0),l+=s.length,a.push(s),l+=T.length,a.push(T),l+=b.length,a.push(b),(s=tc(46)).write_shift(4,0x2014b50),s.write_shift(2,0),s.write_shift(2,20),s.write_shift(2,0),s.write_shift(2,i),s.write_shift(4,0),s.write_shift(-4,p[f]),s.write_shift(4,b.length),s.write_shift(4,d.content.length),s.write_shift(2,T.length),s.write_shift(2,0),s.write_shift(2,0),s.write_shift(2,0),s.write_shift(2,0),s.write_shift(4,0),s.write_shift(4,v),m+=s.l,n.push(s),m+=T.length,n.push(T),++f}return(s=tc(22)).write_shift(4,0x6054b50),s.write_shift(2,0),s.write_shift(2,0),s.write_shift(2,f),s.write_shift(2,f),s.write_shift(4,m),s.write_shift(4,l),s.write_shift(2,0),J([J(a),J(n),s])}(e,a);var n=function(e){for(var r=0,t=0,a=0;a<e.FileIndex.length;++a){var n=e.FileIndex[a];if(n.content){var s=n.content.length;s>0&&(s<4096?r+=s+63>>6:t+=s+511>>9)}}for(var i=e.FullPaths.length+3>>2,c=r+7>>3,o=r+127>>7,l=c+t+i+o,f=l+127>>7,h=f<=109?0:Math.ceil((f-109)/127);l+f+h+127>>7>f;)h=++f<=109?0:Math.ceil((f-109)/127);var u=[1,h,f,o,i,t,r,0];return e.FileIndex[0].size=r<<6,u[7]=(e.FileIndex[0].start=u[0]+u[1]+u[2]+u[3]+u[4]+u[5])+(u[6]+7>>3),u}(e),s=tc(n[7]<<9),i=0,c=0;for(i=0;i<8;++i)s.write_shift(1,m[i]);for(i=0;i<8;++i)s.write_shift(2,0);for(s.write_shift(2,62),s.write_shift(2,3),s.write_shift(2,65534),s.write_shift(2,9),s.write_shift(2,6),i=0;i<3;++i)s.write_shift(2,0);for(s.write_shift(4,0),s.write_shift(4,n[2]),s.write_shift(4,n[0]+n[1]+n[2]+n[3]-1),s.write_shift(4,0),s.write_shift(4,4096),s.write_shift(4,n[3]?n[0]+n[1]+n[2]-1:d),s.write_shift(4,n[3]),s.write_shift(-4,n[1]?n[0]-1:d),s.write_shift(4,n[1]),i=0;i<109;++i)s.write_shift(-4,i<n[2]?n[1]+i:-1);if(n[1])for(c=0;c<n[1];++c){for(;i<236+127*c;++i)s.write_shift(-4,i<n[2]?n[1]+i:-1);s.write_shift(-4,c===n[1]-1?d:c+1)}var o=function(e){for(c+=e;i<c-1;++i)s.write_shift(-4,i+1);e&&(++i,s.write_shift(-4,d))};for(c=(i=0)+n[1];i<c;++i)s.write_shift(-4,v.DIFSECT);for(c+=n[2];i<c;++i)s.write_shift(-4,v.FATSECT);o(n[3]),o(n[4]);for(var l=0,h=0,u=e.FileIndex[0];l<e.FileIndex.length;++l)(u=e.FileIndex[l]).content&&((h=u.content.length)<4096||(u.start=c,o(h+511>>9)));for(o(n[6]+7>>3);511&s.l;)s.write_shift(-4,v.ENDOFCHAIN);for(l=0,c=i=0;l<e.FileIndex.length;++l)(u=e.FileIndex[l]).content&&(h=u.content.length)&&!(h>=4096)&&(u.start=c,o(h+63>>6));for(;511&s.l;)s.write_shift(-4,v.ENDOFCHAIN);for(i=0;i<n[4]<<2;++i){var p=e.FullPaths[i];if(!p||0===p.length){for(l=0;l<17;++l)s.write_shift(4,0);for(l=0;l<3;++l)s.write_shift(4,-1);for(l=0;l<12;++l)s.write_shift(4,0);continue}u=e.FileIndex[i],0===i&&(u.start=u.size?u.start-1:d);var g=0===i&&a.root||u.name;if(h=2*(g.length+1),s.write_shift(64,g,"utf16le"),s.write_shift(2,h),s.write_shift(1,u.type),s.write_shift(1,u.color),s.write_shift(-4,u.L),s.write_shift(-4,u.R),s.write_shift(-4,u.C),u.clsid)s.write_shift(16,u.clsid,"hex");else for(l=0;l<4;++l)s.write_shift(4,0);s.write_shift(4,u.state||0),s.write_shift(4,0),s.write_shift(4,0),s.write_shift(4,0),s.write_shift(4,0),s.write_shift(4,u.start),s.write_shift(4,u.size),s.write_shift(4,0)}for(i=1;i<e.FileIndex.length;++i)if((u=e.FileIndex[i]).size>=4096)if(s.l=u.start+1<<9,G&&Buffer.isBuffer(u.content))u.content.copy(s,s.l,0,u.size),s.l+=u.size+511&-512;else{for(l=0;l<u.size;++l)s.write_shift(1,u.content[l]);for(;511&l;++l)s.write_shift(1,0)}for(i=1;i<e.FileIndex.length;++i)if((u=e.FileIndex[i]).size>0&&u.size<4096)if(G&&Buffer.isBuffer(u.content))u.content.copy(s,s.l,0,u.size),s.l+=u.size+63&-64;else{for(l=0;l<u.size;++l)s.write_shift(1,u.content[l]);for(;63&l;++l)s.write_shift(1,0)}if(G)s.l=s.length;else for(;s.l<s.length;)s.write_shift(1,0);return s}t.version="1.2.1";var u=64,d=-2,p="d0cf11e0a1b11ae1",m=[208,207,17,224,161,177,26,225],g="00000000000000000000000000000000",v={MAXREGSECT:-6,DIFSECT:-4,FATSECT:-3,ENDOFCHAIN:-2,FREESECT:-1,HEADER_SIGNATURE:p,HEADER_MINOR_VERSION:"3e00",MAXREGSID:-6,NOSTREAM:-1,HEADER_CLSID:g,EntryTypes:["unknown","storage","stream","lockbytes","property","root"]};function T(e){for(var r=Array(e.length),t=0;t<e.length;++t)r[t]=String.fromCharCode(e[t]);return r.join("")}for(var b=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15],E=[3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258],w=[1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577],A="undefined"!=typeof Uint8Array,S=A?new Uint8Array(256):[],k=0;k<256;++k)S[k]=function(e){var r=(e<<1|e<<11)&139536|(e<<5|e<<15)&558144;return(r>>16|r>>8|r)&255}(k);function y(e,r){var t=7&r,a=r>>>3;return(e[a]|(t<=5?0:e[a+1]<<8))>>>t&7}function C(e,r){var t=7&r,a=r>>>3;return(e[a]|(t<=3?0:e[a+1]<<8))>>>t&31}function R(e,r){var t=7&r,a=r>>>3;return(e[a]|(t<=1?0:e[a+1]<<8))>>>t&127}function O(e,r,t){var a=7&r,n=r>>>3,s=(1<<t)-1,i=e[n]>>>a;return t<8-a||(i|=e[n+1]<<8-a,t<16-a||(i|=e[n+2]<<16-a,t<24-a))?i&s:(i|=e[n+3]<<24-a)&s}function x(e,r,t){var a=7&r,n=r>>>3;return a<=5?e[n]|=(7&t)<<a:(e[n]|=t<<a&255,e[n+1]=(7&t)>>8-a),r+3}function _(e,r,t){var a=r>>>3;return t<<=7&r,e[a]|=255&t,t>>>=8,e[a+1]=t,r+8}function I(e,r,t){var a=r>>>3;return t<<=7&r,e[a]|=255&t,t>>>=8,e[a+1]=255&t,e[a+2]=t>>>8,r+16}function N(e,r){var t=e.length,a=2*t>r?2*t:r+5,n=0;if(t>=r)return e;if(G){var s=j(a);if(e.copy)e.copy(s);else for(;n<e.length;++n)s[n]=e[n];return s}if(A){var i=new Uint8Array(a);if(i.set)i.set(e);else for(;n<t;++n)i[n]=e[n];return i}return e.length=a,e}function D(e){for(var r=Array(e),t=0;t<e;++t)r[t]=0;return r}function F(e,r,t){var a=1,n=0,s=0,i=0,c=0,o=e.length,l=A?new Uint16Array(32):D(32);for(s=0;s<32;++s)l[s]=0;for(s=o;s<t;++s)e[s]=0;o=e.length;var f=A?new Uint16Array(o):D(o);for(s=0;s<o;++s)l[n=e[s]]++,a<n&&(a=n),f[s]=0;for(s=1,l[0]=0;s<=a;++s)l[s+16]=c=c+l[s-1]<<1;for(s=0;s<o;++s)0!=(c=e[s])&&(f[s]=l[c+16]++);var h=0;for(s=0;s<o;++s)if(0!=(h=e[s]))for(c=function(e,r){var t=S[255&e];return r<=8?t>>>8-r:(t=t<<8|S[e>>8&255],r<=16)?t>>>16-r:(t=t<<8|S[e>>16&255])>>>24-r}(f[s],a)>>a-h,i=(1<<a+4-h)-1;i>=0;--i)r[c|i<<h]=15&h|s<<4;return a}var P=A?new Uint16Array(512):D(512),L=A?new Uint16Array(32):D(32);if(!A){for(var M=0;M<512;++M)P[M]=0;for(M=0;M<32;++M)L[M]=0}for(var U=[],B=0;B<32;B++)U.push(5);F(U,L,32);var H=[];for(B=0;B<=143;B++)H.push(8);for(;B<=255;B++)H.push(9);for(;B<=279;B++)H.push(7);for(;B<=287;B++)H.push(8);F(H,P,288);var X=function(){for(var e=A?new Uint8Array(32768):[],r=0,t=0;r<w.length-1;++r)for(;t<w[r+1];++t)e[t]=r;for(;t<32768;++t)e[t]=29;var a=A?new Uint8Array(259):[];for(r=0,t=0;r<E.length-1;++r)for(;t<E[r+1];++t)a[t]=r;return function(r,t){if(r.length<8){for(var n=0;n<r.length;){var s=Math.min(65535,r.length-n),i=n+s==r.length;for(t.write_shift(1,+i),t.write_shift(2,s),t.write_shift(2,65535&~s);s-- >0;)t[t.l++]=r[n++]}return t.l}return function(r,t){for(var n=0,s=0,i=A?new Uint16Array(32768):[];s<r.length;){var c=Math.min(65535,r.length-s);if(c<10){for(7&(n=x(t,n,+(s+c==r.length)))&&(n+=8-(7&n)),t.l=n/8|0,t.write_shift(2,c),t.write_shift(2,65535&~c);c-- >0;)t[t.l++]=r[s++];n=8*t.l;continue}n=x(t,n,+(s+c==r.length)+2);for(var o=0;c-- >0;){var l,f,h=r[s],u=-1,d=0;if((u=i[o=(o<<5^h)&32767])&&((u|=-32768&s)>s&&(u-=32768),u<s))for(;r[u+d]==r[s+d]&&d<250;)++d;if(d>2){(h=a[d])<=22?n=_(t,n,S[h+1]>>1)-1:(_(t,n,3),_(t,n+=5,S[h-23]>>5),n+=3);var p=h<8?0:h-4>>2;p>0&&(I(t,n,d-E[h]),n+=p),n=_(t,n,S[h=e[s-u]]>>3)-3;var m=h<4?0:h-2>>1;m>0&&(I(t,n,s-u-w[h]),n+=m);for(var g=0;g<d;++g)i[o]=32767&s,o=(o<<5^r[s])&32767,++s;c-=d-1}else h<=143?h+=48:(f=(1&(f=1))<<(7&(l=n)),t[l>>>3]|=f,n=l+1),n=_(t,n,S[h]),i[o]=32767&s,++s}n=_(t,n,0)-1}return t.l=(n+7)/8|0,t.l}(r,t)}}();function q(e){var r=tc(50+Math.floor(1.1*e.length)),t=X(e,r);return r.slice(0,t)}var ee=A?new Uint16Array(32768):D(32768),er=A?new Uint16Array(32768):D(32768),et=A?new Uint16Array(128):D(128),ea=1,en=1;function es(e,r){var t=function(e,r){if(3==e[0]&&!(3&e[1]))return[Y(r),2];for(var t=0,a=0,n=j(r||262144),s=0,i=n.length>>>0,c=0,o=0;(1&a)==0;){if(a=y(e,t),t+=3,a>>>1==0){7&t&&(t+=8-(7&t));var l=e[t>>>3]|e[(t>>>3)+1]<<8;if(t+=32,l>0)for(!r&&i<s+l&&(i=(n=N(n,s+l)).length);l-- >0;)n[s++]=e[t>>>3],t+=8;continue}for(a>>1==1?(c=9,o=5):(t=function(e,r){var t,a,n,s=C(e,r)+257,i=C(e,r+=5)+1;r+=5;var c=(a=7&(t=r),((e[n=t>>>3]|(a<=4?0:e[n+1]<<8))>>>a&15)+4);r+=4;for(var o=0,l=A?new Uint8Array(19):D(19),f=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],h=1,u=A?new Uint8Array(8):D(8),d=A?new Uint8Array(8):D(8),p=l.length,m=0;m<c;++m)l[b[m]]=o=y(e,r),h<o&&(h=o),u[o]++,r+=3;var g=0;for(m=1,u[0]=0;m<=h;++m)d[m]=g=g+u[m-1]<<1;for(m=0;m<p;++m)0!=(g=l[m])&&(f[m]=d[g]++);var v=0;for(m=0;m<p;++m)if(0!=(v=l[m])){g=S[f[m]]>>8-v;for(var T=(1<<7-v)-1;T>=0;--T)et[g|T<<v]=7&v|m<<3}var E=[];for(h=1;E.length<s+i;)switch(g=et[R(e,r)],r+=7&g,g>>>=3){case 16:for(o=3+function(e,r){var t=7&r,a=r>>>3;return(e[a]|(t<=6?0:e[a+1]<<8))>>>t&3}(e,r),r+=2,g=E[E.length-1];o-- >0;)E.push(g);break;case 17:for(o=3+y(e,r),r+=3;o-- >0;)E.push(0);break;case 18:for(o=11+R(e,r),r+=7;o-- >0;)E.push(0);break;default:E.push(g),h<g&&(h=g)}var w=E.slice(0,s),k=E.slice(s);for(m=s;m<286;++m)w[m]=0;for(m=i;m<30;++m)k[m]=0;return ea=F(w,ee,286),en=F(k,er,30),r}(e,t),c=ea,o=en);;){!r&&i<s+32767&&(i=(n=N(n,s+32767)).length);var f=O(e,t,c),h=a>>>1==1?P[f]:ee[f];if(t+=15&h,((h>>>=4)>>>8&255)==0)n[s++]=h;else if(256==h)break;else{var u=(h-=257)<8?0:h-4>>2;u>5&&(u=0);var d=s+E[h];u>0&&(d+=O(e,t,u),t+=u),f=O(e,t,o),t+=15&(h=a>>>1==1?L[f]:er[f]);var p=(h>>>=4)<4?0:h-2>>1,m=w[h];for(p>0&&(m+=O(e,t,p),t+=p),!r&&i<d&&(i=(n=N(n,d+100)).length);s<d;)n[s]=n[s-m],++s}}}return r?[n,t+7>>>3]:[n.slice(0,s),t+7>>>3]}(e.slice(e.l||0),r);return e.l+=t[1],t[0]}function ei(e,r){if(e)"undefined"!=typeof console&&console.error(r);else throw Error(r)}function ec(e,t){ts(e,0);var a={FileIndex:[],FullPaths:[]};l(a,{root:t.root});for(var n=e.length-4;(80!=e[n]||75!=e[n+1]||5!=e[n+2]||6!=e[n+3])&&n>=0;)--n;e.l=n+4,e.l+=4;var i=e.read_shift(2);e.l+=6;var c=e.read_shift(4);for(n=0,e.l=c;n<i;++n){e.l+=20;var o=e.read_shift(4),f=e.read_shift(4),h=e.read_shift(2),u=e.read_shift(2),d=e.read_shift(2);e.l+=8;var p=e.read_shift(4),m=s(e.slice(e.l+h,e.l+h+u));e.l+=h+u+d;var g=e.l;e.l=p+4,function(e,t,a,n,i){e.l+=2;var c,o,l,f,h,u,d,p=e.read_shift(2),m=e.read_shift(2),g=(c=65535&e.read_shift(2),o=65535&e.read_shift(2),l=new Date,f=31&o,h=15&(o>>>=5),o>>>=4,l.setMilliseconds(0),l.setFullYear(o+1980),l.setMonth(h-1),l.setDate(f),u=31&c,d=63&(c>>>=5),c>>>=6,l.setHours(c),l.setMinutes(d),l.setSeconds(u<<1),l);if(8257&p)throw Error("Unsupported ZIP encryption");for(var v=e.read_shift(4),T=e.read_shift(4),b=e.read_shift(4),E=e.read_shift(2),w=e.read_shift(2),A="",S=0;S<E;++S)A+=String.fromCharCode(e[e.l++]);if(w){var k=s(e.slice(e.l,e.l+w));(k[21589]||{}).mt&&(g=k[21589].mt),((i||{})[21589]||{}).mt&&(g=i[21589].mt)}e.l+=w;var y=e.slice(e.l,e.l+T);switch(m){case 8:y=function(e,t){if(!r)return es(e,t);var a=new r.InflateRaw,n=a._processChunk(e.slice(e.l),a._finishFlushFlag);return e.l+=a.bytesRead,n}(e,b);break;case 0:break;default:throw Error("Unsupported ZIP Compression method "+m)}var C=!1;8&p&&(0x8074b50==e.read_shift(4)&&(e.read_shift(4),C=!0),T=e.read_shift(4),b=e.read_shift(4)),T!=t&&ei(C,"Bad compressed size: "+t+" != "+T),b!=a&&ei(C,"Bad uncompressed size: "+a+" != "+b),el(n,A,y,{unsafe:!0,mt:g})}(e,o,f,a,m),e.l=g}return a}var eo={htm:"text/html",xml:"text/xml",gif:"image/gif",jpg:"image/jpeg",png:"image/png",mso:"application/x-mso",thmx:"application/vnd.ms-officetheme",sh33tj5:"application/octet-stream"};function el(e,r,t,a){var s=a&&a.unsafe;s||l(e);var i=!s&&eH.find(e,r);if(!i){var c=e.FullPaths[0];r.slice(0,c.length)==c?c=r:("/"!=c.slice(-1)&&(c+="/"),c=(c+r).replace("//","/")),i={name:n(r),type:2},e.FileIndex.push(i),e.FullPaths.push(c),s||eH.utils.cfb_gc(e)}return i.content=t,i.size=t?t.length:0,a&&(a.CLSID&&(i.clsid=a.CLSID),a.mt&&(i.mt=a.mt),a.ct&&(i.ct=a.ct)),i}return t.find=function(e,r){var t=e.FullPaths.map(function(e){return e.toUpperCase()}),a=t.map(function(e){var r=e.split("/");return r[r.length-("/"==e.slice(-1)?2:1)]}),n=!1;47===r.charCodeAt(0)?(n=!0,r=t[0].slice(0,-1)+r):n=-1!==r.indexOf("/");var s=r.toUpperCase(),i=!0===n?t.indexOf(s):a.indexOf(s);if(-1!==i)return e.FileIndex[i];var c=!s.match(Q);for(s=s.replace(Z,""),c&&(s=s.replace(Q,"!")),i=0;i<t.length;++i)if((c?t[i].replace(Q,"!"):t[i]).replace(Z,"")==s||(c?a[i].replace(Q,"!"):a[i]).replace(Z,"")==s)return e.FileIndex[i];return null},t.read=function(r,t){var a=t&&t.type;switch(!a&&G&&Buffer.isBuffer(r)&&(a="buffer"),a||"base64"){case"file":return i(),c(e.readFileSync(r),t);case"base64":return c(K(V(r)),t);case"binary":return c(K(r),t)}return c(r,t)},t.parse=c,t.write=function(r,t){var a=h(r,t);switch(t&&t.type||"buffer"){case"file":i(),e.writeFileSync(t.filename,a);break;case"binary":return"string"==typeof a?a:T(a);case"base64":return W("string"==typeof a?a:T(a));case"buffer":if(G)return Buffer.isBuffer(a)?a:z(a);case"array":return"string"==typeof a?K(a):a}return a},t.writeFile=function(r,t,a){i();var n=h(r,a);e.writeFileSync(t,n)},t.utils={cfb_new:function(e){var r={};return l(r,e),r},cfb_add:el,cfb_del:function(e,r){l(e);var t=eH.find(e,r);if(t){for(var a=0;a<e.FileIndex.length;++a)if(e.FileIndex[a]==t)return e.FileIndex.splice(a,1),e.FullPaths.splice(a,1),!0}return!1},cfb_mov:function(e,r,t){l(e);var a=eH.find(e,r);if(a){for(var s=0;s<e.FileIndex.length;++s)if(e.FileIndex[s]==a)return e.FileIndex[s].name=n(t),e.FullPaths[s]=t,!0}return!1},cfb_gc:function(e){f(e,!0)},ReadShift:r9,CheckField:tn,prep_blob:ts,bconcat:J,use_zlib:function(e){try{var t=new e.InflateRaw;if(t._processChunk(new Uint8Array([3,0]),t._finishFlushFlag),t.bytesRead)r=e;else throw Error("zlib does not expose bytesRead")}catch(e){console.error("cannot use native zlib: "+(e.message||e))}},_deflateRaw:q,_inflateRaw:es,consts:v},t}();function eW(e){for(var r=Object.keys(e),t=[],a=0;a<r.length;++a)Object.prototype.hasOwnProperty.call(e,r[a])&&t.push(r[a]);return t}function eV(e){for(var r=[],t=eW(e),a=0;a!==t.length;++a)r[e[t[a]]]=t[a];return r}var eG=new Date(1899,11,30,0,0,0);function ez(e,r){var t=e.getTime();return r&&(t-=1263168e5),(t-(eG.getTime()+(e.getTimezoneOffset()-eG.getTimezoneOffset())*6e4))/864e5}var e$=new Date,eY=eG.getTime()+(e$.getTimezoneOffset()-eG.getTimezoneOffset())*6e4,ej=e$.getTimezoneOffset();function eK(e){var r=new Date;return r.setTime(24*e*36e5+eY),r.getTimezoneOffset()!==ej&&r.setTime(r.getTime()+(r.getTimezoneOffset()-ej)*6e4),r}var eX=new Date("2017-02-19T19:06:09.000Z"),eq=isNaN(eX.getFullYear())?new Date("2/19/17"):eX,eJ=2017==eq.getFullYear();function eZ(e,r){var t=new Date(e);if(eJ)return r>0?t.setTime(t.getTime()+60*t.getTimezoneOffset()*1e3):r<0&&t.setTime(t.getTime()-60*t.getTimezoneOffset()*1e3),t;if(e instanceof Date)return e;if(1917==eq.getFullYear()&&!isNaN(t.getFullYear())){var a=t.getFullYear();return e.indexOf(""+a)>-1||t.setFullYear(t.getFullYear()+100),t}var n=e.match(/\d+/g)||["2017","2","19","0","0","0"],s=new Date(+n[0],n[1]-1,+n[2],+n[3]||0,+n[4]||0,+n[5]||0);return e.indexOf("Z")>-1&&(s=new Date(s.getTime()-60*s.getTimezoneOffset()*1e3)),s}function eQ(e,r){if(G&&Buffer.isBuffer(e)){if(r){if(255==e[0]&&254==e[1])return rC(e.slice(2).toString("utf16le"));if(254==e[1]&&255==e[2])return rC(L(e.slice(2).toString("binary")))}return e.toString("binary")}if("undefined"!=typeof TextDecoder)try{if(r){if(255==e[0]&&254==e[1])return rC(new TextDecoder("utf-16le").decode(e.slice(2)));if(254==e[0]&&255==e[1])return rC(new TextDecoder("utf-16be").decode(e.slice(2)))}var t={"€":"","‚":"",ƒ:"","„":"","…":"","†":"","‡":"",ˆ:"","‰":"",Š:"","‹":"",Œ:"",Ž:"","‘":"","’":"","“":"","”":"","•":"","–":"","—":"","˜":"","™":"",š:"","›":"",œ:"",ž:"",Ÿ:""};return Array.isArray(e)&&(e=new Uint8Array(e)),new TextDecoder("latin1").decode(e).replace(/[€‚ƒ„…†‡ˆ‰Š‹ŒŽ‘’“”•–—˜™š›œžŸ]/g,function(e){return t[e]||e})}catch(e){}for(var a=[],n=0;n!=e.length;++n)a.push(String.fromCharCode(e[n]));return a.join("")}function e1(e){if("undefined"!=typeof JSON&&!Array.isArray(e))return JSON.parse(JSON.stringify(e));if("object"!=typeof e||null==e)return e;if(e instanceof Date)return new Date(e.getTime());var r={};for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&(r[t]=e1(e[t]));return r}function e0(e,r){for(var t="";t.length<r;)t+=e;return t}function e2(e){var r=Number(e);if(!isNaN(r))return isFinite(r)?r:NaN;if(!/\d/.test(e))return r;var t=1,a=e.replace(/([\d]),([\d])/g,"$1$2").replace(/[$]/g,"").replace(/[%]/g,function(){return t*=100,""});return isNaN(r=Number(a))&&isNaN(r=Number(a=a.replace(/[(](.*)[)]/,function(e,r){return t=-t,r})))?r:r/t}var e4=["january","february","march","april","may","june","july","august","september","october","november","december"];function e3(e){var r=new Date(e),t=new Date(NaN),a=r.getYear(),n=r.getMonth(),s=r.getDate();if(isNaN(s))return t;var i=e.toLowerCase();if(i.match(/jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec/)){if((i=i.replace(/[^a-z]/g,"").replace(/([^a-z]|^)[ap]m?([^a-z]|$)/,"")).length>3&&-1==e4.indexOf(i))return t}else if(i.match(/[a-z]/))return t;return a<0||a>8099?t:(n>0||s>1)&&101!=a?r:e.match(/[^-0-9:,\/\\]/)?t:r}var e5=function(){var e=5=="abacaba".split(/(:?b)/i).length;return function(r,t,a){if(e||"string"==typeof t)return r.split(t);for(var n=r.split(t),s=[n[0]],i=1;i<n.length;++i)s.push(a),s.push(n[i]);return s}}();function e6(e){return e?e.content&&e.type?eQ(e.content,!0):e.data?M(e.data):e.asNodeBuffer&&G?M(e.asNodeBuffer().toString("binary")):e.asBinary?M(e.asBinary()):e._data&&e._data.getContent?M(eQ(Array.prototype.slice.call(e._data.getContent(),0))):null:null}function e8(e){if(!e)return null;if(e.data)return P(e.data);if(e.asNodeBuffer&&G)return e.asNodeBuffer();if(e._data&&e._data.getContent){var r=e._data.getContent();return"string"==typeof r?P(r):Array.prototype.slice.call(r)}return e.content&&e.type?e.content:null}function e7(e,r){for(var t=e.FullPaths||eW(e.files),a=r.toLowerCase().replace(/[\/]/g,"\\"),n=a.replace(/\\/g,"/"),s=0;s<t.length;++s){var i=t[s].replace(/^Root Entry[\/]/,"").toLowerCase();if(a==i||n==i)return e.files?e.files[t[s]]:e.FileIndex[s]}return null}function e9(e,r){var t=e7(e,r);if(null==t)throw Error("Cannot find file "+r+" in zip");return t}function re(e,r,t){if(!t){var a;return(a=e9(e,r))&&".bin"===a.name.slice(-4)?e8(a):e6(a)}if(!r)return null;try{return re(e,r)}catch(e){return null}}function rr(e,r,t){if(!t)return e6(e9(e,r));if(!r)return null;try{return rr(e,r)}catch(e){return null}}function rt(e){for(var r=e.FullPaths||eW(e.files),t=[],a=0;a<r.length;++a)"/"!=r[a].slice(-1)&&t.push(r[a].replace(/^Root Entry[\/]/,""));return t.sort()}function ra(e,r){switch(r.type){case"base64":return eH.read(e,{type:"base64"});case"binary":return eH.read(e,{type:"binary"});case"buffer":case"array":return eH.read(e,{type:"buffer"})}throw Error("Unrecognized type "+r.type)}function rn(e,r){if("/"==e.charAt(0))return e.slice(1);var t=r.split("/");"/"!=r.slice(-1)&&t.pop();for(var a=e.split("/");0!==a.length;){var n=a.shift();".."===n?t.pop():"."!==n&&t.push(n)}return t.join("/")}var rs='<?xml version="1.0" encoding="UTF-8" standalone="yes"?>\r\n',ri=/([^"\s?>\/]+)\s*=\s*((?:")([^"]*)(?:")|(?:')([^']*)(?:')|([^'">\s]+))/g,rc=/<[\/\?]?[a-zA-Z0-9:_-]+(?:\s+[^"\s?>\/]+\s*=\s*(?:"[^"]*"|'[^']*'|[^'">\s=]+))*\s*[\/\?]?>/mg,ro=rs.match(rc)?rc:/<[^>]*>/g,rl=/<\w*:/,rf=/<(\/?)\w+:/;function rh(e,r,t){for(var a={},n=0,s=0;n!==e.length&&32!==(s=e.charCodeAt(n))&&10!==s&&13!==s;++n);if(r||(a[0]=e.slice(0,n)),n===e.length)return a;var i=e.match(ri),c=0,o="",l=0,f="",h="",u=1;if(i)for(l=0;l!=i.length;++l){for(s=0,h=i[l];s!=h.length&&61!==h.charCodeAt(s);++s);for(f=h.slice(0,s).trim();32==h.charCodeAt(s+1);)++s;for(c=0,u=+(34==(n=h.charCodeAt(s+1))||39==n),o=h.slice(s+1+u,h.length-u);c!=f.length&&58!==f.charCodeAt(c);++c);if(c===f.length)f.indexOf("_")>0&&(f=f.slice(0,f.indexOf("_"))),a[f]=o,t||(a[f.toLowerCase()]=o);else{var d=(5===c&&"xmlns"===f.slice(0,5)?"xmlns":"")+f.slice(c+1);if(a[d]&&"ext"==f.slice(c-3,c))continue;a[d]=o,t||(a[d.toLowerCase()]=o)}}return a}function ru(e){return e.replace(rf,"<$1")}var rd={"&quot;":'"',"&apos;":"'","&gt;":">","&lt;":"<","&amp;":"&"},rp=eV(rd),rm=function(){var e=/&(?:quot|apos|gt|lt|amp|#x?([\da-fA-F]+));/ig,r=/_x([\da-fA-F]{4})_/ig;return function t(a){var n=a+"",s=n.indexOf("<![CDATA[");if(-1==s)return n.replace(e,function(e,r){return rd[e]||String.fromCharCode(parseInt(r,e.indexOf("x")>-1?16:10))||e}).replace(r,function(e,r){return String.fromCharCode(parseInt(r,16))});var i=n.indexOf("]]>");return t(n.slice(0,s))+n.slice(s+9,i)+t(n.slice(i+3))}}(),rg=/[&<>'"]/g,rv=/[\u0000-\u001f]/g;function rT(e){return(e+"").replace(rg,function(e){return rp[e]}).replace(/\n/g,"<br/>").replace(rv,function(e){return"&#x"+("000"+e.charCodeAt(0).toString(16)).slice(-4)+";"})}var rb=function(){var e=/&#(\d+);/g;function r(e,r){return String.fromCharCode(parseInt(r,10))}return function(t){return t.replace(e,r)}}();function rE(e){switch(e){case 1:case!0:case"1":case"true":case"TRUE":return!0;default:return!1}}function rw(e){for(var r="",t=0,a=0,n=0,s=0,i=0,c=0;t<e.length;){if((a=e.charCodeAt(t++))<128){r+=String.fromCharCode(a);continue}if(n=e.charCodeAt(t++),a>191&&a<224){r+=String.fromCharCode((31&a)<<6|63&n);continue}if(s=e.charCodeAt(t++),a<240){r+=String.fromCharCode((15&a)<<12|(63&n)<<6|63&s);continue}r+=String.fromCharCode(55296+((c=((7&a)<<18|(63&n)<<12|(63&s)<<6|63&e.charCodeAt(t++))-65536)>>>10&1023)),r+=String.fromCharCode(56320+(1023&c))}return r}function rA(e){var r,t,a,n=Y(2*e.length),s=1,i=0,c=0;for(t=0;t<e.length;t+=s)s=1,(a=e.charCodeAt(t))<128?r=a:a<224?(r=(31&a)*64+(63&e.charCodeAt(t+1)),s=2):a<240?(r=(15&a)*4096+(63&e.charCodeAt(t+1))*64+(63&e.charCodeAt(t+2)),s=3):(s=4,c=55296+((r=(7&a)*262144+(63&e.charCodeAt(t+1))*4096+(63&e.charCodeAt(t+2))*64+(63&e.charCodeAt(t+3))-65536)>>>10&1023),r=56320+(1023&r)),0!==c&&(n[i++]=255&c,n[i++]=c>>>8,c=0),n[i++]=r%256,n[i++]=r>>>8;return n.slice(0,i).toString("ucs2")}function rS(e){return z(e,"binary").toString("utf8")}var rk="foo bar bazâð£",ry=G&&(rS(rk)==rw(rk)&&rS||rA(rk)==rw(rk)&&rA)||rw,rC=G?function(e){return z(e,"utf8").toString("binary")}:function(e){for(var r=[],t=0,a=0,n=0;t<e.length;)switch(a=e.charCodeAt(t++),!0){case a<128:r.push(String.fromCharCode(a));break;case a<2048:r.push(String.fromCharCode(192+(a>>6))),r.push(String.fromCharCode(128+(63&a)));break;case a>=55296&&a<57344:a-=55296,r.push(String.fromCharCode(240+((n=e.charCodeAt(t++)-56320+(a<<10))>>18&7))),r.push(String.fromCharCode(144+(n>>12&63))),r.push(String.fromCharCode(128+(n>>6&63))),r.push(String.fromCharCode(128+(63&n)));break;default:r.push(String.fromCharCode(224+(a>>12))),r.push(String.fromCharCode(128+(a>>6&63))),r.push(String.fromCharCode(128+(63&a)))}return r.join("")},rR=function(){var e={};return function(r,t){var a=r+"|"+(t||"");return e[a]?e[a]:e[a]=RegExp("<(?:\\w+:)?"+r+'(?: xml:space="preserve")?(?:[^>]*)>([\\s\\S]*?)</(?:\\w+:)?'+r+">",t||"")}}(),rO=function(){var e=[["nbsp"," "],["middot","·"],["quot",'"'],["apos","'"],["gt",">"],["lt","<"],["amp","&"]].map(function(e){return[RegExp("&"+e[0]+";","ig"),e[1]]});return function(r){for(var t=r.replace(/^[\t\n\r ]+/,"").replace(/[\t\n\r ]+$/,"").replace(/>\s+/g,">").replace(/\s+</g,"<").replace(/[\t\n\r ]+/g," ").replace(/<\s*[bB][rR]\s*\/?>/g,"\n").replace(/<[^>]*>/g,""),a=0;a<e.length;++a)t=t.replace(e[a][0],e[a][1]);return t}}(),rx=function(){var e={};return function(r){return void 0!==e[r]?e[r]:e[r]=RegExp("<(?:vt:)?"+r+">([\\s\\S]*?)</(?:vt:)?"+r+">","g")}}(),r_=/<\/?(?:vt:)?variant>/g,rI=/<(?:vt:)([^>]*)>([\s\S]*)</;function rN(e,r){var t=rh(e),a=e.match(rx(t.baseType))||[],n=[];if(a.length!=t.size){if(r.WTF)throw Error("unexpected vector length "+a.length+" != "+t.size);return n}return a.forEach(function(e){var r=e.replace(r_,"").match(rI);r&&n.push({v:ry(r[2]),t:r[1]})}),n}function rD(e){if(G&&Buffer.isBuffer(e))return e.toString("utf8");if("string"==typeof e)return e;if("undefined"!=typeof Uint8Array&&e instanceof Uint8Array)return ry(X(q(e)));throw Error("Bad input format: expected Buffer or string")}var rF=/<(\/?)([^\s?><!\/:]*:|)([^\s?<>:\/]+)(?:[\s?:\/][^>]*)?>/mg,rP={CT:"http://schemas.openxmlformats.org/package/2006/content-types"},rL=["http://schemas.openxmlformats.org/spreadsheetml/2006/main","http://purl.oclc.org/ooxml/spreadsheetml/main","http://schemas.microsoft.com/office/excel/2006/main","http://schemas.microsoft.com/office/excel/2006/2"],rM=function(e){for(var r=[],t=0;t<e[0].length;++t)if(e[0][t])for(var a=0,n=e[0][t].length;a<n;a+=10240)r.push.apply(r,e[0][t].slice(a,a+10240));return r},rU=G?function(e){return e[0].length>0&&Buffer.isBuffer(e[0][0])?Buffer.concat(e[0].map(function(e){return Buffer.isBuffer(e)?e:z(e)})):rM(e)}:rM,rB=function(e,r,t){for(var a=[],n=r;n<t;n+=2)a.push(String.fromCharCode(r5(e,n)));return a.join("").replace(Z,"")},rH=G?function(e,r,t){return Buffer.isBuffer(e)?e.toString("utf16le",r,t).replace(Z,""):rB(e,r,t)}:rB,rW=function(e,r,t){for(var a=[],n=r;n<r+t;++n)a.push(("0"+e[n].toString(16)).slice(-2));return a.join("")},rV=G?function(e,r,t){return Buffer.isBuffer(e)?e.toString("hex",r,r+t):rW(e,r,t)}:rW,rG=function(e,r,t){for(var a=[],n=r;n<t;n++)a.push(String.fromCharCode(r3(e,n)));return a.join("")},rz=G?function(e,r,t){return Buffer.isBuffer(e)?e.toString("utf8",r,t):rG(e,r,t)}:rG,r$=function(e,r){var t=r8(e,r);return t>0?rz(e,r+4,r+4+t-1):""},rY=r$,rj=function(e,r){var t=r8(e,r);return t>0?rz(e,r+4,r+4+t-1):""},rK=rj,rX=function(e,r){var t=2*r8(e,r);return t>0?rz(e,r+4,r+4+t-1):""},rq=rX,rJ=function(e,r){var t=r8(e,r);return t>0?rH(e,r+4,r+4+t):""},rZ=rJ,rQ=function(e,r){var t=r8(e,r);return t>0?rz(e,r+4,r+4+t):""},r1=rQ,r0=function(e,r){for(var t=1-2*(e[r+7]>>>7),a=((127&e[r+7])<<4)+(e[r+6]>>>4&15),n=15&e[r+6],s=5;s>=0;--s)n=256*n+e[r+s];return 2047==a?0==n?1/0*t:NaN:(0==a?a=-1022:(a-=1023,n+=0x10000000000000),t*Math.pow(2,a-52)*n)},r2=r0,r4=function(e){return Array.isArray(e)||"undefined"!=typeof Uint8Array&&e instanceof Uint8Array};G&&(rY=function(e,r){if(!Buffer.isBuffer(e))return r$(e,r);var t=e.readUInt32LE(r);return t>0?e.toString("utf8",r+4,r+4+t-1):""},rK=function(e,r){if(!Buffer.isBuffer(e))return rj(e,r);var t=e.readUInt32LE(r);return t>0?e.toString("utf8",r+4,r+4+t-1):""},rq=function(e,r){if(!Buffer.isBuffer(e))return rX(e,r);var t=2*e.readUInt32LE(r);return e.toString("utf16le",r+4,r+4+t-1)},rZ=function(e,r){if(!Buffer.isBuffer(e))return rJ(e,r);var t=e.readUInt32LE(r);return e.toString("utf16le",r+4,r+4+t)},r1=function(e,r){if(!Buffer.isBuffer(e))return rQ(e,r);var t=e.readUInt32LE(r);return e.toString("utf8",r+4,r+4+t)},r2=function(e,r){return Buffer.isBuffer(e)?e.readDoubleLE(r):r0(e,r)},r4=function(e){return Buffer.isBuffer(e)||Array.isArray(e)||"undefined"!=typeof Uint8Array&&e instanceof Uint8Array}),void 0!==t&&(rH=function(e,r,a){return t.utils.decode(1200,e.slice(r,a)).replace(Z,"")},rz=function(e,r,a){return t.utils.decode(65001,e.slice(r,a))},rY=function(e,r){var a=r8(e,r);return a>0?t.utils.decode(x,e.slice(r+4,r+4+a-1)):""},rK=function(e,r){var a=r8(e,r);return a>0?t.utils.decode(O,e.slice(r+4,r+4+a-1)):""},rq=function(e,r){var a=2*r8(e,r);return a>0?t.utils.decode(1200,e.slice(r+4,r+4+a-1)):""},rZ=function(e,r){var a=r8(e,r);return a>0?t.utils.decode(1200,e.slice(r+4,r+4+a)):""},r1=function(e,r){var a=r8(e,r);return a>0?t.utils.decode(65001,e.slice(r+4,r+4+a)):""});var r3=function(e,r){return e[r]},r5=function(e,r){return 256*e[r+1]+e[r]},r6=function(e,r){var t=256*e[r+1]+e[r];return t<32768?t:-((65535-t+1)*1)},r8=function(e,r){return 0x1000000*e[r+3]+(e[r+2]<<16)+(e[r+1]<<8)+e[r]},r7=function(e,r){return e[r+3]<<24|e[r+2]<<16|e[r+1]<<8|e[r]};function r9(e,r){var a,n,s,i,c,o,l="",f=[];switch(r){case"dbcs":if(o=this.l,G&&Buffer.isBuffer(this))l=this.slice(this.l,this.l+2*e).toString("utf16le");else for(c=0;c<e;++c)l+=String.fromCharCode(r5(this,o)),o+=2;e*=2;break;case"utf8":l=rz(this,this.l,this.l+e);break;case"utf16le":e*=2,l=rH(this,this.l,this.l+e);break;case"wstr":if(void 0===t)return r9.call(this,e,"dbcs");l=t.utils.decode(O,this.slice(this.l,this.l+2*e)),e*=2;break;case"lpstr-ansi":l=rY(this,this.l),e=4+r8(this,this.l);break;case"lpstr-cp":l=rK(this,this.l),e=4+r8(this,this.l);break;case"lpwstr":l=rq(this,this.l),e=4+2*r8(this,this.l);break;case"lpp4":e=4+r8(this,this.l),l=rZ(this,this.l),2&e&&(e+=2);break;case"8lpp4":e=4+r8(this,this.l),l=r1(this,this.l),3&e&&(e+=4-(3&e));break;case"cstr":for(e=0,l="";0!==(s=r3(this,this.l+e++));)f.push(U(s));l=f.join("");break;case"_wstr":for(e=0,l="";0!==(s=r5(this,this.l+e));)f.push(U(s)),e+=2;e+=2,l=f.join("");break;case"dbcs-cont":for(c=0,l="",o=this.l;c<e;++c){if(this.lens&&-1!==this.lens.indexOf(o))return s=r3(this,o),this.l=o+1,i=r9.call(this,e-c,s?"dbcs-cont":"sbcs-cont"),f.join("")+i;f.push(U(r5(this,o))),o+=2}l=f.join(""),e*=2;break;case"cpstr":if(void 0!==t){l=t.utils.decode(O,this.slice(this.l,this.l+e));break}case"sbcs-cont":for(c=0,l="",o=this.l;c!=e;++c){if(this.lens&&-1!==this.lens.indexOf(o))return s=r3(this,o),this.l=o+1,i=r9.call(this,e-c,s?"dbcs-cont":"sbcs-cont"),f.join("")+i;f.push(U(r3(this,o))),o+=1}l=f.join("");break;default:switch(e){case 1:return a=r3(this,this.l),this.l++,a;case 2:return a=("i"===r?r6:r5)(this,this.l),this.l+=2,a;case 4:case -4:if("i"===r||(128&this[this.l+3])==0)return a=(e>0?r7:function(e,r){return e[r]<<24|e[r+1]<<16|e[r+2]<<8|e[r+3]})(this,this.l),this.l+=4,a;return n=r8(this,this.l),this.l+=4,n;case 8:case -8:if("f"===r)return n=8==e?r2(this,this.l):r2([this[this.l+7],this[this.l+6],this[this.l+5],this[this.l+4],this[this.l+3],this[this.l+2],this[this.l+1],this[this.l+0]],0),this.l+=8,n;e=8;case 16:l=rV(this,this.l,e)}}return this.l+=e,l}var te=function(e,r,t){e[t]=255&r,e[t+1]=r>>>8&255,e[t+2]=r>>>16&255,e[t+3]=r>>>24&255},tr=function(e,r,t){e[t]=255&r,e[t+1]=r>>8&255,e[t+2]=r>>16&255,e[t+3]=r>>24&255},tt=function(e,r,t){e[t]=255&r,e[t+1]=r>>>8&255};function ta(e,r,a){var n=0,s=0;if("dbcs"===a){for(s=0;s!=r.length;++s)tt(this,r.charCodeAt(s),this.l+2*s);n=2*r.length}else if("sbcs"===a){if(void 0!==t&&874==x)for(s=0;s!=r.length;++s){var i=t.utils.encode(x,r.charAt(s));this[this.l+s]=i[0]}else for(s=0,r=r.replace(/[^\x00-\x7F]/g,"_");s!=r.length;++s)this[this.l+s]=255&r.charCodeAt(s);n=r.length}else if("hex"===a){for(;s<e;++s)this[this.l++]=parseInt(r.slice(2*s,2*s+2),16)||0;return this}else if("utf16le"===a){var c=Math.min(this.l+e,this.length);for(s=0;s<Math.min(r.length,e);++s){var o=r.charCodeAt(s);this[this.l++]=255&o,this[this.l++]=o>>8}for(;this.l<c;)this[this.l++]=0;return this}else switch(e){case 1:n=1,this[this.l]=255&r;break;case 2:n=2,this[this.l]=255&r,r>>>=8,this[this.l+1]=255&r;break;case 3:n=3,this[this.l]=255&r,r>>>=8,this[this.l+1]=255&r,r>>>=8,this[this.l+2]=255&r;break;case 4:n=4,te(this,r,this.l);break;case 8:if(n=8,"f"===a){!function(e,r,t){var a=(r<0||1/r==-1/0)<<7,n=0,s=0,i=a?-r:r;isFinite(i)?0==i?n=s=0:(n=Math.floor(Math.log(i)/Math.LN2),s=i*Math.pow(2,52-n),n<=-1023&&(!isFinite(s)||s<0x10000000000000)?n=-1022:(s-=0x10000000000000,n+=1023)):(n=2047,s=26985*!!isNaN(r));for(var c=0;c<=5;++c,s/=256)e[t+c]=255&s;e[t+6]=(15&n)<<4|15&s,e[t+7]=n>>4|a}(this,r,this.l);break}case 16:break;case -4:n=4,tr(this,r,this.l)}return this.l+=n,this}function tn(e,r){var t=rV(this,this.l,e.length>>1);if(t!==e)throw Error(r+"Expected "+e+" saw "+t);this.l+=e.length>>1}function ts(e,r){e.l=r,e.read_shift=r9,e.chk=tn,e.write_shift=ta}function ti(e,r){e.l+=r}function tc(e){var r=Y(e);return ts(r,0),r}function to(e,r,t){if(e){ts(e,e.l||0);for(var a,n,s,i=e.length,c=0,o=0;e.l<i;){128&(c=e.read_shift(1))&&(c=(127&c)+((127&e.read_shift(1))<<7));var l=sS[c]||sS[65535];for(n=1,s=127&(a=e.read_shift(1));n<4&&128&a;++n)s+=(127&(a=e.read_shift(1)))<<7*n;o=e.l+s;var f=l.f&&l.f(e,s,t);if(e.l=o,r(f,l,c))return}}}function tl(){var e=[],r=G?256:2048,t=function(e){var r=tc(e);return ts(r,0),r},a=t(r),n=function(){a&&(a.length>a.l&&((a=a.slice(0,a.l)).l=a.length),a.length>0&&e.push(a),a=null)},s=function(e){return a&&e<a.length-a.l?a:(n(),a=t(Math.max(e+1,r)))};return{next:s,push:function(e){n(),null==(a=e).l&&(a.l=a.length),s(r)},end:function(){return n(),J(e)},_bufs:e}}function tf(e,r,t){var a=e1(e);if(r.s?(a.cRel&&(a.c+=r.s.c),a.rRel&&(a.r+=r.s.r)):(a.cRel&&(a.c+=r.c),a.rRel&&(a.r+=r.r)),!t||t.biff<12){for(;a.c>=256;)a.c-=256;for(;a.r>=65536;)a.r-=65536}return a}function th(e,r,t){var a=e1(e);return a.s=tf(a.s,r.s,t),a.e=tf(a.e,r.s,t),a}function tu(e,r){if(e.cRel&&e.c<0)for(e=e1(e);e.c<0;)e.c+=r>8?16384:256;if(e.rRel&&e.r<0)for(e=e1(e);e.r<0;)e.r+=r>8?1048576:r>5?65536:16384;var t=tb(e);return e.cRel||null==e.cRel||(t=t.replace(/^([A-Z])/,"$$$1")),e.rRel||null==e.rRel||(t=t.replace(/([A-Z]|^)(\d+)$/,"$1$$$2")),t}function td(e,r){return 0!=e.s.r||e.s.rRel||e.e.r!=(r.biff>=12?1048575:r.biff>=8?65536:16384)||e.e.rRel?0!=e.s.c||e.s.cRel||e.e.c!=(r.biff>=12?16383:255)||e.e.cRel?tu(e.s,r.biff)+":"+tu(e.e,r.biff):(e.s.rRel?"":"$")+tm(e.s.r)+":"+(e.e.rRel?"":"$")+tm(e.e.r):(e.s.cRel?"":"$")+tv(e.s.c)+":"+(e.e.cRel?"":"$")+tv(e.e.c)}function tp(e){return parseInt(e.replace(/\$(\d+)$/,"$1"),10)-1}function tm(e){return""+(e+1)}function tg(e){for(var r=e.replace(/^\$([A-Z])/,"$1"),t=0,a=0;a!==r.length;++a)t=26*t+r.charCodeAt(a)-64;return t-1}function tv(e){if(e<0)throw Error("invalid column "+e);var r="";for(++e;e;e=Math.floor((e-1)/26))r=String.fromCharCode((e-1)%26+65)+r;return r}function tT(e){for(var r=0,t=0,a=0;a<e.length;++a){var n=e.charCodeAt(a);n>=48&&n<=57?r=10*r+(n-48):n>=65&&n<=90&&(t=26*t+(n-64))}return{c:t-1,r:r-1}}function tb(e){for(var r=e.c+1,t="";r;r=(r-1)/26|0)t=String.fromCharCode((r-1)%26+65)+t;return t+(e.r+1)}function tE(e){var r=e.indexOf(":");return -1==r?{s:tT(e),e:tT(e)}:{s:tT(e.slice(0,r)),e:tT(e.slice(r+1))}}function tw(e,r){return void 0===r||"number"==typeof r?tw(e.s,e.e):("string"!=typeof e&&(e=tb(e)),"string"!=typeof r&&(r=tb(r)),e==r?e:e+":"+r)}function tA(e){var r={s:{c:0,r:0},e:{c:0,r:0}},t=0,a=0,n=0,s=e.length;for(t=0;a<s&&!((n=e.charCodeAt(a)-64)<1)&&!(n>26);++a)t=26*t+n;for(r.s.c=--t,t=0;a<s&&!((n=e.charCodeAt(a)-48)<0)&&!(n>9);++a)t=10*t+n;if(r.s.r=--t,a===s||10!=n)return r.e.c=r.s.c,r.e.r=r.s.r,r;for(++a,t=0;a!=s&&!((n=e.charCodeAt(a)-64)<1)&&!(n>26);++a)t=26*t+n;for(r.e.c=--t,t=0;a!=s&&!((n=e.charCodeAt(a)-48)<0)&&!(n>9);++a)t=10*t+n;return r.e.r=--t,r}function tS(e,r){var t="d"==e.t&&r instanceof Date;if(null!=e.z)try{return e.w=eF(e.z,t?ez(r):r)}catch(e){}try{return e.w=eF((e.XF||{}).numFmtId||14*!!t,t?ez(r):r)}catch(e){return""+r}}function tk(e,r,t){return null==e||null==e.t||"z"==e.t?"":void 0!==e.w?e.w:("d"==e.t&&!e.z&&t&&t.dateNF&&(e.z=t.dateNF),"e"==e.t)?tV[e.v]||e.v:void 0==r?tS(e,e.v):tS(e,r)}function ty(e,r){var t=r&&r.sheet?r.sheet:"Sheet1",a={};return a[t]=e,{SheetNames:[t],Sheets:a}}function tC(e,r){return function(e,r,t){var a=t||{},n=(0,a.dense),s=e||(n?[]:{}),i=0,c=0;if(s&&null!=a.origin){if("number"==typeof a.origin)i=a.origin;else{var o="string"==typeof a.origin?tT(a.origin):a.origin;i=o.r,c=o.c}s["!ref"]||(s["!ref"]="A1:A1")}var l={s:{c:1e7,r:1e7},e:{c:0,r:0}};if(s["!ref"]){var f=tA(s["!ref"]);l.s.c=f.s.c,l.s.r=f.s.r,l.e.c=Math.max(l.e.c,f.e.c),l.e.r=Math.max(l.e.r,f.e.r),-1==i&&(l.e.r=i=f.e.r+1)}for(var h=0;h!=r.length;++h)if(r[h]){if(!Array.isArray(r[h]))throw Error("aoa_to_sheet expects an array of arrays");for(var u=0;u!=r[h].length;++u)if(void 0!==r[h][u]){var d={v:r[h][u]},p=i+h,m=c+u;if(l.s.r>p&&(l.s.r=p),l.s.c>m&&(l.s.c=m),l.e.r<p&&(l.e.r=p),l.e.c<m&&(l.e.c=m),!r[h][u]||"object"!=typeof r[h][u]||Array.isArray(r[h][u])||r[h][u]instanceof Date)if(Array.isArray(d.v)&&(d.f=r[h][u][1],d.v=d.v[0]),null===d.v)if(d.f)d.t="n";else if(a.nullError)d.t="e",d.v=0;else{if(!a.sheetStubs)continue;d.t="z"}else"number"==typeof d.v?d.t="n":"boolean"==typeof d.v?d.t="b":d.v instanceof Date?(d.z=a.dateNF||eo[14],a.cellDates?(d.t="d",d.w=eF(d.z,ez(d.v))):(d.t="n",d.v=ez(d.v),d.w=eF(d.z,d.v))):d.t="s";else d=r[h][u];if(n)s[p]||(s[p]=[]),s[p][m]&&s[p][m].z&&(d.z=s[p][m].z),s[p][m]=d;else{var g=tb({c:m,r:p});s[g]&&s[g].z&&(d.z=s[g].z),s[g]=d}}}return l.s.c<1e7&&(s["!ref"]=tw(l)),s}(null,e,r)}function tR(e){var r=e.read_shift(4);return 0===r?"":e.read_shift(r,"dbcs")}function tO(e,r){var t=e.l,a=e.read_shift(1),n=tR(e),s=[],i={t:n,h:n};if((1&a)!=0){for(var c=e.read_shift(4),o=0;o!=c;++o)s.push({ich:e.read_shift(2),ifnt:e.read_shift(2)});i.r=s}else i.r=[{ich:0,ifnt:0}];return e.l=t+r,i}function tx(e){var r=e.read_shift(4),t=e.read_shift(2);return t+=e.read_shift(1)<<16,e.l++,{c:r,iStyleRef:t}}function t_(e){var r=e.read_shift(2);return r+=e.read_shift(1)<<16,e.l++,{c:-1,iStyleRef:r}}function tI(e){var r=e.read_shift(4);return 0===r||0xffffffff===r?"":e.read_shift(r,"dbcs")}function tN(e){var r=e.slice(e.l,e.l+4),t=1&r[0],a=2&r[0];e.l+=4;var n=0===a?r2([0,0,0,0,252&r[0],r[1],r[2],r[3]],0):r7(r,0)>>2;return t?n/100:n}function tD(e){var r={s:{},e:{}};return r.s.r=e.read_shift(4),r.e.r=e.read_shift(4),r.s.c=e.read_shift(4),r.e.c=e.read_shift(4),r}function tF(e){if(e.length-e.l<8)throw"XLS Xnum Buffer underflow";return e.read_shift(8,"f")}function tP(e,r){var t=e.read_shift(4);switch(t){case 0:return"";case 0xffffffff:case 0xfffffffe:return({2:"BITMAP",3:"METAFILEPICT",8:"DIB",14:"ENHMETAFILE"})[e.read_shift(4)]||""}if(t>400)throw Error("Unsupported Clipboard: "+t.toString(16));return e.l-=4,e.read_shift(0,1==r?"lpstr":"lpwstr")}var tL=[80,81],tM={1:{n:"CodePage",t:2},2:{n:"Category",t:80},3:{n:"PresentationFormat",t:80},4:{n:"ByteCount",t:3},5:{n:"LineCount",t:3},6:{n:"ParagraphCount",t:3},7:{n:"SlideCount",t:3},8:{n:"NoteCount",t:3},9:{n:"HiddenCount",t:3},10:{n:"MultimediaClipCount",t:3},11:{n:"ScaleCrop",t:11},12:{n:"HeadingPairs",t:4108},13:{n:"TitlesOfParts",t:4126},14:{n:"Manager",t:80},15:{n:"Company",t:80},16:{n:"LinksUpToDate",t:11},17:{n:"CharacterCount",t:3},19:{n:"SharedDoc",t:11},22:{n:"HyperlinksChanged",t:11},23:{n:"AppVersion",t:3,p:"version"},24:{n:"DigSig",t:65},26:{n:"ContentType",t:80},27:{n:"ContentStatus",t:80},28:{n:"Language",t:80},29:{n:"Version",t:80},255:{},0x80000000:{n:"Locale",t:19},0x80000003:{n:"Behavior",t:19},0x72627262:{}},tU={1:{n:"CodePage",t:2},2:{n:"Title",t:80},3:{n:"Subject",t:80},4:{n:"Author",t:80},5:{n:"Keywords",t:80},6:{n:"Comments",t:80},7:{n:"Template",t:80},8:{n:"LastAuthor",t:80},9:{n:"RevNumber",t:80},10:{n:"EditTime",t:64},11:{n:"LastPrinted",t:64},12:{n:"CreatedDate",t:64},13:{n:"ModifiedDate",t:64},14:{n:"PageCount",t:3},15:{n:"WordCount",t:3},16:{n:"CharCount",t:3},17:{n:"Thumbnail",t:71},18:{n:"Application",t:80},19:{n:"DocSecurity",t:3},255:{},0x80000000:{n:"Locale",t:19},0x80000003:{n:"Behavior",t:19},0x72627262:{}},tB={1:"US",2:"CA",3:"",7:"RU",20:"EG",30:"GR",31:"NL",32:"BE",33:"FR",34:"ES",36:"HU",39:"IT",41:"CH",43:"AT",44:"GB",45:"DK",46:"SE",47:"NO",48:"PL",49:"DE",52:"MX",55:"BR",61:"AU",64:"NZ",66:"TH",81:"JP",82:"KR",84:"VN",86:"CN",90:"TR",105:"JS",213:"DZ",216:"MA",218:"LY",351:"PT",354:"IS",358:"FI",420:"CZ",886:"TW",961:"LB",962:"JO",963:"SY",964:"IQ",965:"KW",966:"SA",971:"AE",972:"IL",974:"QA",981:"IR",65535:"US"},tH=[null,"solid","mediumGray","darkGray","lightGray","darkHorizontal","darkVertical","darkDown","darkUp","darkGrid","darkTrellis","lightHorizontal","lightVertical","lightDown","lightUp","lightGrid","lightTrellis","gray125","gray0625"],tW=e1([0,0xffffff,0xff0000,65280,255,0xffff00,0xff00ff,65535,0,0xffffff,0xff0000,65280,255,0xffff00,0xff00ff,65535,8388608,32768,128,8421376,8388736,32896,0xc0c0c0,8421504,0x9999ff,0x993366,0xffffcc,0xccffff,6684774,0xff8080,26316,0xccccff,128,0xff00ff,0xffff00,65535,8388736,8388608,32896,255,52479,0xccffff,0xccffcc,0xffff99,0x99ccff,0xff99cc,0xcc99ff,0xffcc99,3368703,3394764,0x99cc00,0xffcc00,0xff9900,0xff6600,6710937,9868950,13158,3381606,13056,3355392,0x993300,0x993366,3355545,3355443,0xffffff,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0].map(function(e){return[e>>16&255,e>>8&255,255&e]})),tV={0:"#NULL!",7:"#DIV/0!",15:"#VALUE!",23:"#REF!",29:"#NAME?",36:"#NUM!",42:"#N/A",43:"#GETTING_DATA",255:"#WTF?"},tG={"#NULL!":0,"#DIV/0!":7,"#VALUE!":15,"#REF!":23,"#NAME?":29,"#NUM!":36,"#N/A":42,"#GETTING_DATA":43,"#WTF?":255},tz={"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet.main+xml":"workbooks","application/vnd.ms-excel.sheet.macroEnabled.main+xml":"workbooks","application/vnd.ms-excel.sheet.binary.macroEnabled.main":"workbooks","application/vnd.ms-excel.addin.macroEnabled.main+xml":"workbooks","application/vnd.openxmlformats-officedocument.spreadsheetml.template.main+xml":"workbooks","application/vnd.openxmlformats-officedocument.spreadsheetml.worksheet+xml":"sheets","application/vnd.ms-excel.worksheet":"sheets","application/vnd.ms-excel.binIndexWs":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.chartsheet+xml":"charts","application/vnd.ms-excel.chartsheet":"charts","application/vnd.ms-excel.macrosheet+xml":"macros","application/vnd.ms-excel.macrosheet":"macros","application/vnd.ms-excel.intlmacrosheet":"TODO","application/vnd.ms-excel.binIndexMs":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.dialogsheet+xml":"dialogs","application/vnd.ms-excel.dialogsheet":"dialogs","application/vnd.openxmlformats-officedocument.spreadsheetml.sharedStrings+xml":"strs","application/vnd.ms-excel.sharedStrings":"strs","application/vnd.openxmlformats-officedocument.spreadsheetml.styles+xml":"styles","application/vnd.ms-excel.styles":"styles","application/vnd.openxmlformats-package.core-properties+xml":"coreprops","application/vnd.openxmlformats-officedocument.custom-properties+xml":"custprops","application/vnd.openxmlformats-officedocument.extended-properties+xml":"extprops","application/vnd.openxmlformats-officedocument.customXmlProperties+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.customProperty":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.comments+xml":"comments","application/vnd.ms-excel.comments":"comments","application/vnd.ms-excel.threadedcomments+xml":"threadedcomments","application/vnd.ms-excel.person+xml":"people","application/vnd.openxmlformats-officedocument.spreadsheetml.sheetMetadata+xml":"metadata","application/vnd.ms-excel.sheetMetadata":"metadata","application/vnd.ms-excel.pivotTable":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.pivotTable+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.chart+xml":"TODO","application/vnd.ms-office.chartcolorstyle+xml":"TODO","application/vnd.ms-office.chartstyle+xml":"TODO","application/vnd.ms-office.chartex+xml":"TODO","application/vnd.ms-excel.calcChain":"calcchains","application/vnd.openxmlformats-officedocument.spreadsheetml.calcChain+xml":"calcchains","application/vnd.openxmlformats-officedocument.spreadsheetml.printerSettings":"TODO","application/vnd.ms-office.activeX":"TODO","application/vnd.ms-office.activeX+xml":"TODO","application/vnd.ms-excel.attachedToolbars":"TODO","application/vnd.ms-excel.connections":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.connections+xml":"TODO","application/vnd.ms-excel.externalLink":"links","application/vnd.openxmlformats-officedocument.spreadsheetml.externalLink+xml":"links","application/vnd.ms-excel.pivotCacheDefinition":"TODO","application/vnd.ms-excel.pivotCacheRecords":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.pivotCacheDefinition+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.pivotCacheRecords+xml":"TODO","application/vnd.ms-excel.queryTable":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.queryTable+xml":"TODO","application/vnd.ms-excel.userNames":"TODO","application/vnd.ms-excel.revisionHeaders":"TODO","application/vnd.ms-excel.revisionLog":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.revisionHeaders+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.revisionLog+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.userNames+xml":"TODO","application/vnd.ms-excel.tableSingleCells":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.tableSingleCells+xml":"TODO","application/vnd.ms-excel.slicer":"TODO","application/vnd.ms-excel.slicerCache":"TODO","application/vnd.ms-excel.slicer+xml":"TODO","application/vnd.ms-excel.slicerCache+xml":"TODO","application/vnd.ms-excel.wsSortMap":"TODO","application/vnd.ms-excel.table":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.table+xml":"TODO","application/vnd.openxmlformats-officedocument.theme+xml":"themes","application/vnd.openxmlformats-officedocument.themeOverride+xml":"TODO","application/vnd.ms-excel.Timeline+xml":"TODO","application/vnd.ms-excel.TimelineCache+xml":"TODO","application/vnd.ms-office.vbaProject":"vba","application/vnd.ms-office.vbaProjectSignature":"TODO","application/vnd.ms-office.volatileDependencies":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.volatileDependencies+xml":"TODO","application/vnd.ms-excel.controlproperties+xml":"TODO","application/vnd.openxmlformats-officedocument.model+data":"TODO","application/vnd.ms-excel.Survey+xml":"TODO","application/vnd.openxmlformats-officedocument.drawing+xml":"drawings","application/vnd.openxmlformats-officedocument.drawingml.chartshapes+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramColors+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramData+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramLayout+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramStyle+xml":"TODO","application/vnd.openxmlformats-officedocument.vmlDrawing":"TODO","application/vnd.openxmlformats-package.relationships+xml":"rels","application/vnd.openxmlformats-officedocument.oleObject":"TODO","image/png":"TODO",sheet:"js"},t$={CMNT:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/comments",CS:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/chartsheet",WS:["http://schemas.openxmlformats.org/officeDocument/2006/relationships/worksheet","http://purl.oclc.org/ooxml/officeDocument/relationships/worksheet"],DS:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/dialogsheet",MS:"http://schemas.microsoft.com/office/2006/relationships/xlMacrosheet",TCMNT:"http://schemas.microsoft.com/office/2017/10/relationships/threadedComment"};function tY(e){var r=e.lastIndexOf("/");return e.slice(0,r+1)+"_rels/"+e.slice(r+1)+".rels"}function tj(e,r){var t={"!id":{}};if(!e)return t;"/"!==r.charAt(0)&&(r="/"+r);var a={};return(e.match(ro)||[]).forEach(function(e){var n=rh(e);if("<Relationship"===n[0]){var s={};s.Type=n.Type,s.Target=n.Target,s.Id=n.Id,n.TargetMode&&(s.TargetMode=n.TargetMode),t["External"===n.TargetMode?n.Target:rn(n.Target,r)]=s,a[n.Id]=s}}),t["!id"]=a,t}var tK=[["cp:category","Category"],["cp:contentStatus","ContentStatus"],["cp:keywords","Keywords"],["cp:lastModifiedBy","LastAuthor"],["cp:lastPrinted","LastPrinted"],["cp:revision","RevNumber"],["cp:version","Version"],["dc:creator","Author"],["dc:description","Comments"],["dc:identifier","Identifier"],["dc:language","Language"],["dc:subject","Subject"],["dc:title","Title"],["dcterms:created","CreatedDate","date"],["dcterms:modified","ModifiedDate","date"]],tX=function(){for(var e=Array(tK.length),r=0;r<tK.length;++r){var t=tK[r],a="(?:"+t[0].slice(0,t[0].indexOf(":"))+":)"+t[0].slice(t[0].indexOf(":")+1);e[r]=RegExp("<"+a+"[^>]*>([\\s\\S]*?)</"+a+">")}return e}();function tq(e){var r={};e=ry(e);for(var t=0;t<tK.length;++t){var a=tK[t],n=e.match(tX[t]);null!=n&&n.length>0&&(r[a[1]]=rm(n[1])),"date"===a[2]&&r[a[1]]&&(r[a[1]]=eZ(r[a[1]]))}return r}var tJ=[["Application","Application","string"],["AppVersion","AppVersion","string"],["Company","Company","string"],["DocSecurity","DocSecurity","string"],["Manager","Manager","string"],["HyperlinksChanged","HyperlinksChanged","bool"],["SharedDoc","SharedDoc","bool"],["LinksUpToDate","LinksUpToDate","bool"],["ScaleCrop","ScaleCrop","bool"],["HeadingPairs","HeadingPairs","raw"],["TitlesOfParts","TitlesOfParts","raw"]];function tZ(e,r,t,a){var n=[];if("string"==typeof e)n=rN(e,a);else for(var s=0;s<e.length;++s)n=n.concat(e[s].map(function(e){return{v:e}}));var i="string"==typeof r?rN(r,a).map(function(e){return e.v}):r,c=0,o=0;if(i.length>0)for(var l=0;l!==n.length;l+=2){switch(o=+n[l+1].v,n[l].v){case"Worksheets":case"工作表":case"Листы":case"أوراق العمل":case"ワークシート":case"גליונות עבודה":case"Arbeitsblätter":case"Çalışma Sayfaları":case"Feuilles de calcul":case"Fogli di lavoro":case"Folhas de cálculo":case"Planilhas":case"Regneark":case"Hojas de cálculo":case"Werkbladen":t.Worksheets=o,t.SheetNames=i.slice(c,c+o);break;case"Named Ranges":case"Rangos con nombre":case"名前付き一覧":case"Benannte Bereiche":case"Navngivne områder":t.NamedRanges=o,t.DefinedNames=i.slice(c,c+o);break;case"Charts":case"Diagramme":t.Chartsheets=o,t.ChartNames=i.slice(c,c+o)}c+=o}}var tQ=/<[^>]+>[^<]*/g,t1={Title:"Title",Subject:"Subject",Author:"Author",Keywords:"Keywords",Comments:"Description",LastAuthor:"LastAuthor",RevNumber:"Revision",Application:"AppName",LastPrinted:"LastPrinted",CreatedDate:"Created",ModifiedDate:"LastSaved",Category:"Category",Manager:"Manager",Company:"Company",AppVersion:"Version",ContentStatus:"ContentStatus",Identifier:"Identifier",Language:"Language"};function t0(e){var r=e.read_shift(4);return new Date((e.read_shift(4)/1e7*0x100000000+r/1e7-0x2b6109100)*1e3).toISOString().replace(/\.000/,"")}function t2(e,r,t){var a=e.l,n=e.read_shift(0,"lpstr-cp");if(t)for(;e.l-a&3;)++e.l;return n}function t4(e,r,t){var a=e.read_shift(0,"lpwstr");return t&&(e.l+=4-(a.length+1&3)&3),a}function t3(e,r,t){return 31===r?t4(e):t2(e,r,t)}function t5(e,r,t){return t3(e,r,4*(!1!==t))}function t6(e,r){for(var t=e.read_shift(4),a={},n=0;n!=t;++n){var s=e.read_shift(4),i=e.read_shift(4);a[s]=e.read_shift(i,1200===r?"utf16le":"utf8").replace(Z,"").replace(Q,"!"),1200===r&&i%2&&(e.l+=2)}return 3&e.l&&(e.l=e.l>>3<<2),a}function t8(e){var r=e.read_shift(4),t=e.slice(e.l,e.l+r);return e.l+=r,(3&r)>0&&(e.l+=4-(3&r)&3),t}function t7(e,r,t){var a,n,s=e.read_shift(2),i=t||{};if(e.l+=2,12!==r&&s!==r&&-1===tL.indexOf(r)&&((65534&r)!=4126||(65534&s)!=4126))throw Error("Expected type "+r+" saw "+s);switch(12===r?s:r){case 2:return n=e.read_shift(2,"i"),i.raw||(e.l+=2),n;case 3:return e.read_shift(4,"i");case 11:return 0!==e.read_shift(4);case 19:return e.read_shift(4);case 30:return t2(e,s,4).replace(Z,"");case 31:return t4(e);case 64:return t0(e);case 65:return t8(e);case 71:return(a={}).Size=e.read_shift(4),e.l+=a.Size+3-(a.Size-1)%4,a;case 80:return t5(e,s,!i.raw).replace(Z,"");case 81:return(function(e,r){if(!r)throw Error("VtUnalignedString must have positive length");return t3(e,r,0)})(e,s).replace(Z,"");case 4108:for(var c=e.read_shift(4),o=[],l=0;l<c/2;++l)o.push(function(e){var r=e.l,t=t7(e,81);return 0==e[e.l]&&0==e[e.l+1]&&e.l-r&2&&(e.l+=2),[t,t7(e,3)]}(e));return o;case 4126:case 4127:return 4127==s?function(e){for(var r=e.read_shift(4),t=[],a=0;a!=r;++a){var n=e.l;t[a]=e.read_shift(0,"lpwstr").replace(Z,""),e.l-n&2&&(e.l+=2)}return t}(e):function(e){for(var r=e.read_shift(4),t=[],a=0;a!=r;++a)t[a]=e.read_shift(0,"lpstr-cp").replace(Z,"");return t}(e);default:throw Error("TypedPropertyValue unrecognized type "+r+" "+s)}}function t9(e,r){var t=e.l,a=e.read_shift(4),n=e.read_shift(4),s=[],i=0,c=0,o=-1,l={};for(i=0;i!=n;++i){var f=e.read_shift(4),h=e.read_shift(4);s[i]=[f,h+t]}s.sort(function(e,r){return e[1]-r[1]});var u={};for(i=0;i!=n;++i){if(e.l!==s[i][1]){var d=!0;if(i>0&&r)switch(r[s[i-1][0]].t){case 2:e.l+2===s[i][1]&&(e.l+=2,d=!1);break;case 80:case 4108:e.l<=s[i][1]&&(e.l=s[i][1],d=!1)}if((!r||0==i)&&e.l<=s[i][1]&&(d=!1,e.l=s[i][1]),d)throw Error("Read Error: Expected address "+s[i][1]+" at "+e.l+" :"+i)}if(r){var p=r[s[i][0]];if(u[p.n]=t7(e,p.t,{raw:!0}),"version"===p.p&&(u[p.n]=String(u[p.n]>>16)+"."+("0000"+String(65535&u[p.n])).slice(-4)),"CodePage"==p.n)switch(u[p.n]){case 0:u[p.n]=1252;case 874:case 932:case 936:case 949:case 950:case 1250:case 1251:case 1253:case 1254:case 1255:case 1256:case 1257:case 1258:case 1e4:case 1200:case 1201:case 1252:case 65e3:case -536:case 65001:case -535:D(c=u[p.n]>>>0&65535);break;default:throw Error("Unsupported CodePage: "+u[p.n])}}else if(1===s[i][0]){if(D(c=u.CodePage=t7(e,2)),-1!==o){var m=e.l;e.l=s[o][1],l=t6(e,c),e.l=m}}else if(0===s[i][0]){if(0===c){o=i,e.l=s[i+1][1];continue}l=t6(e,c)}else{var g,v=l[s[i][0]];switch(e[e.l]){case 65:e.l+=4,g=t8(e);break;case 30:case 31:e.l+=4,g=t5(e,e[e.l-4]).replace(/\u0000+$/,"");break;case 3:e.l+=4,g=e.read_shift(4,"i");break;case 19:e.l+=4,g=e.read_shift(4);break;case 5:e.l+=4,g=e.read_shift(8,"f");break;case 11:e.l+=4,g=at(e,4);break;case 64:e.l+=4,g=eZ(t0(e));break;default:throw Error("unparsed value: "+e[e.l])}u[v]=g}}return e.l=t+a,u}function ae(e,r,t){var a,n=e.content;if(!n)return{};ts(n,0);var s,i,c,o,l=0;n.chk("feff","Byte Order: "),n.read_shift(2);var f=n.read_shift(4),h=n.read_shift(16);if(h!==eH.utils.consts.HEADER_CLSID&&h!==t)throw Error("Bad PropertySet CLSID "+h);if(1!==(s=n.read_shift(4))&&2!==s)throw Error("Unrecognized #Sets: "+s);if(i=n.read_shift(16),o=n.read_shift(4),1===s&&o!==n.l)throw Error("Length mismatch: "+o+" !== "+n.l);2===s&&(c=n.read_shift(16),l=n.read_shift(4));var u=t9(n,r),d={SystemIdentifier:f};for(var p in u)d[p]=u[p];if(d.FMTID=i,1===s)return d;if(l-n.l==2&&(n.l+=2),n.l!==l)throw Error("Length mismatch 2: "+n.l+" !== "+l);try{a=t9(n,null)}catch(e){}for(p in a)d[p]=a[p];return d.FMTID=[i,c],d}function ar(e,r){return e.read_shift(r),null}function at(e,r){return 1===e.read_shift(r)}function aa(e){return e.read_shift(2,"u")}function an(e,r){for(var t=[],a=e.l+r;e.l<a;)t.push(aa(e,a-e.l));if(a!==e.l)throw Error("Slurp error");return t}function as(e,r,t){var a=e.read_shift(t&&t.biff>=12?2:1),n="sbcs-cont",s=O;t&&t.biff>=8&&(O=1200),t&&8!=t.biff?12==t.biff&&(n="wstr"):e.read_shift(1)&&(n="dbcs-cont"),t.biff>=2&&t.biff<=5&&(n="cpstr");var i=a?e.read_shift(a,n):"";return O=s,i}function ai(e,r,t){if(t){if(t.biff>=2&&t.biff<=5)return e.read_shift(r,"cpstr");if(t.biff>=12)return e.read_shift(r,"dbcs-cont")}return 0===e.read_shift(1)?e.read_shift(r,"sbcs-cont"):e.read_shift(r,"dbcs-cont")}function ac(e,r,t){var a=e.read_shift(t&&2==t.biff?1:2);return 0===a?(e.l++,""):ai(e,a,t)}function ao(e,r,t){if(t.biff>5)return ac(e,r,t);var a=e.read_shift(1);return 0===a?(e.l++,""):e.read_shift(a,t.biff<=4||!e.lens?"cpstr":"sbcs-cont")}function al(e){var r=e.read_shift(4);return r>0?e.read_shift(r,"utf16le").replace(Z,""):""}function af(e){return[e.read_shift(1),e.read_shift(1),e.read_shift(1),e.read_shift(1)]}function ah(e,r){var t=af(e,r);return t[3]=0,t}function au(e){return{r:e.read_shift(2),c:e.read_shift(2),ixfe:e.read_shift(2)}}function ad(e){return[e.read_shift(2),tN(e)]}function ap(e){var r=e.read_shift(2),t=e.read_shift(2);return{s:{c:e.read_shift(2),r:r},e:{c:e.read_shift(2),r:t}}}function am(e){var r=e.read_shift(2),t=e.read_shift(2);return{s:{c:e.read_shift(1),r:r},e:{c:e.read_shift(1),r:t}}}function ag(e){e.l+=4;var r=e.read_shift(2),t=e.read_shift(2),a=e.read_shift(2);return e.l+=12,[t,r,a]}function av(e){e.l+=2,e.l+=e.read_shift(2)}var aT={0:av,4:av,5:av,6:av,7:function(e){return e.l+=4,e.cf=e.read_shift(2),{}},8:av,9:av,10:av,11:av,12:av,13:function(e){var r={};return e.l+=4,e.l+=16,r.fSharedNote=e.read_shift(2),e.l+=4,r},14:av,15:av,16:av,17:av,18:av,19:av,20:av,21:ag};function ab(e,r){var t={BIFFVer:0,dt:0};switch(t.BIFFVer=e.read_shift(2),(r-=2)>=2&&(t.dt=e.read_shift(2),e.l-=2),t.BIFFVer){case 1536:case 1280:case 1024:case 768:case 512:case 2:case 7:break;default:if(r>6)throw Error("Unexpected BIFF Ver "+t.BIFFVer)}return e.read_shift(r),t}function aE(e,r,t){var a=0;t&&2==t.biff||(a=e.read_shift(2));var n=e.read_shift(2);return t&&2==t.biff&&(a=1-(n>>15),n&=32767),[{Unsynced:1&a,DyZero:(2&a)>>1,ExAsc:(4&a)>>2,ExDsc:(8&a)>>3},n]}function aw(e,r,t){var a=e.l+r,n=8!=t.biff&&t.biff?2:4,s=e.read_shift(n),i=e.read_shift(n),c=e.read_shift(2),o=e.read_shift(2);return e.l=a,{s:{r:s,c:c},e:{r:i,c:o}}}function aA(e,r,t){var a,n=au(e,6);(2==t.biff||9==r)&&++e.l;var s=(a=e.read_shift(1),1===e.read_shift(1)?a:1===a);return n.val=s,n.t=!0===s||!1===s?"b":"e",n}var aS=function(e,r,t){return 0===r?"":ao(e,r,t)};function ak(e,r,t){var a,n=e.read_shift(2),s={fBuiltIn:1&n,fWantAdvise:n>>>1&1,fWantPict:n>>>2&1,fOle:n>>>3&1,fOleLink:n>>>4&1,cf:n>>>5&1023,fIcon:n>>>15&1};return 14849===t.sbcch&&(a=function(e,r,t){e.l+=4,r-=4;var a=e.l+r,n=as(e,r,t),s=e.read_shift(2);if(s!==(a-=e.l))throw Error("Malformed AddinUdf: padding = "+a+" != "+s);return e.l+=s,n}(e,r-2,t)),s.body=a||e.read_shift(r-2),"string"==typeof a&&(s.Name=a),s}var ay=["_xlnm.Consolidate_Area","_xlnm.Auto_Open","_xlnm.Auto_Close","_xlnm.Extract","_xlnm.Database","_xlnm.Criteria","_xlnm.Print_Area","_xlnm.Print_Titles","_xlnm.Recorder","_xlnm.Data_Form","_xlnm.Auto_Activate","_xlnm.Auto_Deactivate","_xlnm.Sheet_Title","_xlnm._FilterDatabase"];function aC(e,r,t){var a,n,s,i,c,o,l,f=e.l+r,h=e.read_shift(2),u=e.read_shift(1),d=e.read_shift(1),p=e.read_shift(t&&2==t.biff?1:2),m=0;(!t||t.biff>=5)&&(5!=t.biff&&(e.l+=2),m=e.read_shift(2),5==t.biff&&(e.l+=2),e.l+=4);var g=ai(e,d,t);32&h&&(g=ay[g.charCodeAt(0)]);var v=f-e.l;return t&&2==t.biff&&--v,{chKey:u,Name:g,itab:m,rgce:f!=e.l&&0!==p&&v>0?(a=e,n=v,s=t,i=p,o=a.l+n,l=nL(a,i,s),o!==a.l&&(c=nP(a,o-a.l,l,s)),[l,c]):[]}}function aR(e,r,t){if(t.biff<8){var a,n,s,i;return a=e,n=r,s=t,3==a[a.l+1]&&a[a.l]++,3==(i=as(a,n,s)).charCodeAt(0)?i.slice(1):i}for(var c=[],o=e.l+r,l=e.read_shift(t.biff>8?4:2);0!=l--;)c.push(function(e,r,t){var a=t.biff>8?4:2;return[e.read_shift(a),e.read_shift(a,"i"),e.read_shift(a,"i")]}(e,t.biff,t));if(e.l!=o)throw Error("Bad ExternSheet: "+e.l+" != "+o);return c}function aO(e,r,t){var a=am(e,6);switch(t.biff){case 2:e.l++,r-=7;break;case 3:case 4:e.l+=2,r-=8;break;default:e.l+=6,r-=12}return[a,function(e,r,t){var a,n,s=e.l+r,i=2==t.biff?1:2,c=e.read_shift(i);if(65535==c)return[[],(a=r-2,void(e.l+=a))];var o=nL(e,c,t);return r!==c+i&&(n=nP(e,r-c-i,o,t)),e.l=s,[o,n]}(e,r,t,a)]}var ax={8:function(e,r){var t=e.l+r;e.l+=10;var a=e.read_shift(2);e.l+=4,e.l+=2,e.l+=2,e.l+=2,e.l+=4;var n=e.read_shift(1);return e.l+=n,e.l=t,{fmt:a}}};function a_(e,r,t){if(!t.cellStyles)return void(e.l+=r);var a=t&&t.biff>=12?4:2,n=e.read_shift(a),s=e.read_shift(a),i=e.read_shift(a),c=e.read_shift(a),o=e.read_shift(2);2==a&&(e.l+=2);var l={s:n,e:s,w:i,ixfe:c,flags:o};return(t.biff>=5||!t.biff)&&(l.level=o>>8&7),l}var aI=[2,3,48,49,131,139,140,245],aN=function(){var e={1:437,2:850,3:1252,4:1e4,100:852,101:866,102:865,103:861,104:895,105:620,106:737,107:857,120:950,121:949,122:936,123:932,124:874,125:1255,126:1256,150:10007,151:10029,152:10006,200:1250,201:1251,202:1254,203:1253,0:20127,8:865,9:437,10:850,11:437,13:437,14:850,15:437,16:850,17:437,18:850,19:932,20:850,21:437,22:850,23:865,24:437,25:437,26:850,27:437,28:863,29:850,31:852,34:852,35:852,36:860,37:850,38:866,55:850,64:852,77:936,78:949,79:950,80:874,87:1252,88:1252,89:1252,108:863,134:737,135:852,136:857,204:1257,255:16969},r=eV({1:437,2:850,3:1252,4:1e4,100:852,101:866,102:865,103:861,104:895,105:620,106:737,107:857,120:950,121:949,122:936,123:932,124:874,125:1255,126:1256,150:10007,151:10029,152:10006,200:1250,201:1251,202:1254,203:1253,0:20127});function a(r,a){var n=a||{};n.dateNF||(n.dateNF="yyyymmdd");var s=tC(function(r,a){var n=[],s=Y(1);switch(a.type){case"base64":s=K(V(r));break;case"binary":s=K(r);break;case"buffer":case"array":s=r}ts(s,0);var i=s.read_shift(1),c=!!(136&i),o=!1,l=!1;switch(i){case 2:case 3:case 131:case 139:case 245:break;case 48:case 49:o=!0,c=!0;break;case 140:l=!0;break;default:throw Error("DBF Unsupported Version: "+i.toString(16))}var f=0,h=521;2==i&&(f=s.read_shift(2)),s.l+=3,2!=i&&(f=s.read_shift(4)),f>1048576&&(f=1e6),2!=i&&(h=s.read_shift(2));var u=s.read_shift(2),d=a.codepage||1252;2!=i&&(s.l+=16,s.read_shift(1),0!==s[s.l]&&(d=e[s[s.l]]),s.l+=1,s.l+=2),l&&(s.l+=36);for(var p=[],m={},g=Math.min(s.length,2==i?521:h-10-264*!!o),v=l?32:11;s.l<g&&13!=s[s.l];)switch((m={}).name=t.utils.decode(d,s.slice(s.l,s.l+v)).replace(/[\u0000\r\n].*$/g,""),s.l+=v,m.type=String.fromCharCode(s.read_shift(1)),2!=i&&!l&&(m.offset=s.read_shift(4)),m.len=s.read_shift(1),2==i&&(m.offset=s.read_shift(2)),m.dec=s.read_shift(1),m.name.length&&p.push(m),2!=i&&(s.l+=l?13:14),m.type){case"B":(!o||8!=m.len)&&a.WTF&&console.log("Skipping "+m.name+":"+m.type);break;case"G":case"P":a.WTF&&console.log("Skipping "+m.name+":"+m.type);break;case"+":case"0":case"@":case"C":case"D":case"F":case"I":case"L":case"M":case"N":case"O":case"T":case"Y":break;default:throw Error("Unknown Field Type: "+m.type)}if(13!==s[s.l]&&(s.l=h-1),13!==s.read_shift(1))throw Error("DBF Terminator not found "+s.l+" "+s[s.l]);s.l=h;var T=0,b=0;for(b=0,n[0]=[];b!=p.length;++b)n[0][b]=p[b].name;for(;f-- >0;){if(42===s[s.l]){s.l+=u;continue}for(++s.l,n[++T]=[],b=0,b=0;b!=p.length;++b){var E=s.slice(s.l,s.l+p[b].len);s.l+=p[b].len,ts(E,0);var w=t.utils.decode(d,E);switch(p[b].type){case"C":w.trim().length&&(n[T][b]=w.replace(/\s+$/,""));break;case"D":8===w.length?n[T][b]=new Date(+w.slice(0,4),w.slice(4,6)-1,+w.slice(6,8)):n[T][b]=w;break;case"F":n[T][b]=parseFloat(w.trim());break;case"+":case"I":n[T][b]=l?0x80000000^E.read_shift(-4,"i"):E.read_shift(4,"i");break;case"L":switch(w.trim().toUpperCase()){case"Y":case"T":n[T][b]=!0;break;case"N":case"F":n[T][b]=!1;break;case"":case"?":break;default:throw Error("DBF Unrecognized L:|"+w+"|")}break;case"M":if(!c)throw Error("DBF Unexpected MEMO for type "+i.toString(16));n[T][b]="##MEMO##"+(l?parseInt(w.trim(),10):E.read_shift(4));break;case"N":(w=w.replace(/\u0000/g,"").trim())&&"."!=w&&(n[T][b]=+w||0);break;case"@":n[T][b]=new Date(E.read_shift(-8,"f")-621356832e5);break;case"T":n[T][b]=new Date((E.read_shift(4)-2440588)*864e5+E.read_shift(4));break;case"Y":n[T][b]=E.read_shift(4,"i")/1e4+E.read_shift(4,"i")/1e4*0x100000000;break;case"O":n[T][b]=-E.read_shift(-8,"f");break;case"B":if(o&&8==p[b].len){n[T][b]=E.read_shift(8,"f");break}case"G":case"P":E.l+=p[b].len;break;case"0":if("_NullFlags"===p[b].name)break;default:throw Error("DBF Unsupported data type "+p[b].type)}}}if(2!=i&&s.l<s.length&&26!=s[s.l++])throw Error("DBF EOF Marker missing "+(s.l-1)+" of "+s.length+" "+s[s.l-1].toString(16));return a&&a.sheetRows&&(n=n.slice(0,a.sheetRows)),a.DBF=p,n}(r,n),n);return s["!cols"]=n.DBF.map(function(e){return{wch:e.len,DBF:e}}),delete n.DBF,s}var n={B:8,C:250,L:1,D:8,"?":0,"":0};return{to_workbook:function(e,r){try{return ty(a(e,r),r)}catch(e){if(r&&r.WTF)throw e}return{SheetNames:[],Sheets:{}}},to_sheet:a,from_sheet:function(e,t){var a=t||{};if(+a.codepage>=0&&D(+a.codepage),"string"==a.type)throw Error("Cannot write DBF to JS string");var s=tl(),i=s$(e,{header:1,raw:!0,cellDates:!0}),c=i[0],o=i.slice(1),l=e["!cols"]||[],f=0,h=0,u=0,d=1;for(f=0;f<c.length;++f){if(((l[f]||{}).DBF||{}).name){c[f]=l[f].DBF.name,++u;continue}if(null!=c[f]){if(++u,"number"==typeof c[f]&&(c[f]=c[f].toString(10)),"string"!=typeof c[f])throw Error("DBF Invalid column name "+c[f]+" |"+typeof c[f]+"|");if(c.indexOf(c[f])!==f){for(h=0;h<1024;++h)if(-1==c.indexOf(c[f]+"_"+h)){c[f]+="_"+h;break}}}}var p=tA(e["!ref"]),m=[],g=[],v=[];for(f=0;f<=p.e.c-p.s.c;++f){var T="",b="",E=0,w=[];for(h=0;h<o.length;++h)null!=o[h][f]&&w.push(o[h][f]);if(0==w.length||null==c[f]){m[f]="?";continue}for(h=0;h<w.length;++h){switch(typeof w[h]){case"number":b="B";break;case"string":default:b="C";break;case"boolean":b="L";break;case"object":b=w[h]instanceof Date?"D":"C"}E=Math.max(E,String(w[h]).length),T=T&&T!=b?"C":b}E>250&&(E=250),"C"==(b=((l[f]||{}).DBF||{}).type)&&l[f].DBF.len>E&&(E=l[f].DBF.len),"B"==T&&"N"==b&&(T="N",v[f]=l[f].DBF.dec,E=l[f].DBF.len),g[f]="C"==T||"N"==b?E:n[T]||0,d+=g[f],m[f]=T}var A=s.next(32);for(A.write_shift(4,0x13021130),A.write_shift(4,o.length),A.write_shift(2,296+32*u),A.write_shift(2,d),f=0;f<4;++f)A.write_shift(4,0);for(A.write_shift(4,(+r[x]||3)<<8),f=0,h=0;f<c.length;++f)if(null!=c[f]){var S=s.next(32),k=(c[f].slice(-10)+"\0\0\0\0\0\0\0\0\0\0\0").slice(0,11);S.write_shift(1,k,"sbcs"),S.write_shift(1,"?"==m[f]?"C":m[f],"sbcs"),S.write_shift(4,h),S.write_shift(1,g[f]||n[m[f]]||0),S.write_shift(1,v[f]||0),S.write_shift(1,2),S.write_shift(4,0),S.write_shift(1,0),S.write_shift(4,0),S.write_shift(4,0),h+=g[f]||n[m[f]]||0}var y=s.next(264);for(y.write_shift(4,13),f=0;f<65;++f)y.write_shift(4,0);for(f=0;f<o.length;++f){var C=s.next(d);for(C.write_shift(1,0),h=0;h<c.length;++h)if(null!=c[h])switch(m[h]){case"L":C.write_shift(1,null==o[f][h]?63:o[f][h]?84:70);break;case"B":C.write_shift(8,o[f][h]||0,"f");break;case"N":var R="0";for("number"==typeof o[f][h]&&(R=o[f][h].toFixed(v[h]||0)),u=0;u<g[h]-R.length;++u)C.write_shift(1,32);C.write_shift(1,R,"sbcs");break;case"D":o[f][h]?(C.write_shift(4,("0000"+o[f][h].getFullYear()).slice(-4),"sbcs"),C.write_shift(2,("00"+(o[f][h].getMonth()+1)).slice(-2),"sbcs"),C.write_shift(2,("00"+o[f][h].getDate()).slice(-2),"sbcs")):C.write_shift(8,"00000000","sbcs");break;case"C":var O=String(null!=o[f][h]?o[f][h]:"").slice(0,g[h]);for(C.write_shift(1,O,"sbcs"),u=0;u<g[h]-O.length;++u)C.write_shift(1,32)}}return s.next(1).write_shift(1,26),s.end()}}}(),aD=function(){var e={AA:"À",BA:"Á",CA:"Â",DA:195,HA:"Ä",JA:197,AE:"È",BE:"É",CE:"Ê",HE:"Ë",AI:"Ì",BI:"Í",CI:"Î",HI:"Ï",AO:"Ò",BO:"Ó",CO:"Ô",DO:213,HO:"Ö",AU:"Ù",BU:"Ú",CU:"Û",HU:"Ü",Aa:"à",Ba:"á",Ca:"â",Da:227,Ha:"ä",Ja:229,Ae:"è",Be:"é",Ce:"ê",He:"ë",Ai:"ì",Bi:"í",Ci:"î",Hi:"ï",Ao:"ò",Bo:"ó",Co:"ô",Do:245,Ho:"ö",Au:"ù",Bu:"ú",Cu:"û",Hu:"ü",KC:"Ç",Kc:"ç",q:"æ",z:"œ",a:"Æ",j:"Œ",DN:209,Dn:241,Hy:255,S:169,c:170,R:174,"B ":180,0:176,1:177,2:178,3:179,5:181,6:182,7:183,Q:185,k:186,b:208,i:216,l:222,s:240,y:248,"!":161,'"':162,"#":163,"(":164,"%":165,"'":167,"H ":168,"+":171,";":187,"<":188,"=":189,">":190,"?":191,"{":223},r=RegExp("\x1bN("+eW(e).join("|").replace(/\|\|\|/,"|\\||").replace(/([?()+])/g,"\\$1")+"|\\|)","gm"),a=function(r,t){var a=e[t];return"number"==typeof a?B(a):a},n=function(e,r,t){var a=r.charCodeAt(0)-32<<4|t.charCodeAt(0)-48;return 59==a?e:B(a)};function s(e,s){var i,c=e.split(/[\n\r]+/),o=-1,l=-1,f=0,h=0,u=[],d=[],p=null,m={},g=[],v=[],T=[],b=0;for(+s.codepage>=0&&D(+s.codepage);f!==c.length;++f){b=0;var E,w=c[f].trim().replace(/\x1B([\x20-\x2F])([\x30-\x3F])/g,n).replace(r,a),A=w.replace(/;;/g,"\0").split(";").map(function(e){return e.replace(/\u0000/g,";")}),S=A[0];if(w.length>0)switch(S){case"ID":case"E":case"B":case"O":case"W":break;case"P":"P"==A[1].charAt(0)&&d.push(w.slice(3).replace(/;;/g,";"));break;case"C":var k=!1,y=!1,C=!1,R=!1,O=-1,x=-1;for(h=1;h<A.length;++h)switch(A[h].charAt(0)){case"A":case"G":break;case"X":l=parseInt(A[h].slice(1))-1,y=!0;break;case"Y":for(o=parseInt(A[h].slice(1))-1,y||(l=0),i=u.length;i<=o;++i)u[i]=[];break;case"K":'"'===(E=A[h].slice(1)).charAt(0)?E=E.slice(1,E.length-1):"TRUE"===E?E=!0:"FALSE"===E?E=!1:isNaN(e2(E))?isNaN(e3(E).getDate())||(E=eZ(E)):(E=e2(E),null!==p&&eI(p)&&(E=eK(E))),void 0!==t&&"string"==typeof E&&"string"!=(s||{}).type&&(s||{}).codepage&&(E=t.utils.decode(s.codepage,E)),k=!0;break;case"E":R=!0;var _=nm(A[h].slice(1),{r:o,c:l});u[o][l]=[u[o][l],_];break;case"S":C=!0,u[o][l]=[u[o][l],"S5S"];break;case"R":O=parseInt(A[h].slice(1))-1;break;case"C":x=parseInt(A[h].slice(1))-1;break;default:if(s&&s.WTF)throw Error("SYLK bad record "+w)}if(k&&(u[o][l]&&2==u[o][l].length?u[o][l][0]=E:u[o][l]=E,p=null),C){if(R)throw Error("SYLK shared formula cannot have own formula");var I=O>-1&&u[O][x];if(!I||!I[1])throw Error("SYLK shared formula cannot find base");u[o][l][1]=nT(I[1],{r:o-O,c:l-x})}break;case"F":var N=0;for(h=1;h<A.length;++h)switch(A[h].charAt(0)){case"X":l=parseInt(A[h].slice(1))-1,++N;break;case"Y":for(o=parseInt(A[h].slice(1))-1,i=u.length;i<=o;++i)u[i]=[];break;case"M":b=parseInt(A[h].slice(1))/20;break;case"F":case"G":case"S":case"D":case"N":break;case"P":p=d[parseInt(A[h].slice(1))];break;case"W":for(i=parseInt((T=A[h].slice(1).split(" "))[0],10);i<=parseInt(T[1],10);++i)b=parseInt(T[2],10),v[i-1]=0===b?{hidden:!0}:{wch:b},a9(v[i-1]);break;case"C":v[l=parseInt(A[h].slice(1))-1]||(v[l]={});break;case"R":g[o=parseInt(A[h].slice(1))-1]||(g[o]={}),b>0?(g[o].hpt=b,g[o].hpx=ne(b)):0===b&&(g[o].hidden=!0);break;default:if(s&&s.WTF)throw Error("SYLK bad record "+w)}N<1&&(p=null);break;default:if(s&&s.WTF)throw Error("SYLK bad record "+w)}}return g.length>0&&(m["!rows"]=g),v.length>0&&(m["!cols"]=v),s&&s.sheetRows&&(u=u.slice(0,s.sheetRows)),[u,m]}function i(e,r){var t=function(e,r){switch(r.type){case"base64":return s(V(e),r);case"binary":return s(e,r);case"buffer":return s(G&&Buffer.isBuffer(e)?e.toString("binary"):X(e),r);case"array":return s(eQ(e),r)}throw Error("Unrecognized type "+r.type)}(e,r),a=t[0],n=t[1],i=tC(a,r);return eW(n).forEach(function(e){i[e]=n[e]}),i}return e["|"]=254,{to_workbook:function(e,r){return ty(i(e,r),r)},to_sheet:i,from_sheet:function(e,r){var t,a=["ID;PWXL;N;E"],n=[],s=tA(e["!ref"]),i=Array.isArray(e);a.push("P;PGeneral"),a.push("F;P0;DG0G8;M255"),e["!cols"]&&e["!cols"].forEach(function(e,r){var t="F;W"+(r+1)+" "+(r+1)+" ";e.hidden?t+="0":("number"!=typeof e.width||e.wpx||(e.wpx=a3(e.width)),"number"!=typeof e.wpx||e.wch||(e.wch=a5(e.wpx)),"number"==typeof e.wch&&(t+=Math.round(e.wch)))," "!=t.charAt(t.length-1)&&a.push(t)}),e["!rows"]&&e["!rows"].forEach(function(e,r){var t="F;";e.hidden?t+="M0;":e.hpt?t+="M"+20*e.hpt+";":e.hpx&&(t+="M"+20*(96*e.hpx/96)+";"),t.length>2&&a.push(t+"R"+(r+1))}),a.push("B;Y"+(s.e.r-s.s.r+1)+";X"+(s.e.c-s.s.c+1)+";D"+[s.s.c,s.s.r,s.e.c,s.e.r].join(" "));for(var c=s.s.r;c<=s.e.r;++c)for(var o=s.s.c;o<=s.e.c;++o){var l=tb({r:c,c:o});(t=i?(e[c]||[])[o]:e[l])&&(null!=t.v||t.f&&!t.F)&&n.push(function(e,r,t,a){var n="C;Y"+(t+1)+";X"+(a+1)+";K";switch(e.t){case"n":n+=e.v||0,e.f&&!e.F&&(n+=";E"+nv(e.f,{r:t,c:a}));break;case"b":n+=e.v?"TRUE":"FALSE";break;case"e":n+=e.w||e.v;break;case"d":n+='"'+(e.w||e.v)+'"';break;case"s":n+='"'+e.v.replace(/"/g,"").replace(/;/g,";;")+'"'}return n}(t,0,c,o,r))}return a.join("\r\n")+"\r\n"+n.join("\r\n")+"\r\nE\r\n"}}}(),aF=function(){var e,r;function t(e,r){for(var t=e.split("\n"),a=-1,n=-1,s=0,i=[];s!==t.length;++s){if("BOT"===t[s].trim()){i[++a]=[],n=0;continue}if(!(a<0)){for(var c=t[s].trim().split(","),o=c[0],l=c[1],f=t[++s]||"";1&(f.match(/["]/g)||[]).length&&s<t.length-1;)f+="\n"+t[++s];switch(f=f.trim(),+o){case -1:if("BOT"===f){i[++a]=[],n=0;continue}if("EOD"!==f)throw Error("Unrecognized DIF special command "+f);break;case 0:"TRUE"===f?i[a][n]=!0:"FALSE"===f?i[a][n]=!1:isNaN(e2(l))?isNaN(e3(l).getDate())?i[a][n]=l:i[a][n]=eZ(l):i[a][n]=e2(l),++n;break;case 1:(f=(f=f.slice(1,f.length-1)).replace(/""/g,'"'))&&f.match(/^=".*"$/)&&(f=f.slice(2,-1)),i[a][n++]=""!==f?f:null}if("EOD"===f)break}}return r&&r.sheetRows&&(i=i.slice(0,r.sheetRows)),i}function a(e,r){return tC(function(e,r){switch(r.type){case"base64":return t(V(e),r);case"binary":return t(e,r);case"buffer":return t(G&&Buffer.isBuffer(e)?e.toString("binary"):X(e),r);case"array":return t(eQ(e),r)}throw Error("Unrecognized type "+r.type)}(e,r),r)}return{to_workbook:function(e,r){return ty(a(e,r),r)},to_sheet:a,from_sheet:(e=function(e,r,t,a,n){e.push(r),e.push(t+","+a),e.push('"'+n.replace(/"/g,'""')+'"')},r=function(e,r,t,a){e.push(r+","+t),e.push(1==r?'"'+a.replace(/"/g,'""')+'"':a)},function(t){var a,n=[],s=tA(t["!ref"]),i=Array.isArray(t);e(n,"TABLE",0,1,"sheetjs"),e(n,"VECTORS",0,s.e.r-s.s.r+1,""),e(n,"TUPLES",0,s.e.c-s.s.c+1,""),e(n,"DATA",0,0,"");for(var c=s.s.r;c<=s.e.r;++c){r(n,-1,0,"BOT");for(var o=s.s.c;o<=s.e.c;++o){var l=tb({r:c,c:o});if(!(a=i?(t[c]||[])[o]:t[l])){r(n,1,0,"");continue}switch(a.t){case"n":var f=a.w;f||null==a.v||(f=a.v),null==f?!a.f||a.F?r(n,1,0,""):r(n,1,0,"="+a.f):r(n,0,f,"V");break;case"b":r(n,0,+!!a.v,a.v?"TRUE":"FALSE");break;case"s":r(n,1,0,isNaN(a.v)?a.v:'="'+a.v+'"');break;case"d":a.w||(a.w=eF(a.z||eo[14],ez(eZ(a.v)))),r(n,0,a.w,"V");break;default:r(n,1,0,"")}}}return r(n,-1,0,"EOD"),n.join("\r\n")})}}(),aP=function(){function e(e){return e.replace(/\\/g,"\\b").replace(/:/g,"\\c").replace(/\n/g,"\\n")}function r(e,r){return tC(function(e,r){for(var t=e.split("\n"),a=-1,n=-1,s=0,i=[];s!==t.length;++s){var c=t[s].trim().split(":");if("cell"===c[0]){var o=tT(c[1]);if(i.length<=o.r)for(a=i.length;a<=o.r;++a)i[a]||(i[a]=[]);switch(a=o.r,n=o.c,c[2]){case"t":i[a][n]=c[3].replace(/\\b/g,"\\").replace(/\\c/g,":").replace(/\\n/g,"\n");break;case"v":i[a][n]=+c[3];break;case"vtf":var l=c[c.length-1];case"vtc":"nl"===c[3]?i[a][n]=!!+c[4]:i[a][n]=+c[4],"vtf"==c[2]&&(i[a][n]=[i[a][n],l])}}}return r&&r.sheetRows&&(i=i.slice(0,r.sheetRows)),i}(e,r),r)}var t="--SocialCalcSpreadsheetControlSave\nContent-type: text/plain; charset=UTF-8\n";return{to_workbook:function(e,t){return ty(r(e,t),t)},to_sheet:r,from_sheet:function(r){return["socialcalc:version:1.5\nMIME-Version: 1.0\nContent-Type: multipart/mixed; boundary=SocialCalcSpreadsheetControlSave",t,"# SocialCalc Spreadsheet Control Save\npart:sheet",t,function(r){if(!r||!r["!ref"])return"";for(var t,a=[],n=[],s="",i=tE(r["!ref"]),c=Array.isArray(r),o=i.s.r;o<=i.e.r;++o)for(var l=i.s.c;l<=i.e.c;++l)if(s=tb({r:o,c:l}),(t=c?(r[o]||[])[l]:r[s])&&null!=t.v&&"z"!==t.t){switch(n=["cell",s,"t"],t.t){case"s":case"str":n.push(e(t.v));break;case"n":t.f?(n[2]="vtf",n[3]="n",n[4]=t.v,n[5]=e(t.f)):(n[2]="v",n[3]=t.v);break;case"b":n[2]="vt"+(t.f?"f":"c"),n[3]="nl",n[4]=t.v?"1":"0",n[5]=e(t.f||(t.v?"TRUE":"FALSE"));break;case"d":var f=ez(eZ(t.v));n[2]="vtc",n[3]="nd",n[4]=""+f,n[5]=t.w||eF(t.z||eo[14],f);break;case"e":continue}a.push(n.join(":"))}return a.push("sheet:c:"+(i.e.c-i.s.c+1)+":r:"+(i.e.r-i.s.r+1)+":tvf:1"),a.push("valueformat:1:text-wiki"),a.join("\n")}(r),"--SocialCalcSpreadsheetControlSave--"].join("\n")}}}(),aL=function(){function e(e,r,t,a,n){n.raw?r[t][a]=e:""===e||("TRUE"===e?r[t][a]=!0:"FALSE"===e?r[t][a]=!1:isNaN(e2(e))?isNaN(e3(e).getDate())?r[t][a]=e:r[t][a]=eZ(e):r[t][a]=e2(e))}var r={44:",",9:"	",59:";",124:"|"},a={44:3,9:2,59:1,124:0};function n(e){for(var t={},n=!1,s=0,i=0;s<e.length;++s)34==(i=e.charCodeAt(s))?n=!n:!n&&i in r&&(t[i]=(t[i]||0)+1);for(s in i=[],t)Object.prototype.hasOwnProperty.call(t,s)&&i.push([t[s],s]);if(!i.length)for(s in t=a)Object.prototype.hasOwnProperty.call(t,s)&&i.push([t[s],s]);return i.sort(function(e,r){return e[0]-r[0]||a[e[1]]-a[r[1]]}),r[i.pop()[1]]||44}function s(r,a){var s,i="",c="string"==a.type?[0,0,0,0]:sV(r,a);switch(a.type){case"base64":i=V(r);break;case"binary":case"string":i=r;break;case"buffer":i=65001==a.codepage?r.toString("utf8"):a.codepage&&void 0!==t?t.utils.decode(a.codepage,r):G&&Buffer.isBuffer(r)?r.toString("binary"):X(r);break;case"array":i=eQ(r);break;default:throw Error("Unrecognized type "+a.type)}return(239==c[0]&&187==c[1]&&191==c[2]?i=ry(i.slice(3)):"string"!=a.type&&"buffer"!=a.type&&65001==a.codepage?i=ry(i):"binary"==a.type&&void 0!==t&&a.codepage&&(i=t.utils.decode(a.codepage,t.utils.encode(28591,i))),"socialcalc:version:"==i.slice(0,19))?aP.to_sheet("string"==a.type?i:ry(i),a):(s=i,!(a&&a.PRN)||a.FS||"sep="==s.slice(0,4)||s.indexOf("	")>=0||s.indexOf(",")>=0||s.indexOf(";")>=0?function(e,r){var t,a=r||{},s="",i=a.dense?[]:{},c={s:{c:0,r:0},e:{c:0,r:0}};"sep="==e.slice(0,4)?13==e.charCodeAt(5)&&10==e.charCodeAt(6)?(s=e.charAt(4),e=e.slice(7)):13==e.charCodeAt(5)||10==e.charCodeAt(5)?(s=e.charAt(4),e=e.slice(6)):s=n(e.slice(0,1024)):s=a&&a.FS?a.FS:n(e.slice(0,1024));var o=0,l=0,f=0,h=0,u=0,d=s.charCodeAt(0),p=!1,m=0,g=e.charCodeAt(0);e=e.replace(/\r\n/mg,"\n");var v=null!=a.dateNF?RegExp("^"+("number"==typeof(t=a.dateNF)?eo[t]:t).replace(eU,"(\\d+)")+"$"):null;function T(){var r=e.slice(h,u),t={};if('"'==r.charAt(0)&&'"'==r.charAt(r.length-1)&&(r=r.slice(1,-1).replace(/""/g,'"')),0===r.length)t.t="z";else if(a.raw)t.t="s",t.v=r;else if(0===r.trim().length)t.t="s",t.v=r;else if(61==r.charCodeAt(0))34==r.charCodeAt(1)&&34==r.charCodeAt(r.length-1)?(t.t="s",t.v=r.slice(2,-1).replace(/""/g,'"')):1!=r.length?(t.t="n",t.f=r.slice(1)):(t.t="s",t.v=r);else if("TRUE"==r)t.t="b",t.v=!0;else if("FALSE"==r)t.t="b",t.v=!1;else if(isNaN(f=e2(r)))if(!isNaN(e3(r).getDate())||v&&r.match(v)){t.z=a.dateNF||eo[14];var n,s,p,T,b,E,w,A,S,k,y=0;v&&r.match(v)&&(n=a.dateNF,s=r.match(v)||[],p=-1,T=-1,b=-1,E=-1,w=-1,A=-1,(n.match(eU)||[]).forEach(function(e,r){var t=parseInt(s[r+1],10);switch(e.toLowerCase().charAt(0)){case"y":p=t;break;case"d":b=t;break;case"h":E=t;break;case"s":A=t;break;case"m":E>=0?w=t:T=t}}),A>=0&&-1==w&&T>=0&&(w=T,T=-1),7==(S=(""+(p>=0?p:new Date().getFullYear())).slice(-4)+"-"+("00"+(T>=1?T:1)).slice(-2)+"-"+("00"+(b>=1?b:1)).slice(-2)).length&&(S="0"+S),8==S.length&&(S="20"+S),k=("00"+(E>=0?E:0)).slice(-2)+":"+("00"+(w>=0?w:0)).slice(-2)+":"+("00"+(A>=0?A:0)).slice(-2),r=-1==E&&-1==w&&-1==A?S:-1==p&&-1==T&&-1==b?k:S+"T"+k,y=1),a.cellDates?(t.t="d",t.v=eZ(r,y)):(t.t="n",t.v=ez(eZ(r,y))),!1!==a.cellText&&(t.w=eF(t.z,t.v instanceof Date?ez(t.v):t.v)),a.cellNF||delete t.z}else t.t="s",t.v=r;else t.t="n",!1!==a.cellText&&(t.w=r),t.v=f;if("z"==t.t||(a.dense?(i[o]||(i[o]=[]),i[o][l]=t):i[tb({c:l,r:o})]=t),h=u+1,g=e.charCodeAt(h),c.e.c<l&&(c.e.c=l),c.e.r<o&&(c.e.r=o),m==d)++l;else if(l=0,++o,a.sheetRows&&a.sheetRows<=o)return!0}e:for(;u<e.length;++u)switch(m=e.charCodeAt(u)){case 34:34===g&&(p=!p);break;case d:case 10:case 13:if(!p&&T())break e}return u-h>0&&T(),i["!ref"]=tw(c),i}(s,a):tC(function(r,t){var a=t||{},n=[];if(!r||0===r.length)return n;for(var s=r.split(/[\r\n]/),i=s.length-1;i>=0&&0===s[i].length;)--i;for(var c=10,o=0,l=0;l<=i;++l)-1==(o=s[l].indexOf(" "))?o=s[l].length:o++,c=Math.max(c,o);for(l=0;l<=i;++l){n[l]=[];var f=0;for(e(s[l].slice(0,c).trim(),n,l,f,a),f=1;f<=(s[l].length-c)/10+1;++f)e(s[l].slice(c+(f-1)*10,c+10*f).trim(),n,l,f,a)}return a.sheetRows&&(n=n.slice(0,a.sheetRows)),n}(s,a),a))}return{to_workbook:function(e,r){return ty(s(e,r),r)},to_sheet:s,from_sheet:function(e){for(var r,t=[],a=tA(e["!ref"]),n=Array.isArray(e),s=a.s.r;s<=a.e.r;++s){for(var i=[],c=a.s.c;c<=a.e.c;++c){var o=tb({r:s,c:c});if(!(r=n?(e[s]||[])[c]:e[o])||null==r.v){i.push("          ");continue}for(var l=(r.w||(tk(r),r.w)||"").slice(0,10);l.length<10;)l+=" ";i.push(l+(0===c?" ":""))}t.push(i.join(""))}return t.join("\n")}}}(),aM=function(){function e(e,r,t){if(e){ts(e,e.l||0);for(var a=t.Enum||h;e.l<e.length;){var n=e.read_shift(2),s=a[n]||a[65535],i=e.read_shift(2),c=e.l+i,o=s.f&&s.f(e,i,t);if(e.l=c,r(o,s,n))return}}}function r(r,t){if(!r)return r;var a=t||{},n=a.dense?[]:{},s="Sheet1",i="",c=0,o={},l=[],f=[],d={s:{r:0,c:0},e:{r:0,c:0}},p=a.sheetRows||0;if(0==r[2]&&(8==r[3]||9==r[3])&&r.length>=16&&5==r[14]&&108===r[15])throw Error("Unsupported Works 3 for Mac file");if(2==r[2])a.Enum=h,e(r,function(e,r,t){switch(t){case 0:a.vers=e,e>=4096&&(a.qpro=!0);break;case 6:d=e;break;case 204:e&&(i=e);break;case 222:i=e;break;case 15:case 51:a.qpro||(e[1].v=e[1].v.slice(1));case 13:case 14:case 16:14==t&&(112&e[2])==112&&(15&e[2])>1&&(15&e[2])<15&&(e[1].z=a.dateNF||eo[14],a.cellDates&&(e[1].t="d",e[1].v=eK(e[1].v))),a.qpro&&e[3]>c&&(n["!ref"]=tw(d),o[s]=n,l.push(s),n=a.dense?[]:{},d={s:{r:0,c:0},e:{r:0,c:0}},c=e[3],s=i||"Sheet"+(c+1),i="");var f=a.dense?(n[e[0].r]||[])[e[0].c]:n[tb(e[0])];if(f){f.t=e[1].t,f.v=e[1].v,null!=e[1].z&&(f.z=e[1].z),null!=e[1].f&&(f.f=e[1].f);break}a.dense?(n[e[0].r]||(n[e[0].r]=[]),n[e[0].r][e[0].c]=e[1]):n[tb(e[0])]=e[1]}},a);else if(26==r[2]||14==r[2])a.Enum=u,14==r[2]&&(a.qpro=!0,r.l=0),e(r,function(e,r,t){switch(t){case 204:s=e;break;case 22:e[1].v=e[1].v.slice(1);case 23:case 24:case 25:case 37:case 39:case 40:if(e[3]>c&&(n["!ref"]=tw(d),o[s]=n,l.push(s),n=a.dense?[]:{},d={s:{r:0,c:0},e:{r:0,c:0}},s="Sheet"+((c=e[3])+1)),p>0&&e[0].r>=p)break;a.dense?(n[e[0].r]||(n[e[0].r]=[]),n[e[0].r][e[0].c]=e[1]):n[tb(e[0])]=e[1],d.e.c<e[0].c&&(d.e.c=e[0].c),d.e.r<e[0].r&&(d.e.r=e[0].r);break;case 27:e[14e3]&&(f[e[14e3][0]]=e[14e3][1]);break;case 1537:f[e[0]]=e[1],e[0]==c&&(s=e[1])}},a);else throw Error("Unrecognized LOTUS BOF "+r[2]);if(n["!ref"]=tw(d),o[i||s]=n,l.push(i||s),!f.length)return{SheetNames:l,Sheets:o};for(var m={},g=[],v=0;v<f.length;++v)o[l[v]]?(g.push(f[v]||l[v]),m[f[v]]=o[f[v]]||o[l[v]]):(g.push(f[v]),m[f[v]]={"!ref":"A1"});return{SheetNames:g,Sheets:m}}function t(e,r,t){var a=[{c:0,r:0},{t:"n",v:0},0,0];return t.qpro&&20768!=t.vers?(a[0].c=e.read_shift(1),a[3]=e.read_shift(1),a[0].r=e.read_shift(2),e.l+=2):(a[2]=e.read_shift(1),a[0].c=e.read_shift(2),a[0].r=e.read_shift(2)),a}function a(e,r,a){var n=e.l+r,s=t(e,r,a);if(s[1].t="s",20768==a.vers){e.l++;var i=e.read_shift(1);return s[1].v=e.read_shift(i,"utf8"),s}return a.qpro&&e.l++,s[1].v=e.read_shift(n-e.l,"cstr"),s}function n(e,r,t){var a=32768&r;return r&=-32769,r=(a?e:0)+(r>=8192?r-16384:r),(a?"":"$")+(t?tv(r):tm(r))}var s={51:["FALSE",0],52:["TRUE",0],70:["LEN",1],80:["SUM",69],81:["AVERAGEA",69],82:["COUNTA",69],83:["MINA",69],84:["MAXA",69],111:["T",1]},i=["","","","","","","","","","+","-","*","/","^","=","<>","<=",">=","<",">","","","","","&","","","","","","",""];function c(e){var r=[{c:0,r:0},{t:"n",v:0},0];return r[0].r=e.read_shift(2),r[3]=e[e.l++],r[0].c=e[e.l++],r}function o(e,r){var t=c(e,r),a=e.read_shift(4),n=e.read_shift(4),s=e.read_shift(2);if(65535==s)return 0===a&&0xc0000000===n?(t[1].t="e",t[1].v=15):0===a&&0xd0000000===n?(t[1].t="e",t[1].v=42):t[1].v=0,t;var i=32768&s;return s=(32767&s)-16446,t[1].v=(1-2*i)*(n*Math.pow(2,s+32)+a*Math.pow(2,s)),t}function l(e,r){var t=c(e,r),a=e.read_shift(8,"f");return t[1].v=a,t}function f(e,r){return 0==e[e.l+r-1]?e.read_shift(r,"cstr"):""}var h={0:{n:"BOF",f:aa},1:{n:"EOF"},2:{n:"CALCMODE"},3:{n:"CALCORDER"},4:{n:"SPLIT"},5:{n:"SYNC"},6:{n:"RANGE",f:function(e,r,t){var a={s:{c:0,r:0},e:{c:0,r:0}};return 8==r&&t.qpro?(a.s.c=e.read_shift(1),e.l++,a.s.r=e.read_shift(2),a.e.c=e.read_shift(1),e.l++,a.e.r=e.read_shift(2)):(a.s.c=e.read_shift(2),a.s.r=e.read_shift(2),12==r&&t.qpro&&(e.l+=2),a.e.c=e.read_shift(2),a.e.r=e.read_shift(2),12==r&&t.qpro&&(e.l+=2),65535==a.s.c&&(a.s.c=a.e.c=a.s.r=a.e.r=0)),a}},7:{n:"WINDOW1"},8:{n:"COLW1"},9:{n:"WINTWO"},10:{n:"COLW2"},11:{n:"NAME"},12:{n:"BLANK"},13:{n:"INTEGER",f:function(e,r,a){var n=t(e,r,a);return n[1].v=e.read_shift(2,"i"),n}},14:{n:"NUMBER",f:function(e,r,a){var n=t(e,r,a);return n[1].v=e.read_shift(8,"f"),n}},15:{n:"LABEL",f:a},16:{n:"FORMULA",f:function(e,r,a){var c=e.l+r,o=t(e,r,a);if(o[1].v=e.read_shift(8,"f"),a.qpro)e.l=c;else{var l=e.read_shift(2);(function(e,r){ts(e,0);for(var t=[],a=0,c="",o="",l="",f="";e.l<e.length;){var h=e[e.l++];switch(h){case 0:t.push(e.read_shift(8,"f"));break;case 1:o=n(r[0].c,e.read_shift(2),!0),c=n(r[0].r,e.read_shift(2),!1),t.push(o+c);break;case 2:var u=n(r[0].c,e.read_shift(2),!0),d=n(r[0].r,e.read_shift(2),!1);o=n(r[0].c,e.read_shift(2),!0),c=n(r[0].r,e.read_shift(2),!1),t.push(u+d+":"+o+c);break;case 3:if(e.l<e.length)return void console.error("WK1 premature formula end");break;case 4:t.push("("+t.pop()+")");break;case 5:t.push(e.read_shift(2));break;case 6:for(var p="";h=e[e.l++];)p+=String.fromCharCode(h);t.push('"'+p.replace(/"/g,'""')+'"');break;case 8:t.push("-"+t.pop());break;case 23:t.push("+"+t.pop());break;case 22:t.push("NOT("+t.pop()+")");break;case 20:case 21:f=t.pop(),l=t.pop(),t.push(["AND","OR"][h-20]+"("+l+","+f+")");break;default:if(h<32&&i[h])f=t.pop(),l=t.pop(),t.push(l+i[h]+f);else if(s[h]){if(69==(a=s[h][1])&&(a=e[e.l++]),a>t.length)return void console.error("WK1 bad formula parse 0x"+h.toString(16)+":|"+t.join("|")+"|");var m=t.slice(-a);t.length-=a,t.push(s[h][0]+"("+m.join(",")+")")}else if(h<=7)return console.error("WK1 invalid opcode "+h.toString(16));else if(h<=24)return console.error("WK1 unsupported op "+h.toString(16));else if(h<=30)return console.error("WK1 invalid opcode "+h.toString(16));else if(h<=115)return console.error("WK1 unsupported function opcode "+h.toString(16));else return console.error("WK1 unrecognized opcode "+h.toString(16))}}1==t.length?r[1].f=""+t[0]:console.error("WK1 bad formula parse |"+t.join("|")+"|")})(e.slice(e.l,e.l+l),o),e.l+=l}return o}},24:{n:"TABLE"},25:{n:"ORANGE"},26:{n:"PRANGE"},27:{n:"SRANGE"},28:{n:"FRANGE"},29:{n:"KRANGE1"},32:{n:"HRANGE"},35:{n:"KRANGE2"},36:{n:"PROTEC"},37:{n:"FOOTER"},38:{n:"HEADER"},39:{n:"SETUP"},40:{n:"MARGINS"},41:{n:"LABELFMT"},42:{n:"TITLES"},43:{n:"SHEETJS"},45:{n:"GRAPH"},46:{n:"NGRAPH"},47:{n:"CALCCOUNT"},48:{n:"UNFORMATTED"},49:{n:"CURSORW12"},50:{n:"WINDOW"},51:{n:"STRING",f:a},55:{n:"PASSWORD"},56:{n:"LOCKED"},60:{n:"QUERY"},61:{n:"QUERYNAME"},62:{n:"PRINT"},63:{n:"PRINTNAME"},64:{n:"GRAPH2"},65:{n:"GRAPHNAME"},66:{n:"ZOOM"},67:{n:"SYMSPLIT"},68:{n:"NSROWS"},69:{n:"NSCOLS"},70:{n:"RULER"},71:{n:"NNAME"},72:{n:"ACOMM"},73:{n:"AMACRO"},74:{n:"PARSE"},102:{n:"PRANGES??"},103:{n:"RRANGES??"},104:{n:"FNAME??"},105:{n:"MRANGES??"},204:{n:"SHEETNAMECS",f:f},222:{n:"SHEETNAMELP",f:function(e,r){var t=e[e.l++];t>r-1&&(t=r-1);for(var a="";a.length<t;)a+=String.fromCharCode(e[e.l++]);return a}},65535:{n:""}},u={0:{n:"BOF"},1:{n:"EOF"},2:{n:"PASSWORD"},3:{n:"CALCSET"},4:{n:"WINDOWSET"},5:{n:"SHEETCELLPTR"},6:{n:"SHEETLAYOUT"},7:{n:"COLUMNWIDTH"},8:{n:"HIDDENCOLUMN"},9:{n:"USERRANGE"},10:{n:"SYSTEMRANGE"},11:{n:"ZEROFORCE"},12:{n:"SORTKEYDIR"},13:{n:"FILESEAL"},14:{n:"DATAFILLNUMS"},15:{n:"PRINTMAIN"},16:{n:"PRINTSTRING"},17:{n:"GRAPHMAIN"},18:{n:"GRAPHSTRING"},19:{n:"??"},20:{n:"ERRCELL"},21:{n:"NACELL"},22:{n:"LABEL16",f:function(e,r){var t=c(e,r);return t[1].t="s",t[1].v=e.read_shift(r-4,"cstr"),t}},23:{n:"NUMBER17",f:o},24:{n:"NUMBER18",f:function(e,r){var t=c(e,r);t[1].v=e.read_shift(2);var a=t[1].v>>1;if(1&t[1].v)switch(7&a){case 0:a=(a>>3)*5e3;break;case 1:a=(a>>3)*500;break;case 2:a=(a>>3)/20;break;case 3:a=(a>>3)/200;break;case 4:a=(a>>3)/2e3;break;case 5:a=(a>>3)/2e4;break;case 6:a=(a>>3)/16;break;case 7:a=(a>>3)/64}return t[1].v=a,t}},25:{n:"FORMULA19",f:function(e,r){var t=o(e,14);return e.l+=r-14,t}},26:{n:"FORMULA1A"},27:{n:"XFORMAT",f:function(e,r){for(var t={},a=e.l+r;e.l<a;){var n=e.read_shift(2);if(14e3==n){for(t[n]=[0,""],t[n][0]=e.read_shift(2);e[e.l];)t[n][1]+=String.fromCharCode(e[e.l]),e.l++;e.l++}}return t}},28:{n:"DTLABELMISC"},29:{n:"DTLABELCELL"},30:{n:"GRAPHWINDOW"},31:{n:"CPA"},32:{n:"LPLAUTO"},33:{n:"QUERY"},34:{n:"HIDDENSHEET"},35:{n:"??"},37:{n:"NUMBER25",f:function(e,r){var t=c(e,r),a=e.read_shift(4);return t[1].v=a>>6,t}},38:{n:"??"},39:{n:"NUMBER27",f:l},40:{n:"FORMULA28",f:function(e,r){var t=l(e,14);return e.l+=r-10,t}},142:{n:"??"},147:{n:"??"},150:{n:"??"},151:{n:"??"},152:{n:"??"},153:{n:"??"},154:{n:"??"},155:{n:"??"},156:{n:"??"},163:{n:"??"},174:{n:"??"},175:{n:"??"},176:{n:"??"},177:{n:"??"},184:{n:"??"},185:{n:"??"},186:{n:"??"},187:{n:"??"},188:{n:"??"},195:{n:"??"},201:{n:"??"},204:{n:"SHEETNAMECS",f:f},205:{n:"??"},206:{n:"??"},207:{n:"??"},208:{n:"??"},256:{n:"??"},259:{n:"??"},260:{n:"??"},261:{n:"??"},262:{n:"??"},263:{n:"??"},265:{n:"??"},266:{n:"??"},267:{n:"??"},268:{n:"??"},270:{n:"??"},271:{n:"??"},384:{n:"??"},389:{n:"??"},390:{n:"??"},393:{n:"??"},396:{n:"??"},512:{n:"??"},514:{n:"??"},513:{n:"??"},516:{n:"??"},517:{n:"??"},640:{n:"??"},641:{n:"??"},642:{n:"??"},643:{n:"??"},644:{n:"??"},645:{n:"??"},646:{n:"??"},647:{n:"??"},648:{n:"??"},658:{n:"??"},659:{n:"??"},660:{n:"??"},661:{n:"??"},662:{n:"??"},665:{n:"??"},666:{n:"??"},768:{n:"??"},772:{n:"??"},1537:{n:"SHEETINFOQP",f:function(e,r,t){if(t.qpro&&!(r<21)){var a=e.read_shift(1);return e.l+=17,e.l+=1,e.l+=2,[a,e.read_shift(r-21,"cstr")]}}},1600:{n:"??"},1602:{n:"??"},1793:{n:"??"},1794:{n:"??"},1795:{n:"??"},1796:{n:"??"},1920:{n:"??"},2048:{n:"??"},2049:{n:"??"},2052:{n:"??"},2688:{n:"??"},10998:{n:"??"},12849:{n:"??"},28233:{n:"??"},28484:{n:"??"},65535:{n:""}};return{sheet_to_wk1:function(e,r){var t,a,n,s,i=r||{};if(+i.codepage>=0&&D(+i.codepage),"string"==i.type)throw Error("Cannot write WK1 to JS string");var c=tl(),o=tA(e["!ref"]),l=Array.isArray(e),f=[];sy(c,0,(t=1030,(a=tc(2)).write_shift(2,1030),a)),sy(c,6,(n=o,(s=tc(8)).write_shift(2,n.s.c),s.write_shift(2,n.s.r),s.write_shift(2,n.e.c),s.write_shift(2,n.e.r),s));for(var h=Math.min(o.e.r,8191),u=o.s.r;u<=h;++u)for(var d=tm(u),p=o.s.c;p<=o.e.c;++p){u===o.s.r&&(f[p]=tv(p));var m=f[p]+d,g=l?(e[u]||[])[p]:e[m];g&&"z"!=g.t&&("n"==g.t?(0|g.v)==g.v&&g.v>=-32768&&g.v<=32767?sy(c,13,function(e,r,t){var a=tc(7);return a.write_shift(1,255),a.write_shift(2,r),a.write_shift(2,e),a.write_shift(2,t,"i"),a}(u,p,g.v)):sy(c,14,function(e,r,t){var a=tc(13);return a.write_shift(1,255),a.write_shift(2,r),a.write_shift(2,e),a.write_shift(8,t,"f"),a}(u,p,g.v)):sy(c,15,function(e,r,t){var a=tc(7+t.length);a.write_shift(1,255),a.write_shift(2,r),a.write_shift(2,e),a.write_shift(1,39);for(var n=0;n<a.length;++n){var s=t.charCodeAt(n);a.write_shift(1,s>=128?95:s)}return a.write_shift(1,0),a}(u,p,tk(g).slice(0,239))))}return sy(c,1),c.end()},book_to_wk3:function(e,r){var t=r||{};if(+t.codepage>=0&&D(+t.codepage),"string"==t.type)throw Error("Cannot write WK3 to JS string");var a=tl();sy(a,0,function(e){var r=tc(26);r.write_shift(2,4096),r.write_shift(2,4),r.write_shift(4,0);for(var t=0,a=0,n=0,s=0;s<e.SheetNames.length;++s){var i=e.SheetNames[s],c=e.Sheets[i];if(c&&c["!ref"]){++n;var o=tE(c["!ref"]);t<o.e.r&&(t=o.e.r),a<o.e.c&&(a=o.e.c)}}return t>8191&&(t=8191),r.write_shift(2,t),r.write_shift(1,n),r.write_shift(1,a),r.write_shift(2,0),r.write_shift(2,0),r.write_shift(1,1),r.write_shift(1,2),r.write_shift(4,0),r.write_shift(4,0),r}(e));for(var n=0,s=0;n<e.SheetNames.length;++n)(e.Sheets[e.SheetNames[n]]||{})["!ref"]&&sy(a,27,function(e,r){var t=tc(5+e.length);t.write_shift(2,14e3),t.write_shift(2,r);for(var a=0;a<e.length;++a){var n=e.charCodeAt(a);t[t.l++]=n>127?95:n}return t[t.l++]=0,t}(e.SheetNames[n],s++));var i=0;for(n=0;n<e.SheetNames.length;++n){var c=e.Sheets[e.SheetNames[n]];if(c&&c["!ref"]){for(var o=tA(c["!ref"]),l=Array.isArray(c),f=[],h=Math.min(o.e.r,8191),u=o.s.r;u<=h;++u)for(var d=tm(u),p=o.s.c;p<=o.e.c;++p){u===o.s.r&&(f[p]=tv(p));var m=f[p]+d,g=l?(c[u]||[])[p]:c[m];g&&"z"!=g.t&&("n"==g.t?sy(a,23,function(e,r,t,a){var n=tc(14);if(n.write_shift(2,e),n.write_shift(1,t),n.write_shift(1,r),0==a)return n.write_shift(4,0),n.write_shift(4,0),n.write_shift(2,65535),n;var s=0,i=0,c=0,o=0;return a<0&&(s=1,a=-a),i=0|Math.log2(a),a/=Math.pow(2,i-31),(0x80000000&(o=a>>>0))==0&&(a/=2,++i,o=a>>>0),a-=o,o|=0x80000000,o>>>=0,a*=0x100000000,c=a>>>0,n.write_shift(4,c),n.write_shift(4,o),i+=16383+32768*!!s,n.write_shift(2,i),n}(u,p,i,g.v)):sy(a,22,function(e,r,t,a){var n=tc(6+a.length);n.write_shift(2,e),n.write_shift(1,t),n.write_shift(1,r),n.write_shift(1,39);for(var s=0;s<a.length;++s){var i=a.charCodeAt(s);n.write_shift(1,i>=128?95:i)}return n.write_shift(1,0),n}(u,p,i,tk(g).slice(0,239))))}++i}}return sy(a,1),a.end()},to_workbook:function(e,t){switch(t.type){case"base64":return r(K(V(e)),t);case"binary":return r(K(e),t);case"buffer":case"array":return r(e,t)}throw"Unsupported type "+t.type}}}(),aU=function(){var e=rR("t"),r=rR("rPr");function t(t){var a=t.match(e);if(!a)return{t:"s",v:""};var n={t:"s",v:rm(a[1])},s=t.match(r);return s&&(n.s=function(e){var r={},t=e.match(ro),a=0,n=!1;if(t)for(;a!=t.length;++a){var s=rh(t[a]);switch(s[0].replace(/\w*:/g,"")){case"<condense":case"<extend":break;case"<shadow":if(!s.val)break;case"<shadow>":case"<shadow/>":r.shadow=1;break;case"</shadow>":break;case"<charset":if("1"==s.val)break;r.cp=I[parseInt(s.val,10)];break;case"<outline":if(!s.val)break;case"<outline>":case"<outline/>":r.outline=1;break;case"</outline>":break;case"<rFont":r.name=s.val;break;case"<sz":r.sz=s.val;break;case"<strike":if(!s.val)break;case"<strike>":case"<strike/>":r.strike=1;break;case"</strike>":break;case"<u":if(!s.val)break;switch(s.val){case"double":r.uval="double";break;case"singleAccounting":r.uval="single-accounting";break;case"doubleAccounting":r.uval="double-accounting"}case"<u>":case"<u/>":r.u=1;break;case"</u>":break;case"<b":if("0"==s.val)break;case"<b>":case"<b/>":r.b=1;break;case"</b>":break;case"<i":if("0"==s.val)break;case"<i>":case"<i/>":r.i=1;break;case"</i>":case"<color>":case"<color/>":case"</color>":case"<family>":case"<family/>":case"</family>":case"<vertAlign>":case"<vertAlign/>":case"</vertAlign>":case"<scheme":case"<scheme>":case"<scheme/>":case"</scheme>":case"<extLst":case"<extLst>":case"</extLst>":break;case"<color":s.rgb&&(r.color=s.rgb.slice(2,8));break;case"<family":r.family=s.val;break;case"<vertAlign":r.valign=s.val;break;case"<ext":n=!0;break;case"</ext>":n=!1;break;default:if(47!==s[0].charCodeAt(1)&&!n)throw Error("Unrecognized rich format "+s[0])}}return r}(s[1])),n}var a=/<(?:\w+:)?r>/g,n=/<\/(?:\w+:)?r>/;return function(e){return e.replace(a,"").split(n).map(t).filter(function(e){return e.v})}}(),aB=function(){var e=/(\r\n|\n)/g;function r(r){var t,a,n,s,i,c=[[],r.v,[]];return r.v?(r.s&&(t=r.s,a=c[0],n=c[2],s=[],t.u&&s.push("text-decoration: underline;"),t.uval&&s.push("text-underline-style:"+t.uval+";"),t.sz&&s.push("font-size:"+t.sz+"pt;"),t.outline&&s.push("text-effect: outline;"),t.shadow&&s.push("text-shadow: auto;"),a.push('<span style="'+s.join("")+'">'),t.b&&(a.push("<b>"),n.push("</b>")),t.i&&(a.push("<i>"),n.push("</i>")),t.strike&&(a.push("<s>"),n.push("</s>")),"superscript"==(i=t.valign||"")||"super"==i?i="sup":"subscript"==i&&(i="sub"),""!=i&&(a.push("<"+i+">"),n.push("</"+i+">")),n.push("</span>")),c[0].join("")+c[1].replace(e,"<br/>")+c[2].join("")):""}return function(e){return e.map(r).join("")}}(),aH=/<(?:\w+:)?t[^>]*>([^<]*)<\/(?:\w+:)?t>/g,aW=/<(?:\w+:)?r>/,aV=/<(?:\w+:)?rPh.*?>([\s\S]*?)<\/(?:\w+:)?rPh>/g;function aG(e,r){var t=!r||r.cellHTML,a={};return e?(e.match(/^\s*<(?:\w+:)?t[^>]*>/)?(a.t=rm(ry(e.slice(e.indexOf(">")+1).split(/<\/(?:\w+:)?t>/)[0]||"")),a.r=ry(e),t&&(a.h=rT(a.t))):e.match(aW)&&(a.r=ry(e),a.t=rm(ry((e.replace(aV,"").match(aH)||[]).join("").replace(ro,""))),t&&(a.h=aB(aU(a.r)))),a):{t:""}}var az=/<(?:\w+:)?sst([^>]*)>([\s\S]*)<\/(?:\w+:)?sst>/,a$=/<(?:\w+:)?(?:si|sstItem)>/g,aY=/<\/(?:\w+:)?(?:si|sstItem)>/;function aj(e){if(void 0!==t)return t.utils.encode(x,e);for(var r=[],a=e.split(""),n=0;n<a.length;++n)r[n]=a[n].charCodeAt(0);return r}function aK(e,r){var t={};return t.Major=e.read_shift(2),t.Minor=e.read_shift(2),r>=4&&(e.l+=r-4),t}function aX(e,r){var t=e.l+r,a={};a.Flags=63&e.read_shift(4),e.l+=4,a.AlgID=e.read_shift(4);var n=!1;switch(a.AlgID){case 26126:case 26127:case 26128:n=36==a.Flags;break;case 26625:n=4==a.Flags;break;case 0:n=16==a.Flags||4==a.Flags||36==a.Flags;break;default:throw"Unrecognized encryption algorithm: "+a.AlgID}if(!n)throw Error("Encryption Flags/AlgID mismatch");return a.AlgIDHash=e.read_shift(4),a.KeySize=e.read_shift(4),a.ProviderType=e.read_shift(4),e.l+=8,a.CSPName=e.read_shift(t-e.l>>1,"utf16le"),e.l=t,a}function aq(e,r){var t={},a=e.l+r;return e.l+=4,t.Salt=e.slice(e.l,e.l+16),e.l+=16,t.Verifier=e.slice(e.l,e.l+16),e.l+=16,e.read_shift(4),t.VerifierHash=e.slice(e.l,a),e.l=a,t}var aJ=function(){var e=[187,255,255,186,255,255,185,128,0,190,15,0,191,15,0],r=[57840,7439,52380,33984,4364,3600,61902,12606,6258,57657,54287,34041,10252,43370,20163],t=[44796,19929,39858,10053,20106,40212,10761,31585,63170,64933,60267,50935,40399,11199,17763,35526,1453,2906,5812,11624,23248,885,1770,3540,7080,14160,28320,56640,55369,41139,20807,41614,21821,43642,17621,28485,56970,44341,19019,38038,14605,29210,60195,50791,40175,10751,21502,43004,24537,18387,36774,3949,7898,15796,31592,63184,47201,24803,49606,37805,14203,28406,56812,17824,35648,1697,3394,6788,13576,27152,43601,17539,35078,557,1114,2228,4456,30388,60776,51953,34243,7079,14158,28316,14128,28256,56512,43425,17251,34502,7597,13105,26210,52420,35241,883,1766,3532,4129,8258,16516,33032,4657,9314,18628],a=function(e,r){var t;return((t=e^r)/2|128*t)&255},n=function(e){for(var a=r[e.length-1],n=104,s=e.length-1;s>=0;--s)for(var i=e[s],c=0;7!=c;++c)64&i&&(a^=t[n]),i*=2,--n;return a};return function(r){for(var t,s,i,c=aj(r),o=n(c),l=c.length,f=Y(16),h=0;16!=h;++h)f[h]=0;for((1&l)==1&&(t=o>>8,f[l]=a(e[0],t),--l,t=255&o,s=c[c.length-1],f[l]=a(s,t));l>0;)--l,t=o>>8,f[l]=a(c[l],t),--l,t=255&o,f[l]=a(c[l],t);for(l=15,i=15-c.length;i>0;)t=o>>8,f[l]=a(e[i],t),--l,--i,t=255&o,f[l]=a(c[l],t),--l,--i;return f}}(),aZ=function(e,r,t,a,n){var s,i;for(n||(n=r),a||(a=aJ(e)),s=0;s!=r.length;++s)i=((i=r[s]^a[t])>>5|i<<3)&255,n[s]=i,++t;return[n,t,a]},aQ=function(e){var r=0,t=aJ(e);return function(e){var a=aZ("",e,r,t);return r=a[1],a[0]}},a1=function(){function e(e,t){switch(t.type){case"base64":return r(V(e),t);case"binary":return r(e,t);case"buffer":return r(G&&Buffer.isBuffer(e)?e.toString("binary"):X(e),t);case"array":return r(eQ(e),t)}throw Error("Unrecognized type "+t.type)}function r(e,r){var t=(r||{}).dense?[]:{},a=e.match(/\\trowd.*?\\row\b/g);if(!a.length)throw Error("RTF missing table");var n={s:{c:0,r:0},e:{c:0,r:a.length-1}};return a.forEach(function(e,r){Array.isArray(t)&&(t[r]=[]);for(var a,s=/\\\w+\b/g,i=0,c=-1;a=s.exec(e);){if("\\cell"===a[0]){var o=e.slice(i,s.lastIndex-a[0].length);if(" "==o[0]&&(o=o.slice(1)),++c,o.length){var l={v:o,t:"s"};Array.isArray(t)?t[r][c]=l:t[tb({r:r,c:c})]=l}}i=s.lastIndex}c>n.e.c&&(n.e.c=c)}),t["!ref"]=tw(n),t}return{to_workbook:function(r,t){return ty(e(r,t),t)},to_sheet:e,from_sheet:function(e){for(var r,t=["{\\rtf1\\ansi"],a=tA(e["!ref"]),n=Array.isArray(e),s=a.s.r;s<=a.e.r;++s){t.push("\\trowd\\trautofit1");for(var i=a.s.c;i<=a.e.c;++i)t.push("\\cellx"+(i+1));for(t.push("\\pard\\intbl"),i=a.s.c;i<=a.e.c;++i){var c=tb({r:s,c:i});(r=n?(e[s]||[])[i]:e[c])&&(null!=r.v||r.f&&!r.F)&&(t.push(" "+(r.w||(tk(r),r.w))),t.push("\\cell"))}t.push("\\pard\\intbl\\row")}return t.join("")+"}"}}}();function a0(e){for(var r=0,t=1;3!=r;++r)t=256*t+(e[r]>255?255:e[r]<0?0:e[r]);return t.toString(16).toUpperCase().slice(1)}function a2(e,r){if(0===r)return e;var t,a=function(e){var r=e[0]/255,t=e[1]/255,a=e[2]/255,n=Math.max(r,t,a),s=Math.min(r,t,a),i=n-s;if(0===i)return[0,0,r];var c=0,o=0,l=n+s;switch(o=i/(l>1?2-l:l),n){case r:c=((t-a)/i+6)%6;break;case t:c=(a-r)/i+2;break;case a:c=(r-t)/i+4}return[c/6,o,l/2]}([parseInt((t=e.slice(+("#"===e[0])).slice(0,6)).slice(0,2),16),parseInt(t.slice(2,4),16),parseInt(t.slice(4,6),16)]);return r<0?a[2]=a[2]*(1+r):a[2]=1-(1-a[2])*(1-r),a0(function(e){var r,t=e[0],a=e[1],n=e[2],s=2*a*(n<.5?n:1-n),i=n-s/2,c=[i,i,i],o=6*t;if(0!==a)switch(0|o){case 0:case 6:r=s*o,c[0]+=s,c[1]+=r;break;case 1:r=s*(2-o),c[0]+=r,c[1]+=s;break;case 2:r=s*(o-2),c[1]+=s,c[2]+=r;break;case 3:r=s*(4-o),c[1]+=r,c[2]+=s;break;case 4:r=s*(o-4),c[2]+=s,c[0]+=r;break;case 5:r=s*(6-o),c[2]+=r,c[0]+=s}for(var l=0;3!=l;++l)c[l]=Math.round(255*c[l]);return c}(a))}var a4=6;function a3(e){return Math.floor((e+Math.round(128/a4)/256)*a4)}function a5(e){return Math.floor((e-5)/a4*100+.5)/100}function a6(e){return Math.round((e*a4+5)/a4*256)/256}function a8(e){return a6(a5(a3(e)))}function a7(e){var r=Math.abs(e-a8(e)),t=a4;if(r>.005)for(a4=1;a4<15;++a4)Math.abs(e-a8(e))<=r&&(r=Math.abs(e-a8(e)),t=a4);a4=t}function a9(e){e.width?(e.wpx=a3(e.width),e.wch=a5(e.wpx),e.MDW=a4):e.wpx?(e.wch=a5(e.wpx),e.width=a6(e.wch),e.MDW=a4):"number"==typeof e.wch&&(e.width=a6(e.wch),e.wpx=a3(e.width),e.MDW=a4),e.customWidth&&delete e.customWidth}function ne(e){return 96*e/96}var nr={None:"none",Solid:"solid",Gray50:"mediumGray",Gray75:"darkGray",Gray25:"lightGray",HorzStripe:"darkHorizontal",VertStripe:"darkVertical",ReverseDiagStripe:"darkDown",DiagStripe:"darkUp",DiagCross:"darkGrid",ThickDiagCross:"darkTrellis",ThinHorzStripe:"lightHorizontal",ThinVertStripe:"lightVertical",ThinReverseDiagStripe:"lightDown",ThinHorzCross:"lightGrid"},nt=["numFmtId","fillId","fontId","borderId","xfId"],na=["applyAlignment","applyBorder","applyFill","applyFont","applyNumberFormat","applyProtection","pivotButton","quotePrefix"],nn=function(){var e=/<(?:\w+:)?numFmts([^>]*)>[\S\s]*?<\/(?:\w+:)?numFmts>/,r=/<(?:\w+:)?cellXfs([^>]*)>[\S\s]*?<\/(?:\w+:)?cellXfs>/,t=/<(?:\w+:)?fills([^>]*)>[\S\s]*?<\/(?:\w+:)?fills>/,a=/<(?:\w+:)?fonts([^>]*)>[\S\s]*?<\/(?:\w+:)?fonts>/,n=/<(?:\w+:)?borders([^>]*)>[\S\s]*?<\/(?:\w+:)?borders>/;return function(s,i,c){var o,l,f,h,u,d,p,m,g,v,T,b,E,w={};return s?((E=(s=s.replace(/<!--([\s\S]*?)-->/mg,"").replace(/<!DOCTYPE[^\[]*\[[^\]]*\]>/gm,"")).match(e))&&function(e,r,t){r.NumberFmt=[];for(var a=eW(eo),n=0;n<a.length;++n)r.NumberFmt[a[n]]=eo[a[n]];var s=e[0].match(ro);if(s)for(n=0;n<s.length;++n){var i=rh(s[n]);switch(ru(i[0])){case"<numFmts":case"</numFmts>":case"<numFmts/>":case"<numFmts>":case"</numFmt>":break;case"<numFmt":var c=rm(ry(i.formatCode)),o=parseInt(i.numFmtId,10);if(r.NumberFmt[o]=c,o>0){if(o>392){for(o=392;o>60&&null!=r.NumberFmt[o];--o);r.NumberFmt[o]=c}eP(c,o)}break;default:if(t.WTF)throw Error("unrecognized "+i[0]+" in numFmts")}}}(E,w,c),(E=s.match(a))&&(o=E,w.Fonts=[],l={},f=!1,(o[0].match(ro)||[]).forEach(function(e){var r=rh(e);switch(ru(r[0])){case"<fonts":case"<fonts>":case"</fonts>":case"<font":case"<font>":case"<name/>":case"</name>":case"<sz/>":case"</sz>":case"<vertAlign/>":case"</vertAlign>":case"<family/>":case"</family>":case"<scheme/>":case"</scheme>":case"<color/>":case"</color>":case"<extLst":case"<extLst>":case"</extLst>":break;case"</font>":case"<font/>":w.Fonts.push(l),l={};break;case"<name":r.val&&(l.name=ry(r.val));break;case"<b":l.bold=r.val?rE(r.val):1;break;case"<b/>":l.bold=1;break;case"<i":l.italic=r.val?rE(r.val):1;break;case"<i/>":l.italic=1;break;case"<u":switch(r.val){case"none":l.underline=0;break;case"single":l.underline=1;break;case"double":l.underline=2;break;case"singleAccounting":l.underline=33;break;case"doubleAccounting":l.underline=34}break;case"<u/>":l.underline=1;break;case"<strike":l.strike=r.val?rE(r.val):1;break;case"<strike/>":l.strike=1;break;case"<outline":l.outline=r.val?rE(r.val):1;break;case"<outline/>":l.outline=1;break;case"<shadow":l.shadow=r.val?rE(r.val):1;break;case"<shadow/>":l.shadow=1;break;case"<condense":l.condense=r.val?rE(r.val):1;break;case"<condense/>":l.condense=1;break;case"<extend":l.extend=r.val?rE(r.val):1;break;case"<extend/>":l.extend=1;break;case"<sz":r.val&&(l.sz=+r.val);break;case"<vertAlign":r.val&&(l.vertAlign=r.val);break;case"<family":r.val&&(l.family=parseInt(r.val,10));break;case"<scheme":r.val&&(l.scheme=r.val);break;case"<charset":if("1"==r.val)break;r.codepage=I[parseInt(r.val,10)];break;case"<color":if(l.color||(l.color={}),r.auto&&(l.color.auto=rE(r.auto)),r.rgb)l.color.rgb=r.rgb.slice(-6);else if(r.indexed){l.color.index=parseInt(r.indexed,10);var t=tW[l.color.index];81==l.color.index&&(t=tW[1]),t||(t=tW[1]),l.color.rgb=t[0].toString(16)+t[1].toString(16)+t[2].toString(16)}else r.theme&&(l.color.theme=parseInt(r.theme,10),r.tint&&(l.color.tint=parseFloat(r.tint)),r.theme&&i.themeElements&&i.themeElements.clrScheme&&(l.color.rgb=a2(i.themeElements.clrScheme[l.color.theme].rgb,l.color.tint||0)));break;case"<AlternateContent":case"<ext":f=!0;break;case"</AlternateContent>":case"</ext>":f=!1;break;default:if(c&&c.WTF&&!f)throw Error("unrecognized "+r[0]+" in fonts")}})),(E=s.match(t))&&(h=E,w.Fills=[],u={},d=!1,(h[0].match(ro)||[]).forEach(function(e){var r=rh(e);switch(ru(r[0])){case"<fills":case"<fills>":case"</fills>":case"</fill>":case"<gradientFill>":case"<patternFill/>":case"</patternFill>":case"<bgColor/>":case"</bgColor>":case"<fgColor/>":case"</fgColor>":case"<stop":case"<stop/>":case"</stop>":case"<color":case"<color/>":case"</color>":case"<extLst":case"<extLst>":case"</extLst>":break;case"<fill>":case"<fill":case"<fill/>":u={},w.Fills.push(u);break;case"<gradientFill":case"</gradientFill>":w.Fills.push(u),u={};break;case"<patternFill":case"<patternFill>":r.patternType&&(u.patternType=r.patternType);break;case"<bgColor":u.bgColor||(u.bgColor={}),r.indexed&&(u.bgColor.indexed=parseInt(r.indexed,10)),r.theme&&(u.bgColor.theme=parseInt(r.theme,10)),r.tint&&(u.bgColor.tint=parseFloat(r.tint)),r.rgb&&(u.bgColor.rgb=r.rgb.slice(-6));break;case"<fgColor":u.fgColor||(u.fgColor={}),r.theme&&(u.fgColor.theme=parseInt(r.theme,10)),r.tint&&(u.fgColor.tint=parseFloat(r.tint)),null!=r.rgb&&(u.fgColor.rgb=r.rgb.slice(-6));break;case"<ext":d=!0;break;case"</ext>":d=!1;break;default:if(c&&c.WTF&&!d)throw Error("unrecognized "+r[0]+" in fills")}})),(E=s.match(n))&&(p=E,w.Borders=[],m={},g=!1,(p[0].match(ro)||[]).forEach(function(e){var r=rh(e);switch(ru(r[0])){case"<borders":case"<borders>":case"</borders>":case"</border>":case"<left/>":case"<left":case"<left>":case"</left>":case"<right/>":case"<right":case"<right>":case"</right>":case"<top/>":case"<top":case"<top>":case"</top>":case"<bottom/>":case"<bottom":case"<bottom>":case"</bottom>":case"<diagonal":case"<diagonal>":case"<diagonal/>":case"</diagonal>":case"<horizontal":case"<horizontal>":case"<horizontal/>":case"</horizontal>":case"<vertical":case"<vertical>":case"<vertical/>":case"</vertical>":case"<start":case"<start>":case"<start/>":case"</start>":case"<end":case"<end>":case"<end/>":case"</end>":case"<color":case"<color>":case"<color/>":case"</color>":case"<extLst":case"<extLst>":case"</extLst>":break;case"<border":case"<border>":case"<border/>":m={},r.diagonalUp&&(m.diagonalUp=rE(r.diagonalUp)),r.diagonalDown&&(m.diagonalDown=rE(r.diagonalDown)),w.Borders.push(m);break;case"<ext":g=!0;break;case"</ext>":g=!1;break;default:if(c&&c.WTF&&!g)throw Error("unrecognized "+r[0]+" in borders")}})),(E=s.match(r))&&(v=E,w.CellXf=[],b=!1,(v[0].match(ro)||[]).forEach(function(e){var r=rh(e),t=0;switch(ru(r[0])){case"<cellXfs":case"<cellXfs>":case"<cellXfs/>":case"</cellXfs>":case"</xf>":case"</alignment>":case"<protection":case"</protection>":case"<protection/>":case"<extLst":case"<extLst>":case"</extLst>":break;case"<xf":case"<xf/>":for(T=r,delete T[0],t=0;t<nt.length;++t)T[nt[t]]&&(T[nt[t]]=parseInt(T[nt[t]],10));for(t=0;t<na.length;++t)T[na[t]]&&(T[na[t]]=rE(T[na[t]]));if(w.NumberFmt&&T.numFmtId>392){for(t=392;t>60;--t)if(w.NumberFmt[T.numFmtId]==w.NumberFmt[t]){T.numFmtId=t;break}}w.CellXf.push(T);break;case"<alignment":case"<alignment/>":var a={};r.vertical&&(a.vertical=r.vertical),r.horizontal&&(a.horizontal=r.horizontal),null!=r.textRotation&&(a.textRotation=r.textRotation),r.indent&&(a.indent=r.indent),r.wrapText&&(a.wrapText=rE(r.wrapText)),T.alignment=a;break;case"<AlternateContent":case"<ext":b=!0;break;case"</AlternateContent>":case"</ext>":b=!1;break;default:if(c&&c.WTF&&!b)throw Error("unrecognized "+r[0]+" in cellXfs")}})),w):w}}(),ns=["</a:lt1>","</a:dk1>","</a:lt2>","</a:dk2>","</a:accent1>","</a:accent2>","</a:accent3>","</a:accent4>","</a:accent5>","</a:accent6>","</a:hlink>","</a:folHlink>"];function ni(e,r,t){r.themeElements.clrScheme=[];var a={};(e[0].match(ro)||[]).forEach(function(e){var n=rh(e);switch(n[0]){case"<a:clrScheme":case"</a:clrScheme>":break;case"<a:srgbClr":a.rgb=n.val;break;case"<a:sysClr":a.rgb=n.lastClr;break;case"<a:dk1>":case"</a:dk1>":case"<a:lt1>":case"</a:lt1>":case"<a:dk2>":case"</a:dk2>":case"<a:lt2>":case"</a:lt2>":case"<a:accent1>":case"</a:accent1>":case"<a:accent2>":case"</a:accent2>":case"<a:accent3>":case"</a:accent3>":case"<a:accent4>":case"</a:accent4>":case"<a:accent5>":case"</a:accent5>":case"<a:accent6>":case"</a:accent6>":case"<a:hlink>":case"</a:hlink>":case"<a:folHlink>":case"</a:folHlink>":"/"===n[0].charAt(1)?(r.themeElements.clrScheme[ns.indexOf(n[0])]=a,a={}):a.name=n[0].slice(3,n[0].length-1);break;default:if(t&&t.WTF)throw Error("Unrecognized "+n[0]+" in clrScheme")}})}function nc(){}function no(){}var nl=/<a:clrScheme([^>]*)>[\s\S]*<\/a:clrScheme>/,nf=/<a:fontScheme([^>]*)>[\s\S]*<\/a:fontScheme>/,nh=/<a:fmtScheme([^>]*)>[\s\S]*<\/a:fmtScheme>/,nu=/<a:themeElements([^>]*)>[\s\S]*<\/a:themeElements>/;function nd(e,r){e&&0!==e.length||(e=function(e,r){!1;!1;var t=[rs];return t[t.length]='<a:theme xmlns:a="http://schemas.openxmlformats.org/drawingml/2006/main" name="Office Theme">',t[t.length]="<a:themeElements>",t[t.length]='<a:clrScheme name="Office">',t[t.length]='<a:dk1><a:sysClr val="windowText" lastClr="000000"/></a:dk1>',t[t.length]='<a:lt1><a:sysClr val="window" lastClr="FFFFFF"/></a:lt1>',t[t.length]='<a:dk2><a:srgbClr val="1F497D"/></a:dk2>',t[t.length]='<a:lt2><a:srgbClr val="EEECE1"/></a:lt2>',t[t.length]='<a:accent1><a:srgbClr val="4F81BD"/></a:accent1>',t[t.length]='<a:accent2><a:srgbClr val="C0504D"/></a:accent2>',t[t.length]='<a:accent3><a:srgbClr val="9BBB59"/></a:accent3>',t[t.length]='<a:accent4><a:srgbClr val="8064A2"/></a:accent4>',t[t.length]='<a:accent5><a:srgbClr val="4BACC6"/></a:accent5>',t[t.length]='<a:accent6><a:srgbClr val="F79646"/></a:accent6>',t[t.length]='<a:hlink><a:srgbClr val="0000FF"/></a:hlink>',t[t.length]='<a:folHlink><a:srgbClr val="800080"/></a:folHlink>',t[t.length]="</a:clrScheme>",t[t.length]='<a:fontScheme name="Office">',t[t.length]="<a:majorFont>",t[t.length]='<a:latin typeface="Cambria"/>',t[t.length]='<a:ea typeface=""/>',t[t.length]='<a:cs typeface=""/>',t[t.length]='<a:font script="Jpan" typeface="ＭＳ Ｐゴシック"/>',t[t.length]='<a:font script="Hang" typeface="맑은 고딕"/>',t[t.length]='<a:font script="Hans" typeface="宋体"/>',t[t.length]='<a:font script="Hant" typeface="新細明體"/>',t[t.length]='<a:font script="Arab" typeface="Times New Roman"/>',t[t.length]='<a:font script="Hebr" typeface="Times New Roman"/>',t[t.length]='<a:font script="Thai" typeface="Tahoma"/>',t[t.length]='<a:font script="Ethi" typeface="Nyala"/>',t[t.length]='<a:font script="Beng" typeface="Vrinda"/>',t[t.length]='<a:font script="Gujr" typeface="Shruti"/>',t[t.length]='<a:font script="Khmr" typeface="MoolBoran"/>',t[t.length]='<a:font script="Knda" typeface="Tunga"/>',t[t.length]='<a:font script="Guru" typeface="Raavi"/>',t[t.length]='<a:font script="Cans" typeface="Euphemia"/>',t[t.length]='<a:font script="Cher" typeface="Plantagenet Cherokee"/>',t[t.length]='<a:font script="Yiii" typeface="Microsoft Yi Baiti"/>',t[t.length]='<a:font script="Tibt" typeface="Microsoft Himalaya"/>',t[t.length]='<a:font script="Thaa" typeface="MV Boli"/>',t[t.length]='<a:font script="Deva" typeface="Mangal"/>',t[t.length]='<a:font script="Telu" typeface="Gautami"/>',t[t.length]='<a:font script="Taml" typeface="Latha"/>',t[t.length]='<a:font script="Syrc" typeface="Estrangelo Edessa"/>',t[t.length]='<a:font script="Orya" typeface="Kalinga"/>',t[t.length]='<a:font script="Mlym" typeface="Kartika"/>',t[t.length]='<a:font script="Laoo" typeface="DokChampa"/>',t[t.length]='<a:font script="Sinh" typeface="Iskoola Pota"/>',t[t.length]='<a:font script="Mong" typeface="Mongolian Baiti"/>',t[t.length]='<a:font script="Viet" typeface="Times New Roman"/>',t[t.length]='<a:font script="Uigh" typeface="Microsoft Uighur"/>',t[t.length]='<a:font script="Geor" typeface="Sylfaen"/>',t[t.length]="</a:majorFont>",t[t.length]="<a:minorFont>",t[t.length]='<a:latin typeface="Calibri"/>',t[t.length]='<a:ea typeface=""/>',t[t.length]='<a:cs typeface=""/>',t[t.length]='<a:font script="Jpan" typeface="ＭＳ Ｐゴシック"/>',t[t.length]='<a:font script="Hang" typeface="맑은 고딕"/>',t[t.length]='<a:font script="Hans" typeface="宋体"/>',t[t.length]='<a:font script="Hant" typeface="新細明體"/>',t[t.length]='<a:font script="Arab" typeface="Arial"/>',t[t.length]='<a:font script="Hebr" typeface="Arial"/>',t[t.length]='<a:font script="Thai" typeface="Tahoma"/>',t[t.length]='<a:font script="Ethi" typeface="Nyala"/>',t[t.length]='<a:font script="Beng" typeface="Vrinda"/>',t[t.length]='<a:font script="Gujr" typeface="Shruti"/>',t[t.length]='<a:font script="Khmr" typeface="DaunPenh"/>',t[t.length]='<a:font script="Knda" typeface="Tunga"/>',t[t.length]='<a:font script="Guru" typeface="Raavi"/>',t[t.length]='<a:font script="Cans" typeface="Euphemia"/>',t[t.length]='<a:font script="Cher" typeface="Plantagenet Cherokee"/>',t[t.length]='<a:font script="Yiii" typeface="Microsoft Yi Baiti"/>',t[t.length]='<a:font script="Tibt" typeface="Microsoft Himalaya"/>',t[t.length]='<a:font script="Thaa" typeface="MV Boli"/>',t[t.length]='<a:font script="Deva" typeface="Mangal"/>',t[t.length]='<a:font script="Telu" typeface="Gautami"/>',t[t.length]='<a:font script="Taml" typeface="Latha"/>',t[t.length]='<a:font script="Syrc" typeface="Estrangelo Edessa"/>',t[t.length]='<a:font script="Orya" typeface="Kalinga"/>',t[t.length]='<a:font script="Mlym" typeface="Kartika"/>',t[t.length]='<a:font script="Laoo" typeface="DokChampa"/>',t[t.length]='<a:font script="Sinh" typeface="Iskoola Pota"/>',t[t.length]='<a:font script="Mong" typeface="Mongolian Baiti"/>',t[t.length]='<a:font script="Viet" typeface="Arial"/>',t[t.length]='<a:font script="Uigh" typeface="Microsoft Uighur"/>',t[t.length]='<a:font script="Geor" typeface="Sylfaen"/>',t[t.length]="</a:minorFont>",t[t.length]="</a:fontScheme>",t[t.length]='<a:fmtScheme name="Office">',t[t.length]="<a:fillStyleLst>",t[t.length]='<a:solidFill><a:schemeClr val="phClr"/></a:solidFill>',t[t.length]='<a:gradFill rotWithShape="1">',t[t.length]="<a:gsLst>",t[t.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="50000"/><a:satMod val="300000"/></a:schemeClr></a:gs>',t[t.length]='<a:gs pos="35000"><a:schemeClr val="phClr"><a:tint val="37000"/><a:satMod val="300000"/></a:schemeClr></a:gs>',t[t.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:tint val="15000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',t[t.length]="</a:gsLst>",t[t.length]='<a:lin ang="16200000" scaled="1"/>',t[t.length]="</a:gradFill>",t[t.length]='<a:gradFill rotWithShape="1">',t[t.length]="<a:gsLst>",t[t.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="100000"/><a:shade val="100000"/><a:satMod val="130000"/></a:schemeClr></a:gs>',t[t.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:tint val="50000"/><a:shade val="100000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',t[t.length]="</a:gsLst>",t[t.length]='<a:lin ang="16200000" scaled="0"/>',t[t.length]="</a:gradFill>",t[t.length]="</a:fillStyleLst>",t[t.length]="<a:lnStyleLst>",t[t.length]='<a:ln w="9525" cap="flat" cmpd="sng" algn="ctr"><a:solidFill><a:schemeClr val="phClr"><a:shade val="95000"/><a:satMod val="105000"/></a:schemeClr></a:solidFill><a:prstDash val="solid"/></a:ln>',t[t.length]='<a:ln w="25400" cap="flat" cmpd="sng" algn="ctr"><a:solidFill><a:schemeClr val="phClr"/></a:solidFill><a:prstDash val="solid"/></a:ln>',t[t.length]='<a:ln w="38100" cap="flat" cmpd="sng" algn="ctr"><a:solidFill><a:schemeClr val="phClr"/></a:solidFill><a:prstDash val="solid"/></a:ln>',t[t.length]="</a:lnStyleLst>",t[t.length]="<a:effectStyleLst>",t[t.length]="<a:effectStyle>",t[t.length]="<a:effectLst>",t[t.length]='<a:outerShdw blurRad="40000" dist="20000" dir="5400000" rotWithShape="0"><a:srgbClr val="000000"><a:alpha val="38000"/></a:srgbClr></a:outerShdw>',t[t.length]="</a:effectLst>",t[t.length]="</a:effectStyle>",t[t.length]="<a:effectStyle>",t[t.length]="<a:effectLst>",t[t.length]='<a:outerShdw blurRad="40000" dist="23000" dir="5400000" rotWithShape="0"><a:srgbClr val="000000"><a:alpha val="35000"/></a:srgbClr></a:outerShdw>',t[t.length]="</a:effectLst>",t[t.length]="</a:effectStyle>",t[t.length]="<a:effectStyle>",t[t.length]="<a:effectLst>",t[t.length]='<a:outerShdw blurRad="40000" dist="23000" dir="5400000" rotWithShape="0"><a:srgbClr val="000000"><a:alpha val="35000"/></a:srgbClr></a:outerShdw>',t[t.length]="</a:effectLst>",t[t.length]='<a:scene3d><a:camera prst="orthographicFront"><a:rot lat="0" lon="0" rev="0"/></a:camera><a:lightRig rig="threePt" dir="t"><a:rot lat="0" lon="0" rev="1200000"/></a:lightRig></a:scene3d>',t[t.length]='<a:sp3d><a:bevelT w="63500" h="25400"/></a:sp3d>',t[t.length]="</a:effectStyle>",t[t.length]="</a:effectStyleLst>",t[t.length]="<a:bgFillStyleLst>",t[t.length]='<a:solidFill><a:schemeClr val="phClr"/></a:solidFill>',t[t.length]='<a:gradFill rotWithShape="1">',t[t.length]="<a:gsLst>",t[t.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="40000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',t[t.length]='<a:gs pos="40000"><a:schemeClr val="phClr"><a:tint val="45000"/><a:shade val="99000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',t[t.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:shade val="20000"/><a:satMod val="255000"/></a:schemeClr></a:gs>',t[t.length]="</a:gsLst>",t[t.length]='<a:path path="circle"><a:fillToRect l="50000" t="-80000" r="50000" b="180000"/></a:path>',t[t.length]="</a:gradFill>",t[t.length]='<a:gradFill rotWithShape="1">',t[t.length]="<a:gsLst>",t[t.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="80000"/><a:satMod val="300000"/></a:schemeClr></a:gs>',t[t.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:shade val="30000"/><a:satMod val="200000"/></a:schemeClr></a:gs>',t[t.length]="</a:gsLst>",t[t.length]='<a:path path="circle"><a:fillToRect l="50000" t="50000" r="50000" b="50000"/></a:path>',t[t.length]="</a:gradFill>",t[t.length]="</a:bgFillStyleLst>",t[t.length]="</a:fmtScheme>",t[t.length]="</a:themeElements>",t[t.length]="<a:objectDefaults>",t[t.length]="<a:spDef>",t[t.length]='<a:spPr/><a:bodyPr/><a:lstStyle/><a:style><a:lnRef idx="1"><a:schemeClr val="accent1"/></a:lnRef><a:fillRef idx="3"><a:schemeClr val="accent1"/></a:fillRef><a:effectRef idx="2"><a:schemeClr val="accent1"/></a:effectRef><a:fontRef idx="minor"><a:schemeClr val="lt1"/></a:fontRef></a:style>',t[t.length]="</a:spDef>",t[t.length]="<a:lnDef>",t[t.length]='<a:spPr/><a:bodyPr/><a:lstStyle/><a:style><a:lnRef idx="2"><a:schemeClr val="accent1"/></a:lnRef><a:fillRef idx="0"><a:schemeClr val="accent1"/></a:fillRef><a:effectRef idx="1"><a:schemeClr val="accent1"/></a:effectRef><a:fontRef idx="minor"><a:schemeClr val="tx1"/></a:fontRef></a:style>',t[t.length]="</a:lnDef>",t[t.length]="</a:objectDefaults>",t[t.length]="<a:extraClrSchemeLst/>",t[t.length]="</a:theme>",t.join("")}());var t,a,n,s={};if(!(n=e.match(nu)))throw Error("themeElements not found in theme");return t=n[0],s.themeElements={},[["clrScheme",nl,ni],["fontScheme",nf,nc],["fmtScheme",nh,no]].forEach(function(e){if(!(a=t.match(e[1])))throw Error(e[0]+" not found in themeElements");e[2](a,s,r)}),s.raw=e,s}function np(e,r,t,a){var n,s=Array.isArray(e);r.forEach(function(r){var i=tT(r.ref);if(s?(e[i.r]||(e[i.r]=[]),n=e[i.r][i.c]):n=e[r.ref],!n){n={t:"z"},s?e[i.r][i.c]=n:e[r.ref]=n;var c=tA(e["!ref"]||"BDWGO1000001:A1");c.s.r>i.r&&(c.s.r=i.r),c.e.r<i.r&&(c.e.r=i.r),c.s.c>i.c&&(c.s.c=i.c),c.e.c<i.c&&(c.e.c=i.c);var o=tw(c);o!==e["!ref"]&&(e["!ref"]=o)}n.c||(n.c=[]);var l={a:r.author,t:r.t,r:r.r,T:t};r.h&&(l.h=r.h);for(var f=n.c.length-1;f>=0;--f){if(!t&&n.c[f].T)return;t&&!n.c[f].T&&n.c.splice(f,1)}if(t&&a){for(f=0;f<a.length;++f)if(l.a==a[f].id){l.a=a[f].name||l.a;break}}n.c.push(l)})}var nm=function(){var e=/(^|[^A-Za-z_])R(\[?-?\d+\]|[1-9]\d*|)C(\[?-?\d+\]|[1-9]\d*|)(?![A-Za-z0-9_])/g,r={r:0,c:0};function t(e,t,a,n){var s=!1,i=!1;0==a.length?i=!0:"["==a.charAt(0)&&(i=!0,a=a.slice(1,-1)),0==n.length?s=!0:"["==n.charAt(0)&&(s=!0,n=n.slice(1,-1));var c=a.length>0?0|parseInt(a,10):0,o=n.length>0?0|parseInt(n,10):0;return s?o+=r.c:--o,i?c+=r.r:--c,t+(s?"":"$")+tv(o)+(i?"":"$")+tm(c)}return function(a,n){return r=n,a.replace(e,t)}}(),ng=/(^|[^._A-Z0-9])([$]?)([A-Z]{1,2}|[A-W][A-Z]{2}|X[A-E][A-Z]|XF[A-D])([$]?)(10[0-3]\d{4}|104[0-7]\d{3}|1048[0-4]\d{2}|10485[0-6]\d|104857[0-6]|[1-9]\d{0,5})(?![_.\(A-Za-z0-9])/g,nv=function(e,r){return e.replace(ng,function(e,t,a,n,s,i){var c=tg(n)-(a?0:r.c),o=tp(i)-(s?0:r.r);return t+"R"+(0==o?"":s?o+1:"["+o+"]")+"C"+(0==c?"":a?c+1:"["+c+"]")})};function nT(e,r){return e.replace(ng,function(e,t,a,n,s,i){return t+("$"==a?a+n:tv(tg(n)+r.c))+("$"==s?s+i:tm(tp(i)+r.r))})}function nb(e){return e.replace(/_xlfn\./g,"")}function nE(e){e.l+=1}function nw(e,r){var t=e.read_shift(1==r?1:2);return[16383&t,t>>14&1,t>>15&1]}function nA(e,r,t){var a=2;if(t)if(t.biff>=2&&t.biff<=5)return nS(e,r,t);else 12==t.biff&&(a=4);var n=e.read_shift(a),s=e.read_shift(a),i=nw(e,2),c=nw(e,2);return{s:{r:n,c:i[0],cRel:i[1],rRel:i[2]},e:{r:s,c:c[0],cRel:c[1],rRel:c[2]}}}function nS(e){var r=nw(e,2),t=nw(e,2),a=e.read_shift(1),n=e.read_shift(1);return{s:{r:r[0],c:a,cRel:r[1],rRel:r[2]},e:{r:t[0],c:n,cRel:t[1],rRel:t[2]}}}function nk(e,r,t){if(t&&t.biff>=2&&t.biff<=5){var a,n,s;return n=nw(a=e,2),s=a.read_shift(1),{r:n[0],c:s,cRel:n[1],rRel:n[2]}}var i=e.read_shift(t&&12==t.biff?4:2),c=nw(e,2);return{r:i,c:c[0],cRel:c[1],rRel:c[2]}}function ny(e){var r=1&e[e.l+1];return e.l+=4,[r,1]}function nC(e){return[e.read_shift(1),e.read_shift(1)]}function nR(e,r,t){var a;return e.l+=2,[{r:e.read_shift(2),c:255&(a=e.read_shift(2)),fQuoted:!!(16384&a),cRel:a>>15,rRel:a>>15}]}function nO(e){return e.l+=6,[]}function nx(e){return e.l+=2,[aa(e),1&e.read_shift(2)]}var n_=["Data","All","Headers","??","?Data2","??","?DataHeaders","??","Totals","??","??","??","?DataTotals","??","??","??","?Current"],nI={1:{n:"PtgExp",f:function(e,r,t){return(e.l++,t&&12==t.biff)?[e.read_shift(4,"i"),0]:[e.read_shift(2),e.read_shift(t&&2==t.biff?1:2)]}},2:{n:"PtgTbl",f:ti},3:{n:"PtgAdd",f:nE},4:{n:"PtgSub",f:nE},5:{n:"PtgMul",f:nE},6:{n:"PtgDiv",f:nE},7:{n:"PtgPower",f:nE},8:{n:"PtgConcat",f:nE},9:{n:"PtgLt",f:nE},10:{n:"PtgLe",f:nE},11:{n:"PtgEq",f:nE},12:{n:"PtgGe",f:nE},13:{n:"PtgGt",f:nE},14:{n:"PtgNe",f:nE},15:{n:"PtgIsect",f:nE},16:{n:"PtgUnion",f:nE},17:{n:"PtgRange",f:nE},18:{n:"PtgUplus",f:nE},19:{n:"PtgUminus",f:nE},20:{n:"PtgPercent",f:nE},21:{n:"PtgParen",f:nE},22:{n:"PtgMissArg",f:nE},23:{n:"PtgStr",f:function(e,r,t){return e.l++,as(e,r-1,t)}},26:{n:"PtgSheet",f:function(e,r,t){return e.l+=5,e.l+=2,e.l+=2==t.biff?1:4,["PTGSHEET"]}},27:{n:"PtgEndSheet",f:function(e,r,t){return e.l+=2==t.biff?4:5,["PTGENDSHEET"]}},28:{n:"PtgErr",f:function(e){return e.l++,tV[e.read_shift(1)]}},29:{n:"PtgBool",f:function(e){return e.l++,0!==e.read_shift(1)}},30:{n:"PtgInt",f:function(e){return e.l++,e.read_shift(2)}},31:{n:"PtgNum",f:function(e){return e.l++,tF(e,8)}},32:{n:"PtgArray",f:function(e,r,t){var a=(96&e[e.l++])>>5;return e.l+=2==t.biff?6:12==t.biff?14:7,[a]}},33:{n:"PtgFunc",f:function(e,r,t){var a=(96&e[e.l])>>5;e.l+=1;var n=e.read_shift(t&&t.biff<=3?1:2);return[n$[n],nz[n],a]}},34:{n:"PtgFuncVar",f:function(e,r,t){var a,n=e[e.l++],s=e.read_shift(1),i=t&&t.biff<=3?[88==n?-1:0,e.read_shift(1)]:[(a=e)[a.l+1]>>7,32767&a.read_shift(2)];return[s,(0===i[0]?nz:nG)[i[1]]]}},35:{n:"PtgName",f:function(e,r,t){var a=e.read_shift(1)>>>5&3,n=!t||t.biff>=8?4:2,s=e.read_shift(n);switch(t.biff){case 2:e.l+=5;break;case 3:case 4:e.l+=8;break;case 5:e.l+=12}return[a,0,s]}},36:{n:"PtgRef",f:function(e,r,t){var a=(96&e[e.l])>>5;return e.l+=1,[a,nk(e,0,t)]}},37:{n:"PtgArea",f:function(e,r,t){return[(96&e[e.l++])>>5,nA(e,t.biff>=2&&t.biff<=5?6:8,t)]}},38:{n:"PtgMemArea",f:function(e,r,t){var a=e.read_shift(1)>>>5&3;return e.l+=t&&2==t.biff?3:4,[a,e.read_shift(t&&2==t.biff?1:2)]}},39:{n:"PtgMemErr",f:ti},40:{n:"PtgMemNoMem",f:ti},41:{n:"PtgMemFunc",f:function(e,r,t){return[e.read_shift(1)>>>5&3,e.read_shift(t&&2==t.biff?1:2)]}},42:{n:"PtgRefErr",f:function(e,r,t){var a=e.read_shift(1)>>>5&3;return e.l+=4,t.biff<8&&e.l--,12==t.biff&&(e.l+=2),[a]}},43:{n:"PtgAreaErr",f:function(e,r,t){var a=(96&e[e.l++])>>5;return e.l+=t&&t.biff>8?12:t.biff<8?6:8,[a]}},44:{n:"PtgRefN",f:function(e,r,t){var a=(96&e[e.l])>>5;return e.l+=1,[a,function(e,r,t){var a,n,s,i,c,o=t&&t.biff?t.biff:8;if(o>=2&&o<=5){return n=(a=e).read_shift(2),s=a.read_shift(1),i=(32768&n)>>15,c=(16384&n)>>14,n&=16383,1==i&&n>=8192&&(n-=16384),1==c&&s>=128&&(s-=256),{r:n,c:s,cRel:c,rRel:i}}var l=e.read_shift(o>=12?4:2),f=e.read_shift(2),h=(16384&f)>>14,u=(32768&f)>>15;if(f&=16383,1==u)for(;l>524287;)l-=1048576;if(1==h)for(;f>8191;)f-=16384;return{r:l,c:f,cRel:h,rRel:u}}(e,0,t)]}},45:{n:"PtgAreaN",f:function(e,r,t){return[(96&e[e.l++])>>5,function(e,r,t){if(t.biff<8)return nS(e,r,t);var a=e.read_shift(12==t.biff?4:2),n=e.read_shift(12==t.biff?4:2),s=nw(e,2),i=nw(e,2);return{s:{r:a,c:s[0],cRel:s[1],rRel:s[2]},e:{r:n,c:i[0],cRel:i[1],rRel:i[2]}}}(e,r-1,t)]}},46:{n:"PtgMemAreaN",f:function(e){return[e.read_shift(1)>>>5&3,e.read_shift(2)]}},47:{n:"PtgMemNoMemN",f:function(e){return[e.read_shift(1)>>>5&3,e.read_shift(2)]}},57:{n:"PtgNameX",f:function(e,r,t){var a,n,s,i;return 5==t.biff?(n=(a=e).read_shift(1)>>>5&3,s=a.read_shift(2,"i"),a.l+=8,i=a.read_shift(2),a.l+=12,[n,s,i]):[e.read_shift(1)>>>5&3,e.read_shift(2),e.read_shift(4)]}},58:{n:"PtgRef3d",f:function(e,r,t){var a=(96&e[e.l])>>5;e.l+=1;var n=e.read_shift(2);return t&&5==t.biff&&(e.l+=12),[a,n,nk(e,0,t)]}},59:{n:"PtgArea3d",f:function(e,r,t){var a=(96&e[e.l++])>>5,n=e.read_shift(2,"i"),s=8;if(t)switch(t.biff){case 5:e.l+=12,s=6;break;case 12:s=12}return[a,n,nA(e,s,t)]}},60:{n:"PtgRefErr3d",f:function(e,r,t){var a=(96&e[e.l++])>>5,n=e.read_shift(2),s=4;if(t)switch(t.biff){case 5:s=15;break;case 12:s=6}return e.l+=s,[a,n]}},61:{n:"PtgAreaErr3d",f:function(e,r,t){var a=(96&e[e.l++])>>5,n=e.read_shift(2),s=8;if(t)switch(t.biff){case 5:e.l+=12,s=6;break;case 12:s=12}return e.l+=s,[a,n]}},255:{}},nN={64:32,96:32,65:33,97:33,66:34,98:34,67:35,99:35,68:36,100:36,69:37,101:37,70:38,102:38,71:39,103:39,72:40,104:40,73:41,105:41,74:42,106:42,75:43,107:43,76:44,108:44,77:45,109:45,78:46,110:46,79:47,111:47,88:34,120:34,89:57,121:57,90:58,122:58,91:59,123:59,92:60,124:60,93:61,125:61},nD={1:{n:"PtgElfLel",f:nx},2:{n:"PtgElfRw",f:nR},3:{n:"PtgElfCol",f:nR},6:{n:"PtgElfRwV",f:nR},7:{n:"PtgElfColV",f:nR},10:{n:"PtgElfRadical",f:nR},11:{n:"PtgElfRadicalS",f:nO},13:{n:"PtgElfColS",f:nO},15:{n:"PtgElfColSV",f:nO},16:{n:"PtgElfRadicalLel",f:nx},25:{n:"PtgList",f:function(e){e.l+=2;var r=e.read_shift(2),t=e.read_shift(2),a=e.read_shift(4),n=e.read_shift(2),s=e.read_shift(2),i=n_[t>>2&31];return{ixti:r,coltype:3&t,rt:i,idx:a,c:n,C:s}}},29:{n:"PtgSxName",f:function(e){return e.l+=2,[e.read_shift(4)]}},255:{}},nF={0:{n:"PtgAttrNoop",f:function(e){return e.l+=4,[0,0]}},1:{n:"PtgAttrSemi",f:function(e,r,t){var a=255&e[e.l+1]?1:0;return e.l+=t&&2==t.biff?3:4,[a]}},2:{n:"PtgAttrIf",f:function(e,r,t){var a=255&e[e.l+1]?1:0;return e.l+=2,[a,e.read_shift(t&&2==t.biff?1:2)]}},4:{n:"PtgAttrChoose",f:function(e,r,t){e.l+=2;for(var a=e.read_shift(t&&2==t.biff?1:2),n=[],s=0;s<=a;++s)n.push(e.read_shift(t&&2==t.biff?1:2));return n}},8:{n:"PtgAttrGoto",f:function(e,r,t){var a=255&e[e.l+1]?1:0;return e.l+=2,[a,e.read_shift(t&&2==t.biff?1:2)]}},16:{n:"PtgAttrSum",f:function(e,r,t){e.l+=t&&2==t.biff?3:4}},32:{n:"PtgAttrBaxcel",f:ny},33:{n:"PtgAttrBaxcel",f:ny},64:{n:"PtgAttrSpace",f:function(e){return e.read_shift(2),nC(e,2)}},65:{n:"PtgAttrSpaceSemi",f:function(e){return e.read_shift(2),nC(e,2)}},128:{n:"PtgAttrIfError",f:function(e){var r=255&e[e.l+1]?1:0;return e.l+=2,[r,e.read_shift(2)]}},255:{}};function nP(e,r,t,a){if(a.biff<8)return n=r,void(e.l+=n);for(var n,s,i=e.l+r,c=[],o=0;o!==t.length;++o)switch(t[o][0]){case"PtgArray":t[o][1]=function(e,r,t){var a=0,n=0;12==t.biff?(a=e.read_shift(4),n=e.read_shift(4)):(n=1+e.read_shift(1),a=1+e.read_shift(2)),t.biff>=2&&t.biff<8&&(--a,0==--n&&(n=256));for(var s=0,i=[];s!=a&&(i[s]=[]);++s)for(var c=0;c!=n;++c)i[s][c]=function(e,r){var t=[e.read_shift(1)];if(12==r)switch(t[0]){case 2:t[0]=4;break;case 4:t[0]=16;break;case 0:t[0]=1;break;case 1:t[0]=2}switch(t[0]){case 4:t[1]=at(e,1)?"TRUE":"FALSE",12!=r&&(e.l+=7);break;case 37:case 16:t[1]=tV[e[e.l]],e.l+=12==r?4:8;break;case 0:e.l+=8;break;case 1:t[1]=tF(e,8);break;case 2:t[1]=ao(e,0,{biff:r>0&&r<8?2:r});break;default:throw Error("Bad SerAr: "+t[0])}return t}(e,t.biff);return i}(e,0,a),c.push(t[o][1]);break;case"PtgMemArea":t[o][2]=function(e,r,t){for(var a=e.read_shift(12==t.biff?4:2),n=[],s=0;s!=a;++s)n.push((12==t.biff?tD:ap)(e,8));return n}(e,t[o][1],a),c.push(t[o][2]);break;case"PtgExp":a&&12==a.biff&&(t[o][1][1]=e.read_shift(4),c.push(t[o][1]));break;case"PtgList":case"PtgElfRadicalS":case"PtgElfColS":case"PtgElfColSV":throw"Unsupported "+t[o][0]}return 0!=(r=i-e.l)&&c.push((s=r,void(e.l+=s))),c}function nL(e,r,t){for(var a,n,s,i=e.l+r,c=[];i!=e.l;)(r=i-e.l,n=nI[s=e[e.l]]||nI[nN[s]],(24===s||25===s)&&(n=(24===s?nD:nF)[e[e.l+1]]),n&&n.f)?c.push([n.n,n.f(e,r,t)]):(a=r,e.l+=a);return c}var nM={PtgAdd:"+",PtgConcat:"&",PtgDiv:"/",PtgEq:"=",PtgGe:">=",PtgGt:">",PtgLe:"<=",PtgLt:"<",PtgMul:"*",PtgNe:"<>",PtgPower:"^",PtgSub:"-"};function nU(e,r,t){if(!e)return"SH33TJSERR0";if(t.biff>8&&(!e.XTI||!e.XTI[r]))return e.SheetNames[r];if(!e.XTI)return"SH33TJSERR6";var a=e.XTI[r];if(t.biff<8)return r>1e4&&(r-=65536),r<0&&(r=-r),0==r?"":e.XTI[r-1];if(!a)return"SH33TJSERR1";var n="";if(t.biff>8)switch(e[a[0]][0]){case 357:return n=-1==a[1]?"#REF":e.SheetNames[a[1]],a[1]==a[2]?n:n+":"+e.SheetNames[a[2]];case 358:if(null!=t.SID)return e.SheetNames[t.SID];return"SH33TJSSAME"+e[a[0]][0];default:return"SH33TJSSRC"+e[a[0]][0]}switch(e[a[0]][0][0]){case 1025:return n=-1==a[1]?"#REF":e.SheetNames[a[1]]||"SH33TJSERR3",a[1]==a[2]?n:n+":"+e.SheetNames[a[2]];case 14849:return e[a[0]].slice(1).map(function(e){return e.Name}).join(";;");default:if(!e[a[0]][0][3])return"SH33TJSERR2";return n=-1==a[1]?"#REF":e[a[0]][0][3][a[1]]||"SH33TJSERR4",a[1]==a[2]?n:n+":"+e[a[0]][0][3][a[2]]}}function nB(e,r,t){var a=nU(e,r,t);return"#REF"==a?a:function(e,r){if(!e&&!(r&&r.biff<=5&&r.biff>=2))throw Error("empty sheet name");return/[^\w\u4E00-\u9FFF\u3040-\u30FF]/.test(e)?"'"+e+"'":e}(a,t)}function nH(e,r,t,a,n){var s,i,c,o,l=n&&n.biff||8,f={s:{c:0,r:0},e:{c:0,r:0}},h=[],u=0,d=0,p="";if(!e[0]||!e[0][0])return"";for(var m=-1,g="",v=0,T=e[0].length;v<T;++v){var b=e[0][v];switch(b[0]){case"PtgUminus":h.push("-"+h.pop());break;case"PtgUplus":h.push("+"+h.pop());break;case"PtgPercent":h.push(h.pop()+"%");break;case"PtgAdd":case"PtgConcat":case"PtgDiv":case"PtgEq":case"PtgGe":case"PtgGt":case"PtgLe":case"PtgLt":case"PtgMul":case"PtgNe":case"PtgPower":case"PtgSub":if(s=h.pop(),i=h.pop(),m>=0){switch(e[0][m][1][0]){case 0:g=e0(" ",e[0][m][1][1]);break;case 1:g=e0("\r",e[0][m][1][1]);break;default:if(g="",n.WTF)throw Error("Unexpected PtgAttrSpaceType "+e[0][m][1][0])}i+=g,m=-1}h.push(i+nM[b[0]]+s);break;case"PtgIsect":s=h.pop(),i=h.pop(),h.push(i+" "+s);break;case"PtgUnion":s=h.pop(),i=h.pop(),h.push(i+","+s);break;case"PtgRange":s=h.pop(),i=h.pop(),h.push(i+":"+s);break;case"PtgAttrChoose":case"PtgAttrGoto":case"PtgAttrIf":case"PtgAttrIfError":case"PtgAttrBaxcel":case"PtgAttrSemi":case"PtgMemArea":case"PtgTbl":case"PtgMemErr":case"PtgMemAreaN":case"PtgMemNoMemN":case"PtgAttrNoop":case"PtgSheet":case"PtgEndSheet":case"PtgMemFunc":case"PtgMemNoMem":break;case"PtgRef":c=tf(b[1][1],f,n),h.push(tu(c,l));break;case"PtgRefN":c=t?tf(b[1][1],t,n):b[1][1],h.push(tu(c,l));break;case"PtgRef3d":u=b[1][1],c=tf(b[1][2],f,n),p=nB(a,u,n),h.push(p+"!"+tu(c,l));break;case"PtgFunc":case"PtgFuncVar":var E=b[1][0],w=b[1][1];E||(E=0);var A=0==(E&=127)?[]:h.slice(-E);h.length-=E,"User"===w&&(w=A.shift()),h.push(w+"("+A.join(",")+")");break;case"PtgBool":h.push(b[1]?"TRUE":"FALSE");break;case"PtgInt":case"PtgErr":h.push(b[1]);break;case"PtgNum":h.push(String(b[1]));break;case"PtgStr":h.push('"'+b[1].replace(/"/g,'""')+'"');break;case"PtgAreaN":o=th(b[1][1],t?{s:t}:f,n),h.push(td(o,n));break;case"PtgArea":o=th(b[1][1],f,n),h.push(td(o,n));break;case"PtgArea3d":u=b[1][1],o=b[1][2],p=nB(a,u,n),h.push(p+"!"+td(o,n));break;case"PtgAttrSum":h.push("SUM("+h.pop()+")");break;case"PtgName":d=b[1][2];var S=(a.names||[])[d-1]||(a[0]||[])[d],k=S?S.Name:"SH33TJSNAME"+String(d);k&&"_xlfn."==k.slice(0,6)&&!n.xlfn&&(k=k.slice(6)),h.push(k);break;case"PtgNameX":var y,C=b[1][1];if(d=b[1][2],n.biff<=5)C<0&&(C=-C),a[C]&&(y=a[C][d]);else{var R="";if(14849==((a[C]||[])[0]||[])[0]||(1025==((a[C]||[])[0]||[])[0]?a[C][d]&&a[C][d].itab>0&&(R=a.SheetNames[a[C][d].itab-1]+"!"):R=a.SheetNames[d-1]+"!"),a[C]&&a[C][d])R+=a[C][d].Name;else if(a[0]&&a[0][d])R+=a[0][d].Name;else{var O=(nU(a,C,n)||"").split(";;");O[d-1]?R=O[d-1]:R+="SH33TJSERRX"}h.push(R);break}y||(y={Name:"SH33TJSERRY"}),h.push(y.Name);break;case"PtgParen":var x="(",_=")";if(m>=0){switch(g="",e[0][m][1][0]){case 2:x=e0(" ",e[0][m][1][1])+x;break;case 3:x=e0("\r",e[0][m][1][1])+x;break;case 4:_=e0(" ",e[0][m][1][1])+_;break;case 5:_=e0("\r",e[0][m][1][1])+_;break;default:if(n.WTF)throw Error("Unexpected PtgAttrSpaceType "+e[0][m][1][0])}m=-1}h.push(x+h.pop()+_);break;case"PtgRefErr":case"PtgRefErr3d":case"PtgAreaErr":case"PtgAreaErr3d":h.push("#REF!");break;case"PtgExp":c={c:b[1][1],r:b[1][0]};var I={c:t.c,r:t.r};if(a.sharedf[tb(c)]){var N=a.sharedf[tb(c)];h.push(nH(N,f,I,a,n))}else{var D=!1;for(s=0;s!=a.arrayf.length;++s)if((i=a.arrayf[s],!(c.c<i[0].s.c)&&!(c.c>i[0].e.c))&&!(c.r<i[0].s.r)&&!(c.r>i[0].e.r)){h.push(nH(i[1],f,I,a,n)),D=!0;break}D||h.push(b[1])}break;case"PtgArray":h.push("{"+function(e){for(var r=[],t=0;t<e.length;++t){for(var a=e[t],n=[],s=0;s<a.length;++s){var i=a[s];i?2===i[0]?n.push('"'+i[1].replace(/"/g,'""')+'"'):n.push(i[1]):n.push("")}r.push(n.join(","))}return r.join(";")}(b[1])+"}");break;case"PtgAttrSpace":case"PtgAttrSpaceSemi":m=v;break;case"PtgMissArg":h.push("");break;case"PtgList":h.push("Table"+b[1].idx+"[#"+b[1].rt+"]");break;case"PtgElfCol":case"PtgElfColS":case"PtgElfColSV":case"PtgElfColV":case"PtgElfLel":case"PtgElfRadical":case"PtgElfRadicalLel":case"PtgElfRadicalS":case"PtgElfRw":case"PtgElfRwV":throw Error("Unsupported ELFs");default:throw Error("Unrecognized Formula Token: "+String(b))}var F=["PtgAttrSpace","PtgAttrSpaceSemi","PtgAttrGoto"];if(3!=n.biff&&m>=0&&-1==F.indexOf(e[0][v][0])){b=e[0][m];var P=!0;switch(b[1][0]){case 4:P=!1;case 0:g=e0(" ",b[1][1]);break;case 5:P=!1;case 1:g=e0("\r",b[1][1]);break;default:if(g="",n.WTF)throw Error("Unexpected PtgAttrSpaceType "+b[1][0])}h.push((P?g:"")+h.pop()+(P?"":g)),m=-1}}if(h.length>1&&n.WTF)throw Error("bad formula stack");return h[0]}function nW(e,r,t){var a=e.l+r,n=au(e,6);2==t.biff&&++e.l;var s=function(e){var r;if(65535!==r5(e,e.l+6))return[tF(e),"n"];switch(e[e.l]){case 0:return e.l+=8,["String","s"];case 1:return r=1===e[e.l+2],e.l+=8,[r,"b"];case 2:return r=e[e.l+2],e.l+=8,[r,"e"];case 3:return e.l+=8,["","s"]}return[]}(e,8),i=e.read_shift(1);2!=t.biff&&(e.read_shift(1),t.biff>=5&&e.read_shift(4));var c=function(e,r,t){var a,n,s=e.l+r,i=2==t.biff?1:2,c=e.read_shift(i);if(65535==c)return[[],(a=r-2,void(e.l+=a))];var o=nL(e,c,t);return r!==c+i&&(n=nP(e,r-c-i,o,t)),e.l=s,[o,n]}(e,a-e.l,t);return{cell:n,val:s[0],formula:c,shared:i>>3&1,tt:s[1]}}function nV(e,r,t){var a=e.read_shift(4),n=nL(e,a,t),s=e.read_shift(4),i=s>0?nP(e,s,n,t):null;return[n,i]}var nG={0:"BEEP",1:"OPEN",2:"OPEN.LINKS",3:"CLOSE.ALL",4:"SAVE",5:"SAVE.AS",6:"FILE.DELETE",7:"PAGE.SETUP",8:"PRINT",9:"PRINTER.SETUP",10:"QUIT",11:"NEW.WINDOW",12:"ARRANGE.ALL",13:"WINDOW.SIZE",14:"WINDOW.MOVE",15:"FULL",16:"CLOSE",17:"RUN",22:"SET.PRINT.AREA",23:"SET.PRINT.TITLES",24:"SET.PAGE.BREAK",25:"REMOVE.PAGE.BREAK",26:"FONT",27:"DISPLAY",28:"PROTECT.DOCUMENT",29:"PRECISION",30:"A1.R1C1",31:"CALCULATE.NOW",32:"CALCULATION",34:"DATA.FIND",35:"EXTRACT",36:"DATA.DELETE",37:"SET.DATABASE",38:"SET.CRITERIA",39:"SORT",40:"DATA.SERIES",41:"TABLE",42:"FORMAT.NUMBER",43:"ALIGNMENT",44:"STYLE",45:"BORDER",46:"CELL.PROTECTION",47:"COLUMN.WIDTH",48:"UNDO",49:"CUT",50:"COPY",51:"PASTE",52:"CLEAR",53:"PASTE.SPECIAL",54:"EDIT.DELETE",55:"INSERT",56:"FILL.RIGHT",57:"FILL.DOWN",61:"DEFINE.NAME",62:"CREATE.NAMES",63:"FORMULA.GOTO",64:"FORMULA.FIND",65:"SELECT.LAST.CELL",66:"SHOW.ACTIVE.CELL",67:"GALLERY.AREA",68:"GALLERY.BAR",69:"GALLERY.COLUMN",70:"GALLERY.LINE",71:"GALLERY.PIE",72:"GALLERY.SCATTER",73:"COMBINATION",74:"PREFERRED",75:"ADD.OVERLAY",76:"GRIDLINES",77:"SET.PREFERRED",78:"AXES",79:"LEGEND",80:"ATTACH.TEXT",81:"ADD.ARROW",82:"SELECT.CHART",83:"SELECT.PLOT.AREA",84:"PATTERNS",85:"MAIN.CHART",86:"OVERLAY",87:"SCALE",88:"FORMAT.LEGEND",89:"FORMAT.TEXT",90:"EDIT.REPEAT",91:"PARSE",92:"JUSTIFY",93:"HIDE",94:"UNHIDE",95:"WORKSPACE",96:"FORMULA",97:"FORMULA.FILL",98:"FORMULA.ARRAY",99:"DATA.FIND.NEXT",100:"DATA.FIND.PREV",101:"FORMULA.FIND.NEXT",102:"FORMULA.FIND.PREV",103:"ACTIVATE",104:"ACTIVATE.NEXT",105:"ACTIVATE.PREV",106:"UNLOCKED.NEXT",107:"UNLOCKED.PREV",108:"COPY.PICTURE",109:"SELECT",110:"DELETE.NAME",111:"DELETE.FORMAT",112:"VLINE",113:"HLINE",114:"VPAGE",115:"HPAGE",116:"VSCROLL",117:"HSCROLL",118:"ALERT",119:"NEW",120:"CANCEL.COPY",121:"SHOW.CLIPBOARD",122:"MESSAGE",124:"PASTE.LINK",125:"APP.ACTIVATE",126:"DELETE.ARROW",127:"ROW.HEIGHT",128:"FORMAT.MOVE",129:"FORMAT.SIZE",130:"FORMULA.REPLACE",131:"SEND.KEYS",132:"SELECT.SPECIAL",133:"APPLY.NAMES",134:"REPLACE.FONT",135:"FREEZE.PANES",136:"SHOW.INFO",137:"SPLIT",138:"ON.WINDOW",139:"ON.DATA",140:"DISABLE.INPUT",142:"OUTLINE",143:"LIST.NAMES",144:"FILE.CLOSE",145:"SAVE.WORKBOOK",146:"DATA.FORM",147:"COPY.CHART",148:"ON.TIME",149:"WAIT",150:"FORMAT.FONT",151:"FILL.UP",152:"FILL.LEFT",153:"DELETE.OVERLAY",155:"SHORT.MENUS",159:"SET.UPDATE.STATUS",161:"COLOR.PALETTE",162:"DELETE.STYLE",163:"WINDOW.RESTORE",164:"WINDOW.MAXIMIZE",166:"CHANGE.LINK",167:"CALCULATE.DOCUMENT",168:"ON.KEY",169:"APP.RESTORE",170:"APP.MOVE",171:"APP.SIZE",172:"APP.MINIMIZE",173:"APP.MAXIMIZE",174:"BRING.TO.FRONT",175:"SEND.TO.BACK",185:"MAIN.CHART.TYPE",186:"OVERLAY.CHART.TYPE",187:"SELECT.END",188:"OPEN.MAIL",189:"SEND.MAIL",190:"STANDARD.FONT",191:"CONSOLIDATE",192:"SORT.SPECIAL",193:"GALLERY.3D.AREA",194:"GALLERY.3D.COLUMN",195:"GALLERY.3D.LINE",196:"GALLERY.3D.PIE",197:"VIEW.3D",198:"GOAL.SEEK",199:"WORKGROUP",200:"FILL.GROUP",201:"UPDATE.LINK",202:"PROMOTE",203:"DEMOTE",204:"SHOW.DETAIL",206:"UNGROUP",207:"OBJECT.PROPERTIES",208:"SAVE.NEW.OBJECT",209:"SHARE",210:"SHARE.NAME",211:"DUPLICATE",212:"APPLY.STYLE",213:"ASSIGN.TO.OBJECT",214:"OBJECT.PROTECTION",215:"HIDE.OBJECT",216:"SET.EXTRACT",217:"CREATE.PUBLISHER",218:"SUBSCRIBE.TO",219:"ATTRIBUTES",220:"SHOW.TOOLBAR",222:"PRINT.PREVIEW",223:"EDIT.COLOR",224:"SHOW.LEVELS",225:"FORMAT.MAIN",226:"FORMAT.OVERLAY",227:"ON.RECALC",228:"EDIT.SERIES",229:"DEFINE.STYLE",240:"LINE.PRINT",243:"ENTER.DATA",249:"GALLERY.RADAR",250:"MERGE.STYLES",251:"EDITION.OPTIONS",252:"PASTE.PICTURE",253:"PASTE.PICTURE.LINK",254:"SPELLING",256:"ZOOM",259:"INSERT.OBJECT",260:"WINDOW.MINIMIZE",265:"SOUND.NOTE",266:"SOUND.PLAY",267:"FORMAT.SHAPE",268:"EXTEND.POLYGON",269:"FORMAT.AUTO",272:"GALLERY.3D.BAR",273:"GALLERY.3D.SURFACE",274:"FILL.AUTO",276:"CUSTOMIZE.TOOLBAR",277:"ADD.TOOL",278:"EDIT.OBJECT",279:"ON.DOUBLECLICK",280:"ON.ENTRY",281:"WORKBOOK.ADD",282:"WORKBOOK.MOVE",283:"WORKBOOK.COPY",284:"WORKBOOK.OPTIONS",285:"SAVE.WORKSPACE",288:"CHART.WIZARD",289:"DELETE.TOOL",290:"MOVE.TOOL",291:"WORKBOOK.SELECT",292:"WORKBOOK.ACTIVATE",293:"ASSIGN.TO.TOOL",295:"COPY.TOOL",296:"RESET.TOOL",297:"CONSTRAIN.NUMERIC",298:"PASTE.TOOL",302:"WORKBOOK.NEW",305:"SCENARIO.CELLS",306:"SCENARIO.DELETE",307:"SCENARIO.ADD",308:"SCENARIO.EDIT",309:"SCENARIO.SHOW",310:"SCENARIO.SHOW.NEXT",311:"SCENARIO.SUMMARY",312:"PIVOT.TABLE.WIZARD",313:"PIVOT.FIELD.PROPERTIES",314:"PIVOT.FIELD",315:"PIVOT.ITEM",316:"PIVOT.ADD.FIELDS",318:"OPTIONS.CALCULATION",319:"OPTIONS.EDIT",320:"OPTIONS.VIEW",321:"ADDIN.MANAGER",322:"MENU.EDITOR",323:"ATTACH.TOOLBARS",324:"VBAActivate",325:"OPTIONS.CHART",328:"VBA.INSERT.FILE",330:"VBA.PROCEDURE.DEFINITION",336:"ROUTING.SLIP",338:"ROUTE.DOCUMENT",339:"MAIL.LOGON",342:"INSERT.PICTURE",343:"EDIT.TOOL",344:"GALLERY.DOUGHNUT",350:"CHART.TREND",352:"PIVOT.ITEM.PROPERTIES",354:"WORKBOOK.INSERT",355:"OPTIONS.TRANSITION",356:"OPTIONS.GENERAL",370:"FILTER.ADVANCED",373:"MAIL.ADD.MAILER",374:"MAIL.DELETE.MAILER",375:"MAIL.REPLY",376:"MAIL.REPLY.ALL",377:"MAIL.FORWARD",378:"MAIL.NEXT.LETTER",379:"DATA.LABEL",380:"INSERT.TITLE",381:"FONT.PROPERTIES",382:"MACRO.OPTIONS",383:"WORKBOOK.HIDE",384:"WORKBOOK.UNHIDE",385:"WORKBOOK.DELETE",386:"WORKBOOK.NAME",388:"GALLERY.CUSTOM",390:"ADD.CHART.AUTOFORMAT",391:"DELETE.CHART.AUTOFORMAT",392:"CHART.ADD.DATA",393:"AUTO.OUTLINE",394:"TAB.ORDER",395:"SHOW.DIALOG",396:"SELECT.ALL",397:"UNGROUP.SHEETS",398:"SUBTOTAL.CREATE",399:"SUBTOTAL.REMOVE",400:"RENAME.OBJECT",412:"WORKBOOK.SCROLL",413:"WORKBOOK.NEXT",414:"WORKBOOK.PREV",415:"WORKBOOK.TAB.SPLIT",416:"FULL.SCREEN",417:"WORKBOOK.PROTECT",420:"SCROLLBAR.PROPERTIES",421:"PIVOT.SHOW.PAGES",422:"TEXT.TO.COLUMNS",423:"FORMAT.CHARTTYPE",424:"LINK.FORMAT",425:"TRACER.DISPLAY",430:"TRACER.NAVIGATE",431:"TRACER.CLEAR",432:"TRACER.ERROR",433:"PIVOT.FIELD.GROUP",434:"PIVOT.FIELD.UNGROUP",435:"CHECKBOX.PROPERTIES",436:"LABEL.PROPERTIES",437:"LISTBOX.PROPERTIES",438:"EDITBOX.PROPERTIES",439:"PIVOT.REFRESH",440:"LINK.COMBO",441:"OPEN.TEXT",442:"HIDE.DIALOG",443:"SET.DIALOG.FOCUS",444:"ENABLE.OBJECT",445:"PUSHBUTTON.PROPERTIES",446:"SET.DIALOG.DEFAULT",447:"FILTER",448:"FILTER.SHOW.ALL",449:"CLEAR.OUTLINE",450:"FUNCTION.WIZARD",451:"ADD.LIST.ITEM",452:"SET.LIST.ITEM",453:"REMOVE.LIST.ITEM",454:"SELECT.LIST.ITEM",455:"SET.CONTROL.VALUE",456:"SAVE.COPY.AS",458:"OPTIONS.LISTS.ADD",459:"OPTIONS.LISTS.DELETE",460:"SERIES.AXES",461:"SERIES.X",462:"SERIES.Y",463:"ERRORBAR.X",464:"ERRORBAR.Y",465:"FORMAT.CHART",466:"SERIES.ORDER",467:"MAIL.LOGOFF",468:"CLEAR.ROUTING.SLIP",469:"APP.ACTIVATE.MICROSOFT",470:"MAIL.EDIT.MAILER",471:"ON.SHEET",472:"STANDARD.WIDTH",473:"SCENARIO.MERGE",474:"SUMMARY.INFO",475:"FIND.FILE",476:"ACTIVE.CELL.FONT",477:"ENABLE.TIPWIZARD",478:"VBA.MAKE.ADDIN",480:"INSERTDATATABLE",481:"WORKGROUP.OPTIONS",482:"MAIL.SEND.MAILER",485:"AUTOCORRECT",489:"POST.DOCUMENT",491:"PICKLIST",493:"VIEW.SHOW",494:"VIEW.DEFINE",495:"VIEW.DELETE",509:"SHEET.BACKGROUND",510:"INSERT.MAP.OBJECT",511:"OPTIONS.MENONO",517:"MSOCHECKS",518:"NORMAL",519:"LAYOUT",520:"RM.PRINT.AREA",521:"CLEAR.PRINT.AREA",522:"ADD.PRINT.AREA",523:"MOVE.BRK",545:"HIDECURR.NOTE",546:"HIDEALL.NOTES",547:"DELETE.NOTE",548:"TRAVERSE.NOTES",549:"ACTIVATE.NOTES",620:"PROTECT.REVISIONS",621:"UNPROTECT.REVISIONS",647:"OPTIONS.ME",653:"WEB.PUBLISH",667:"NEWWEBQUERY",673:"PIVOT.TABLE.CHART",753:"OPTIONS.SAVE",755:"OPTIONS.SPELL",808:"HIDEALL.INKANNOTS"},nz={0:"COUNT",1:"IF",2:"ISNA",3:"ISERROR",4:"SUM",5:"AVERAGE",6:"MIN",7:"MAX",8:"ROW",9:"COLUMN",10:"NA",11:"NPV",12:"STDEV",13:"DOLLAR",14:"FIXED",15:"SIN",16:"COS",17:"TAN",18:"ATAN",19:"PI",20:"SQRT",21:"EXP",22:"LN",23:"LOG10",24:"ABS",25:"INT",26:"SIGN",27:"ROUND",28:"LOOKUP",29:"INDEX",30:"REPT",31:"MID",32:"LEN",33:"VALUE",34:"TRUE",35:"FALSE",36:"AND",37:"OR",38:"NOT",39:"MOD",40:"DCOUNT",41:"DSUM",42:"DAVERAGE",43:"DMIN",44:"DMAX",45:"DSTDEV",46:"VAR",47:"DVAR",48:"TEXT",49:"LINEST",50:"TREND",51:"LOGEST",52:"GROWTH",53:"GOTO",54:"HALT",55:"RETURN",56:"PV",57:"FV",58:"NPER",59:"PMT",60:"RATE",61:"MIRR",62:"IRR",63:"RAND",64:"MATCH",65:"DATE",66:"TIME",67:"DAY",68:"MONTH",69:"YEAR",70:"WEEKDAY",71:"HOUR",72:"MINUTE",73:"SECOND",74:"NOW",75:"AREAS",76:"ROWS",77:"COLUMNS",78:"OFFSET",79:"ABSREF",80:"RELREF",81:"ARGUMENT",82:"SEARCH",83:"TRANSPOSE",84:"ERROR",85:"STEP",86:"TYPE",87:"ECHO",88:"SET.NAME",89:"CALLER",90:"DEREF",91:"WINDOWS",92:"SERIES",93:"DOCUMENTS",94:"ACTIVE.CELL",95:"SELECTION",96:"RESULT",97:"ATAN2",98:"ASIN",99:"ACOS",100:"CHOOSE",101:"HLOOKUP",102:"VLOOKUP",103:"LINKS",104:"INPUT",105:"ISREF",106:"GET.FORMULA",107:"GET.NAME",108:"SET.VALUE",109:"LOG",110:"EXEC",111:"CHAR",112:"LOWER",113:"UPPER",114:"PROPER",115:"LEFT",116:"RIGHT",117:"EXACT",118:"TRIM",119:"REPLACE",120:"SUBSTITUTE",121:"CODE",122:"NAMES",123:"DIRECTORY",124:"FIND",125:"CELL",126:"ISERR",127:"ISTEXT",128:"ISNUMBER",129:"ISBLANK",130:"T",131:"N",132:"FOPEN",133:"FCLOSE",134:"FSIZE",135:"FREADLN",136:"FREAD",137:"FWRITELN",138:"FWRITE",139:"FPOS",140:"DATEVALUE",141:"TIMEVALUE",142:"SLN",143:"SYD",144:"DDB",145:"GET.DEF",146:"REFTEXT",147:"TEXTREF",148:"INDIRECT",149:"REGISTER",150:"CALL",151:"ADD.BAR",152:"ADD.MENU",153:"ADD.COMMAND",154:"ENABLE.COMMAND",155:"CHECK.COMMAND",156:"RENAME.COMMAND",157:"SHOW.BAR",158:"DELETE.MENU",159:"DELETE.COMMAND",160:"GET.CHART.ITEM",161:"DIALOG.BOX",162:"CLEAN",163:"MDETERM",164:"MINVERSE",165:"MMULT",166:"FILES",167:"IPMT",168:"PPMT",169:"COUNTA",170:"CANCEL.KEY",171:"FOR",172:"WHILE",173:"BREAK",174:"NEXT",175:"INITIATE",176:"REQUEST",177:"POKE",178:"EXECUTE",179:"TERMINATE",180:"RESTART",181:"HELP",182:"GET.BAR",183:"PRODUCT",184:"FACT",185:"GET.CELL",186:"GET.WORKSPACE",187:"GET.WINDOW",188:"GET.DOCUMENT",189:"DPRODUCT",190:"ISNONTEXT",191:"GET.NOTE",192:"NOTE",193:"STDEVP",194:"VARP",195:"DSTDEVP",196:"DVARP",197:"TRUNC",198:"ISLOGICAL",199:"DCOUNTA",200:"DELETE.BAR",201:"UNREGISTER",204:"USDOLLAR",205:"FINDB",206:"SEARCHB",207:"REPLACEB",208:"LEFTB",209:"RIGHTB",210:"MIDB",211:"LENB",212:"ROUNDUP",213:"ROUNDDOWN",214:"ASC",215:"DBCS",216:"RANK",219:"ADDRESS",220:"DAYS360",221:"TODAY",222:"VDB",223:"ELSE",224:"ELSE.IF",225:"END.IF",226:"FOR.CELL",227:"MEDIAN",228:"SUMPRODUCT",229:"SINH",230:"COSH",231:"TANH",232:"ASINH",233:"ACOSH",234:"ATANH",235:"DGET",236:"CREATE.OBJECT",237:"VOLATILE",238:"LAST.ERROR",239:"CUSTOM.UNDO",240:"CUSTOM.REPEAT",241:"FORMULA.CONVERT",242:"GET.LINK.INFO",243:"TEXT.BOX",244:"INFO",245:"GROUP",246:"GET.OBJECT",247:"DB",248:"PAUSE",251:"RESUME",252:"FREQUENCY",253:"ADD.TOOLBAR",254:"DELETE.TOOLBAR",255:"User",256:"RESET.TOOLBAR",257:"EVALUATE",258:"GET.TOOLBAR",259:"GET.TOOL",260:"SPELLING.CHECK",261:"ERROR.TYPE",262:"APP.TITLE",263:"WINDOW.TITLE",264:"SAVE.TOOLBAR",265:"ENABLE.TOOL",266:"PRESS.TOOL",267:"REGISTER.ID",268:"GET.WORKBOOK",269:"AVEDEV",270:"BETADIST",271:"GAMMALN",272:"BETAINV",273:"BINOMDIST",274:"CHIDIST",275:"CHIINV",276:"COMBIN",277:"CONFIDENCE",278:"CRITBINOM",279:"EVEN",280:"EXPONDIST",281:"FDIST",282:"FINV",283:"FISHER",284:"FISHERINV",285:"FLOOR",286:"GAMMADIST",287:"GAMMAINV",288:"CEILING",289:"HYPGEOMDIST",290:"LOGNORMDIST",291:"LOGINV",292:"NEGBINOMDIST",293:"NORMDIST",294:"NORMSDIST",295:"NORMINV",296:"NORMSINV",297:"STANDARDIZE",298:"ODD",299:"PERMUT",300:"POISSON",301:"TDIST",302:"WEIBULL",303:"SUMXMY2",304:"SUMX2MY2",305:"SUMX2PY2",306:"CHITEST",307:"CORREL",308:"COVAR",309:"FORECAST",310:"FTEST",311:"INTERCEPT",312:"PEARSON",313:"RSQ",314:"STEYX",315:"SLOPE",316:"TTEST",317:"PROB",318:"DEVSQ",319:"GEOMEAN",320:"HARMEAN",321:"SUMSQ",322:"KURT",323:"SKEW",324:"ZTEST",325:"LARGE",326:"SMALL",327:"QUARTILE",328:"PERCENTILE",329:"PERCENTRANK",330:"MODE",331:"TRIMMEAN",332:"TINV",334:"MOVIE.COMMAND",335:"GET.MOVIE",336:"CONCATENATE",337:"POWER",338:"PIVOT.ADD.DATA",339:"GET.PIVOT.TABLE",340:"GET.PIVOT.FIELD",341:"GET.PIVOT.ITEM",342:"RADIANS",343:"DEGREES",344:"SUBTOTAL",345:"SUMIF",346:"COUNTIF",347:"COUNTBLANK",348:"SCENARIO.GET",349:"OPTIONS.LISTS.GET",350:"ISPMT",351:"DATEDIF",352:"DATESTRING",353:"NUMBERSTRING",354:"ROMAN",355:"OPEN.DIALOG",356:"SAVE.DIALOG",357:"VIEW.GET",358:"GETPIVOTDATA",359:"HYPERLINK",360:"PHONETIC",361:"AVERAGEA",362:"MAXA",363:"MINA",364:"STDEVPA",365:"VARPA",366:"STDEVA",367:"VARA",368:"BAHTTEXT",369:"THAIDAYOFWEEK",370:"THAIDIGIT",371:"THAIMONTHOFYEAR",372:"THAINUMSOUND",373:"THAINUMSTRING",374:"THAISTRINGLENGTH",375:"ISTHAIDIGIT",376:"ROUNDBAHTDOWN",377:"ROUNDBAHTUP",378:"THAIYEAR",379:"RTD",380:"CUBEVALUE",381:"CUBEMEMBER",382:"CUBEMEMBERPROPERTY",383:"CUBERANKEDMEMBER",384:"HEX2BIN",385:"HEX2DEC",386:"HEX2OCT",387:"DEC2BIN",388:"DEC2HEX",389:"DEC2OCT",390:"OCT2BIN",391:"OCT2HEX",392:"OCT2DEC",393:"BIN2DEC",394:"BIN2OCT",395:"BIN2HEX",396:"IMSUB",397:"IMDIV",398:"IMPOWER",399:"IMABS",400:"IMSQRT",401:"IMLN",402:"IMLOG2",403:"IMLOG10",404:"IMSIN",405:"IMCOS",406:"IMEXP",407:"IMARGUMENT",408:"IMCONJUGATE",409:"IMAGINARY",410:"IMREAL",411:"COMPLEX",412:"IMSUM",413:"IMPRODUCT",414:"SERIESSUM",415:"FACTDOUBLE",416:"SQRTPI",417:"QUOTIENT",418:"DELTA",419:"GESTEP",420:"ISEVEN",421:"ISODD",422:"MROUND",423:"ERF",424:"ERFC",425:"BESSELJ",426:"BESSELK",427:"BESSELY",428:"BESSELI",429:"XIRR",430:"XNPV",431:"PRICEMAT",432:"YIELDMAT",433:"INTRATE",434:"RECEIVED",435:"DISC",436:"PRICEDISC",437:"YIELDDISC",438:"TBILLEQ",439:"TBILLPRICE",440:"TBILLYIELD",441:"PRICE",442:"YIELD",443:"DOLLARDE",444:"DOLLARFR",445:"NOMINAL",446:"EFFECT",447:"CUMPRINC",448:"CUMIPMT",449:"EDATE",450:"EOMONTH",451:"YEARFRAC",452:"COUPDAYBS",453:"COUPDAYS",454:"COUPDAYSNC",455:"COUPNCD",456:"COUPNUM",457:"COUPPCD",458:"DURATION",459:"MDURATION",460:"ODDLPRICE",461:"ODDLYIELD",462:"ODDFPRICE",463:"ODDFYIELD",464:"RANDBETWEEN",465:"WEEKNUM",466:"AMORDEGRC",467:"AMORLINC",468:"CONVERT",724:"SHEETJS",469:"ACCRINT",470:"ACCRINTM",471:"WORKDAY",472:"NETWORKDAYS",473:"GCD",474:"MULTINOMIAL",475:"LCM",476:"FVSCHEDULE",477:"CUBEKPIMEMBER",478:"CUBESET",479:"CUBESETCOUNT",480:"IFERROR",481:"COUNTIFS",482:"SUMIFS",483:"AVERAGEIF",484:"AVERAGEIFS"},n$={2:1,3:1,10:0,15:1,16:1,17:1,18:1,19:0,20:1,21:1,22:1,23:1,24:1,25:1,26:1,27:2,30:2,31:3,32:1,33:1,34:0,35:0,38:1,39:2,40:3,41:3,42:3,43:3,44:3,45:3,47:3,48:2,53:1,61:3,63:0,65:3,66:3,67:1,68:1,69:1,70:1,71:1,72:1,73:1,74:0,75:1,76:1,77:1,79:2,80:2,83:1,85:0,86:1,89:0,90:1,94:0,95:0,97:2,98:1,99:1,101:3,102:3,105:1,106:1,108:2,111:1,112:1,113:1,114:1,117:2,118:1,119:4,121:1,126:1,127:1,128:1,129:1,130:1,131:1,133:1,134:1,135:1,136:2,137:2,138:2,140:1,141:1,142:3,143:4,144:4,161:1,162:1,163:1,164:1,165:2,172:1,175:2,176:2,177:3,178:2,179:1,184:1,186:1,189:3,190:1,195:3,196:3,197:1,198:1,199:3,201:1,207:4,210:3,211:1,212:2,213:2,214:1,215:1,225:0,229:1,230:1,231:1,232:1,233:1,234:1,235:3,244:1,247:4,252:2,257:1,261:1,271:1,273:4,274:2,275:2,276:2,277:3,278:3,279:1,280:3,281:3,282:3,283:1,284:1,285:2,286:4,287:3,288:2,289:4,290:3,291:3,292:3,293:4,294:1,295:3,296:1,297:3,298:1,299:2,300:3,301:3,302:4,303:2,304:2,305:2,306:2,307:2,308:2,309:3,310:2,311:2,312:2,313:2,314:2,315:2,316:4,325:2,326:2,327:2,328:2,331:2,332:2,337:2,342:1,343:1,346:2,347:1,350:4,351:3,352:1,353:2,360:1,368:1,369:1,370:1,371:1,372:1,373:1,374:1,375:1,376:1,377:1,378:1,382:3,385:1,392:1,393:1,396:2,397:2,398:2,399:1,400:1,401:1,402:1,403:1,404:1,405:1,406:1,407:1,408:1,409:1,410:1,414:4,415:1,416:1,417:2,420:1,421:1,422:2,424:1,425:2,426:2,427:2,428:2,430:3,438:3,439:3,440:3,443:2,444:2,445:2,446:2,447:6,448:6,449:2,450:2,464:2,468:3,476:2,479:1,480:2,65535:0};function nY(e){return"of:"==e.slice(0,3)&&(e=e.slice(3)),61==e.charCodeAt(0)&&61==(e=e.slice(1)).charCodeAt(0)&&(e=e.slice(1)),(e=(e=(e=e.replace(/COM\.MICROSOFT\./g,"")).replace(/\[((?:\.[A-Z]+[0-9]+)(?::\.[A-Z]+[0-9]+)?)\]/g,function(e,r){return r.replace(/\./g,"")})).replace(/\[.(#[A-Z]*[?!])\]/g,"$1")).replace(/[;~]/g,",").replace(/\|/g,";")}function nj(e){var r=e.split(":");return[r[0].split(".")[0],r[0].split(".")[1]+(r.length>1?":"+(r[1].split(".")[1]||r[1].split(".")[0]):"")]}var nK={},nX={};function nq(e,r){if(e){var t=[.7,.7,.75,.75,.3,.3];"xlml"==r&&(t=[1,1,1,1,.5,.5]),null==e.left&&(e.left=t[0]),null==e.right&&(e.right=t[1]),null==e.top&&(e.top=t[2]),null==e.bottom&&(e.bottom=t[3]),null==e.header&&(e.header=t[4]),null==e.footer&&(e.footer=t[5])}}function nJ(e,r,t,a,n,s){try{a.cellNF&&(e.z=eo[r])}catch(e){if(a.WTF)throw e}if("z"!==e.t||a.cellStyles){if("d"===e.t&&"string"==typeof e.v&&(e.v=eZ(e.v)),(!a||!1!==a.cellText)&&"z"!==e.t)try{if(null==eo[r]&&eP(eM[r]||"General",r),"e"===e.t)e.w=e.w||tV[e.v];else if(0===r)if("n"===e.t)(0|e.v)===e.v?e.w=e.v.toString(10):e.w=eT(e.v);else if("d"===e.t){var i=ez(e.v);(0|i)===i?e.w=i.toString(10):e.w=eT(i)}else{if(void 0===e.v)return"";e.w=eb(e.v,nX)}else"d"===e.t?e.w=eF(r,ez(e.v),nX):e.w=eF(r,e.v,nX)}catch(e){if(a.WTF)throw e}if(a.cellStyles&&null!=t)try{e.s=s.Fills[t],e.s.fgColor&&e.s.fgColor.theme&&!e.s.fgColor.rgb&&(e.s.fgColor.rgb=a2(n.themeElements.clrScheme[e.s.fgColor.theme].rgb,e.s.fgColor.tint||0),a.WTF&&(e.s.fgColor.raw_rgb=n.themeElements.clrScheme[e.s.fgColor.theme].rgb)),e.s.bgColor&&e.s.bgColor.theme&&(e.s.bgColor.rgb=a2(n.themeElements.clrScheme[e.s.bgColor.theme].rgb,e.s.bgColor.tint||0),a.WTF&&(e.s.bgColor.raw_rgb=n.themeElements.clrScheme[e.s.bgColor.theme].rgb))}catch(e){if(a.WTF&&s.Fills)throw e}}}var nZ=/<(?:\w:)?mergeCell ref="[A-Z0-9:]+"\s*[\/]?>/g,nQ=/<(?:\w+:)?sheetData[^>]*>([\s\S]*)<\/(?:\w+:)?sheetData>/,n1=/<(?:\w:)?hyperlink [^>]*>/mg,n0=/"(\w*:\w*)"/,n2=/<(?:\w:)?col\b[^>]*[\/]?>/g,n4=/<(?:\w:)?autoFilter[^>]*([\/]|>([\s\S]*)<\/(?:\w:)?autoFilter)>/g,n3=/<(?:\w:)?pageMargins[^>]*\/>/g,n5=/<(?:\w:)?sheetPr\b(?:[^>a-z][^>]*)?\/>/,n6=/<(?:\w:)?sheetPr[^>]*(?:[\/]|>([\s\S]*)<\/(?:\w:)?sheetPr)>/,n8=/<(?:\w:)?sheetViews[^>]*(?:[\/]|>([\s\S]*)<\/(?:\w:)?sheetViews)>/;function n7(e,r,t,a){var n=rh(e);t.Sheets[a]||(t.Sheets[a]={}),n.codeName&&(t.Sheets[a].CodeName=rm(ry(n.codeName)))}var n9=/<(?:\w:)?sheetView(?:[^>a-z][^>]*)?\/?>/,se=function(){var e=/<(?:\w+:)?c[ \/>]/,r=/<\/(?:\w+:)?row>/,t=/r=["']([^"']*)["']/,a=/<(?:\w+:)?is>([\S\s]*?)<\/(?:\w+:)?is>/,n=/ref=["']([^"']*)["']/,s=rR("v"),i=rR("f");return function(c,o,l,f,h,u){for(var d,p,m,g,v,T=0,b="",E=[],w=[],A=0,S=0,k=0,y="",C=0,R=0,O=0,x=0,_=Array.isArray(u.CellXf),I=[],N=[],D=Array.isArray(o),F=[],P={},L=!1,M=!!l.sheetStubs,U=c.split(r),B=0,H=U.length;B!=H;++B){var W=(b=U[B].trim()).length;if(0!==W){var V=0;r:for(T=0;T<W;++T)switch(b[T]){case">":if("/"!=b[T-1]){++T;break r}if(l&&l.cellStyles){if(C=null!=(p=rh(b.slice(V,T),!0)).r?parseInt(p.r,10):C+1,R=-1,l.sheetRows&&l.sheetRows<C)continue;P={},L=!1,p.ht&&(L=!0,P.hpt=parseFloat(p.ht),P.hpx=ne(P.hpt)),"1"==p.hidden&&(L=!0,P.hidden=!0),null!=p.outlineLevel&&(L=!0,P.level=+p.outlineLevel),L&&(F[C-1]=P)}break;case"<":V=T}if(V>=T)break;if(C=null!=(p=rh(b.slice(V,T),!0)).r?parseInt(p.r,10):C+1,R=-1,!l.sheetRows||!(l.sheetRows<C)){f.s.r>C-1&&(f.s.r=C-1),f.e.r<C-1&&(f.e.r=C-1),l&&l.cellStyles&&(P={},L=!1,p.ht&&(L=!0,P.hpt=parseFloat(p.ht),P.hpx=ne(P.hpt)),"1"==p.hidden&&(L=!0,P.hidden=!0),null!=p.outlineLevel&&(L=!0,P.level=+p.outlineLevel),L&&(F[C-1]=P)),E=b.slice(T).split(e);for(var G=0;G!=E.length&&"<"==E[G].trim().charAt(0);++G);for(T=0,E=E.slice(G);T!=E.length;++T)if(0!==(b=E[T].trim()).length){if(w=b.match(t),A=T,S=0,k=0,b="<c "+("<"==b.slice(0,1)?">":"")+b,null!=w&&2===w.length){for(S=0,A=0,y=w[1];S!=y.length&&!((k=y.charCodeAt(S)-64)<1)&&!(k>26);++S)A=26*A+k;R=--A}else++R;for(S=0;S!=b.length&&62!==b.charCodeAt(S);++S);if(++S,(p=rh(b.slice(0,S),!0)).r||(p.r=tb({r:C-1,c:R})),y=b.slice(S),d={t:""},null!=(w=y.match(s))&&""!==w[1]&&(d.v=rm(w[1])),l.cellFormula){if(null!=(w=y.match(i))&&""!==w[1]){if(d.f=rm(ry(w[1])).replace(/\r\n/g,"\n"),l.xlfn||(d.f=nb(d.f)),w[0].indexOf('t="array"')>-1)d.F=(y.match(n)||[])[1],d.F.indexOf(":")>-1&&I.push([tA(d.F),d.F]);else if(w[0].indexOf('t="shared"')>-1){g=rh(w[0]);var z=rm(ry(w[1]));l.xlfn||(z=nb(z)),N[parseInt(g.si,10)]=[g,z,p.r]}}else(w=y.match(/<f[^>]*\/>/))&&N[(g=rh(w[0])).si]&&(d.f=function(e,r,t){var a=tE(r).s,n=tT(t);return nT(e,{r:n.r-a.r,c:n.c-a.c})}(N[g.si][1],N[g.si][2],p.r));var Y=tT(p.r);for(S=0;S<I.length;++S)Y.r>=I[S][0].s.r&&Y.r<=I[S][0].e.r&&Y.c>=I[S][0].s.c&&Y.c<=I[S][0].e.c&&(d.F=I[S][1])}if(null==p.t&&void 0===d.v)if(d.f||d.F)d.v=0,d.t="n";else{if(!M)continue;d.t="z"}else d.t=p.t||"n";switch(f.s.c>R&&(f.s.c=R),f.e.c<R&&(f.e.c=R),d.t){case"n":if(""==d.v||null==d.v){if(!M)continue;d.t="z"}else d.v=parseFloat(d.v);break;case"s":if(void 0===d.v){if(!M)continue;d.t="z"}else m=nK[parseInt(d.v,10)],d.v=m.t,d.r=m.r,l.cellHTML&&(d.h=m.h);break;case"str":d.t="s",d.v=null!=d.v?ry(d.v):"",l.cellHTML&&(d.h=rT(d.v));break;case"inlineStr":w=y.match(a),d.t="s",null!=w&&(m=aG(w[1]))?(d.v=m.t,l.cellHTML&&(d.h=m.h)):d.v="";break;case"b":d.v=rE(d.v);break;case"d":l.cellDates?d.v=eZ(d.v,1):(d.v=ez(eZ(d.v,1)),d.t="n");break;case"e":l&&!1===l.cellText||(d.w=d.v),d.v=tG[d.v]}if(O=x=0,v=null,_&&void 0!==p.s&&null!=(v=u.CellXf[p.s])&&(null!=v.numFmtId&&(O=v.numFmtId),l.cellStyles&&null!=v.fillId&&(x=v.fillId)),nJ(d,O,x,l,h,u),l.cellDates&&_&&"n"==d.t&&eI(eo[O])&&(d.t="d",d.v=eK(d.v)),p.cm&&l.xlmeta){var j=(l.xlmeta.Cell||[])[p.cm-1];j&&"XLDAPR"==j.type&&(d.D=!0)}if(D){var K=tT(p.r);o[K.r]||(o[K.r]=[]),o[K.r][K.c]=d}else o[p.r]=d}}}}F.length>0&&(o["!rows"]=F)}}();function sr(e){return[t_(e),tF(e),"n"]}var st=["left","right","top","bottom","header","footer"],sa=[["allowRefreshQuery",!1,"bool"],["autoCompressPictures",!0,"bool"],["backupFile",!1,"bool"],["checkCompatibility",!1,"bool"],["CodeName",""],["date1904",!1,"bool"],["defaultThemeVersion",0,"int"],["filterPrivacy",!1,"bool"],["hidePivotFieldList",!1,"bool"],["promptedSolutions",!1,"bool"],["publishItems",!1,"bool"],["refreshAllConnections",!1,"bool"],["saveExternalLinkValues",!0,"bool"],["showBorderUnselectedTables",!0,"bool"],["showInkAnnotation",!0,"bool"],["showObjects","all"],["showPivotChartFilter",!1,"bool"],["updateLinks","userSet"]],sn=[["activeTab",0,"int"],["autoFilterDateGrouping",!0,"bool"],["firstSheet",0,"int"],["minimized",!1,"bool"],["showHorizontalScroll",!0,"bool"],["showSheetTabs",!0,"bool"],["showVerticalScroll",!0,"bool"],["tabRatio",600,"int"],["visibility","visible"]],ss=[],si=[["calcCompleted","true"],["calcMode","auto"],["calcOnSave","true"],["concurrentCalc","true"],["fullCalcOnLoad","false"],["fullPrecision","true"],["iterate","false"],["iterateCount","100"],["iterateDelta","0.001"],["refMode","A1"]];function sc(e,r){for(var t=0;t!=e.length;++t)for(var a=e[t],n=0;n!=r.length;++n){var s=r[n];if(null==a[s[0]])a[s[0]]=s[1];else switch(s[2]){case"bool":"string"==typeof a[s[0]]&&(a[s[0]]=rE(a[s[0]]));break;case"int":"string"==typeof a[s[0]]&&(a[s[0]]=parseInt(a[s[0]],10))}}}function so(e,r){for(var t=0;t!=r.length;++t){var a=r[t];if(null==e[a[0]])e[a[0]]=a[1];else switch(a[2]){case"bool":"string"==typeof e[a[0]]&&(e[a[0]]=rE(e[a[0]]));break;case"int":"string"==typeof e[a[0]]&&(e[a[0]]=parseInt(e[a[0]],10))}}}function sl(e){so(e.WBProps,sa),so(e.CalcPr,si),sc(e.WBView,sn),sc(e.Sheets,ss),nX.date1904=rE(e.WBProps.date1904)}var sf="][*?/\\".split(""),sh=/<\w+:workbook/;function su(e,r){var t={};return e.read_shift(4),t.ArchID=e.read_shift(4),e.l+=r-8,t}var sd=/([\w:]+)=((?:")([^"]*)(?:")|(?:')([^']*)(?:'))/g,sp=/([\w:]+)=((?:")(?:[^"]*)(?:")|(?:')(?:[^']*)(?:'))/;function sm(e,r){var t=e.split(/\s+/),a=[];if(r||(a[0]=t[0]),1===t.length)return a;var n,s,i,c=e.match(sd);if(c)for(i=0;i!=c.length;++i)-1===(s=(n=c[i].match(sp))[1].indexOf(":"))?a[n[1]]=n[2].slice(1,n[2].length-1):a["xmlns:"===n[1].slice(0,6)?"xmlns"+n[1].slice(6):n[1].slice(s+1)]=n[2].slice(1,n[2].length-1);return a}function sg(e,r){var s,i,c,o=r||{};eL();var l=M(rD(e));("binary"==o.type||"array"==o.type||"base64"==o.type)&&(l=void 0!==t?t.utils.decode(65001,P(l)):ry(l));var f=l.slice(0,1024).toLowerCase(),h=!1;if((1023&(f=f.replace(/".*?"/g,"")).indexOf(">"))>Math.min(1023&f.indexOf(","),1023&f.indexOf(";"))){var u=e1(o);return u.type="string",aL.to_workbook(l,u)}if(-1==f.indexOf("<?xml")&&["html","table","head","meta","script","style","div"].forEach(function(e){f.indexOf("<"+e)>=0&&(h=!0)}),h){var d=l,p=o,m=d.match(/<table[\s\S]*?>[\s\S]*?<\/table>/gi);if(!m||0==m.length)throw Error("Invalid HTML: could not find <table>");if(1==m.length)return ty(sC(m[0],p),p);var g=sY();return m.forEach(function(e,r){sj(g,sC(e,p),"Sheet"+(r+1))}),g}n={"General Number":"General","General Date":eo[22],"Long Date":"dddd, mmmm dd, yyyy","Medium Date":eo[15],"Short Date":eo[14],"Long Time":eo[19],"Medium Time":eo[18],"Short Time":eo[20],Currency:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',Fixed:eo[2],Standard:eo[4],Percent:eo[10],Scientific:eo[11],"Yes/No":'"Yes";"Yes";"No";@',"True/False":'"True";"True";"False";@',"On/Off":'"Yes";"Yes";"No";@'};var v,T,b,E=[],w={},A=[],S=o.dense?[]:{},k="",y={},C={},R=sm('<Data ss:Type="String">'),O=0,x=0,_=0,I={s:{r:2e6,c:2e6},e:{r:0,c:0}},N={},D={},F="",L=0,U=[],B={},H={},W=0,V=[],G=[],z={},Y=[],j=!1,K=[],X=[],q={},J=0,Z=0,Q={Sheets:[],WBProps:{date1904:!1}},ee={};rF.lastIndex=0,l=l.replace(/<!--([\s\S]*?)-->/mg,"");for(var er="";v=rF.exec(l);)switch(v[3]=(er=v[3]).toLowerCase()){case"data":if("data"==er){if("/"===v[1]){if((T=E.pop())[0]!==v[3])throw Error("Bad state: "+T.join("|"))}else"/"!==v[0].charAt(v[0].length-2)&&E.push([v[3],!0]);break}if(E[E.length-1][1])break;"/"===v[1]?function(e,r,t,a,s,i,c,o,l,f){var h="General",u=a.StyleID,d={};f=f||{};var p=[],m=0;for(void 0===u&&o&&(u=o.StyleID),void 0===u&&c&&(u=c.StyleID);void 0!==i[u]&&(i[u].nf&&(h=i[u].nf),i[u].Interior&&p.push(i[u].Interior),i[u].Parent);)u=i[u].Parent;switch(t.Type){case"Boolean":a.t="b",a.v=rE(e);break;case"String":a.t="s",a.r=rb(rm(e)),a.v=e.indexOf("<")>-1?rm(r||e).replace(/<.*?>/g,""):a.r;break;case"DateTime":"Z"!=e.slice(-1)&&(e+="Z"),a.v=(eZ(e)-new Date(Date.UTC(1899,11,30)))/864e5,a.v!=a.v?a.v=rm(e):a.v<60&&(a.v=a.v-1),h&&"General"!=h||(h="yyyy-mm-dd");case"Number":void 0===a.v&&(a.v=+e),a.t||(a.t="n");break;case"Error":a.t="e",a.v=tG[e],!1!==f.cellText&&(a.w=e);break;default:""==e&&""==r?a.t="z":(a.t="s",a.v=rb(r||e))}if(!function(e,r,t){if("z"!==e.t){if(!t||!1!==t.cellText)try{if("e"===e.t)e.w=e.w||tV[e.v];else if("General"===r)"n"===e.t?(0|e.v)===e.v?e.w=e.v.toString(10):e.w=eT(e.v):e.w=eb(e.v);else{var a,s,i;a=r||"General",s=e.v,i=n[a]||rm(a),e.w="General"===i?eb(s):eF(i,s)}}catch(e){if(t.WTF)throw e}try{var c=n[r]||r||"General";if(t.cellNF&&(e.z=c),t.cellDates&&"n"==e.t&&eI(c)){var o=eu(e.v);o&&(e.t="d",e.v=new Date(o.y,o.m-1,o.d,o.H,o.M,o.S,o.u))}}catch(e){if(t.WTF)throw e}}}(a,h,f),!1!==f.cellFormula)if(a.Formula){var g=rm(a.Formula);61==g.charCodeAt(0)&&(g=g.slice(1)),a.f=nm(g,s),delete a.Formula,"RC"==a.ArrayRange?a.F=nm("RC:RC",s):a.ArrayRange&&(a.F=nm(a.ArrayRange,s),l.push([tA(a.F),a.F]))}else for(m=0;m<l.length;++m)s.r>=l[m][0].s.r&&s.r<=l[m][0].e.r&&s.c>=l[m][0].s.c&&s.c<=l[m][0].e.c&&(a.F=l[m][1]);f.cellStyles&&(p.forEach(function(e){!d.patternType&&e.patternType&&(d.patternType=e.patternType)}),a.s=d),void 0!==a.StyleID&&(a.ixfe=a.StyleID)}(l.slice(O,v.index),F,R,"comment"==E[E.length-1][0]?z:y,{c:x,r:_},N,Y[x],C,K,o):(F="",R=sm(v[0]),O=v.index+v[0].length);break;case"cell":if("/"===v[1])if(G.length>0&&(y.c=G),(!o.sheetRows||o.sheetRows>_)&&void 0!==y.v&&(o.dense?(S[_]||(S[_]=[]),S[_][x]=y):S[tv(x)+tm(_)]=y),y.HRef&&(y.l={Target:rm(y.HRef)},y.HRefScreenTip&&(y.l.Tooltip=y.HRefScreenTip),delete y.HRef,delete y.HRefScreenTip),(y.MergeAcross||y.MergeDown)&&(J=x+(0|parseInt(y.MergeAcross,10)),Z=_+(0|parseInt(y.MergeDown,10)),U.push({s:{c:x,r:_},e:{c:J,r:Z}})),o.sheetStubs)if(y.MergeAcross||y.MergeDown){for(var et=x;et<=J;++et)for(var ea=_;ea<=Z;++ea)(et>x||ea>_)&&(o.dense?(S[ea]||(S[ea]=[]),S[ea][et]={t:"z"}):S[tv(et)+tm(ea)]={t:"z"});x=J+1}else++x;else y.MergeAcross?x=J+1:++x;else(y=function(e){var r=e.split(/\s+/),t={};if(1===r.length)return t;var a,n,s,i=e.match(sd);if(i)for(s=0;s!=i.length;++s)-1===(n=(a=i[s].match(sp))[1].indexOf(":"))?t[a[1]]=a[2].slice(1,a[2].length-1):t["xmlns:"===a[1].slice(0,6)?"xmlns"+a[1].slice(6):a[1].slice(n+1)]=a[2].slice(1,a[2].length-1);return t}(v[0])).Index&&(x=y.Index-1),x<I.s.c&&(I.s.c=x),x>I.e.c&&(I.e.c=x),"/>"===v[0].slice(-2)&&++x,G=[];break;case"row":"/"===v[1]||"/>"===v[0].slice(-2)?(_<I.s.r&&(I.s.r=_),_>I.e.r&&(I.e.r=_),"/>"===v[0].slice(-2)&&(C=sm(v[0])).Index&&(_=C.Index-1),x=0,++_):((C=sm(v[0])).Index&&(_=C.Index-1),q={},("0"==C.AutoFitHeight||C.Height)&&(q.hpx=parseInt(C.Height,10),q.hpt=96*q.hpx/96,X[_]=q),"1"==C.Hidden&&(q.hidden=!0,X[_]=q));break;case"worksheet":if("/"===v[1]){if((T=E.pop())[0]!==v[3])throw Error("Bad state: "+T.join("|"));A.push(k),I.s.r<=I.e.r&&I.s.c<=I.e.c&&(S["!ref"]=tw(I),o.sheetRows&&o.sheetRows<=I.e.r&&(S["!fullref"]=S["!ref"],I.e.r=o.sheetRows-1,S["!ref"]=tw(I))),U.length&&(S["!merges"]=U),Y.length>0&&(S["!cols"]=Y),X.length>0&&(S["!rows"]=X),w[k]=S}else I={s:{r:2e6,c:2e6},e:{r:0,c:0}},_=x=0,E.push([v[3],!1]),k=rm((T=sm(v[0])).Name),S=o.dense?[]:{},U=[],K=[],X=[],ee={name:k,Hidden:0},Q.Sheets.push(ee);break;case"table":if("/"===v[1]){if((T=E.pop())[0]!==v[3])throw Error("Bad state: "+T.join("|"))}else"/>"==v[0].slice(-2)||(E.push([v[3],!1]),Y=[],j=!1);break;case"style":"/"===v[1]?function(e,r,t){if(t.cellStyles&&r.Interior){var a=r.Interior;a.Pattern&&(a.patternType=nr[a.Pattern]||a.Pattern)}e[r.ID]=r}(N,D,o):D=sm(v[0]);break;case"numberformat":D.nf=rm(sm(v[0]).Format||"General"),n[D.nf]&&(D.nf=n[D.nf]);for(var en=0;392!=en&&eo[en]!=D.nf;++en);if(392==en){for(en=57;392!=en;++en)if(null==eo[en]){eP(D.nf,en);break}}break;case"column":if("table"!==E[E.length-1][0])break;if((b=sm(v[0])).Hidden&&(b.hidden=!0,delete b.Hidden),b.Width&&(b.wpx=parseInt(b.Width,10)),!j&&b.wpx>10){j=!0,a4=6;for(var es=0;es<Y.length;++es)Y[es]&&a9(Y[es])}j&&a9(b),Y[b.Index-1||Y.length]=b;for(var ei=0;ei<+b.Span;++ei)Y[Y.length]=e1(b);break;case"namedrange":if("/"===v[1])break;Q.Names||(Q.Names=[]);var ec=rh(v[0]),el={Name:ec.Name,Ref:nm(ec.RefersTo.slice(1),{r:0,c:0})};Q.Sheets.length>0&&(el.Sheet=Q.Sheets.length-1),Q.Names.push(el);break;case"namedcell":case"b":case"i":case"u":case"s":case"em":case"h2":case"h3":case"sub":case"sup":case"span":case"alignment":case"borders":case"border":case"protection":case"paragraphs":case"name":case"pixelsperinch":case"null":break;case"font":"/>"===v[0].slice(-2)||("/"===v[1]?F+=l.slice(L,v.index):L=v.index+v[0].length);break;case"interior":if(!o.cellStyles)break;D.Interior=sm(v[0]);break;case"author":case"title":case"description":case"created":case"keywords":case"subject":case"category":case"company":case"lastauthor":case"lastsaved":case"lastprinted":case"version":case"revision":case"totaltime":case"hyperlinkbase":case"manager":case"contentstatus":case"identifier":case"language":case"appname":"/>"===v[0].slice(-2)||("/"===v[1]?(s=er,i=l.slice(W,v.index),a||(a=eV(t1)),B[s=a[s]||s]=i):W=v.index+v[0].length);break;case"styles":case"workbook":if("/"===v[1]){if((T=E.pop())[0]!==v[3])throw Error("Bad state: "+T.join("|"))}else E.push([v[3],!1]);break;case"comment":if("/"===v[1]){if((T=E.pop())[0]!==v[3])throw Error("Bad state: "+T.join("|"));(c=z).t=c.v||"",c.t=c.t.replace(/\r\n/g,"\n").replace(/\r/g,"\n"),c.v=c.w=c.ixfe=void 0,G.push(z)}else E.push([v[3],!1]),z={a:(T=sm(v[0])).Author};break;case"autofilter":if("/"===v[1]){if((T=E.pop())[0]!==v[3])throw Error("Bad state: "+T.join("|"))}else if("/"!==v[0].charAt(v[0].length-2)){var ef=sm(v[0]);S["!autofilter"]={ref:nm(ef.Range).replace(/\$/g,"")},E.push([v[3],!0])}break;case"datavalidation":case"componentoptions":case"documentproperties":case"customdocumentproperties":case"officedocumentsettings":case"pivottable":case"pivotcache":case"names":case"mapinfo":case"pagebreaks":case"querytable":case"sorting":case"schema":case"conditionalformatting":case"smarttagtype":case"smarttags":case"excelworkbook":case"workbookoptions":case"worksheetoptions":if("/"===v[1]){if((T=E.pop())[0]!==v[3])throw Error("Bad state: "+T.join("|"))}else"/"!==v[0].charAt(v[0].length-2)&&E.push([v[3],!0]);break;default:if(0==E.length&&"document"==v[3]||0==E.length&&"uof"==v[3])return sO(l,o);var eh=!0;switch(E[E.length-1][0]){case"officedocumentsettings":switch(v[3]){case"allowpng":case"removepersonalinformation":case"downloadcomponents":case"locationofcomponents":case"colors":case"color":case"index":case"rgb":case"targetscreensize":case"readonlyrecommended":break;default:eh=!1}break;case"componentoptions":switch(v[3]){case"toolbar":case"hideofficelogo":case"spreadsheetautofit":case"label":case"caption":case"maxheight":case"maxwidth":case"nextsheetnumber":break;default:eh=!1}break;case"excelworkbook":switch(v[3]){case"date1904":Q.WBProps.date1904=!0;break;case"windowheight":case"windowwidth":case"windowtopx":case"windowtopy":case"tabratio":case"protectstructure":case"protectwindow":case"protectwindows":case"activesheet":case"displayinknotes":case"firstvisiblesheet":case"supbook":case"sheetname":case"sheetindex":case"sheetindexfirst":case"sheetindexlast":case"dll":case"acceptlabelsinformulas":case"donotsavelinkvalues":case"iteration":case"maxiterations":case"maxchange":case"path":case"xct":case"count":case"selectedsheets":case"calculation":case"uncalced":case"startupprompt":case"crn":case"externname":case"formula":case"colfirst":case"collast":case"wantadvise":case"boolean":case"error":case"text":case"ole":case"noautorecover":case"publishobjects":case"donotcalculatebeforesave":case"number":case"refmoder1c1":case"embedsavesmarttags":break;default:eh=!1}break;case"workbookoptions":switch(v[3]){case"owcversion":case"height":case"width":break;default:eh=!1}break;case"worksheetoptions":switch(v[3]){case"visible":if("/>"===v[0].slice(-2));else if("/"===v[1])switch(l.slice(W,v.index)){case"SheetHidden":ee.Hidden=1;break;case"SheetVeryHidden":ee.Hidden=2}else W=v.index+v[0].length;break;case"header":S["!margins"]||nq(S["!margins"]={},"xlml"),isNaN(+rh(v[0]).Margin)||(S["!margins"].header=+rh(v[0]).Margin);break;case"footer":S["!margins"]||nq(S["!margins"]={},"xlml"),isNaN(+rh(v[0]).Margin)||(S["!margins"].footer=+rh(v[0]).Margin);break;case"pagemargins":var ed=rh(v[0]);S["!margins"]||nq(S["!margins"]={},"xlml"),isNaN(+ed.Top)||(S["!margins"].top=+ed.Top),isNaN(+ed.Left)||(S["!margins"].left=+ed.Left),isNaN(+ed.Right)||(S["!margins"].right=+ed.Right),isNaN(+ed.Bottom)||(S["!margins"].bottom=+ed.Bottom);break;case"displayrighttoleft":Q.Views||(Q.Views=[]),Q.Views[0]||(Q.Views[0]={}),Q.Views[0].RTL=!0;break;case"freezepanes":case"frozennosplit":case"splithorizontal":case"splitvertical":case"donotdisplaygridlines":case"activerow":case"activecol":case"toprowbottompane":case"leftcolumnrightpane":case"unsynced":case"print":case"printerrors":case"panes":case"scale":case"pane":case"number":case"layout":case"pagesetup":case"selected":case"protectobjects":case"enableselection":case"protectscenarios":case"validprinterinfo":case"horizontalresolution":case"verticalresolution":case"numberofcopies":case"activepane":case"toprowvisible":case"leftcolumnvisible":case"fittopage":case"rangeselection":case"papersizeindex":case"pagelayoutzoom":case"pagebreakzoom":case"filteron":case"fitwidth":case"fitheight":case"commentslayout":case"zoom":case"lefttoright":case"gridlines":case"allowsort":case"allowfilter":case"allowinsertrows":case"allowdeleterows":case"allowinsertcols":case"allowdeletecols":case"allowinserthyperlinks":case"allowformatcells":case"allowsizecols":case"allowsizerows":case"tabcolorindex":case"donotdisplayheadings":case"showpagelayoutzoom":case"blackandwhite":case"donotdisplayzeros":case"displaypagebreak":case"rowcolheadings":case"donotdisplayoutline":case"noorientation":case"allowusepivottables":case"zeroheight":case"viewablerange":case"selection":case"protectcontents":break;case"nosummaryrowsbelowdetail":S["!outline"]||(S["!outline"]={}),S["!outline"].above=!0;break;case"nosummarycolumnsrightdetail":S["!outline"]||(S["!outline"]={}),S["!outline"].left=!0;break;default:eh=!1}break;case"pivottable":case"pivotcache":switch(v[3]){case"immediateitemsondrop":case"showpagemultipleitemlabel":case"compactrowindent":case"location":case"pivotfield":case"orientation":case"layoutform":case"layoutsubtotallocation":case"layoutcompactrow":case"position":case"pivotitem":case"datatype":case"datafield":case"sourcename":case"parentfield":case"ptlineitems":case"ptlineitem":case"countofsameitems":case"item":case"itemtype":case"ptsource":case"cacheindex":case"consolidationreference":case"filename":case"reference":case"nocolumngrand":case"norowgrand":case"blanklineafteritems":case"hidden":case"subtotal":case"basefield":case"mapchilditems":case"function":case"refreshonfileopen":case"printsettitles":case"mergelabels":case"defaultversion":case"refreshname":case"refreshdate":case"refreshdatecopy":case"versionlastrefresh":case"versionlastupdate":case"versionupdateablemin":case"versionrefreshablemin":case"calculation":break;default:eh=!1}break;case"pagebreaks":switch(v[3]){case"colbreaks":case"colbreak":case"rowbreaks":case"rowbreak":case"colstart":case"colend":case"rowend":break;default:eh=!1}break;case"autofilter":switch(v[3]){case"autofiltercolumn":case"autofiltercondition":case"autofilterand":case"autofilteror":break;default:eh=!1}break;case"querytable":switch(v[3]){case"id":case"autoformatfont":case"autoformatpattern":case"querysource":case"querytype":case"enableredirections":case"refreshedinxl9":case"urlstring":case"htmltables":case"connection":case"commandtext":case"refreshinfo":case"notitles":case"nextid":case"columninfo":case"overwritecells":case"donotpromptforfile":case"textwizardsettings":case"source":case"number":case"decimal":case"thousandseparator":case"trailingminusnumbers":case"formatsettings":case"fieldtype":case"delimiters":case"tab":case"comma":case"autoformatname":case"versionlastedit":case"versionlastrefresh":break;default:eh=!1}break;case"datavalidation":switch(v[3]){case"range":case"type":case"min":case"max":case"sort":case"descending":case"order":case"casesensitive":case"value":case"errorstyle":case"errormessage":case"errortitle":case"inputmessage":case"inputtitle":case"combohide":case"inputhide":case"condition":case"qualifier":case"useblank":case"value1":case"value2":case"format":case"cellrangelist":break;default:eh=!1}break;case"sorting":case"conditionalformatting":switch(v[3]){case"range":case"type":case"min":case"max":case"sort":case"descending":case"order":case"casesensitive":case"value":case"errorstyle":case"errormessage":case"errortitle":case"cellrangelist":case"inputmessage":case"inputtitle":case"combohide":case"inputhide":case"condition":case"qualifier":case"useblank":case"value1":case"value2":case"format":break;default:eh=!1}break;case"mapinfo":case"schema":case"data":switch(v[3]){case"map":case"entry":case"range":case"xpath":case"field":case"xsdtype":case"filteron":case"aggregate":case"elementtype":case"attributetype":case"schema":case"element":case"complextype":case"datatype":case"all":case"attribute":case"extends":case"row":break;default:eh=!1}break;case"smarttags":break;default:eh=!1}if(eh||v[3].match(/!\[CDATA/))break;if(!E[E.length-1][1])throw"Unrecognized tag: "+v[3]+"|"+E.join("|");if("customdocumentproperties"===E[E.length-1][0]){"/>"===v[0].slice(-2)||("/"===v[1]?function(e,r,t,a){var n=a;switch((t[0].match(/dt:dt="([\w.]+)"/)||["",""])[1]){case"boolean":n=rE(a);break;case"i2":case"int":n=parseInt(a,10);break;case"r4":case"float":n=parseFloat(a);break;case"date":case"dateTime.tz":n=eZ(a);break;case"i8":case"string":case"fixed":case"uuid":case"bin.base64":break;default:throw Error("bad custprop:"+t[0])}e[rm(r)]=n}(H,er,V,l.slice(W,v.index)):(V=v,W=v.index+v[0].length));break}if(o.WTF)throw"Unrecognized tag: "+v[3]+"|"+E.join("|")}var ep={};return o.bookSheets||o.bookProps||(ep.Sheets=w),ep.SheetNames=A,ep.Workbook=Q,ep.SSF=e1(eo),ep.Props=B,ep.Custprops=H,ep}function sv(e,r){switch(sH(r=r||{}),r.type||"base64"){case"base64":return sg(V(e),r);case"binary":case"buffer":case"file":return sg(e,r);case"array":return sg(X(e),r)}}var sT=[60,1084,2066,2165,2175];function sb(e,r,t){if("z"!==e.t&&e.XF){var a=0;try{a=e.z||e.XF.numFmtId||0,r.cellNF&&(e.z=eo[a])}catch(e){if(r.WTF)throw e}if(!r||!1!==r.cellText)try{"e"===e.t?e.w=e.w||tV[e.v]:0===a||"General"==a?"n"===e.t?(0|e.v)===e.v?e.w=e.v.toString(10):e.w=eT(e.v):e.w=eb(e.v):e.w=eF(a,e.v,{date1904:!!t,dateNF:r&&r.dateNF})}catch(e){if(r.WTF)throw e}if(r.cellDates&&a&&"n"==e.t&&eI(eo[a]||String(a))){var n=eu(e.v);n&&(e.t="d",e.v=new Date(n.y,n.m-1,n.d,n.H,n.M,n.S,n.u))}}}function sE(e,r,t){return{v:e,ixfe:r,t:t}}var sw={SI:"e0859ff2f94f6810ab9108002b27b3d9",DSI:"02d5cdd59c2e1b10939708002b2cf9ae"};function sA(e,r){if(r||(r={}),sH(r),F(),r.codepage&&N(r.codepage),e.FullPaths){if(eH.find(e,"/encryption"))throw Error("File is password-protected");n=eH.find(e,"!CompObj"),s=eH.find(e,"/Workbook")||eH.find(e,"/Book")}else{switch(r.type){case"base64":e=K(V(e));break;case"binary":e=K(e);break;case"buffer":break;case"array":Array.isArray(e)||(e=Array.prototype.slice.call(e))}ts(e,0),s={content:e}}if(n&&function(e){var r={},t=e.content;if(t.l=28,r.AnsiUserType=t.read_shift(0,"lpstr-ansi"),r.AnsiClipboardFormat=tP(t,1),t.length-t.l<=4)return;var a=t.read_shift(4);if(0!=a&&!(a>40)&&(t.l-=4,r.Reserved1=t.read_shift(0,"lpstr-ansi"),!(t.length-t.l<=4)&&0x71b239f4===(a=t.read_shift(4)))&&(r.UnicodeClipboardFormat=tP(t,2),0!=(a=t.read_shift(4))&&!(a>40)))t.l-=4,r.Reserved2=t.read_shift(0,"lpwstr")}(n),r.bookProps&&!r.bookSheets)i={};else{var t,a,n,s,i,c,o=G?"buffer":"array";if(s&&s.content)i=function(e,r){var t,a,n,s,i={opts:{}},c={},o,l,f,h,u,d=r.dense?[]:{},p={},m={},g=null,v=[],T="",b={},E="",w={},A=[],S=[],k=[],y={Sheets:[],WBProps:{date1904:!1},Views:[{}]},C={},R=function(e){return e<8?tW[e]:e<64&&k[e-8]||tW[e]},O=function(e,r,t){var a,n=r.XF.data;n&&n.patternType&&t&&t.cellStyles&&(r.s={},r.s.patternType=n.patternType,(a=a0(R(n.icvFore)))&&(r.s.fgColor={rgb:a}),(a=a0(R(n.icvBack)))&&(r.s.bgColor={rgb:a}))},x=function(e,r,t){if(!(B>1)&&(!t.sheetRows||!(e.r>=t.sheetRows))){if(t.cellStyles&&r.XF&&r.XF.data&&O(e,r,t),delete r.ixfe,delete r.XF,o=e,E=tb(e),m&&m.s&&m.e||(m={s:{r:0,c:0},e:{r:0,c:0}}),e.r<m.s.r&&(m.s.r=e.r),e.c<m.s.c&&(m.s.c=e.c),e.r+1>m.e.r&&(m.e.r=e.r+1),e.c+1>m.e.c&&(m.e.c=e.c+1),t.cellFormula&&r.f){for(var a=0;a<A.length;++a)if(!(A[a][0].s.c>e.c)&&!(A[a][0].s.r>e.r)&&!(A[a][0].e.c<e.c)&&!(A[a][0].e.r<e.r)){r.F=tw(A[a][0]),(A[a][0].s.c!=e.c||A[a][0].s.r!=e.r)&&delete r.f,r.f&&(r.f=""+nH(A[a][1],m,e,M,_));break}}t.dense?(d[e.r]||(d[e.r]=[]),d[e.r][e.c]=r):d[E]=r}},_={enc:!1,sbcch:0,snames:[],sharedf:w,arrayf:A,rrtabid:[],lastuser:"",biff:8,codepage:0,winlocked:0,cellStyles:!!r&&!!r.cellStyles,WTF:!!r&&!!r.wtf};r.password&&(_.password=r.password);var I=[],N=[],F=[],P=[],L=!1,M=[];M.SheetNames=_.snames,M.sharedf=_.sharedf,M.arrayf=_.arrayf,M.names=[],M.XTI=[];var U=0,B=0,H=0,W=[],V=[];_.codepage=1200,D(1200);for(var G=!1;e.l<e.length-1;){var z=e.l,Y=e.read_shift(2);if(0===Y&&10===U)break;var j=e.l===e.length?0:e.read_shift(2),K=sk[Y];if(K&&K.f){if(r.bookSheets&&133===U&&133!==Y)break;if(U=Y,2===K.r||12==K.r){var X=e.read_shift(2);if(j-=2,!_.enc&&X!==Y&&((255&X)<<8|X>>8)!==Y)throw Error("rt mismatch: "+X+"!="+Y);12==K.r&&(e.l+=10,j-=10)}var q={};if(q=10===Y?K.f(e,j,_):function(e,r,t,a,n){var s=a,i=[],c=t.slice(t.l,t.l+s);if(n&&n.enc&&n.enc.insitu&&c.length>0)switch(e){case 9:case 521:case 1033:case 2057:case 47:case 405:case 225:case 406:case 312:case 404:case 10:case 133:break;default:n.enc.insitu(c)}i.push(c),t.l+=s;for(var o=r5(t,t.l),l=sk[o],f=0;null!=l&&sT.indexOf(o)>-1;)s=r5(t,t.l+2),f=t.l+4,2066==o?f+=4:(2165==o||2175==o)&&(f+=12),c=t.slice(f,t.l+4+s),i.push(c),t.l+=4+s,l=sk[o=r5(t,t.l)];var h=J(i);ts(h,0);var u=0;h.lens=[];for(var d=0;d<i.length;++d)h.lens.push(u),u+=i[d].length;if(h.length<a)throw"XLS Record 0x"+e.toString(16)+" Truncated: "+h.length+" < "+a;return r.f(h,h.length,n)}(Y,K,e,j,_),0==B&&-1===[9,521,1033,2057].indexOf(U))continue;switch(Y){case 34:i.opts.Date1904=y.WBProps.date1904=q;break;case 134:i.opts.WriteProtect=!0;break;case 47:if(_.enc||(e.l=0),_.enc=q,!r.password)throw Error("File is password-protected");if(null==q.valid)throw Error("Encryption scheme unsupported");if(!q.valid)throw Error("Password is incorrect");break;case 92:_.lastuser=q;break;case 66:var Z=Number(q);switch(Z){case 21010:Z=1200;break;case 32768:Z=1e4;break;case 32769:Z=1252}D(_.codepage=Z),G=!0;break;case 317:_.rrtabid=q;break;case 25:_.winlocked=q;break;case 439:i.opts.RefreshAll=q;break;case 12:i.opts.CalcCount=q;break;case 16:i.opts.CalcDelta=q;break;case 17:i.opts.CalcIter=q;break;case 13:i.opts.CalcMode=q;break;case 14:i.opts.CalcPrecision=q;break;case 95:i.opts.CalcSaveRecalc=q;break;case 15:_.CalcRefMode=q;break;case 2211:i.opts.FullCalc=q;break;case 129:q.fDialog&&(d["!type"]="dialog"),q.fBelow||((d["!outline"]||(d["!outline"]={})).above=!0),q.fRight||((d["!outline"]||(d["!outline"]={})).left=!0);break;case 224:S.push(q);break;case 430:M.push([q]),M[M.length-1].XTI=[];break;case 35:case 547:M[M.length-1].push(q);break;case 24:case 536:s={Name:q.Name,Ref:nH(q.rgce,m,null,M,_)},q.itab>0&&(s.Sheet=q.itab-1),M.names.push(s),M[0]||(M[0]=[],M[0].XTI=[]),M[M.length-1].push(q),"_xlnm._FilterDatabase"==q.Name&&q.itab>0&&q.rgce&&q.rgce[0]&&q.rgce[0][0]&&"PtgArea3d"==q.rgce[0][0][0]&&(V[q.itab-1]={ref:tw(q.rgce[0][0][1][2])});break;case 22:_.ExternCount=q;break;case 23:0==M.length&&(M[0]=[],M[0].XTI=[]),M[M.length-1].XTI=M[M.length-1].XTI.concat(q),M.XTI=M.XTI.concat(q);break;case 2196:if(_.biff<8)break;null!=s&&(s.Comment=q[1]);break;case 18:d["!protect"]=q;break;case 19:0!==q&&_.WTF&&console.error("Password verifier: "+q);break;case 133:p[q.pos]=q,_.snames.push(q.name);break;case 10:if(--B)break;if(m.e){if(m.e.r>0&&m.e.c>0){if(m.e.r--,m.e.c--,d["!ref"]=tw(m),r.sheetRows&&r.sheetRows<=m.e.r){var Q=m.e.r;m.e.r=r.sheetRows-1,d["!fullref"]=d["!ref"],d["!ref"]=tw(m),m.e.r=Q}m.e.r++,m.e.c++}I.length>0&&(d["!merges"]=I),N.length>0&&(d["!objects"]=N),F.length>0&&(d["!cols"]=F),P.length>0&&(d["!rows"]=P),y.Sheets.push(C)}""===T?b=d:c[T]=d,d=r.dense?[]:{};break;case 9:case 521:case 1033:case 2057:if(8===_.biff&&(_.biff=({9:2,521:3,1033:4})[Y]||({512:2,768:3,1024:4,1280:5,1536:8,2:2,7:2})[q.BIFFVer]||8),_.biffguess=0==q.BIFFVer,0==q.BIFFVer&&4096==q.dt&&(_.biff=5,G=!0,D(_.codepage=28591)),8==_.biff&&0==q.BIFFVer&&16==q.dt&&(_.biff=2),B++)break;if(d=r.dense?[]:{},_.biff<8&&!G&&(G=!0,D(_.codepage=r.codepage||1252)),_.biff<5||0==q.BIFFVer&&4096==q.dt){""===T&&(T="Sheet1"),m={s:{r:0,c:0},e:{r:0,c:0}};var ee={pos:e.l-j,name:T};p[ee.pos]=ee,_.snames.push(T)}else T=(p[z]||{name:""}).name;32==q.dt&&(d["!type"]="chart"),64==q.dt&&(d["!type"]="macro"),I=[],N=[],_.arrayf=A=[],F=[],P=[],L=!1,C={Hidden:(p[z]||{hs:0}).hs,name:T};break;case 515:case 3:case 2:"chart"==d["!type"]&&(r.dense?(d[q.r]||[])[q.c]:d[tb({c:q.c,r:q.r})])&&++q.c,t={ixfe:q.ixfe,XF:S[q.ixfe]||{},v:q.val,t:"n"},H>0&&(t.z=W[t.ixfe>>8&63]),sb(t,r,i.opts.Date1904),x({c:q.c,r:q.r},t,r);break;case 5:case 517:t={ixfe:q.ixfe,XF:S[q.ixfe],v:q.val,t:q.t},H>0&&(t.z=W[t.ixfe>>8&63]),sb(t,r,i.opts.Date1904),x({c:q.c,r:q.r},t,r);break;case 638:t={ixfe:q.ixfe,XF:S[q.ixfe],v:q.rknum,t:"n"},H>0&&(t.z=W[t.ixfe>>8&63]),sb(t,r,i.opts.Date1904),x({c:q.c,r:q.r},t,r);break;case 189:for(var er=q.c;er<=q.C;++er){var et=q.rkrec[er-q.c][0];t={ixfe:et,XF:S[et],v:q.rkrec[er-q.c][1],t:"n"},H>0&&(t.z=W[t.ixfe>>8&63]),sb(t,r,i.opts.Date1904),x({c:er,r:q.r},t,r)}break;case 6:case 518:case 1030:if("String"==q.val){g=q;break}if((t=sE(q.val,q.cell.ixfe,q.tt)).XF=S[t.ixfe],r.cellFormula){var ea=q.formula;if(ea&&ea[0]&&ea[0][0]&&"PtgExp"==ea[0][0][0]){var en=ea[0][0][1][0],es=ea[0][0][1][1],ei=tb({r:en,c:es});w[ei]?t.f=""+nH(q.formula,m,q.cell,M,_):t.F=((r.dense?(d[en]||[])[es]:d[ei])||{}).F}else t.f=""+nH(q.formula,m,q.cell,M,_)}H>0&&(t.z=W[t.ixfe>>8&63]),sb(t,r,i.opts.Date1904),x(q.cell,t,r),g=q;break;case 7:case 519:if(g)g.val=q,(t=sE(q,g.cell.ixfe,"s")).XF=S[t.ixfe],r.cellFormula&&(t.f=""+nH(g.formula,m,g.cell,M,_)),H>0&&(t.z=W[t.ixfe>>8&63]),sb(t,r,i.opts.Date1904),x(g.cell,t,r),g=null;else throw Error("String record expects Formula");break;case 33:case 545:A.push(q);var ec=tb(q[0].s);if(l=r.dense?(d[q[0].s.r]||[])[q[0].s.c]:d[ec],r.cellFormula&&l){if(!g||!ec||!l)break;l.f=""+nH(q[1],m,q[0],M,_),l.F=tw(q[0])}break;case 1212:if(!r.cellFormula)break;if(E){if(!g)break;w[tb(g.cell)]=q[0],((l=r.dense?(d[g.cell.r]||[])[g.cell.c]:d[tb(g.cell)])||{}).f=""+nH(q[0],m,o,M,_)}break;case 253:t=sE(v[q.isst].t,q.ixfe,"s"),v[q.isst].h&&(t.h=v[q.isst].h),t.XF=S[t.ixfe],H>0&&(t.z=W[t.ixfe>>8&63]),sb(t,r,i.opts.Date1904),x({c:q.c,r:q.r},t,r);break;case 513:r.sheetStubs&&(t={ixfe:q.ixfe,XF:S[q.ixfe],t:"z"},H>0&&(t.z=W[t.ixfe>>8&63]),sb(t,r,i.opts.Date1904),x({c:q.c,r:q.r},t,r));break;case 190:if(r.sheetStubs)for(var el=q.c;el<=q.C;++el){var ef=q.ixfe[el-q.c];t={ixfe:ef,XF:S[ef],t:"z"},H>0&&(t.z=W[t.ixfe>>8&63]),sb(t,r,i.opts.Date1904),x({c:el,r:q.r},t,r)}break;case 214:case 516:case 4:(t=sE(q.val,q.ixfe,"s")).XF=S[t.ixfe],H>0&&(t.z=W[t.ixfe>>8&63]),sb(t,r,i.opts.Date1904),x({c:q.c,r:q.r},t,r);break;case 0:case 512:1===B&&(m=q);break;case 252:v=q;break;case 1054:if(4==_.biff){W[H++]=q[1];for(var eh=0;eh<H+163&&eo[eh]!=q[1];++eh);eh>=163&&eP(q[1],H+163)}else eP(q[1],q[0]);break;case 30:W[H++]=q;for(var eu=0;eu<H+163&&eo[eu]!=q;++eu);eu>=163&&eP(q,H+163);break;case 229:I=I.concat(q);break;case 93:N[q.cmo[0]]=_.lastobj=q;break;case 438:_.lastobj.TxO=q;break;case 127:_.lastobj.ImData=q;break;case 440:for(u=q[0].s.r;u<=q[0].e.r;++u)for(h=q[0].s.c;h<=q[0].e.c;++h)(l=r.dense?(d[u]||[])[h]:d[tb({c:h,r:u})])&&(l.l=q[1]);break;case 2048:for(u=q[0].s.r;u<=q[0].e.r;++u)for(h=q[0].s.c;h<=q[0].e.c;++h)(l=r.dense?(d[u]||[])[h]:d[tb({c:h,r:u})])&&l.l&&(l.l.Tooltip=q[1]);break;case 28:if(_.biff<=5&&_.biff>=2)break;l=r.dense?(d[q[0].r]||[])[q[0].c]:d[tb(q[0])];var ed=N[q[2]];l||(r.dense?(d[q[0].r]||(d[q[0].r]=[]),l=d[q[0].r][q[0].c]={t:"z"}):l=d[tb(q[0])]={t:"z"},m.e.r=Math.max(m.e.r,q[0].r),m.s.r=Math.min(m.s.r,q[0].r),m.e.c=Math.max(m.e.c,q[0].c),m.s.c=Math.min(m.s.c,q[0].c)),l.c||(l.c=[]),f={a:q[1],t:ed.TxO.t},l.c.push(f);break;case 2173:S[q.ixfe],q.ext.forEach(function(e){e[0]});break;case 125:if(!_.cellStyles)break;for(;q.e>=q.s;)F[q.e--]={width:q.w/256,level:q.level||0,hidden:!!(1&q.flags)},L||(L=!0,a7(q.w/256)),a9(F[q.e+1]);break;case 520:var ep={};null!=q.level&&(P[q.r]=ep,ep.level=q.level),q.hidden&&(P[q.r]=ep,ep.hidden=!0),q.hpt&&(P[q.r]=ep,ep.hpt=q.hpt,ep.hpx=ne(q.hpt));break;case 38:case 39:case 40:case 41:d["!margins"]||nq(d["!margins"]={}),d["!margins"][({38:"left",39:"right",40:"top",41:"bottom"})[Y]]=q;break;case 161:d["!margins"]||nq(d["!margins"]={}),d["!margins"].header=q.header,d["!margins"].footer=q.footer;break;case 574:q.RTL&&(y.Views[0].RTL=!0);break;case 146:k=q;break;case 2198:n=q;break;case 140:a=q;break;case 442:T?C.CodeName=q||C.name:y.WBProps.CodeName=q||"ThisWorkbook"}}else K||console.error("Missing Info for XLS Record 0x"+Y.toString(16)),e.l+=j}return i.SheetNames=eW(p).sort(function(e,r){return Number(e)-Number(r)}).map(function(e){return p[e].name}),r.bookSheets||(i.Sheets=c),!i.SheetNames.length&&b["!ref"]?(i.SheetNames.push("Sheet1"),i.Sheets&&(i.Sheets.Sheet1=b)):i.Preamble=b,i.Sheets&&V.forEach(function(e,r){i.Sheets[i.SheetNames[r]]["!autofilter"]=e}),i.Strings=v,i.SSF=e1(eo),_.enc&&(i.Encryption=_.enc),n&&(i.Themes=n),i.Metadata={},void 0!==a&&(i.Metadata.Country=a),M.names.length>0&&(y.Names=M.names),i.Workbook=y,i}(s.content,r);else if((c=eH.find(e,"PerfectOffice_MAIN"))&&c.content)i=aM.to_workbook(c.content,(r.type=o,r));else if((c=eH.find(e,"NativeContent_MAIN"))&&c.content)i=aM.to_workbook(c.content,(r.type=o,r));else if((c=eH.find(e,"MN0"))&&c.content)throw Error("Unsupported Works 4 for Mac file");else throw Error("Cannot find Workbook stream");r.bookVBA&&e.FullPaths&&eH.find(e,"/_VBA_PROJECT_CUR/VBA/dir")&&(i.vbaraw=(t=e,a=eH.utils.cfb_new({root:"R"}),t.FullPaths.forEach(function(e,r){if("/"!==e.slice(-1)&&e.match(/_VBA_PROJECT_CUR/)){var n=e.replace(/^[^\/]*/,"R").replace(/\/_VBA_PROJECT_CUR\u0000*/,"");eH.utils.cfb_add(a,n,t.FileIndex[r].content)}}),eH.write(a)))}var l={};return e.FullPaths&&function(e,r,t){var a=eH.find(e,"/!DocumentSummaryInformation");if(a&&a.size>0)try{var n=ae(a,tM,sw.DSI);for(var s in n)r[s]=n[s]}catch(e){if(t.WTF)throw e}var i=eH.find(e,"/!SummaryInformation");if(i&&i.size>0)try{var c=ae(i,tU,sw.SI);for(var o in c)null==r[o]&&(r[o]=c[o])}catch(e){if(t.WTF)throw e}r.HeadingPairs&&r.TitlesOfParts&&(tZ(r.HeadingPairs,r.TitlesOfParts,r,t),delete r.HeadingPairs,delete r.TitlesOfParts)}(e,l,r),i.Props=i.Custprops=l,r.bookFiles&&(i.cfb=e),i}var sS={0:{f:function(e,r){var t={},a=e.l+r;t.r=e.read_shift(4),e.l+=4;var n=e.read_shift(2);e.l+=1;var s=e.read_shift(1);return e.l=a,7&s&&(t.level=7&s),16&s&&(t.hidden=!0),32&s&&(t.hpt=n/20),t}},1:{f:function(e){return[tx(e)]}},2:{f:function(e){return[tx(e),tN(e),"n"]}},3:{f:function(e){return[tx(e),e.read_shift(1),"e"]}},4:{f:function(e){return[tx(e),e.read_shift(1),"b"]}},5:{f:function(e){return[tx(e),tF(e),"n"]}},6:{f:function(e){return[tx(e),tR(e),"str"]}},7:{f:function(e){return[tx(e),e.read_shift(4),"s"]}},8:{f:function(e,r,t){var a=e.l+r,n=tx(e);n.r=t["!row"];var s=[n,tR(e),"str"];if(t.cellFormula){e.l+=2;var i=nV(e,a-e.l,t);s[3]=nH(i,null,n,t.supbooks,t)}else e.l=a;return s}},9:{f:function(e,r,t){var a=e.l+r,n=tx(e);n.r=t["!row"];var s=[n,tF(e),"n"];if(t.cellFormula){e.l+=2;var i=nV(e,a-e.l,t);s[3]=nH(i,null,n,t.supbooks,t)}else e.l=a;return s}},10:{f:function(e,r,t){var a=e.l+r,n=tx(e);n.r=t["!row"];var s=[n,e.read_shift(1),"b"];if(t.cellFormula){e.l+=2;var i=nV(e,a-e.l,t);s[3]=nH(i,null,n,t.supbooks,t)}else e.l=a;return s}},11:{f:function(e,r,t){var a=e.l+r,n=tx(e);n.r=t["!row"];var s=[n,e.read_shift(1),"e"];if(t.cellFormula){e.l+=2;var i=nV(e,a-e.l,t);s[3]=nH(i,null,n,t.supbooks,t)}else e.l=a;return s}},12:{f:function(e){return[t_(e)]}},13:{f:function(e){return[t_(e),tN(e),"n"]}},14:{f:function(e){return[t_(e),e.read_shift(1),"e"]}},15:{f:function(e){return[t_(e),e.read_shift(1),"b"]}},16:{f:sr},17:{f:function(e){return[t_(e),tR(e),"str"]}},18:{f:function(e){return[t_(e),e.read_shift(4),"s"]}},19:{f:tO},20:{},21:{},22:{},23:{},24:{},25:{},26:{},27:{},28:{},29:{},30:{},31:{},32:{},33:{},34:{},35:{T:1},36:{T:-1},37:{T:1},38:{T:-1},39:{f:function(e,r,t){var a=e.l+r;e.l+=4,e.l+=1;var n=e.read_shift(4),s=tR(e),i=nV(e,0,t),c=tI(e);e.l=a;var o={Name:s,Ptg:i};return n<0xfffffff&&(o.Sheet=n),c&&(o.Comment=c),o}},40:{},42:{},43:{f:function(e,r,t){var a,n={};n.sz=e.read_shift(2)/20;var s=(a=e.read_shift(1),e.l++,{fBold:1&a,fItalic:2&a,fUnderline:4&a,fStrikeout:8&a,fOutline:16&a,fShadow:32&a,fCondense:64&a,fExtend:128&a});switch(s.fItalic&&(n.italic=1),s.fCondense&&(n.condense=1),s.fExtend&&(n.extend=1),s.fShadow&&(n.shadow=1),s.fOutline&&(n.outline=1),s.fStrikeout&&(n.strike=1),700===e.read_shift(2)&&(n.bold=1),e.read_shift(2)){case 1:n.vertAlign="superscript";break;case 2:n.vertAlign="subscript"}var i=e.read_shift(1);0!=i&&(n.underline=i);var c=e.read_shift(1);c>0&&(n.family=c);var o=e.read_shift(1);switch(o>0&&(n.charset=o),e.l++,n.color=function(e){var r={},t=e.read_shift(1),a=e.read_shift(1),n=e.read_shift(2,"i"),s=e.read_shift(1),i=e.read_shift(1),c=e.read_shift(1);switch(e.l++,t>>>1){case 0:r.auto=1;break;case 1:r.index=a;var o=tW[a];o&&(r.rgb=a0(o));break;case 2:r.rgb=a0([s,i,c]);break;case 3:r.theme=a}return 0!=n&&(r.tint=n>0?n/32767:n/32768),r}(e,8),e.read_shift(1)){case 1:n.scheme="major";break;case 2:n.scheme="minor"}return n.name=tR(e,r-21),n}},44:{f:function(e,r){return[e.read_shift(2),tR(e,r-2)]}},45:{f:ti},46:{f:ti},47:{f:function(e,r){var t=e.l+r,a=e.read_shift(2),n=e.read_shift(2);return e.l=t,{ixfe:a,numFmtId:n}}},48:{},49:{f:function(e){return e.read_shift(4,"i")}},50:{},51:{f:function(e){for(var r=[],t=e.read_shift(4);t-- >0;)r.push([e.read_shift(4),e.read_shift(4)]);return r}},52:{T:1},53:{T:-1},54:{T:1},55:{T:-1},56:{T:1},57:{T:-1},58:{},59:{},60:{f:a_},62:{f:function(e){return[tx(e),tO(e),"is"]}},63:{f:function(e){var r={};r.i=e.read_shift(4);var t={};t.r=e.read_shift(4),t.c=e.read_shift(4),r.r=tb(t);var a=e.read_shift(1);return 2&a&&(r.l="1"),8&a&&(r.a="1"),r}},64:{f:function(){}},65:{},66:{},67:{},68:{},69:{},70:{},128:{},129:{T:1},130:{T:-1},131:{T:1,f:ti,p:0},132:{T:-1},133:{T:1},134:{T:-1},135:{T:1},136:{T:-1},137:{T:1,f:function(e){var r=e.read_shift(2);return e.l+=28,{RTL:32&r}}},138:{T:-1},139:{T:1},140:{T:-1},141:{T:1},142:{T:-1},143:{T:1},144:{T:-1},145:{T:1},146:{T:-1},147:{f:function(e,r){var t={},a=e[e.l];return++e.l,t.above=!(64&a),t.left=!(128&a),e.l+=18,t.name=tR(e,r-19),t}},148:{f:tD,p:16},151:{f:function(){}},152:{},153:{f:function(e,r){var t={},a=e.read_shift(4);t.defaultThemeVersion=e.read_shift(4);var n=r>8?tR(e):"";return n.length>0&&(t.CodeName=n),t.autoCompressPictures=!!(65536&a),t.backupFile=!!(64&a),t.checkCompatibility=!!(4096&a),t.date1904=!!(1&a),t.filterPrivacy=!!(8&a),t.hidePivotFieldList=!!(1024&a),t.promptedSolutions=!!(16&a),t.publishItems=!!(2048&a),t.refreshAllConnections=!!(262144&a),t.saveExternalLinkValues=!!(128&a),t.showBorderUnselectedTables=!!(4&a),t.showInkAnnotation=!!(32&a),t.showObjects=["all","placeholders","none"][a>>13&3],t.showPivotChartFilter=!!(32768&a),t.updateLinks=["userSet","never","always"][a>>8&3],t}},154:{},155:{},156:{f:function(e,r){var t={};return t.Hidden=e.read_shift(4),t.iTabID=e.read_shift(4),t.strRelID=tI(e,r-8),t.name=tR(e),t}},157:{},158:{},159:{T:1,f:function(e){return[e.read_shift(4),e.read_shift(4)]}},160:{T:-1},161:{T:1,f:tD},162:{T:-1},163:{T:1},164:{T:-1},165:{T:1},166:{T:-1},167:{},168:{},169:{},170:{},171:{},172:{T:1},173:{T:-1},174:{},175:{},176:{f:tD},177:{T:1},178:{T:-1},179:{T:1},180:{T:-1},181:{T:1},182:{T:-1},183:{T:1},184:{T:-1},185:{T:1},186:{T:-1},187:{T:1},188:{T:-1},189:{T:1},190:{T:-1},191:{T:1},192:{T:-1},193:{T:1},194:{T:-1},195:{T:1},196:{T:-1},197:{T:1},198:{T:-1},199:{T:1},200:{T:-1},201:{T:1},202:{T:-1},203:{T:1},204:{T:-1},205:{T:1},206:{T:-1},207:{T:1},208:{T:-1},209:{T:1},210:{T:-1},211:{T:1},212:{T:-1},213:{T:1},214:{T:-1},215:{T:1},216:{T:-1},217:{T:1},218:{T:-1},219:{T:1},220:{T:-1},221:{T:1},222:{T:-1},223:{T:1},224:{T:-1},225:{T:1},226:{T:-1},227:{T:1},228:{T:-1},229:{T:1},230:{T:-1},231:{T:1},232:{T:-1},233:{T:1},234:{T:-1},235:{T:1},236:{T:-1},237:{T:1},238:{T:-1},239:{T:1},240:{T:-1},241:{T:1},242:{T:-1},243:{T:1},244:{T:-1},245:{T:1},246:{T:-1},247:{T:1},248:{T:-1},249:{T:1},250:{T:-1},251:{T:1},252:{T:-1},253:{T:1},254:{T:-1},255:{T:1},256:{T:-1},257:{T:1},258:{T:-1},259:{T:1},260:{T:-1},261:{T:1},262:{T:-1},263:{T:1},264:{T:-1},265:{T:1},266:{T:-1},267:{T:1},268:{T:-1},269:{T:1},270:{T:-1},271:{T:1},272:{T:-1},273:{T:1},274:{T:-1},275:{T:1},276:{T:-1},277:{},278:{T:1},279:{T:-1},280:{T:1},281:{T:-1},282:{T:1},283:{T:1},284:{T:-1},285:{T:1},286:{T:-1},287:{T:1},288:{T:-1},289:{T:1},290:{T:-1},291:{T:1},292:{T:-1},293:{T:1},294:{T:-1},295:{T:1},296:{T:-1},297:{T:1},298:{T:-1},299:{T:1},300:{T:-1},301:{T:1},302:{T:-1},303:{T:1},304:{T:-1},305:{T:1},306:{T:-1},307:{T:1},308:{T:-1},309:{T:1},310:{T:-1},311:{T:1},312:{T:-1},313:{T:-1},314:{T:1},315:{T:-1},316:{T:1},317:{T:-1},318:{T:1},319:{T:-1},320:{T:1},321:{T:-1},322:{T:1},323:{T:-1},324:{T:1},325:{T:-1},326:{T:1},327:{T:-1},328:{T:1},329:{T:-1},330:{T:1},331:{T:-1},332:{T:1},333:{T:-1},334:{T:1},335:{f:function(e,r){return{flags:e.read_shift(4),version:e.read_shift(4),name:tR(e,r-8)}}},336:{T:-1},337:{f:function(e){return e.l+=4,0!=e.read_shift(4)},T:1},338:{T:-1},339:{T:1},340:{T:-1},341:{T:1},342:{T:-1},343:{T:1},344:{T:-1},345:{T:1},346:{T:-1},347:{T:1},348:{T:-1},349:{T:1},350:{T:-1},351:{},352:{},353:{T:1},354:{T:-1},355:{f:tI},357:{},358:{},359:{},360:{T:1},361:{},362:{f:aR},363:{},364:{},366:{},367:{},368:{},369:{},370:{},371:{},372:{T:1},373:{T:-1},374:{T:1},375:{T:-1},376:{T:1},377:{T:-1},378:{T:1},379:{T:-1},380:{T:1},381:{T:-1},382:{T:1},383:{T:-1},384:{T:1},385:{T:-1},386:{T:1},387:{T:-1},388:{T:1},389:{T:-1},390:{T:1},391:{T:-1},392:{T:1},393:{T:-1},394:{T:1},395:{T:-1},396:{},397:{},398:{},399:{},400:{},401:{T:1},403:{},404:{},405:{},406:{},407:{},408:{},409:{},410:{},411:{},412:{},413:{},414:{},415:{},416:{},417:{},418:{},419:{},420:{},421:{},422:{T:1},423:{T:1},424:{T:-1},425:{T:-1},426:{f:function(e,r,t){var a=e.l+r,n=tD(e,16),s=e.read_shift(1),i=[n];if(i[2]=s,t.cellFormula){var c=nV(e,a-e.l,t);i[1]=c}else e.l=a;return i}},427:{f:function(e,r,t){var a=e.l+r,n=[tD(e,16)];if(t.cellFormula){var s=nV(e,a-e.l,t);n[1]=s,e.l=a}else e.l=a;return n}},428:{},429:{T:1},430:{T:-1},431:{T:1},432:{T:-1},433:{T:1},434:{T:-1},435:{T:1},436:{T:-1},437:{T:1},438:{T:-1},439:{T:1},440:{T:-1},441:{T:1},442:{T:-1},443:{T:1},444:{T:-1},445:{T:1},446:{T:-1},447:{T:1},448:{T:-1},449:{T:1},450:{T:-1},451:{T:1},452:{T:-1},453:{T:1},454:{T:-1},455:{T:1},456:{T:-1},457:{T:1},458:{T:-1},459:{T:1},460:{T:-1},461:{T:1},462:{T:-1},463:{T:1},464:{T:-1},465:{T:1},466:{T:-1},467:{T:1},468:{T:-1},469:{T:1},470:{T:-1},471:{},472:{},473:{T:1},474:{T:-1},475:{},476:{f:function(e){var r={};return st.forEach(function(t){r[t]=tF(e,8)}),r}},477:{},478:{},479:{T:1},480:{T:-1},481:{T:1},482:{T:-1},483:{T:1},484:{T:-1},485:{f:function(){}},486:{T:1},487:{T:-1},488:{T:1},489:{T:-1},490:{T:1},491:{T:-1},492:{T:1},493:{T:-1},494:{f:function(e,r){var t=e.l+r,a=tD(e,16),n=tI(e),s=tR(e),i=tR(e),c=tR(e);e.l=t;var o={rfx:a,relId:n,loc:s,display:c};return i&&(o.Tooltip=i),o}},495:{T:1},496:{T:-1},497:{T:1},498:{T:-1},499:{},500:{T:1},501:{T:-1},502:{T:1},503:{T:-1},504:{},505:{T:1},506:{T:-1},507:{},508:{T:1},509:{T:-1},510:{T:1},511:{T:-1},512:{},513:{},514:{T:1},515:{T:-1},516:{T:1},517:{T:-1},518:{T:1},519:{T:-1},520:{T:1},521:{T:-1},522:{},523:{},524:{},525:{},526:{},527:{},528:{T:1},529:{T:-1},530:{T:1},531:{T:-1},532:{T:1},533:{T:-1},534:{},535:{},536:{},537:{},538:{T:1},539:{T:-1},540:{T:1},541:{T:-1},542:{T:1},548:{},549:{},550:{f:tI},551:{},552:{},553:{},554:{T:1},555:{T:-1},556:{T:1},557:{T:-1},558:{T:1},559:{T:-1},560:{T:1},561:{T:-1},562:{},564:{},565:{T:1},566:{T:-1},569:{T:1},570:{T:-1},572:{},573:{T:1},574:{T:-1},577:{},578:{},579:{},580:{},581:{},582:{},583:{},584:{},585:{},586:{},587:{},588:{T:-1},589:{},590:{T:1},591:{T:-1},592:{T:1},593:{T:-1},594:{T:1},595:{T:-1},596:{},597:{T:1},598:{T:-1},599:{T:1},600:{T:-1},601:{T:1},602:{T:-1},603:{T:1},604:{T:-1},605:{T:1},606:{T:-1},607:{},608:{T:1},609:{T:-1},610:{},611:{T:1},612:{T:-1},613:{T:1},614:{T:-1},615:{T:1},616:{T:-1},617:{T:1},618:{T:-1},619:{T:1},620:{T:-1},625:{},626:{T:1},627:{T:-1},628:{T:1},629:{T:-1},630:{T:1},631:{T:-1},632:{f:tR},633:{T:1},634:{T:-1},635:{T:1,f:function(e){var r={};r.iauthor=e.read_shift(4);var t=tD(e,16);return r.rfx=t.s,r.ref=tb(t.s),e.l+=16,r}},636:{T:-1},637:{f:tO},638:{T:1},639:{},640:{T:-1},641:{T:1},642:{T:-1},643:{T:1},644:{},645:{T:-1},646:{T:1},648:{T:1},649:{},650:{T:-1},651:{f:function(e,r){return e.l+=10,{name:tR(e,r-10)}}},652:{},653:{T:1},654:{T:-1},655:{T:1},656:{T:-1},657:{T:1},658:{T:-1},659:{},660:{T:1},661:{},662:{T:-1},663:{},664:{T:1},665:{},666:{T:-1},667:{},668:{},669:{},671:{T:1},672:{T:-1},673:{T:1},674:{T:-1},675:{},676:{},677:{},678:{},679:{},680:{},681:{},1024:{},1025:{},1026:{T:1},1027:{T:-1},1028:{T:1},1029:{T:-1},1030:{},1031:{T:1},1032:{T:-1},1033:{T:1},1034:{T:-1},1035:{},1036:{},1037:{},1038:{T:1},1039:{T:-1},1040:{},1041:{T:1},1042:{T:-1},1043:{},1044:{},1045:{},1046:{T:1},1047:{T:-1},1048:{T:1},1049:{T:-1},1050:{},1051:{T:1},1052:{T:1},1053:{f:function(){}},1054:{T:1},1055:{},1056:{T:1},1057:{T:-1},1058:{T:1},1059:{T:-1},1061:{},1062:{T:1},1063:{T:-1},1064:{T:1},1065:{T:-1},1066:{T:1},1067:{T:-1},1068:{T:1},1069:{T:-1},1070:{T:1},1071:{T:-1},1072:{T:1},1073:{T:-1},1075:{T:1},1076:{T:-1},1077:{T:1},1078:{T:-1},1079:{T:1},1080:{T:-1},1081:{T:1},1082:{T:-1},1083:{T:1},1084:{T:-1},1085:{},1086:{T:1},1087:{T:-1},1088:{T:1},1089:{T:-1},1090:{T:1},1091:{T:-1},1092:{T:1},1093:{T:-1},1094:{T:1},1095:{T:-1},1096:{},1097:{T:1},1098:{},1099:{T:-1},1100:{T:1},1101:{T:-1},1102:{},1103:{},1104:{},1105:{},1111:{},1112:{},1113:{T:1},1114:{T:-1},1115:{T:1},1116:{T:-1},1117:{},1118:{T:1},1119:{T:-1},1120:{T:1},1121:{T:-1},1122:{T:1},1123:{T:-1},1124:{T:1},1125:{T:-1},1126:{},1128:{T:1},1129:{T:-1},1130:{},1131:{T:1},1132:{T:-1},1133:{T:1},1134:{T:-1},1135:{T:1},1136:{T:-1},1137:{T:1},1138:{T:-1},1139:{T:1},1140:{T:-1},1141:{},1142:{T:1},1143:{T:-1},1144:{T:1},1145:{T:-1},1146:{},1147:{T:1},1148:{T:-1},1149:{T:1},1150:{T:-1},1152:{T:1},1153:{T:-1},1154:{T:-1},1155:{T:-1},1156:{T:-1},1157:{T:1},1158:{T:-1},1159:{T:1},1160:{T:-1},1161:{T:1},1162:{T:-1},1163:{T:1},1164:{T:-1},1165:{T:1},1166:{T:-1},1167:{T:1},1168:{T:-1},1169:{T:1},1170:{T:-1},1171:{},1172:{T:1},1173:{T:-1},1177:{},1178:{T:1},1180:{},1181:{},1182:{},2048:{T:1},2049:{T:-1},2050:{},2051:{T:1},2052:{T:-1},2053:{},2054:{},2055:{T:1},2056:{T:-1},2057:{T:1},2058:{T:-1},2060:{},2067:{},2068:{T:1},2069:{T:-1},2070:{},2071:{},2072:{T:1},2073:{T:-1},2075:{},2076:{},2077:{T:1},2078:{T:-1},2079:{},2080:{T:1},2081:{T:-1},2082:{},2083:{T:1},2084:{T:-1},2085:{T:1},2086:{T:-1},2087:{T:1},2088:{T:-1},2089:{T:1},2090:{T:-1},2091:{},2092:{},2093:{T:1},2094:{T:-1},2095:{},2096:{T:1},2097:{T:-1},2098:{T:1},2099:{T:-1},2100:{T:1},2101:{T:-1},2102:{},2103:{T:1},2104:{T:-1},2105:{},2106:{T:1},2107:{T:-1},2108:{},2109:{T:1},2110:{T:-1},2111:{T:1},2112:{T:-1},2113:{T:1},2114:{T:-1},2115:{},2116:{},2117:{},2118:{T:1},2119:{T:-1},2120:{},2121:{T:1},2122:{T:-1},2123:{T:1},2124:{T:-1},2125:{},2126:{T:1},2127:{T:-1},2128:{},2129:{T:1},2130:{T:-1},2131:{T:1},2132:{T:-1},2133:{T:1},2134:{},2135:{},2136:{},2137:{T:1},2138:{T:-1},2139:{T:1},2140:{T:-1},2141:{},3072:{},3073:{},4096:{T:1},4097:{T:-1},5002:{T:1},5003:{T:-1},5081:{T:1},5082:{T:-1},5083:{},5084:{T:1},5085:{T:-1},5086:{T:1},5087:{T:-1},5088:{},5089:{},5090:{},5092:{T:1},5093:{T:-1},5094:{},5095:{T:1},5096:{T:-1},5097:{},5099:{},65535:{n:""}},sk={6:{f:nW},10:{f:ar},12:{f:aa},13:{f:aa},14:{f:at},15:{f:at},16:{f:tF},17:{f:at},18:{f:at},19:{f:aa},20:{f:aS},21:{f:aS},23:{f:aR},24:{f:aC},25:{f:at},26:{},27:{},28:{f:function(e,r,t){return function(e,r,t){if(!(t.biff<8)){var a=e.read_shift(2),n=e.read_shift(2),s=e.read_shift(2),i=e.read_shift(2),c=ao(e,0,t);return t.biff<8&&e.read_shift(1),[{r:a,c:n},c,i,s]}}(e,0,t)}},29:{},34:{f:at},35:{f:ak},38:{f:tF},39:{f:tF},40:{f:tF},41:{f:tF},42:{f:at},43:{f:at},47:{f:function(e,r,t){var a,n,s,i={Type:t.biff>=8?e.read_shift(2):0};return i.Type?(a=r-2,(n=i||{}).Info=e.read_shift(2),e.l-=2,1===n.Info?n.Data=function(e){var r={},t=r.EncryptionVersionInfo=aK(e,4);if(1!=t.Major||1!=t.Minor)throw"unrecognized version code "+t.Major+" : "+t.Minor;return r.Salt=e.read_shift(16),r.EncryptedVerifier=e.read_shift(16),r.EncryptedVerifierHash=e.read_shift(16),r}(e,a):n.Data=function(e,r){var t={},a=t.EncryptionVersionInfo=aK(e,4);if(r-=4,2!=a.Minor)throw Error("unrecognized minor version code: "+a.Minor);if(a.Major>4||a.Major<2)throw Error("unrecognized major version code: "+a.Major);t.Flags=e.read_shift(4),r-=4;var n=e.read_shift(4);return r-=4,t.EncryptionHeader=aX(e,n),t.EncryptionVerifier=aq(e,r-=n),t}(e,a)):(t.biff,s={key:aa(e),verificationBytes:aa(e)},t.password&&(s.verifier=function(e){var r,t,a=0,n=aj(e),s=n.length+1;for(t=1,(r=Y(s))[0]=n.length;t!=s;++t)r[t]=n[t-1];for(t=s-1;t>=0;--t)a=((16384&a)!=0|a<<1&32767)^r[t];return 52811^a}(t.password)),i.valid=s.verificationBytes===s.verifier,i.valid&&(i.insitu=aQ(t.password))),i}},49:{f:function(e,r,t){var a={dyHeight:e.read_shift(2),fl:e.read_shift(2)};switch(t&&t.biff||8){case 2:break;case 3:case 4:e.l+=2;break;default:e.l+=10}return a.name=as(e,0,t),a}},51:{f:aa},60:{},61:{f:function(e){return{Pos:[e.read_shift(2),e.read_shift(2)],Dim:[e.read_shift(2),e.read_shift(2)],Flags:e.read_shift(2),CurTab:e.read_shift(2),FirstTab:e.read_shift(2),Selected:e.read_shift(2),TabRatio:e.read_shift(2)}}},64:{f:at},65:{f:function(){}},66:{f:aa},77:{},80:{},81:{},82:{},85:{f:aa},89:{},90:{},91:{},92:{f:function(e,r,t){if(t.enc)return e.l+=r,"";var a=e.l,n=ao(e,0,t);return e.read_shift(r+a-e.l),n}},93:{f:function(e,r,t){if(t&&t.biff<8){var a,n,s,i,c,o,l;return a=e,n=r,s=t,a.l+=4,i=a.read_shift(2),c=a.read_shift(2),o=a.read_shift(2),a.l+=2,a.l+=2,a.l+=2,a.l+=2,a.l+=2,a.l+=2,a.l+=2,a.l+=2,a.l+=2,a.l+=6,n-=36,(l=[]).push((ax[i]||ti)(a,n,s)),{cmo:[c,i,o],ft:l}}var f=ag(e,22),h=function(e,r){for(var t=e.l+r,a=[];e.l<t;){var n=e.read_shift(2);e.l-=2;try{a.push(aT[n](e,t-e.l))}catch(r){return e.l=t,a}}return e.l!=t&&(e.l=t),a}(e,r-22,f[1]);return{cmo:f,ft:h}}},94:{},95:{f:at},96:{},97:{},99:{f:at},125:{f:a_},128:{f:function(e){e.l+=4;var r=[e.read_shift(2),e.read_shift(2)];if(0!==r[0]&&r[0]--,0!==r[1]&&r[1]--,r[0]>7||r[1]>7)throw Error("Bad Gutters: "+r.join("|"));return r}},129:{f:function(e,r,t){var a=t&&8==t.biff||2==r?e.read_shift(2):(e.l+=r,0);return{fDialog:16&a,fBelow:64&a,fRight:128&a}}},130:{f:aa},131:{f:at},132:{f:at},133:{f:function(e,r,t){var a=e.read_shift(4),n=3&e.read_shift(1),s=e.read_shift(1);switch(s){case 0:s="Worksheet";break;case 1:s="Macrosheet";break;case 2:s="Chartsheet";break;case 6:s="VBAModule"}var i=as(e,0,t);return 0===i.length&&(i="Sheet1"),{pos:a,hs:n,dt:s,name:i}}},134:{},140:{f:function(e){var r,t=[0,0];return r=e.read_shift(2),t[0]=tB[r]||r,r=e.read_shift(2),t[1]=tB[r]||r,t}},141:{f:aa},144:{},146:{f:function(e){for(var r=e.read_shift(2),t=[];r-- >0;)t.push(ah(e,8));return t}},151:{},152:{},153:{},154:{},155:{},156:{f:aa},157:{},158:{},160:{f:an},161:{f:function(e,r){var t={};return r<32||(e.l+=16,t.header=tF(e,8),t.footer=tF(e,8),e.l+=2),t}},174:{},175:{},176:{},177:{},178:{},180:{},181:{},182:{},184:{},185:{},189:{f:function(e,r){for(var t=e.l+r-2,a=e.read_shift(2),n=e.read_shift(2),s=[];e.l<t;)s.push(ad(e));if(e.l!==t)throw Error("MulRK read error");var i=e.read_shift(2);if(s.length!=i-n+1)throw Error("MulRK length mismatch");return{r:a,c:n,C:i,rkrec:s}}},190:{f:function(e,r){for(var t=e.l+r-2,a=e.read_shift(2),n=e.read_shift(2),s=[];e.l<t;)s.push(e.read_shift(2));if(e.l!==t)throw Error("MulBlank read error");var i=e.read_shift(2);if(s.length!=i-n+1)throw Error("MulBlank length mismatch");return{r:a,c:n,C:i,ixfe:s}}},193:{f:ar},197:{},198:{},199:{},200:{},201:{},202:{f:at},203:{},204:{},205:{},206:{},207:{},208:{},209:{},210:{},211:{},213:{},215:{},216:{},217:{},218:{f:aa},220:{},221:{f:at},222:{},224:{f:function(e,r,t){var a,n,s,i,c,o={};return o.ifnt=e.read_shift(2),o.numFmtId=e.read_shift(2),o.flags=e.read_shift(2),o.fStyle=o.flags>>2&1,r-=6,o.fStyle,a={},n=e.read_shift(4),s=e.read_shift(4),i=e.read_shift(4),c=e.read_shift(2),a.patternType=tH[i>>26],t.cellStyles&&(a.alc=7&n,a.fWrap=n>>3&1,a.alcV=n>>4&7,a.fJustLast=n>>7&1,a.trot=n>>8&255,a.cIndent=n>>16&15,a.fShrinkToFit=n>>20&1,a.iReadOrder=n>>22&2,a.fAtrNum=n>>26&1,a.fAtrFnt=n>>27&1,a.fAtrAlc=n>>28&1,a.fAtrBdr=n>>29&1,a.fAtrPat=n>>30&1,a.fAtrProt=n>>31&1,a.dgLeft=15&s,a.dgRight=s>>4&15,a.dgTop=s>>8&15,a.dgBottom=s>>12&15,a.icvLeft=s>>16&127,a.icvRight=s>>23&127,a.grbitDiag=s>>30&3,a.icvTop=127&i,a.icvBottom=i>>7&127,a.icvDiag=i>>14&127,a.dgDiag=i>>21&15,a.icvFore=127&c,a.icvBack=c>>7&127,a.fsxButton=c>>14&1),o.data=a,o}},225:{f:function(e,r){return 0===r||e.read_shift(2),1200}},226:{f:ar},227:{},229:{f:function(e,r){for(var t=[],a=e.read_shift(2);a--;)t.push(ap(e,r));return t}},233:{},235:{},236:{},237:{},239:{},240:{},241:{},242:{},244:{},245:{},246:{},247:{},248:{},249:{},251:{},252:{f:function(e,r){for(var t=e.l+r,a=e.read_shift(4),n=e.read_shift(4),s=[],i=0;i!=n&&e.l<t;++i)s.push(function(e){var r=O;O=1200;var t,a=e.read_shift(2),n=e.read_shift(1),s=4&n,i=8&n,c=0,o={};i&&(c=e.read_shift(2)),s&&(t=e.read_shift(4));var l=0===a?"":e.read_shift(a,2==1+(1&n)?"dbcs-cont":"sbcs-cont");return i&&(e.l+=4*c),s&&(e.l+=t),o.t=l,i||(o.raw="<t>"+o.t+"</t>",o.r=o.t),O=r,o}(e));return s.Count=a,s.Unique=n,s}},253:{f:function(e){var r=au(e);return r.isst=e.read_shift(4),r}},255:{f:function(e,r){var t={};return t.dsst=e.read_shift(2),e.l+=r-2,t}},256:{},259:{},290:{},311:{},312:{},315:{},317:{f:an},318:{},319:{},320:{},330:{},331:{},333:{},334:{},335:{},336:{},337:{},338:{},339:{},340:{},351:{},352:{f:at},353:{f:ar},401:{},402:{},403:{},404:{},405:{},406:{},407:{},408:{},425:{},426:{},427:{},428:{},429:{},430:{f:function(e,r,t){var a=e.l+r,n=e.read_shift(2),s=e.read_shift(2);if(t.sbcch=s,1025==s||14849==s)return[s,n];if(s<1||s>255)throw Error("Unexpected SupBook type: "+s);for(var i=ai(e,s),c=[];a>e.l;)c.push(ac(e));return[s,n,i,c]}},431:{f:at},432:{},433:{},434:{},437:{},438:{f:function(e,r,t){var a=e.l,n="";try{e.l+=4;var s=(t.lastobj||{cmo:[0,0]}).cmo[1];-1==[0,5,7,11,12,14].indexOf(s)?e.l+=6:(e.read_shift(1),e.l++,e.read_shift(2),e.l+=2);var i=e.read_shift(2);e.read_shift(2),aa(e,2);var c=e.read_shift(2);e.l+=c;for(var o=1;o<e.lens.length-1;++o){if(e.l-a!=e.lens[o])throw Error("TxO: bad continue record");var l=e[e.l],f=ai(e,e.lens[o+1]-e.lens[o]-1);if((n+=f).length>=(l?i:2*i))break}if(n.length!==i&&n.length!==2*i)throw Error("cchText: "+i+" != "+n.length);return e.l=a+r,{t:n}}catch(t){return e.l=a+r,{t:n}}}},439:{f:at},440:{f:function(e,r){var t=ap(e,8);return e.l+=16,[t,function(e,r){var t=e.l+r,a=e.read_shift(4);if(2!==a)throw Error("Unrecognized streamVersion: "+a);var n=e.read_shift(2);e.l+=2;var s,i,c,o,l,f,h="";16&n&&(s=al(e,t-e.l)),128&n&&(i=al(e,t-e.l)),(257&n)==257&&(c=al(e,t-e.l)),(257&n)==1&&(o=function(e,r){var t,a,n,s,i=e.read_shift(16);switch(r-=16,i){case"e0c9ea79f9bace118c8200aa004ba90b":return t=e.read_shift(4),a=e.l,n=!1,t>24&&(e.l+=t-24,"795881f43b1d7f48af2c825dc4852763"===e.read_shift(16)&&(n=!0),e.l=a),s=e.read_shift((n?t-24:t)>>1,"utf16le").replace(Z,""),n&&(e.l+=24),s;case"0303000000000000c000000000000046":for(var c=e.read_shift(2),o="";c-- >0;)o+="../";var l=e.read_shift(0,"lpstr-ansi");if(e.l+=2,57005!=e.read_shift(2))throw Error("Bad FileMoniker");if(0===e.read_shift(4))return o+l.replace(/\\/g,"/");var f=e.read_shift(4);if(3!=e.read_shift(2))throw Error("Bad FileMoniker");return o+e.read_shift(f>>1,"utf16le").replace(Z,"");default:throw Error("Unsupported Moniker "+i)}}(e,t-e.l)),8&n&&(h=al(e,t-e.l)),32&n&&(l=e.read_shift(16)),64&n&&(f=t0(e)),e.l=t;var u=i||c||o||"";u&&h&&(u+="#"+h),u||(u="#"+h),2&n&&"/"==u.charAt(0)&&"/"!=u.charAt(1)&&(u="file://"+u);var d={Target:u};return l&&(d.guid=l),f&&(d.time=f),s&&(d.Tooltip=s),d}(e,r-24)]}},441:{},442:{f:ac},443:{},444:{f:aa},445:{},446:{},448:{f:ar},449:{f:function(e){return e.read_shift(2),e.read_shift(4)},r:2},450:{f:ar},512:{f:aw},513:{f:au},515:{f:function(e,r,t){t.biffguess&&2==t.biff&&(t.biff=5);var a=au(e,6);return a.val=tF(e,8),a}},516:{f:function(e,r,t){t.biffguess&&2==t.biff&&(t.biff=5);var a=e.l+r,n=au(e,6);return 2==t.biff&&e.l++,n.val=ac(e,a-e.l,t),n}},517:{f:aA},519:{f:ac},520:{f:function(e){var r={};r.r=e.read_shift(2),r.c=e.read_shift(2),r.cnt=e.read_shift(2)-r.c;var t=e.read_shift(2);e.l+=4;var a=e.read_shift(1);return e.l+=3,7&a&&(r.level=7&a),32&a&&(r.hidden=!0),64&a&&(r.hpt=t/20),r}},523:{},545:{f:aO},549:{f:aE},566:{},574:{f:function(e,r,t){return t&&t.biff>=2&&t.biff<5?{}:{RTL:64&e.read_shift(2)}}},638:{f:function(e){var r=e.read_shift(2),t=e.read_shift(2),a=ad(e);return{r:r,c:t,ixfe:a[0],rknum:a[1]}}},659:{},1048:{},1054:{f:function(e,r,t){return[e.read_shift(2),ao(e,0,t)]}},1084:{},1212:{f:function(e,r,t){var a=am(e,6);e.l++;var n=e.read_shift(1);return[function(e,r,t){var a,n,s=e.l+r,i=e.read_shift(2),c=nL(e,i,t);return 65535==i?[[],(a=r-2,void(e.l+=a))]:(r!==i+2&&(n=nP(e,s-i-2,c,t)),[c,n])}(e,r-=8,t),n,a]}},2048:{f:function(e,r){e.read_shift(2);var t=ap(e,8),a=e.read_shift((r-10)/2,"dbcs-cont");return[t,a=a.replace(Z,"")]}},2049:{},2050:{},2051:{},2052:{},2053:{},2054:{},2055:{},2056:{},2057:{f:ab},2058:{},2059:{},2060:{},2061:{},2062:{},2063:{},2064:{},2066:{},2067:{},2128:{},2129:{},2130:{},2131:{},2132:{},2133:{},2134:{},2135:{},2136:{},2137:{},2138:{},2146:{},2147:{r:12},2148:{},2149:{},2150:{},2151:{f:ar},2152:{},2154:{},2155:{},2156:{},2161:{},2162:{},2164:{},2165:{},2166:{},2167:{},2168:{},2169:{},2170:{},2171:{},2172:{f:function(e){e.l+=2;var r={cxfs:0,crc:0};return r.cxfs=e.read_shift(2),r.crc=e.read_shift(4),r},r:12},2173:{f:function(e,r){var t=e.l+r;e.l+=2;var a=e.read_shift(2);e.l+=2;for(var n=e.read_shift(2),s=[];n-- >0;)s.push(function(e){var r=e.read_shift(2),t=e.read_shift(2)-4,a=[r];switch(r){case 4:case 5:case 7:case 8:case 9:case 10:case 11:case 13:a[1]=function(e){var r={};switch(r.xclrType=e.read_shift(2),r.nTintShade=e.read_shift(2),r.xclrType){case 0:case 4:e.l+=4;break;case 1:r.xclrValue=function(e,r){e.l+=r}(e,4);break;case 2:r.xclrValue=af(e,4);break;case 3:r.xclrValue=e.read_shift(4)}return e.l+=8,r}(e,t);break;case 6:a[1]=void(e.l+=t);break;case 14:case 15:a[1]=e.read_shift(1===t?1:2);break;default:throw Error("Unrecognized ExtProp type: "+r+" "+t)}return a}(e,t-e.l));return{ixfe:a,ext:s}},r:12},2174:{},2175:{},2180:{},2181:{},2182:{},2183:{},2184:{},2185:{},2186:{},2187:{},2188:{f:at,r:12},2189:{},2190:{r:12},2191:{},2192:{},2194:{},2195:{},2196:{f:function(e,r,t){if(t.biff<8){e.l+=r;return}var a=e.read_shift(2),n=e.read_shift(2);return[ai(e,a,t),ai(e,n,t)]},r:12},2197:{},2198:{f:function(e,r,t){var a,n=e.l+r;if(124226!==e.read_shift(4)){if(!t.cellStyles){e.l=n;return}var s=e.slice(e.l);e.l=n;try{a=ra(s,{type:"array"})}catch(e){return}var i=rr(a,"theme/theme/theme1.xml",!0);if(i)return nd(i,t)}},r:12},2199:{},2200:{},2201:{},2202:{f:function(e){return[0!==e.read_shift(4),0!==e.read_shift(4),e.read_shift(4)]},r:12},2203:{f:ar},2204:{},2205:{},2206:{},2207:{},2211:{f:function(e){var r,t,a=(r=e.read_shift(2),t=e.read_shift(2),e.l+=8,{type:r,flags:t});if(2211!=a.type)throw Error("Invalid Future Record "+a.type);return 0!==e.read_shift(4)}},2212:{},2213:{},2214:{},2215:{},4097:{},4098:{},4099:{},4102:{},4103:{},4105:{},4106:{},4107:{},4108:{},4109:{},4116:{},4117:{},4118:{},4119:{},4120:{},4121:{},4122:{},4123:{},4124:{},4125:{},4126:{},4127:{},4128:{},4129:{},4130:{},4132:{},4133:{},4134:{f:aa},4135:{},4146:{},4147:{},4148:{},4149:{},4154:{},4156:{},4157:{},4158:{},4159:{},4160:{},4161:{},4163:{},4164:{f:function(e,r,t){var a={area:!1};if(5!=t.biff)return e.l+=r,a;var n=e.read_shift(1);return e.l+=3,16&n&&(a.area=!0),a}},4165:{},4166:{},4168:{},4170:{},4171:{},4174:{},4175:{},4176:{},4177:{},4187:{},4188:{f:function(e){for(var r=e.read_shift(2),t=[];r-- >0;)t.push(ah(e,8));return t}},4189:{},4191:{},4192:{},4193:{},4194:{},4195:{},4196:{},4197:{},4198:{},4199:{},4200:{},0:{f:aw},1:{},2:{f:function(e){var r=au(e,6);++e.l;var t=e.read_shift(2);return r.t="n",r.val=t,r}},3:{f:function(e){var r=au(e,6);++e.l;var t=tF(e,8);return r.t="n",r.val=t,r}},4:{f:function(e,r,t){t.biffguess&&5==t.biff&&(t.biff=2);var a=au(e,6);++e.l;var n=ao(e,r-7,t);return a.t="str",a.val=n,a}},5:{f:aA},7:{f:function(e){var r=e.read_shift(1);return 0===r?(e.l++,""):e.read_shift(r,"sbcs-cont")}},8:{},9:{f:ab},11:{},22:{f:aa},30:{f:ao},31:{},32:{},33:{f:aO},36:{},37:{f:aE},50:{f:function(e,r){e.l+=6,e.l+=2,e.l+=1,e.l+=3,e.l+=1,e.l+=r-13}},62:{},52:{},67:{},68:{f:aa},69:{},86:{},126:{},127:{f:function(e){var r=e.read_shift(2),t=e.read_shift(2),a=e.read_shift(4),n={fmt:r,env:t,len:a,data:e.slice(e.l,e.l+a)};return e.l+=a,n}},135:{},136:{},137:{},145:{},148:{},149:{},150:{},169:{},171:{},188:{},191:{},192:{},194:{},195:{},214:{f:function(e,r,t){var a=e.l+r,n=au(e,6),s=e.read_shift(2),i=ai(e,s,t);return e.l=a,n.t="str",n.val=i,n}},223:{},234:{},354:{},421:{},518:{f:nW},521:{f:ab},536:{f:aC},547:{f:ak},561:{},579:{},1030:{f:nW},1033:{f:ab},1091:{},2157:{},2163:{},2177:{},2240:{},2241:{},2242:{},2243:{},2244:{},2245:{},2246:{},2247:{},2248:{},2249:{},2250:{},2251:{},2262:{r:12},29282:{}};function sy(e,r,t,a){if(!isNaN(r)){var n=a||(t||[]).length||0,s=e.next(4);s.write_shift(2,r),s.write_shift(2,n),n>0&&r4(t)&&e.push(t)}}function sC(e,r){var t=r||{},a=t.dense?[]:{},n=(e=e.replace(/<!--.*?-->/g,"")).match(/<table/i);if(!n)throw Error("Invalid HTML: could not find <table>");var s=e.match(/<\/table/i),i=n.index,c=s&&s.index||e.length,o=e5(e.slice(i,c),/(:?<tr[^>]*>)/i,"<tr>"),l=-1,f=0,h=0,u=0,d={s:{r:1e7,c:1e7},e:{r:0,c:0}},p=[];for(i=0;i<o.length;++i){var m=o[i].trim(),g=m.slice(0,3).toLowerCase();if("<tr"==g){if(++l,t.sheetRows&&t.sheetRows<=l){--l;break}f=0;continue}if("<td"==g||"<th"==g){var v=m.split(/<\/t[dh]>/i);for(c=0;c<v.length;++c){var T=v[c].trim();if(T.match(/<t[dh]/i)){for(var b=T,E=0;"<"==b.charAt(0)&&(E=b.indexOf(">"))>-1;)b=b.slice(E+1);for(var w=0;w<p.length;++w){var A=p[w];A.s.c==f&&A.s.r<l&&l<=A.e.r&&(f=A.e.c+1,w=-1)}var S=rh(T.slice(0,T.indexOf(">")));u=S.colspan?+S.colspan:1,((h=+S.rowspan)>1||u>1)&&p.push({s:{r:l,c:f},e:{r:l+(h||1)-1,c:f+u-1}});var k=S.t||S["data-t"]||"";if(!b.length||(b=rO(b),d.s.r>l&&(d.s.r=l),d.e.r<l&&(d.e.r=l),d.s.c>f&&(d.s.c=f),d.e.c<f&&(d.e.c=f),!b.length)){f+=u;continue}var y={t:"s",v:b};t.raw||!b.trim().length||"s"==k||("TRUE"===b?y={t:"b",v:!0}:"FALSE"===b?y={t:"b",v:!1}:isNaN(e2(b))?isNaN(e3(b).getDate())||(y={t:"d",v:eZ(b)},t.cellDates||(y={t:"n",v:ez(y.v)}),y.z=t.dateNF||eo[14]):y={t:"n",v:e2(b)}),t.dense?(a[l]||(a[l]=[]),a[l][f]=y):a[tb({r:l,c:f})]=y,f+=u}}}}return a["!ref"]=tw(d),p.length&&(a["!merges"]=p),a}var sR={day:["d","dd"],month:["m","mm"],year:["y","yy"],hours:["h","hh"],minutes:["m","mm"],seconds:["s","ss"],"am-pm":["A/P","AM/PM"],"day-of-week":["ddd","dddd"],era:["e","ee"],quarter:["\\Qm",'m\\"th quarter"']};function sO(e,r){var t=r||{},a,n,s,i,c,o,l=rD(e),f=[],h={name:""},u="",d=0,p={},m=[],g=t.dense?[]:{},v={value:""},T="",b=0,E=[],w=-1,A=-1,S={s:{r:1e6,c:1e7},e:{r:0,c:0}},k=0,y={},C=[],R={},O=0,x=[],_=1,I=1,N=[],D={Names:[]},F={},P=["",""],L=[],M={},U="",B=0,H=!1,W=!1,V=0;for(rF.lastIndex=0,l=l.replace(/<!--([\s\S]*?)-->/mg,"").replace(/<!DOCTYPE[^\[]*\[[^\]]*\]>/gm,"");c=rF.exec(l);)switch(c[3]=c[3].replace(/_.*$/,"")){case"table":case"工作表":"/"===c[1]?(S.e.c>=S.s.c&&S.e.r>=S.s.r?g["!ref"]=tw(S):g["!ref"]="A1:A1",t.sheetRows>0&&t.sheetRows<=S.e.r&&(g["!fullref"]=g["!ref"],S.e.r=t.sheetRows-1,g["!ref"]=tw(S)),C.length&&(g["!merges"]=C),x.length&&(g["!rows"]=x),s.name=s["名称"]||s.name,"undefined"!=typeof JSON&&JSON.stringify(s),m.push(s.name),p[s.name]=g,W=!1):"/"!==c[0].charAt(c[0].length-2)&&(s=rh(c[0],!1),w=A=-1,S.s.r=S.s.c=1e7,S.e.r=S.e.c=0,g=t.dense?[]:{},C=[],x=[],W=!0);break;case"table-row-group":"/"===c[1]?--k:++k;break;case"table-row":case"行":if("/"===c[1]){w+=_,_=1;break}if((i=rh(c[0],!1))["行号"]?w=i["行号"]-1:-1==w&&(w=0),(_=+i["number-rows-repeated"]||1)<10)for(V=0;V<_;++V)k>0&&(x[w+V]={level:k});A=-1;break;case"covered-table-cell":"/"!==c[1]&&++A,t.sheetStubs&&(t.dense?(g[w]||(g[w]=[]),g[w][A]={t:"z"}):g[tb({r:w,c:A})]={t:"z"}),T="",E=[];break;case"table-cell":case"数据":if("/"===c[0].charAt(c[0].length-2))++A,I=parseInt((v=rh(c[0],!1))["number-columns-repeated"]||"1",10),o={t:"z",v:null},v.formula&&!1!=t.cellFormula&&(o.f=nY(rm(v.formula))),"string"==(v["数据类型"]||v["value-type"])&&(o.t="s",o.v=rm(v["string-value"]||""),t.dense?(g[w]||(g[w]=[]),g[w][A]=o):g[tb({r:w,c:A})]=o),A+=I-1;else if("/"!==c[1]){T="",b=0,E=[],I=1;var G=_?w+_-1:w;if(++A>S.e.c&&(S.e.c=A),A<S.s.c&&(S.s.c=A),w<S.s.r&&(S.s.r=w),G>S.e.r&&(S.e.r=G),v=rh(c[0],!1),L=[],M={},o={t:v["数据类型"]||v["value-type"],v:null},t.cellFormula)if(v.formula&&(v.formula=rm(v.formula)),v["number-matrix-columns-spanned"]&&v["number-matrix-rows-spanned"]&&(R={s:{r:w,c:A},e:{r:w+(O=parseInt(v["number-matrix-rows-spanned"],10)||0)-1,c:A+(parseInt(v["number-matrix-columns-spanned"],10)||0)-1}},o.F=tw(R),N.push([R,o.F])),v.formula)o.f=nY(v.formula);else for(V=0;V<N.length;++V)w>=N[V][0].s.r&&w<=N[V][0].e.r&&A>=N[V][0].s.c&&A<=N[V][0].e.c&&(o.F=N[V][1]);switch((v["number-columns-spanned"]||v["number-rows-spanned"])&&(R={s:{r:w,c:A},e:{r:w+(O=parseInt(v["number-rows-spanned"],10)||0)-1,c:A+(parseInt(v["number-columns-spanned"],10)||0)-1}},C.push(R)),v["number-columns-repeated"]&&(I=parseInt(v["number-columns-repeated"],10)),o.t){case"boolean":o.t="b",o.v=rE(v["boolean-value"]);break;case"float":case"percentage":case"currency":o.t="n",o.v=parseFloat(v.value);break;case"date":o.t="d",o.v=eZ(v["date-value"]),t.cellDates||(o.t="n",o.v=ez(o.v)),o.z="m/d/yy";break;case"time":o.t="n",o.v=function(e){var r=0,t=0,a=!1,n=e.match(/P([0-9\.]+Y)?([0-9\.]+M)?([0-9\.]+D)?T([0-9\.]+H)?([0-9\.]+M)?([0-9\.]+S)?/);if(!n)throw Error("|"+e+"| is not an ISO8601 Duration");for(var s=1;s!=n.length;++s)if(n[s]){switch(t=1,s>3&&(a=!0),n[s].slice(n[s].length-1)){case"Y":throw Error("Unsupported ISO Duration Field: "+n[s].slice(n[s].length-1));case"D":t*=24;case"H":t*=60;case"M":if(a)t*=60;else throw Error("Unsupported ISO Duration Field: M")}r+=t*parseInt(n[s],10)}return r}(v["time-value"])/86400,t.cellDates&&(o.t="d",o.v=eK(o.v)),o.z="HH:MM:SS";break;case"number":o.t="n",o.v=parseFloat(v["数据数值"]);break;default:if("string"!==o.t&&"text"!==o.t&&o.t)throw Error("Unsupported value type "+o.t);o.t="s",null!=v["string-value"]&&(T=rm(v["string-value"]),E=[])}}else{if(H=!1,"s"===o.t&&(o.v=T||"",E.length&&(o.R=E),H=0==b),F.Target&&(o.l=F),L.length>0&&(o.c=L,L=[]),T&&!1!==t.cellText&&(o.w=T),H&&(o.t="z",delete o.v),(!H||t.sheetStubs)&&!(t.sheetRows&&t.sheetRows<=w))for(var z=0;z<_;++z){if(I=parseInt(v["number-columns-repeated"]||"1",10),t.dense)for(g[w+z]||(g[w+z]=[]),g[w+z][A]=0==z?o:e1(o);--I>0;)g[w+z][A+I]=e1(o);else for(g[tb({r:w+z,c:A})]=o;--I>0;)g[tb({r:w+z,c:A+I})]=e1(o);S.e.c<=A&&(S.e.c=A)}A+=(I=parseInt(v["number-columns-repeated"]||"1",10))-1,I=0,o={},T="",E=[]}F={};break;case"document":case"document-content":case"电子表格文档":case"spreadsheet":case"主体":case"scripts":case"styles":case"font-face-decls":case"master-styles":if("/"===c[1]){if((a=f.pop())[0]!==c[3])throw"Bad state: "+a}else"/"!==c[0].charAt(c[0].length-2)&&f.push([c[3],!0]);break;case"annotation":if("/"===c[1]){if((a=f.pop())[0]!==c[3])throw"Bad state: "+a;M.t=T,E.length&&(M.R=E),M.a=U,L.push(M)}else"/"!==c[0].charAt(c[0].length-2)&&f.push([c[3],!1]);U="",B=0,T="",b=0,E=[];break;case"creator":"/"===c[1]?U=l.slice(B,c.index):B=c.index+c[0].length;break;case"meta":case"元数据":case"settings":case"config-item-set":case"config-item-map-indexed":case"config-item-map-entry":case"config-item-map-named":case"shapes":case"frame":case"text-box":case"image":case"data-pilot-tables":case"list-style":case"form":case"dde-links":case"event-listeners":case"chart":if("/"===c[1]){if((a=f.pop())[0]!==c[3])throw"Bad state: "+a}else"/"!==c[0].charAt(c[0].length-2)&&f.push([c[3],!1]);T="",b=0,E=[];break;case"scientific-number":case"currency-symbol":case"currency-style":case"script":case"libraries":case"automatic-styles":case"default-style":case"page-layout":case"style":case"map":case"font-face":case"paragraph-properties":case"table-properties":case"table-column-properties":case"table-row-properties":case"table-cell-properties":case"fraction":case"boolean-style":case"boolean":case"text-style":case"text-content":case"text-properties":case"embedded-text":case"body":case"电子表格":case"forms":case"table-column":case"table-header-rows":case"table-rows":case"table-column-group":case"table-header-columns":case"table-columns":case"null-date":case"graphic-properties":case"calculation-settings":case"named-expressions":case"label-range":case"label-ranges":case"named-expression":case"sort":case"sort-by":case"sort-groups":case"tab":case"line-break":case"span":case"s":case"date":case"object":case"title":case"标题":case"desc":case"binary-data":case"table-source":case"scenario":case"iteration":case"content-validations":case"content-validation":case"help-message":case"error-message":case"database-ranges":case"filter":case"filter-and":case"filter-or":case"filter-condition":case"list-level-style-bullet":case"list-level-style-number":case"list-level-properties":case"sender-firstname":case"sender-lastname":case"sender-initials":case"sender-title":case"sender-position":case"sender-email":case"sender-phone-private":case"sender-fax":case"sender-company":case"sender-phone-work":case"sender-street":case"sender-city":case"sender-postal-code":case"sender-country":case"sender-state-or-province":case"author-name":case"author-initials":case"chapter":case"file-name":case"template-name":case"sheet-name":case"event-listener":case"initial-creator":case"creation-date":case"print-date":case"generator":case"document-statistic":case"user-defined":case"editing-duration":case"editing-cycles":case"config-item":case"page-number":case"page-count":case"time":case"cell-range-source":case"detective":case"operation":case"highlighted-range":case"data-pilot-table":case"source-cell-range":case"source-service":case"data-pilot-field":case"data-pilot-level":case"data-pilot-subtotals":case"data-pilot-subtotal":case"data-pilot-members":case"data-pilot-member":case"data-pilot-display-info":case"data-pilot-sort-info":case"data-pilot-layout-info":case"data-pilot-field-reference":case"data-pilot-groups":case"data-pilot-group":case"data-pilot-group-member":case"rect":case"dde-connection-decls":case"dde-connection-decl":case"dde-link":case"dde-source":case"properties":case"property":case"table-protection":case"data-pilot-grand-total":case"office-document-common-attrs":break;case"number-style":case"percentage-style":case"date-style":case"time-style":if("/"===c[1]){if(y[h.name]=u,(a=f.pop())[0]!==c[3])throw"Bad state: "+a}else"/"!==c[0].charAt(c[0].length-2)&&(u="",h=rh(c[0],!1),f.push([c[3],!0]));break;case"number":case"day":case"month":case"year":case"era":case"day-of-week":case"week-of-year":case"quarter":case"hours":case"minutes":case"seconds":case"am-pm":switch(f[f.length-1][0]){case"time-style":case"date-style":n=rh(c[0],!1),u+=sR[c[3]][+("long"===n.style)]}break;case"text":if("/>"===c[0].slice(-2));else if("/"===c[1])switch(f[f.length-1][0]){case"number-style":case"date-style":case"time-style":u+=l.slice(d,c.index)}else d=c.index+c[0].length;break;case"named-range":P=nj((n=rh(c[0],!1))["cell-range-address"]);var Y={Name:n.name,Ref:P[0]+"!"+P[1]};W&&(Y.Sheet=m.length),D.Names.push(Y);break;case"p":case"文本串":if(["master-styles"].indexOf(f[f.length-1][0])>-1)break;if("/"!==c[1]||v&&v["string-value"])rh(c[0],!1),b=c.index+c[0].length;else{var j=[rm(l.slice(b,c.index).replace(/[\t\r\n]/g," ").trim().replace(/ +/g," ").replace(/<text:s\/>/g," ").replace(/<text:s text:c="(\d+)"\/>/g,function(e,r){return Array(parseInt(r,10)+1).join(" ")}).replace(/<text:tab[^>]*\/>/g,"	").replace(/<text:line-break\/>/g,"\n").replace(/<[^>]*>/g,""))];T=(T.length>0?T+"\n":"")+j[0]}break;case"database-range":if("/"===c[1])break;try{p[(P=nj(rh(c[0])["target-range-address"]))[0]]["!autofilter"]={ref:P[1]}}catch(e){}break;case"a":if("/"!==c[1]){if(!(F=rh(c[0],!1)).href)break;F.Target=rm(F.href),delete F.href,"#"==F.Target.charAt(0)&&F.Target.indexOf(".")>-1?(P=nj(F.Target.slice(1)),F.Target="#"+P[0]+"!"+P[1]):F.Target.match(/^\.\.[\\\/]/)&&(F.Target=F.Target.slice(3))}break;default:switch(c[2]){case"dc:":case"calcext:":case"loext:":case"ooo:":case"chartooo:":case"draw:":case"style:":case"chart:":case"form:":case"uof:":case"表:":case"字:":break;default:if(t.WTF)throw Error(c)}}var K={Sheets:p,SheetNames:m,Workbook:D};return t.bookSheets&&delete K.Sheets,K}function sx(e){return new DataView(e.buffer,e.byteOffset,e.byteLength)}function s_(e){return"undefined"!=typeof TextDecoder?new TextDecoder().decode(e):ry(X(e))}function sI(e){var r=new Uint8Array(e.reduce(function(e,r){return e+r.length},0)),t=0;return e.forEach(function(e){r.set(e,t),t+=e.length}),r}function sN(e){return e-=e>>1&0x55555555,((e=(0x33333333&e)+(e>>2&0x33333333))+(e>>4)&0xf0f0f0f)*0x1010101>>>24}function sD(e,r){var t=r?r[0]:0,a=127&e[t];t:if(e[t++]>=128&&(a|=(127&e[t])<<7,e[t++]<128||(a|=(127&e[t])<<14,e[t++]<128)||(a|=(127&e[t])<<21,e[t++]<128)||(a+=(127&e[t])*0x10000000,++t,e[t++]<128)||(a+=(127&e[t])*0x800000000,++t,e[t++]<128)||(a+=(127&e[t])*0x40000000000,++t,e[t++]<128)))break t;return r&&(r[0]=t),a}function sF(e){var r=0,t=127&e[0];t:if(e[r++]>=128){if(t|=(127&e[r])<<7,e[r++]<128||(t|=(127&e[r])<<14,e[r++]<128)||(t|=(127&e[r])<<21,e[r++]<128))break t;t|=(127&e[r])<<28}return t}function sP(e){for(var r=[],t=[0];t[0]<e.length;){var a,n=t[0],s=sD(e,t),i=7&s,c=0;if(0==(s=Math.floor(s/8)))break;switch(i){case 0:for(var o=t[0];e[t[0]++]>=128;);a=e.slice(o,t[0]);break;case 5:c=4,a=e.slice(t[0],t[0]+c),t[0]+=c;break;case 1:c=8,a=e.slice(t[0],t[0]+c),t[0]+=c;break;case 2:c=sD(e,t),a=e.slice(t[0],t[0]+c),t[0]+=c;break;default:throw Error("PB Type ".concat(i," for Field ").concat(s," at offset ").concat(n))}var l={data:a,type:i};null==r[s]?r[s]=[l]:r[s].push(l)}return r}function sL(e,r){return(null==e?void 0:e.map(function(e){return r(e.data)}))||[]}function sM(e){return sD(sP(e)[1][0].data)}function sU(e,r){var t=sP(r.data),a=sF(t[1][0].data),n=t[3],s=[];return(n||[]).forEach(function(r){var t=sP(r.data),n=sF(t[1][0].data)>>>0;switch(a){case 1:s[n]=s_(t[3][0].data);break;case 8:var i=sP(e[sM(t[9][0].data)][0].data),c=e[sM(i[1][0].data)][0],o=sF(c.meta[1][0].data);if(2001!=o)throw Error("2000 unexpected reference to ".concat(o));var l=sP(c.data);s[n]=l[3].map(function(e){return s_(e.data)}).join("")}}),s}function sB(e){var r,t,a,n,s={},i=[];if(e.FullPaths.forEach(function(e){if(e.match(/\.iwpv2/))throw Error("Unsupported password protection")}),e.FileIndex.forEach(function(e){var r,t;if(e.name.match(/\.iwa$/)){try{r=function(e){for(var r=[],t=0;t<e.length;){var a=e[t++],n=e[t]|e[t+1]<<8|e[t+2]<<16;t+=3,r.push(function(e,r){if(0!=e)throw Error("Unexpected Snappy chunk type ".concat(e));for(var t=[0],a=sD(r,t),n=[];t[0]<r.length;){var s=3&r[t[0]];if(0==s){var i=r[t[0]++]>>2;if(i<60)++i;else{var c=i-59;i=r[t[0]],c>1&&(i|=r[t[0]+1]<<8),c>2&&(i|=r[t[0]+2]<<16),c>3&&(i|=r[t[0]+3]<<24),i>>>=0,i++,t[0]+=c}n.push(r.slice(t[0],t[0]+i)),t[0]+=i;continue}var o=0,l=0;if(1==s?(l=(r[t[0]]>>2&7)+4,o=(224&r[t[0]++])<<3|r[t[0]++]):(l=(r[t[0]++]>>2)+1,2==s?(o=r[t[0]]|r[t[0]+1]<<8,t[0]+=2):(o=(r[t[0]]|r[t[0]+1]<<8|r[t[0]+2]<<16|r[t[0]+3]<<24)>>>0,t[0]+=4)),n=[sI(n)],0==o)throw Error("Invalid offset 0");if(o>n[0].length)throw Error("Invalid offset beyond length");if(l>=o)for(n.push(n[0].slice(-o)),l-=o;l>=n[n.length-1].length;)n.push(n[n.length-1]),l-=n[n.length-1].length;n.push(n[0].slice(-o,-o+l))}var f=sI(n);if(f.length!=a)throw Error("Unexpected length: ".concat(f.length," != ").concat(a));return f}(a,e.slice(t,t+n))),t+=n}if(t!==e.length)throw Error("data is not a valid framed stream!");return sI(r)}(e.content)}catch(r){return console.log("?? "+e.content.length+" "+(r.message||r))}try{t=function(e){for(var r,t=[],a=[0];a[0]<e.length;){var n=sD(e,a),s=sP(e.slice(a[0],a[0]+n));a[0]+=n;var i={id:sF(s[1][0].data),messages:[]};s[2].forEach(function(r){var t=sP(r.data),n=sF(t[3][0].data);i.messages.push({meta:t,data:e.slice(a[0],a[0]+n)}),a[0]+=n}),(null==(r=s[3])?void 0:r[0])&&(i.merge=sF(s[3][0].data)>>>0>0),t.push(i)}return t}(r)}catch(e){return console.log("## "+(e.message||e))}t.forEach(function(e){s[e.id]=e.messages,i.push(e.id)})}}),!i.length)throw Error("File has no messages");var c=(null==(n=null==(a=null==(t=null==(r=null==s?void 0:s[1])?void 0:r[0])?void 0:t.meta)?void 0:a[1])?void 0:n[0].data)&&1==sF(s[1][0].meta[1][0].data)&&s[1][0];if(c||i.forEach(function(e){s[e].forEach(function(e){if(1==sF(e.meta[1][0].data)>>>0)if(c)throw Error("Document has multiple roots");else c=e})}),!c)throw Error("Cannot find Document root");var o=c,l=sY();if(sL(sP(o.data)[1],sM).forEach(function(e){s[e].forEach(function(e){if(2==sF(e.meta[1][0].data)){var r,t,a,n=(a={name:(null==(r=(t=sP(e.data))[1])?void 0:r[0])?s_(t[1][0].data):"",sheets:[]},sL(t[2],sM).forEach(function(e){s[e].forEach(function(e){6e3==sF(e.meta[1][0].data)&&a.sheets.push(function(e,r){var t=sP(r.data),a={"!ref":"A1"},n=e[sM(t[2][0].data)],s=sF(n[0].meta[1][0].data);if(6001!=s)throw Error("6000 unexpected reference to ".concat(s));return!function(e,r,t){var a,n=sP(r.data),s={s:{r:0,c:0},e:{r:0,c:0}};if(s.e.r=(sF(n[6][0].data)>>>0)-1,s.e.r<0)throw Error("Invalid row varint ".concat(n[6][0].data));if(s.e.c=(sF(n[7][0].data)>>>0)-1,s.e.c<0)throw Error("Invalid col varint ".concat(n[7][0].data));t["!ref"]=tw(s);var i=sP(n[4][0].data),c=sU(e,e[sM(i[4][0].data)][0]),o=(null==(a=i[17])?void 0:a[0])?sU(e,e[sM(i[17][0].data)][0]):[],l=sP(i[3][0].data),f=0;l[1].forEach(function(r){var a,n,s,i,l=e[sM(sP(r.data)[2][0].data)][0],h=sF(l.meta[1][0].data);if(6002!=h)throw Error("6001 unexpected reference to ".concat(h));var u=(s=(null==(a=null==(n=sP(l.data))?void 0:n[7])?void 0:a[0])?+(sF(n[7][0].data)>>>0>0):-1,i=sL(n[5],function(e){return function(e,r){var t,a,n,s,i,c,o,l,f,h,u,d,p,m,g,v,T=sP(e),b=sF(T[1][0].data)>>>0,E=sF(T[2][0].data)>>>0,w=(null==(a=null==(t=T[8])?void 0:t[0])?void 0:a.data)&&sF(T[8][0].data)>0||!1;if((null==(s=null==(n=T[7])?void 0:n[0])?void 0:s.data)&&0!=r)g=null==(c=null==(i=T[7])?void 0:i[0])?void 0:c.data,v=null==(l=null==(o=T[6])?void 0:o[0])?void 0:l.data;else if((null==(h=null==(f=T[4])?void 0:f[0])?void 0:h.data)&&1!=r)g=null==(d=null==(u=T[4])?void 0:u[0])?void 0:d.data,v=null==(m=null==(p=T[3])?void 0:p[0])?void 0:m.data;else throw"NUMBERS Tile missing ".concat(r," cell storage");for(var A=w?4:1,S=sx(g),k=[],y=0;y<g.length/2;++y){var C=S.getUint16(2*y,!0);C<65535&&k.push([y,C])}if(k.length!=E)throw"Expected ".concat(E," cells, found ").concat(k.length);var R=[];for(y=0;y<k.length-1;++y)R[k[y][0]]=v.subarray(k[y][1]*A,k[y+1][1]*A);return k.length>=1&&(R[k[k.length-1][0]]=v.subarray(k[k.length-1][1]*A)),{R:b,cells:R}}(e,s)}),{nrows:sF(n[4][0].data)>>>0,data:i.reduce(function(e,r){return e[r.R]||(e[r.R]=[]),r.cells.forEach(function(t,a){if(e[r.R][a])throw Error("Duplicate cell r=".concat(r.R," c=").concat(a));e[r.R][a]=t}),e},[])});u.data.forEach(function(e,r){e.forEach(function(e,a){var n=tb({r:f+r,c:a}),s=function(e,r,t){switch(e[0]){case 0:case 1:case 2:case 3:return function(e,r,t,a){var n,s=sx(e),i=s.getUint32(4,!0),c=(a>1?12:8)+4*sN(i&(a>1?3470:398)),o=-1,l=-1,f=NaN,h=new Date(2001,0,1);switch(512&i&&(o=s.getUint32(c,!0),c+=4),c+=4*sN(i&(a>1?12288:4096)),16&i&&(l=s.getUint32(c,!0),c+=4),32&i&&(f=s.getFloat64(c,!0),c+=8),64&i&&(h.setTime(h.getTime()+1e3*s.getFloat64(c,!0)),c+=8),e[2]){case 0:break;case 2:n={t:"n",v:f};break;case 3:n={t:"s",v:r[l]};break;case 5:n={t:"d",v:h};break;case 6:n={t:"b",v:f>0};break;case 7:n={t:"n",v:f/86400};break;case 8:n={t:"e",v:0};break;case 9:if(o>-1)n={t:"s",v:t[o]};else if(l>-1)n={t:"s",v:r[l]};else if(isNaN(f))throw Error("Unsupported cell type ".concat(e.slice(0,4)));else n={t:"n",v:f};break;default:throw Error("Unsupported cell type ".concat(e.slice(0,4)))}return n}(e,r,t,e[0]);case 5:return function(e,r,t){var a,n=sx(e),s=n.getUint32(8,!0),i=12,c=-1,o=-1,l=NaN,f=NaN,h=new Date(2001,0,1);switch(1&s&&(l=function(e,r){for(var t=(127&e[r+15])<<7|e[r+14]>>1,a=1&e[r+14],n=r+13;n>=r;--n)a=256*a+e[n];return(128&e[r+15]?-a:a)*Math.pow(10,t-6176)}(e,i),i+=16),2&s&&(f=n.getFloat64(i,!0),i+=8),4&s&&(h.setTime(h.getTime()+1e3*n.getFloat64(i,!0)),i+=8),8&s&&(o=n.getUint32(i,!0),i+=4),16&s&&(c=n.getUint32(i,!0),i+=4),e[1]){case 0:break;case 2:case 10:a={t:"n",v:l};break;case 3:a={t:"s",v:r[o]};break;case 5:a={t:"d",v:h};break;case 6:a={t:"b",v:f>0};break;case 7:a={t:"n",v:f/86400};break;case 8:a={t:"e",v:0};break;case 9:if(c>-1)a={t:"s",v:t[c]};else throw Error("Unsupported cell type ".concat(e[1]," : ").concat(31&s," : ").concat(e.slice(0,4)));break;default:throw Error("Unsupported cell type ".concat(e[1]," : ").concat(31&s," : ").concat(e.slice(0,4)))}return a}(e,r,t);default:throw Error("Unsupported payload version ".concat(e[0]))}}(e,c,o);s&&(t[n]=s)})}),f+=u.nrows})}(e,n[0],a),a}(s,e))})}),a);n.sheets.forEach(function(e,r){sj(l,e,0==r?n.name:n.name+"_"+r,!0)})}})}),0==l.SheetNames.length)throw Error("Empty NUMBERS file");return l}function sH(e){var r;(r=[["cellNF",!1],["cellHTML",!0],["cellFormula",!0],["cellStyles",!1],["cellText",!0],["cellDates",!1],["sheetStubs",!1],["sheetRows",0,"n"],["bookDeps",!1],["bookSheets",!1],["bookProps",!1],["bookFiles",!1],["bookVBA",!1],["password",""],["WTF",!1]],function(e){for(var t=0;t!=r.length;++t){var a=r[t];void 0===e[a[0]]&&(e[a[0]]=a[1]),"n"===a[2]&&(e[a[0]]=Number(e[a[0]]))}})(e)}function sW(e){return"/"==e.charAt(0)?e.slice(1):e}function sV(e,r){var t="";switch((r||{}).type||"base64"){case"buffer":case"array":return[e[0],e[1],e[2],e[3],e[4],e[5],e[6],e[7]];case"base64":t=V(e.slice(0,12));break;case"binary":t=e;break;default:throw Error("Unrecognized type "+(r&&r.type||"undefined"))}return[t.charCodeAt(0),t.charCodeAt(1),t.charCodeAt(2),t.charCodeAt(3),t.charCodeAt(4),t.charCodeAt(5),t.charCodeAt(6),t.charCodeAt(7)]}function sG(e,r){var t=0;a:for(;t<e.length;)switch(e.charCodeAt(t)){case 10:case 13:case 32:++t;break;case 60:return sv(e.slice(t),r);default:break a}return aL.to_workbook(e,r)}function sz(e,r,t,a){return a?(t.type="string",aL.to_workbook(e,t)):aL.to_workbook(r,t)}function s$(e,r){if(null==e||null==e["!ref"])return[];var t={t:"n",v:0},a=0,n=1,s=[],i=0,c="",o={s:{r:0,c:0},e:{r:0,c:0}},l=r||{},f=null!=l.range?l.range:e["!ref"];switch(1===l.header?a=1:"A"===l.header?a=2:Array.isArray(l.header)?a=3:null==l.header&&(a=0),typeof f){case"string":o=tA(f);break;case"number":(o=tA(e["!ref"])).s.r=f;break;default:o=f}a>0&&(n=0);var h=tm(o.s.r),u=[],d=[],p=0,m=0,g=Array.isArray(e),v=o.s.r,T=0,b={};g&&!e[v]&&(e[v]=[]);var E=l.skipHidden&&e["!cols"]||[],w=l.skipHidden&&e["!rows"]||[];for(T=o.s.c;T<=o.e.c;++T)if(!(E[T]||{}).hidden)switch(u[T]=tv(T),t=g?e[v][T]:e[u[T]+h],a){case 1:s[T]=T-o.s.c;break;case 2:s[T]=u[T];break;case 3:s[T]=l.header[T-o.s.c];break;default:if(null==t&&(t={w:"__EMPTY",t:"s"}),c=i=tk(t,null,l),m=b[i]||0){do c=i+"_"+m++;while(b[c])b[i]=m,b[c]=1}else b[i]=1;s[T]=c}for(v=o.s.r+n;v<=o.e.r;++v)if(!(w[v]||{}).hidden){var A=function(e,r,t,a,n,s,i,c){var o=tm(t),l=c.defval,f=c.raw||!Object.prototype.hasOwnProperty.call(c,"raw"),h=!0,u=1===n?[]:{};if(1!==n)if(Object.defineProperty)try{Object.defineProperty(u,"__rowNum__",{value:t,enumerable:!1})}catch(e){u.__rowNum__=t}else u.__rowNum__=t;if(!i||e[t])for(var d=r.s.c;d<=r.e.c;++d){var p=i?e[t][d]:e[a[d]+o];if(void 0===p||void 0===p.t){if(void 0===l)continue;null!=s[d]&&(u[s[d]]=l);continue}var m=p.v;switch(p.t){case"z":if(null==m)break;continue;case"e":m=0==m?null:void 0;break;case"s":case"d":case"b":case"n":break;default:throw Error("unrecognized type "+p.t)}if(null!=s[d]){if(null==m)if("e"==p.t&&null===m)u[s[d]]=null;else if(void 0!==l)u[s[d]]=l;else{if(!f||null!==m)continue;u[s[d]]=null}else u[s[d]]=f&&("n"!==p.t||"n"===p.t&&!1!==c.rawNumbers)?m:tk(p,m,c);null!=m&&(h=!1)}}return{row:u,isempty:h}}(e,o,v,u,a,s,g,l);(!1===A.isempty||(1===a?!1!==l.blankrows:l.blankrows))&&(d[p++]=A.row)}return d.length=p,d}function sY(){return{SheetNames:[],Sheets:{}}}function sj(e,r,t,a){var n=1;if(!t)for(;n<=65535&&-1!=e.SheetNames.indexOf(t="Sheet"+n);++n,t=void 0);if(!t||e.SheetNames.length>=65535)throw Error("Too many worksheets");if(a&&e.SheetNames.indexOf(t)>=0){var s=t.match(/(^.*?)(\d+)$/);n=s&&+s[2]||0;var i=s&&s[1]||t;for(++n;n<=65535&&-1!=e.SheetNames.indexOf(t=i+n);++n);}if(!function(e,r){if(e.length>31)throw Error("Sheet names cannot exceed 31 chars");sf.forEach(function(r){if(-1!=e.indexOf(r)&&!0)throw Error("Sheet name cannot contain : \\ / ? * [ ]")})}(t),e.SheetNames.indexOf(t)>=0)throw Error("Worksheet with name |"+t+"| already exists!");return e.SheetNames.push(t),e.Sheets[t]=r,t}var sK={sheet_to_json:s$};async function sX(e){try{let r=await (0,A.getServerSession)(S.authOptions);if(!r?.user)return w.NextResponse.json({error:"Unauthorized"},{status:401});if(!(0,y.hasPermission)(r.user.role,"students:read"))return w.NextResponse.json({error:"Forbidden"},{status:403});let{searchParams:t}=new URL(e.url),a=t.get("classId")||"",n={};a&&(n.currentClassId=a);let s=(await k.prisma.student.findMany({where:n,include:{user:!0,currentClass:!0,currentSection:!0},orderBy:[{user:{lastName:"asc"}},{user:{firstName:"asc"}}]})).map(e=>[e.id,e.user?.firstName||"",e.user?.lastName||"",e.user?.email||"",e.dob.toISOString().split("T")[0],e.gender,e.user?.phone||"",e.address||"",e.guardianName||"",e.guardianPhone||"",e.createdAt.toISOString().split("T")[0],e.currentClass?.name||"",e.currentSection?.name||"",e.guardianName||"",e.guardianPhone||"",""]),i=["ID,First Name,Last Name,Email,Date of Birth,Gender,Phone Number,Address,Emergency Contact,Emergency Phone,Admission Date,Class,Section,Parent Name,Parent Phone,Parent Email",...s.map(e=>e.map(e=>`"${e}"`).join(","))].join("\n");return new w.NextResponse(i,{headers:{"Content-Type":"text/csv","Content-Disposition":`attachment; filename="students-${new Date().toISOString().split("T")[0]}.csv"`}})}catch(e){return console.error("Error exporting students:",e),w.NextResponse.json({error:"Internal server error"},{status:500})}}async function sq(e){try{let a=await (0,A.getServerSession)(S.authOptions);if(!a?.user)return w.NextResponse.json({error:"Unauthorized"},{status:401});if(!(0,y.hasPermission)(a.user.role,"students:write"))return w.NextResponse.json({error:"Forbidden"},{status:403});let n=(await e.formData()).get("file");if(!n)return w.NextResponse.json({error:"No file provided"},{status:400});let s=n.name.endsWith(".xlsx")||n.name.endsWith(".xls"),i=n.name.endsWith(".csv");if(!s&&!i)return w.NextResponse.json({error:"Only CSV and Excel files (.csv, .xlsx, .xls) are allowed"},{status:400});let c=[];if(s){let e=await n.arrayBuffer(),a=function e(a,n){F();var s,i,c,o,l,f,h,u=n||{};if("undefined"!=typeof ArrayBuffer&&a instanceof ArrayBuffer)return e(new Uint8Array(a),((u=e1(u)).type="array",u));"undefined"!=typeof Uint8Array&&a instanceof Uint8Array&&!u.type&&(u.type="undefined"!=typeof Deno?"buffer":"array");var d=a,p=[0,0,0,0],m=!1;if(u.cellStyles&&(u.cellNF=!0,u.sheetStubs=!0),nX={},u.dateNF&&(nX.dateNF=u.dateNF),u.type||(u.type=G&&Buffer.isBuffer(a)?"buffer":"base64"),"file"==u.type&&(u.type=G?"buffer":"binary",d=function(e){if(void 0!==r)return r.readFileSync(e);if("undefined"!=typeof Deno)return Deno.readFileSync(e);if("undefined"!=typeof $&&"undefined"!=typeof File&&"undefined"!=typeof Folder)try{var t=File(e);t.open("r"),t.encoding="binary";var a=t.read();return t.close(),a}catch(e){if(!e.message||!e.message.match(/onstruct/))throw e}throw Error("Cannot access file "+e)}(a),"undefined"==typeof Uint8Array||G||(u.type="array")),"string"==u.type&&(m=!0,u.type="binary",u.codepage=65001,d=a.match(/[^\x00-\x7F]/)?rC(a):a),"array"==u.type&&"undefined"!=typeof Uint8Array&&a instanceof Uint8Array&&"undefined"!=typeof ArrayBuffer){var g=new Uint8Array(new ArrayBuffer(3));if(g.foo="bar",!g.foo)return(u=e1(u)).type="array",e(q(d),u)}switch((p=sV(d,u))[0]){case 208:if(207===p[1]&&17===p[2]&&224===p[3]&&161===p[4]&&177===p[5]&&26===p[6]&&225===p[7])return c=eH.read(d,u),o=u,eH.find(c,"EncryptedPackage")?function(e,r){var t=r||{},a="Workbook",n=eH.find(e,a);try{if(a="/!DataSpaces/Version",!(n=eH.find(e,a))||!n.content||(s=n.content,(i={}).id=s.read_shift(0,"lpp4"),i.R=aK(s,4),i.U=aK(s,4),i.W=aK(s,4),a="/!DataSpaces/DataSpaceMap",!(n=eH.find(e,a))||!n.content))throw Error("ECMA-376 Encrypted file missing "+a);var s,i,c=function(e){var r=[];e.l+=4;for(var t=e.read_shift(4);t-- >0;)r.push(function(e){for(var r=e.read_shift(4),t=e.l+r-4,a={},n=e.read_shift(4),s=[];n-- >0;)s.push({t:e.read_shift(4),v:e.read_shift(0,"lpp4")});if(a.name=e.read_shift(0,"lpp4"),a.comps=s,e.l!=t)throw Error("Bad DataSpaceMapEntry: "+e.l+" != "+t);return a}(e));return r}(n.content);if(1!==c.length||1!==c[0].comps.length||0!==c[0].comps[0].t||"StrongEncryptionDataSpace"!==c[0].name||"EncryptedPackage"!==c[0].comps[0].v)throw Error("ECMA-376 Encrypted file bad "+a);if(a="/!DataSpaces/DataSpaceInfo/StrongEncryptionDataSpace",!(n=eH.find(e,a))||!n.content)throw Error("ECMA-376 Encrypted file missing "+a);var o=function(e){var r=[];e.l+=4;for(var t=e.read_shift(4);t-- >0;)r.push(e.read_shift(0,"lpp4"));return r}(n.content);if(1!=o.length||"StrongEncryptionTransform"!=o[0])throw Error("ECMA-376 Encrypted file bad "+a);if(a="/!DataSpaces/TransformInfo/StrongEncryptionTransform/!Primary",!(n=eH.find(e,a))||!n.content)throw Error("ECMA-376 Encrypted file missing "+a);!function(e){var r,t=(r={},e.read_shift(4),e.l+=4,r.id=e.read_shift(0,"lpp4"),r.name=e.read_shift(0,"lpp4"),r.R=aK(e,4),r.U=aK(e,4),r.W=aK(e,4),r);if(t.ename=e.read_shift(0,"8lpp4"),t.blksz=e.read_shift(4),t.cmode=e.read_shift(4),4!=e.read_shift(4))throw Error("Bad !Primary record")}(n.content)}catch(e){}if(a="/EncryptionInfo",!(n=eH.find(e,a))||!n.content)throw Error("ECMA-376 Encrypted file missing "+a);var l=function(e){var r,t,a,n,s=aK(e);switch(s.Minor){case 2:return[s.Minor,function(e){if((63&e.read_shift(4))!=36)throw Error("EncryptionInfo mismatch");var r=e.read_shift(4);return{t:"Std",h:aX(e,r),v:aq(e,e.length-e.l)}}(e,s)];case 3:return[s.Minor,function(){throw Error("File is password-protected: ECMA-376 Extensible")}(e,s)];case 4:return[s.Minor,(r=e,t=["saltSize","blockSize","keyBits","hashSize","cipherAlgorithm","cipherChaining","hashAlgorithm","saltValue"],r.l+=4,a=r.read_shift(r.length-r.l,"utf8"),n={},a.replace(ro,function(e){var r=rh(e);switch(ru(r[0])){case"<?xml":case"<encryption":case"</encryption>":case"</keyEncryptors>":case"</keyEncryptor>":break;case"<keyData":t.forEach(function(e){n[e]=r[e]});break;case"<dataIntegrity":n.encryptedHmacKey=r.encryptedHmacKey,n.encryptedHmacValue=r.encryptedHmacValue;break;case"<keyEncryptors>":case"<keyEncryptors":n.encs=[];break;case"<keyEncryptor":n.uri=r.uri;break;case"<encryptedKey":n.encs.push(r);break;default:throw r[0]}}),n)]}throw Error("ECMA-376 Encrypted file unrecognized Version: "+s.Minor)}(n.content);if(a="/EncryptedPackage",!(n=eH.find(e,a))||!n.content)throw Error("ECMA-376 Encrypted file missing "+a);if(4==l[0]&&"undefined"!=typeof decrypt_agile)return decrypt_agile(l[1],n.content,t.password||"",t);if(2==l[0]&&"undefined"!=typeof decrypt_std76)return decrypt_std76(l[1],n.content,t.password||"",t);throw Error("File is password-protected")}(c,o):sA(c,o);break;case 9:if(p[1]<=8)return sA(d,u);break;case 60:return sv(d,u);case 73:if(73===p[1]&&42===p[2]&&0===p[3])throw Error("TIFF Image File is not a spreadsheet");if(68===p[1]){var v=d,T=u,b=T||{},E=!!b.WTF;b.WTF=!0;try{var w=aD.to_workbook(v,b);return b.WTF=E,w}catch(e){if(b.WTF=E,!e.message.match(/SYLK bad record ID/)&&E)throw e;return aL.to_workbook(v,T)}}break;case 84:if(65===p[1]&&66===p[2]&&76===p[3])return aF.to_workbook(d,u);break;case 80:return 75===p[1]&&p[2]<9&&p[3]<9?(s=d,(i=u||{}).type||(i.type=G&&Buffer.isBuffer(s)?"buffer":"base64"),function(e,r){if(eL(),sH(r=r||{}),e7(e,"META-INF/manifest.xml")||e7(e,"objectdata.xml")){var t=e,a=r;a=a||{},e7(t,"META-INF/manifest.xml")&&function(e,r){for(var t,a,n=rD(e);t=rF.exec(n);)switch(t[3]){case"manifest":break;case"file-entry":if("/"==(a=rh(t[0],!1)).path&&"application/vnd.oasis.opendocument.spreadsheet"!==a.type)throw Error("This OpenDocument is not a spreadsheet");break;case"encryption-data":case"algorithm":case"start-key-generation":case"key-derivation":throw Error("Unsupported ODS Encryption");default:if(r&&r.WTF)throw t}}(re(t,"META-INF/manifest.xml"),a);var n=rr(t,"content.xml");if(!n)throw Error("Missing content.xml in ODS / UOF file");var s=sO(ry(n),a);return e7(t,"meta.xml")&&(s.Props=tq(re(t,"meta.xml"))),s}if(e7(e,"Index/Document.iwa")){if("undefined"==typeof Uint8Array)throw Error("NUMBERS file parsing requires Uint8Array support");if(e.FileIndex)return sB(e);var i,c,o,l,f,h,u,d,p,m,g,v,T,b=eH.utils.cfb_new();return rt(e).forEach(function(r){!function(e,r,t){if(e.FullPaths){if("string"==typeof t){var a;return a=G?z(t):function(e){for(var r=[],t=0,a=e.length+250,n=Y(e.length+255),s=0;s<e.length;++s){var i=e.charCodeAt(s);if(i<128)n[t++]=i;else if(i<2048)n[t++]=192|i>>6&31,n[t++]=128|63&i;else if(i>=55296&&i<57344){i=(1023&i)+64;var c=1023&e.charCodeAt(++s);n[t++]=240|i>>8&7,n[t++]=128|i>>2&63,n[t++]=128|c>>6&15|(3&i)<<4,n[t++]=128|63&c}else n[t++]=224|i>>12&15,n[t++]=128|i>>6&63,n[t++]=128|63&i;t>a&&(r.push(n.slice(0,t)),t=0,n=Y(65535),a=65530)}return r.push(n.slice(0,t)),J(r)}(t),eH.utils.cfb_add(e,r,a)}eH.utils.cfb_add(e,r,t)}else e.file(r,t)}(b,r,function e(r,t,a){if(!a)return e8(e9(r,t));if(!t)return null;try{return e(r,t)}catch(e){return null}}(e,r))}),sB(b)}if(!e7(e,"[Content_Types].xml")){if(e7(e,"index.xml.gz"))throw Error("Unsupported NUMBERS 08 file");if(e7(e,"index.xml"))throw Error("Unsupported NUMBERS 09 file");throw Error("Unsupported ZIP file")}var E=rt(e),w=function(e){var r={workbooks:[],sheets:[],charts:[],dialogs:[],macros:[],rels:[],strs:[],comments:[],threadedcomments:[],links:[],coreprops:[],extprops:[],custprops:[],themes:[],styles:[],calcchains:[],vba:[],drawings:[],metadata:[],people:[],TODO:[],xmlns:""};if(!e||!e.match)return r;var t={};if((e.match(ro)||[]).forEach(function(e){var a=rh(e);switch(a[0].replace(rl,"<")){case"<?xml":break;case"<Types":r.xmlns=a["xmlns"+(a[0].match(/<(\w+):/)||["",""])[1]];break;case"<Default":t[a.Extension]=a.ContentType;break;case"<Override":void 0!==r[tz[a.ContentType]]&&r[tz[a.ContentType]].push(a.PartName)}}),r.xmlns!==rP.CT)throw Error("Unknown Namespace: "+r.xmlns);return r.calcchain=r.calcchains.length>0?r.calcchains[0]:"",r.sst=r.strs.length>0?r.strs[0]:"",r.style=r.styles.length>0?r.styles[0]:"",r.defaults=t,delete r.calcchains,r}(rr(e,"[Content_Types].xml")),A=!1;if(0===w.workbooks.length&&re(e,g="xl/workbook.xml",!0)&&w.workbooks.push(g),0===w.workbooks.length){if(!re(e,g="xl/workbook.bin",!0))throw Error("Could not find workbook");w.workbooks.push(g),A=!0}"bin"==w.workbooks[0].slice(-3)&&(A=!0);var S={},k={};if(!r.bookSheets&&!r.bookProps){if(nK=[],w.sst)try{nK=function(e,r,t){if(".bin"===r.slice(-4)){var a,n;return a=[],n=!1,to(e,function(e,r,s){switch(s){case 159:a.Count=e[0],a.Unique=e[1];break;case 19:a.push(e);break;case 160:return!0;case 35:n=!0;break;case 36:n=!1;break;default:if(r.T,!n||t.WTF)throw Error("Unexpected record 0x"+s.toString(16))}}),a}return function(e,r){var t=[],a="";if(!e)return t;var n=e.match(az);if(n){a=n[2].replace(a$,"").split(aY);for(var s=0;s!=a.length;++s){var i=aG(a[s].trim(),r);null!=i&&(t[t.length]=i)}t.Count=(n=rh(n[1])).count,t.Unique=n.uniqueCount}return t}(e,t)}(re(e,sW(w.sst)),w.sst,r)}catch(e){if(r.WTF)throw e}r.cellStyles&&w.themes.length&&(i=rr(e,w.themes[0].replace(/^\//,""),!0)||"",w.themes[0],S=nd(i,r)),w.style&&(k=function(e,r,t,a){if(".bin"===r.slice(-4)){var n={};for(var s in n.NumberFmt=[],eo)n.NumberFmt[s]=eo[s];n.CellXf=[],n.Fonts=[];var i=[],c=!1;return to(e,function(e,r,s){switch(s){case 44:n.NumberFmt[e[0]]=e[1],eP(e[1],e[0]);break;case 43:n.Fonts.push(e),null!=e.color.theme&&t&&t.themeElements&&t.themeElements.clrScheme&&(e.color.rgb=a2(t.themeElements.clrScheme[e.color.theme].rgb,e.color.tint||0));break;case 1025:case 45:case 46:case 48:case 507:case 572:case 475:case 1171:case 2102:case 1130:case 512:case 2095:case 3072:break;case 47:617==i[i.length-1]&&n.CellXf.push(e);break;case 35:c=!0;break;case 36:c=!1;break;case 37:i.push(s),c=!0;break;case 38:i.pop(),c=!1;break;default:if(r.T>0)i.push(s);else if(r.T<0)i.pop();else if(!c||a.WTF&&37!=i[i.length-1])throw Error("Unexpected record 0x"+s.toString(16))}}),n}return nn(e,t,a)}(re(e,sW(w.style)),w.style,S,r))}w.links.map(function(t){try{tj(rr(e,tY(sW(t))),t);var a=re(e,sW(t)),n=t,s=r;if(".bin"===n.slice(-4)){if(!a)return a;var i=s||{},c=!1;return void to(a,function(e,r,t){switch(t){case 359:case 363:case 364:case 366:case 367:case 368:case 369:case 370:case 371:case 472:case 577:case 578:case 579:case 580:case 581:case 582:case 583:case 584:case 585:case 586:case 587:break;case 35:c=!0;break;case 36:c=!1;break;default:if(r.T);else if(!c||i.WTF)throw Error("Unexpected record 0x"+t.toString(16))}},i)}return}catch(e){}});var y=function(e,r,t){if(".bin"===r.slice(-4)){var a,n,s,i,c,o;return n={AppVersion:{},WBProps:{},WBView:[],Sheets:[],CalcPr:{},xmlns:""},s=[],i=!1,(a=t)||(a={}),a.biff=12,c=[],(o=[[]]).SheetNames=[],o.XTI=[],sS[16]={n:"BrtFRTArchID$",f:su},to(e,function(e,r,t){switch(t){case 156:o.SheetNames.push(e.name),n.Sheets.push(e);break;case 153:n.WBProps=e;break;case 39:null!=e.Sheet&&(a.SID=e.Sheet),e.Ref=nH(e.Ptg,null,null,o,a),delete a.SID,delete e.Ptg,c.push(e);break;case 1036:case 361:case 2071:case 158:case 143:case 664:case 353:case 3072:case 3073:case 534:case 677:case 157:case 610:case 2050:case 155:case 548:case 676:case 128:case 665:case 2128:case 2125:case 549:case 2053:case 596:case 2076:case 2075:case 2082:case 397:case 154:case 1117:case 553:case 2091:case 16:break;case 357:case 358:case 355:case 667:o[0].length?o.push([t,e]):o[0]=[t,e],o[o.length-1].XTI=[];break;case 362:0===o.length&&(o[0]=[],o[0].XTI=[]),o[o.length-1].XTI=o[o.length-1].XTI.concat(e),o.XTI=o.XTI.concat(e);break;case 35:case 37:s.push(t),i=!0;break;case 36:case 38:s.pop(),i=!1;break;default:if(r.T);else if(!i||a.WTF&&37!=s[s.length-1]&&35!=s[s.length-1])throw Error("Unexpected record 0x"+t.toString(16))}},a),sl(n),n.Names=c,n.supbooks=o,n}return function(e,r){if(!e)throw Error("Could not find file");var t={AppVersion:{},WBProps:{},WBView:[],Sheets:[],CalcPr:{},Names:[],xmlns:""},a=!1,n="xmlns",s={},i=0;if(e.replace(ro,function(c,o){var l=rh(c);switch(ru(l[0])){case"<?xml":case"</workbook>":case"<fileVersion/>":case"</fileVersion>":case"<fileSharing":case"<fileSharing/>":case"</workbookPr>":case"<workbookProtection":case"<workbookProtection/>":case"<bookViews":case"<bookViews>":case"</bookViews>":case"</workbookView>":case"<sheets":case"<sheets>":case"</sheets>":case"</sheet>":case"<functionGroups":case"<functionGroups/>":case"<functionGroup":case"<externalReferences":case"</externalReferences>":case"<externalReferences>":case"<externalReference":case"<definedNames/>":case"<definedName/>":case"</calcPr>":case"<oleSize":case"<customWorkbookViews>":case"</customWorkbookViews>":case"<customWorkbookViews":case"<customWorkbookView":case"</customWorkbookView>":case"<pivotCaches>":case"</pivotCaches>":case"<pivotCaches":case"<pivotCache":case"<smartTagPr":case"<smartTagPr/>":case"<smartTagTypes":case"<smartTagTypes>":case"</smartTagTypes>":case"<smartTagType":case"<webPublishing":case"<webPublishing/>":case"<fileRecoveryPr":case"<fileRecoveryPr/>":case"<webPublishObjects>":case"<webPublishObjects":case"</webPublishObjects>":case"<webPublishObject":case"<extLst":case"<extLst>":case"</extLst>":case"<extLst/>":case"<ArchID":case"<revisionPtr":break;case"<workbook":c.match(sh)&&(n="xmlns"+c.match(/<(\w+):/)[1]),t.xmlns=l[n];break;case"<fileVersion":delete l[0],t.AppVersion=l;break;case"<workbookPr":case"<workbookPr/>":sa.forEach(function(e){if(null!=l[e[0]])switch(e[2]){case"bool":t.WBProps[e[0]]=rE(l[e[0]]);break;case"int":t.WBProps[e[0]]=parseInt(l[e[0]],10);break;default:t.WBProps[e[0]]=l[e[0]]}}),l.codeName&&(t.WBProps.CodeName=ry(l.codeName));break;case"<workbookView":case"<workbookView/>":delete l[0],t.WBView.push(l);break;case"<sheet":switch(l.state){case"hidden":l.Hidden=1;break;case"veryHidden":l.Hidden=2;break;default:l.Hidden=0}delete l.state,l.name=rm(ry(l.name)),delete l[0],t.Sheets.push(l);break;case"<definedNames>":case"<definedNames":case"<ext":case"<AlternateContent":case"<AlternateContent>":a=!0;break;case"</definedNames>":case"</ext>":case"</AlternateContent>":a=!1;break;case"<definedName":(s={}).Name=ry(l.name),l.comment&&(s.Comment=l.comment),l.localSheetId&&(s.Sheet=+l.localSheetId),rE(l.hidden||"0")&&(s.Hidden=!0),i=o+c.length;break;case"</definedName>":s.Ref=rm(ry(e.slice(i,o))),t.Names.push(s);break;case"<calcPr":case"<calcPr/>":delete l[0],t.CalcPr=l;break;default:if(!a&&r.WTF)throw Error("unrecognized "+l[0]+" in workbook")}return c}),-1===rL.indexOf(t.xmlns))throw Error("Unknown Namespace: "+t.xmlns);return sl(t),t}(e,t)}(re(e,sW(w.workbooks[0])),w.workbooks[0],r),C={},R="";w.coreprops.length&&((R=re(e,sW(w.coreprops[0]),!0))&&(C=tq(R)),0!==w.extprops.length)&&(R=re(e,sW(w.extprops[0]),!0))&&(c=R,o=C,l=r,f={},o||(o={}),c=ry(c),tJ.forEach(function(e){var r=(c.match(rR(e[0]))||[])[1];switch(e[2]){case"string":r&&(o[e[1]]=rm(r));break;case"bool":o[e[1]]="true"===r;break;case"raw":var t=c.match(RegExp("<"+e[0]+"[^>]*>([\\s\\S]*?)</"+e[0]+">"));t&&t.length>0&&(f[e[1]]=t[1])}}),f.HeadingPairs&&f.TitlesOfParts&&tZ(f.HeadingPairs,f.TitlesOfParts,o,l));var O={};(!r.bookSheets||r.bookProps)&&0!==w.custprops.length&&(R=rr(e,sW(w.custprops[0]),!0))&&(O=function(e,r){var t={},a="",n=e.match(tQ);if(n)for(var s=0;s!=n.length;++s){var i=n[s],c=rh(i);switch(c[0]){case"<?xml":case"<Properties":break;case"<property":a=rm(c.name);break;case"</property>":a=null;break;default:if(0===i.indexOf("<vt:")){var o=i.split(">"),l=o[0].slice(4),f=o[1];switch(l){case"lpstr":case"bstr":case"lpwstr":case"cy":case"error":t[a]=rm(f);break;case"bool":t[a]=rE(f);break;case"i1":case"i2":case"i4":case"i8":case"int":case"uint":t[a]=parseInt(f,10);break;case"r4":case"r8":case"decimal":t[a]=parseFloat(f);break;case"filetime":case"date":t[a]=eZ(f);break;default:if("/"==l.slice(-1))break;r.WTF&&"undefined"!=typeof console&&console.warn("Unexpected",i,l,o)}}else if("</"===i.slice(0,2));else if(r.WTF)throw Error(i)}}return t}(R,r));var x={};if((r.bookSheets||r.bookProps)&&(y.Sheets?m=y.Sheets.map(function(e){return e.name}):C.Worksheets&&C.SheetNames.length>0&&(m=C.SheetNames),r.bookProps&&(x.Props=C,x.Custprops=O),r.bookSheets&&void 0!==m&&(x.SheetNames=m),r.bookSheets?x.SheetNames:r.bookProps))return x;m={};var _={};r.bookDeps&&w.calcchain&&(_=function(e,r,t){if(".bin"===r.slice(-4)){var a;return a=[],to(e,function(e,r,t){if(63===t)a.push(e);else if(r.T);else if(1)throw Error("Unexpected record 0x"+t.toString(16))}),a}var n=[];if(!e)return n;var s=1;return(e.match(ro)||[]).forEach(function(e){var r=rh(e);switch(r[0]){case"<?xml":case"<calcChain":case"<calcChain>":case"</calcChain>":break;case"<c":delete r[0],r.i?s=r.i:r.i=s,n.push(r)}}),n}(re(e,sW(w.calcchain)),w.calcchain,0));var I=0,N={},D=y.Sheets;C.Worksheets=D.length,C.SheetNames=[];for(var F=0;F!=D.length;++F)C.SheetNames[F]=D[F].name;var P=A?"bin":"xml",L=w.workbooks[0].lastIndexOf("/"),M=(w.workbooks[0].slice(0,L+1)+"_rels/"+w.workbooks[0].slice(L+1)+".rels").replace(/^\//,"");e7(e,M)||(M="xl/_rels/workbook."+P+".rels");var U=tj(rr(e,M,!0),M.replace(/_rels.*/,"s5s"));(w.metadata||[]).length>=1&&(r.xlmeta=function(e,r,t){if(".bin"===r.slice(-4)){var a,n,s,i,c;return a={Types:[],Cell:[],Value:[]},n=t||{},s=[],i=!1,c=2,to(e,function(e,r,t){switch(t){case 335:a.Types.push({name:e.name});break;case 51:e.forEach(function(e){1==c?a.Cell.push({type:a.Types[e[0]-1].name,index:e[1]}):0==c&&a.Value.push({type:a.Types[e[0]-1].name,index:e[1]})});break;case 337:c=+!!e;break;case 338:c=2;break;case 35:s.push(t),i=!0;break;case 36:s.pop(),i=!1;break;default:if(r.T);else if(!i||n.WTF&&35!=s[s.length-1])throw Error("Unexpected record 0x"+t.toString(16))}}),a}var o,l={Types:[],Cell:[],Value:[]};if(!e)return l;var f=!1,h=2;return e.replace(ro,function(e){var r=rh(e);switch(ru(r[0])){case"<?xml":case"<metadata":case"</metadata>":case"<metadataTypes":case"</metadataTypes>":case"</metadataType>":case"</futureMetadata>":case"<bk>":case"</bk>":case"</rc>":case"<extLst":case"<extLst>":case"</extLst>":case"<extLst/>":break;case"<metadataType":l.Types.push({name:r.name});break;case"<futureMetadata":for(var a=0;a<l.Types.length;++a)l.Types[a].name==r.name&&(o=l.Types[a]);break;case"<rc":1==h?l.Cell.push({type:l.Types[r.t-1].name,index:+r.v}):0==h&&l.Value.push({type:l.Types[r.t-1].name,index:+r.v});break;case"<cellMetadata":h=1;break;case"</cellMetadata>":case"</valueMetadata>":h=2;break;case"<valueMetadata":h=0;break;case"<ext":f=!0;break;case"</ext>":f=!1;break;case"<rvb":if(!o)break;o.offsets||(o.offsets=[]),o.offsets.push(+r.i);break;default:if(!f&&t.WTF)throw Error("unrecognized "+r[0]+" in metadata")}return e}),l}(re(e,sW(w.metadata[0])),w.metadata[0],r)),(w.people||[]).length>=1&&(r.people=(h=re(e,sW(w.people[0])),u=r,d=[],p=!1,h.replace(ro,function(e){var r=rh(e);switch(ru(r[0])){case"<?xml":case"<personList":case"</personList>":case"</person>":case"<extLst":case"<extLst>":case"</extLst>":case"<extLst/>":break;case"<person":d.push({name:r.displayname,id:r.id});break;case"<ext":p=!0;break;case"</ext>":p=!1;break;default:if(!p&&u.WTF)throw Error("unrecognized "+r[0]+" in threaded comments")}return e}),d)),U&&(U=function(e,r){if(!e)return 0;try{e=r.map(function(r){var t;return r.id||(r.id=r.strRelID),[r.name,e["!id"][r.id].Target,(t=e["!id"][r.id].Type,t$.WS.indexOf(t)>-1?"sheet":t$.CS&&t==t$.CS?"chart":t$.DS&&t==t$.DS?"dialog":t$.MS&&t==t$.MS?"macro":t&&t.length?t:"sheet")]})}catch(e){return null}return e&&0!==e.length?e:null}(U,y.Sheets));var B=+!!re(e,"xl/worksheets/sheet.xml",!0);for(I=0;I!=C.Worksheets;++I){var H="sheet";if(U&&U[I]?(e7(e,v="xl/"+U[I][1].replace(/[\/]?xl\//,""))||(v=U[I][1]),e7(e,v)||(v=M.replace(/_rels\/.*$/,"")+U[I][1]),H=U[I][2]):v=(v="xl/worksheets/sheet"+(I+1-B)+"."+P).replace(/sheet0\./,"sheet."),T=v.replace(/^(.*)(\/)([^\/]*)$/,"$1/_rels/$3.rels"),r&&null!=r.sheets)switch(typeof r.sheets){case"number":if(I!=r.sheets)continue;break;case"string":if(C.SheetNames[I].toLowerCase()!=r.sheets.toLowerCase())continue;break;default:if(Array.isArray&&Array.isArray(r.sheets)){for(var W=!1,V=0;V!=r.sheets.length;++V)"number"==typeof r.sheets[V]&&r.sheets[V]==I&&(W=1),"string"==typeof r.sheets[V]&&r.sheets[V].toLowerCase()==C.SheetNames[I].toLowerCase()&&(W=1);if(!W)continue}}!function(e,r,t,a,n,s,i,c,o,l,f,h){try{s[a]=tj(rr(e,t,!0),r);var u,d,p=re(e,r);switch(c){case"sheet":u=s[a],d=".bin"===r.slice(-4)?function(e,r,t,a,n,s,i){if(!e)return e;var c,o,l,f,h,u,d,p,m,g,v,T,b=r||{};a||(a={"!id":{}});var E=b.dense?[]:{},w={s:{r:2e6,c:2e6},e:{r:0,c:0}},A=[],S=!1,k=!1,y=[];b.biff=12,b["!row"]=0;var C=0,R=!1,O=[],x={},_=b.supbooks||n.supbooks||[[]];if(_.sharedf=x,_.arrayf=O,_.SheetNames=n.SheetNames||n.Sheets.map(function(e){return e.name}),!b.supbooks&&(b.supbooks=_,n.Names))for(var I=0;I<n.Names.length;++I)_[0][I+1]=n.Names[I];var N=[],D=[],F=!1;if(sS[16]={n:"BrtShortReal",f:sr},to(e,function(e,r,I){if(!k)switch(I){case 148:c=e;break;case 0:o=e,b.sheetRows&&b.sheetRows<=o.r&&(k=!0),m=tm(h=o.r),b["!row"]=o.r,(e.hidden||e.hpt||null!=e.level)&&(e.hpt&&(e.hpx=ne(e.hpt)),D[e.r]=e);break;case 2:case 3:case 4:case 5:case 6:case 7:case 8:case 9:case 10:case 11:case 13:case 14:case 15:case 16:case 17:case 18:case 62:switch(l={t:e[2]},e[2]){case"n":l.v=e[1];break;case"s":p=nK[e[1]],l.v=p.t,l.r=p.r;break;case"b":l.v=!!e[1];break;case"e":l.v=e[1],!1!==b.cellText&&(l.w=tV[l.v]);break;case"str":l.t="s",l.v=e[1];break;case"is":l.t="s",l.v=e[1].t}if((f=i.CellXf[e[0].iStyleRef])&&nJ(l,f.numFmtId,null,b,s,i),u=-1==e[0].c?u+1:e[0].c,b.dense?(E[h]||(E[h]=[]),E[h][u]=l):E[tv(u)+m]=l,b.cellFormula){for(C=0,R=!1;C<O.length;++C){var P=O[C];o.r>=P[0].s.r&&o.r<=P[0].e.r&&u>=P[0].s.c&&u<=P[0].e.c&&(l.F=tw(P[0]),R=!0)}!R&&e.length>3&&(l.f=e[3])}if(w.s.r>o.r&&(w.s.r=o.r),w.s.c>u&&(w.s.c=u),w.e.r<o.r&&(w.e.r=o.r),w.e.c<u&&(w.e.c=u),b.cellDates&&f&&"n"==l.t&&eI(eo[f.numFmtId])){var L=eu(l.v);L&&(l.t="d",l.v=new Date(L.y,L.m-1,L.d,L.H,L.M,L.S,L.u))}v&&("XLDAPR"==v.type&&(l.D=!0),v=void 0),T&&(T=void 0);break;case 1:case 12:if(!b.sheetStubs||S)break;l={t:"z",v:void 0},u=-1==e[0].c?u+1:e[0].c,b.dense?(E[h]||(E[h]=[]),E[h][u]=l):E[tv(u)+m]=l,w.s.r>o.r&&(w.s.r=o.r),w.s.c>u&&(w.s.c=u),w.e.r<o.r&&(w.e.r=o.r),w.e.c<u&&(w.e.c=u),v&&("XLDAPR"==v.type&&(l.D=!0),v=void 0),T&&(T=void 0);break;case 176:y.push(e);break;case 49:v=((b.xlmeta||{}).Cell||[])[e-1];break;case 494:var M=a["!id"][e.relId];for(M?(e.Target=M.Target,e.loc&&(e.Target+="#"+e.loc),e.Rel=M):""==e.relId&&(e.Target="#"+e.loc),h=e.rfx.s.r;h<=e.rfx.e.r;++h)for(u=e.rfx.s.c;u<=e.rfx.e.c;++u)b.dense?(E[h]||(E[h]=[]),E[h][u]||(E[h][u]={t:"z",v:void 0}),E[h][u].l=e):(E[d=tb({c:u,r:h})]||(E[d]={t:"z",v:void 0}),E[d].l=e);break;case 426:if(!b.cellFormula)break;O.push(e),(g=b.dense?E[h][u]:E[tv(u)+m]).f=nH(e[1],w,{r:o.r,c:u},_,b),g.F=tw(e[0]);break;case 427:if(!b.cellFormula)break;x[tb(e[0].s)]=e[1],(g=b.dense?E[h][u]:E[tv(u)+m]).f=nH(e[1],w,{r:o.r,c:u},_,b);break;case 60:if(!b.cellStyles)break;for(;e.e>=e.s;)N[e.e--]={width:e.w/256,hidden:!!(1&e.flags),level:e.level},F||(F=!0,a7(e.w/256)),a9(N[e.e+1]);break;case 161:E["!autofilter"]={ref:tw(e)};break;case 476:E["!margins"]=e;break;case 147:n.Sheets[t]||(n.Sheets[t]={}),e.name&&(n.Sheets[t].CodeName=e.name),(e.above||e.left)&&(E["!outline"]={above:e.above,left:e.left});break;case 137:n.Views||(n.Views=[{}]),n.Views[0]||(n.Views[0]={}),e.RTL&&(n.Views[0].RTL=!0);break;case 485:case 64:case 1053:case 151:case 152:case 175:case 644:case 625:case 562:case 396:case 1112:case 1146:case 471:case 1050:case 649:case 1105:case 589:case 607:case 564:case 1055:case 168:case 174:case 1180:case 499:case 507:case 550:case 171:case 167:case 1177:case 169:case 1181:case 551:case 552:case 661:case 639:case 478:case 537:case 477:case 536:case 1103:case 680:case 1104:case 1024:case 663:case 535:case 678:case 504:case 1043:case 428:case 170:case 3072:case 50:case 2070:case 1045:break;case 35:S=!0;break;case 36:S=!1;break;case 37:A.push(I),S=!0;break;case 38:A.pop(),S=!1;break;default:if(r.T);else if(!S||b.WTF)throw Error("Unexpected record 0x"+I.toString(16))}},b),delete b.supbooks,delete b["!row"],!E["!ref"]&&(w.s.r<2e6||c&&(c.e.r>0||c.e.c>0||c.s.r>0||c.s.c>0))&&(E["!ref"]=tw(c||w)),b.sheetRows&&E["!ref"]){var P=tA(E["!ref"]);b.sheetRows<=+P.e.r&&(P.e.r=b.sheetRows-1,P.e.r>w.e.r&&(P.e.r=w.e.r),P.e.r<P.s.r&&(P.s.r=P.e.r),P.e.c>w.e.c&&(P.e.c=w.e.c),P.e.c<P.s.c&&(P.s.c=P.e.c),E["!fullref"]=E["!ref"],E["!ref"]=tw(P))}return y.length>0&&(E["!merges"]=y),N.length>0&&(E["!cols"]=N),D.length>0&&(E["!rows"]=D),E}(p,o,n,u,l,f,h):function(e,r,t,a,n,s,i){if(!e)return e;a||(a={"!id":{}});var c=r.dense?[]:{},o={s:{r:2e6,c:2e6},e:{r:0,c:0}},l="",f="",h=e.match(nQ);h?(l=e.slice(0,h.index),f=e.slice(h.index+h[0].length)):l=f=e;var u=l.match(n5);u?n7(u[0],c,n,t):(u=l.match(n6))&&(p=u[0],m=u[1],g=c,v=n,T=t,n7(p.slice(0,p.indexOf(">")),g,v,T));var d=(l.match(/<(?:\w*:)?dimension/)||{index:-1}).index;if(d>0){var p,m,g,v,T,b,E=l.slice(d,d+50).match(n0);E&&(b=tA(E[1])).s.r<=b.e.r&&b.s.c<=b.e.c&&b.s.r>=0&&b.s.c>=0&&(c["!ref"]=tw(b))}var w=l.match(n8);w&&w[1]&&function(e,r){r.Views||(r.Views=[{}]),(e.match(n9)||[]).forEach(function(e,t){var a=rh(e);r.Views[t]||(r.Views[t]={}),+a.zoomScale&&(r.Views[t].zoom=+a.zoomScale),rE(a.rightToLeft)&&(r.Views[t].RTL=!0)})}(w[1],n);var A=[];if(r.cellStyles){var S=l.match(n2);S&&function(e,r){for(var t=!1,a=0;a!=r.length;++a){var n=rh(r[a],!0);n.hidden&&(n.hidden=rE(n.hidden));var s=parseInt(n.min,10)-1,i=parseInt(n.max,10)-1;for(n.outlineLevel&&(n.level=+n.outlineLevel||0),delete n.min,delete n.max,n.width=+n.width,!t&&n.width&&(t=!0,a7(n.width)),a9(n);s<=i;)e[s++]=e1(n)}}(A,S)}h&&se(h[1],c,r,o,s,i);var k=f.match(n4);k&&(c["!autofilter"]={ref:(k[0].match(/ref="([^"]*)"/)||[])[1]});var y=[],C=f.match(nZ);if(C)for(d=0;d!=C.length;++d)y[d]=tA(C[d].slice(C[d].indexOf('"')+1));var R=f.match(n1);R&&function(e,r,t){for(var a=Array.isArray(e),n=0;n!=r.length;++n){var s=rh(ry(r[n]),!0);if(!s.ref)return;var i=((t||{})["!id"]||[])[s.id];i?(s.Target=i.Target,s.location&&(s.Target+="#"+rm(s.location))):(s.Target="#"+rm(s.location),i={Target:s.Target,TargetMode:"Internal"}),s.Rel=i,s.tooltip&&(s.Tooltip=s.tooltip,delete s.tooltip);for(var c=tA(s.ref),o=c.s.r;o<=c.e.r;++o)for(var l=c.s.c;l<=c.e.c;++l){var f=tb({c:l,r:o});a?(e[o]||(e[o]=[]),e[o][l]||(e[o][l]={t:"z",v:void 0}),e[o][l].l=s):(e[f]||(e[f]={t:"z",v:void 0}),e[f].l=s)}}}(c,R,a);var O=f.match(n3);if(O&&(c["!margins"]=function(e){var r={};return["left","right","top","bottom","header","footer"].forEach(function(t){e[t]&&(r[t]=parseFloat(e[t]))}),r}(rh(O[0]))),!c["!ref"]&&o.e.c>=o.s.c&&o.e.r>=o.s.r&&(c["!ref"]=tw(o)),r.sheetRows>0&&c["!ref"]){var x=tA(c["!ref"]);r.sheetRows<=+x.e.r&&(x.e.r=r.sheetRows-1,x.e.r>o.e.r&&(x.e.r=o.e.r),x.e.r<x.s.r&&(x.s.r=x.e.r),x.e.c>o.e.c&&(x.e.c=o.e.c),x.e.c<x.s.c&&(x.s.c=x.e.c),c["!fullref"]=c["!ref"],c["!ref"]=tw(x))}return A.length>0&&(c["!cols"]=A),y.length>0&&(c["!merges"]=y),c}(p,o,n,u,l,f,h);break;case"chart":if(!(d=function(e,r,t,a,n,s,i,c){if(".bin"===r.slice(-4)){var o=n;if(!e)return e;o||(o={"!id":{}});var l={"!type":"chart","!drawel":null,"!rel":""},f=[],h=!1;return to(e,function(e,r,n){switch(n){case 550:l["!rel"]=e;break;case 651:s.Sheets[t]||(s.Sheets[t]={}),e.name&&(s.Sheets[t].CodeName=e.name);break;case 562:case 652:case 669:case 679:case 551:case 552:case 476:case 3072:break;case 35:h=!0;break;case 36:h=!1;break;case 37:f.push(n);break;case 38:f.pop();break;default:if(r.T>0)f.push(n);else if(r.T<0)f.pop();else if(!h||a.WTF)throw Error("Unexpected record 0x"+n.toString(16))}},a),o["!id"][l["!rel"]]&&(l["!drawel"]=o["!id"][l["!rel"]]),l}var u=n;if(!e)return e;u||(u={"!id":{}});var d,p={"!type":"chart","!drawel":null,"!rel":""},m=e.match(n5);return m&&n7(m[0],p,s,t),(d=e.match(/drawing r:id="(.*?)"/))&&(p["!rel"]=d[1]),u["!id"][p["!rel"]]&&(p["!drawel"]=u["!id"][p["!rel"]]),p}(p,r,n,o,s[a],l,0,0))||!d["!drawel"])break;var m=rn(d["!drawel"].Target,r),g=tY(m),v=function(e,r){if(!e)return"??";var t=(e.match(/<c:chart [^>]*r:id="([^"]*)"/)||["",""])[1];return r["!id"][t].Target}(rr(e,m,!0),tj(rr(e,g,!0),m)),T=rn(v,m),b=tY(T);d=function(e,r,t,a,n,s){var i=s||{"!type":"chart"};if(!e)return s;var c=0,o=0,l="A",f={s:{r:2e6,c:2e6},e:{r:0,c:0}};return(e.match(/<c:numCache>[\s\S]*?<\/c:numCache>/gm)||[]).forEach(function(e){var r,t,a,n,s=(t=[],a=e.match(/^<c:numCache>/),(e.match(/<c:pt idx="(\d*)">(.*?)<\/c:pt>/mg)||[]).forEach(function(e){var r=e.match(/<c:pt idx="(\d*?)"><c:v>(.*)<\/c:v><\/c:pt>/);r&&(t[+r[1]]=a?+r[2]:r[2])}),n=rm((e.match(/<c:formatCode>([\s\S]*?)<\/c:formatCode>/)||["","General"])[1]),(e.match(/<c:f>(.*?)<\/c:f>/mg)||[]).forEach(function(e){r=e.replace(/<.*?>/g,"")}),[t,n,r]);f.s.r=f.s.c=0,f.e.c=c,l=tv(c),s[0].forEach(function(e,r){i[l+tm(r)]={t:"n",v:e,z:s[1]},o=r}),f.e.r<o&&(f.e.r=o),++c}),c>0&&(i["!ref"]=tw(f)),i}(rr(e,T,!0),0,0,tj(rr(e,b,!0),T),0,d);break;case"macro":s[a],r.slice(-4),d={"!type":"macro"};break;case"dialog":s[a],r.slice(-4),d={"!type":"dialog"};break;default:throw Error("Unrecognized sheet type "+c)}i[a]=d;var E=[];s&&s[a]&&eW(s[a]).forEach(function(t){var n,i,c,l,f,h="";if(s[a][t].Type==t$.CMNT){h=rn(s[a][t].Target,r);var u=function(e,r,t){if(".bin"===r.slice(-4)){var a,n,s,i;return a=[],n=[],s={},i=!1,to(e,function(e,r,c){switch(c){case 632:n.push(e);break;case 635:s=e;break;case 637:s.t=e.t,s.h=e.h,s.r=e.r;break;case 636:if(s.author=n[s.iauthor],delete s.iauthor,t.sheetRows&&s.rfx&&t.sheetRows<=s.rfx.r)break;s.t||(s.t=""),delete s.rfx,a.push(s);break;case 3072:case 37:case 38:break;case 35:i=!0;break;case 36:i=!1;break;default:if(r.T);else if(!i||t.WTF)throw Error("Unexpected record 0x"+c.toString(16))}}),a}if(e.match(/<(?:\w+:)?comments *\/>/))return[];var c=[],o=[],l=e.match(/<(?:\w+:)?authors>([\s\S]*)<\/(?:\w+:)?authors>/);l&&l[1]&&l[1].split(/<\/\w*:?author>/).forEach(function(e){if(""!==e&&""!==e.trim()){var r=e.match(/<(?:\w+:)?author[^>]*>(.*)/);r&&c.push(r[1])}});var f=e.match(/<(?:\w+:)?commentList>([\s\S]*)<\/(?:\w+:)?commentList>/);return f&&f[1]&&f[1].split(/<\/\w*:?comment>/).forEach(function(e){if(""!==e&&""!==e.trim()){var r=e.match(/<(?:\w+:)?comment[^>]*>/);if(r){var a=rh(r[0]),n={author:a.authorId&&c[a.authorId]||"sheetjsghost",ref:a.ref,guid:a.guid},s=tT(a.ref);if(!t.sheetRows||!(t.sheetRows<=s.r)){var i=e.match(/<(?:\w+:)?text>([\s\S]*)<\/(?:\w+:)?text>/),l=!!i&&!!i[1]&&aG(i[1])||{r:"",t:"",h:""};n.r=l.r,"<t></t>"==l.r&&(l.t=l.h=""),n.t=(l.t||"").replace(/\r\n/g,"\n").replace(/\r/g,"\n"),t.cellHTML&&(n.h=l.h),o.push(n)}}}}),o}(re(e,h,!0),h,o);if(!u||!u.length)return;np(d,u,!1)}s[a][t].Type==t$.TCMNT&&(h=rn(s[a][t].Target,r),E=E.concat((n=re(e,h,!0),i=[],c=!1,l={},f=0,n.replace(ro,function(e,r){var t=rh(e);switch(ru(t[0])){case"<?xml":case"<ThreadedComments":case"</ThreadedComments>":case"<extLst":case"<extLst>":case"</extLst>":case"<extLst/>":break;case"<threadedComment":l={author:t.personId,guid:t.id,ref:t.ref,T:1};break;case"</threadedComment>":null!=l.t&&i.push(l);break;case"<text>":case"<text":f=r+e.length;break;case"</text>":l.t=n.slice(f,r).replace(/\r\n/g,"\n").replace(/\r/g,"\n");break;case"<mentions":case"<mentions>":case"<ext":c=!0;break;case"</mentions>":case"</ext>":c=!1;break;default:if(!c&&o.WTF)throw Error("unrecognized "+t[0]+" in threaded comments")}return e}),i)))}),E&&E.length&&np(d,E,!0,o.people||[])}catch(e){if(o.WTF)throw e}}(e,v,T,C.SheetNames[I],I,N,m,H,r,y,S,k)}return x={Directory:w,Workbook:y,Props:C,Custprops:O,Deps:_,Sheets:m,SheetNames:C.SheetNames,Strings:nK,Styles:k,Themes:S,SSF:e1(eo)},r&&r.bookFiles&&(e.files?(x.keys=E,x.files=e.files):(x.keys=[],x.files={},e.FullPaths.forEach(function(r,t){r=r.replace(/^Root Entry[\/]/,""),x.keys.push(r),x.files[r]=e.FileIndex[t]}))),r&&r.bookVBA&&(w.vba.length>0?x.vbaraw=re(e,sW(w.vba[0]),!0):w.defaults&&"application/vnd.ms-office.vbaProject"===w.defaults.bin&&(x.vbaraw=re(e,"xl/vbaProject.bin",!0))),x}(ra(s,i),i)):sz(a,d,u,m);case 239:return 60===p[3]?sv(d,u):sz(a,d,u,m);case 255:if(254===p[1])return l=d,f=u,h=l,"base64"==f.type&&(h=V(h)),h=t.utils.decode(1200,h.slice(2),"str"),f.type="binary",sG(h,f);if(0===p[1]&&2===p[2]&&0===p[3])return aM.to_workbook(d,u);break;case 0:if(0===p[1]&&(p[2]>=2&&0===p[3]||0===p[2]&&(8===p[3]||9===p[3])))return aM.to_workbook(d,u);break;case 3:case 131:case 139:case 140:return aN.to_workbook(d,u);case 123:if(92===p[1]&&114===p[2]&&116===p[3])return a1.to_workbook(d,u);break;case 10:case 13:case 32:var A=d,S=u,k="",y=sV(A,S);switch(S.type){case"base64":k=V(A);break;case"binary":k=A;break;case"buffer":k=A.toString("binary");break;case"array":k=eQ(A);break;default:throw Error("Unrecognized type "+S.type)}return 239==y[0]&&187==y[1]&&191==y[2]&&(k=ry(k)),S.type="binary",sG(k,S);case 137:if(80===p[1]&&78===p[2]&&71===p[3])throw Error("PNG Image File is not a spreadsheet")}return aI.indexOf(p[0])>-1&&p[2]<=12&&p[3]<=31?aN.to_workbook(d,u):sz(a,d,u,m)}(e,{type:"buffer"}),s=a.SheetNames[0],i=a.Sheets[s];c=sK.sheet_to_json(i,{header:1})}else c=(await n.text()).split("\n").filter(e=>e.trim()).map(e=>e.split(",").map(e=>e.trim().replace(/"/g,"")));if(c.length<2)return w.NextResponse.json({error:"File must have at least a header row and one data row"},{status:400});let o=c[0].map(e=>String(e).trim()),l=["first name","last name","email","date of birth","gender","phone number","address","guardian name","guardian phone","class","section","roll number","academic year","admission number"].filter(e=>!o.some(r=>r.toLowerCase()===e.toLowerCase()));if(l.length>0)return w.NextResponse.json({error:"Missing required headers",missingHeaders:l},{status:400});let f={success:0,errors:[],total:c.length-1},h=await k.prisma.class.findMany({include:{sections:!0}});for(let e=1;e<c.length;e++)try{let r=c[e].map(e=>String(e||"").trim()),t={};if(o.forEach((e,a)=>{t[e.toLowerCase()]=r[a]||""}),!t["first name"]||!t["last name"]||!t.email){f.errors.push(`Row ${e+1}: Missing required fields (first name, last name, or email)`);continue}if(!t["date of birth"]){f.errors.push(`Row ${e+1}: Date of birth is required`);continue}if(!t.gender){f.errors.push(`Row ${e+1}: Gender is required`);continue}if(!t["guardian name"]){f.errors.push(`Row ${e+1}: Guardian name is required`);continue}if(!t["guardian phone"]){f.errors.push(`Row ${e+1}: Guardian phone is required`);continue}if(!t.class||!t.section){f.errors.push(`Row ${e+1}: Class and section are required`);continue}if(!t["academic year"]){f.errors.push(`Row ${e+1}: Academic year is required`);continue}if(await k.prisma.user.findUnique({where:{email:t.email}})){f.errors.push(`Row ${e+1}: Email ${t.email} already exists`);continue}let a=null,n=null,s=h.find(e=>e.name.toLowerCase()===t.class.toLowerCase());if(s){let e=s.sections.find(e=>e.name.toLowerCase()===t.section.toLowerCase());e&&(a=s.id,n=e.id)}if(!a){f.errors.push(`Row ${e+1}: Class "${t.class}" Section "${t.section}" not found`);continue}let i=["MALE","FEMALE","OTHER"],l=t.gender?.toUpperCase();if(!i.includes(l)){f.errors.push(`Row ${e+1}: Invalid gender "${t.gender}". Must be MALE, FEMALE, or OTHER`);continue}let u=new Date(t["date of birth"]);if(isNaN(u.getTime())){f.errors.push(`Row ${e+1}: Invalid date of birth "${t["date of birth"]}". Use YYYY-MM-DD format`);continue}let d=t["academic year"];if(!/^\d{4}-\d{4}$/.test(d)){f.errors.push(`Row ${e+1}: Invalid academic year format "${d}". Use YYYY-YYYY format (e.g., 2024-2025)`);continue}let p=await C.default.hash("Student@12345",12),m=await k.prisma.user.create({data:{email:t.email,hashedPassword:p,role:"STUDENT",firstName:t["first name"],lastName:t["last name"],phone:t["phone number"]||null}}),g=t["admission number"]||`STU${Date.now()}-${e}`,v=await k.prisma.student.create({data:{userId:m.id,admissionNo:g,dob:u,gender:l,address:t.address||null,guardianName:t["guardian name"],guardianPhone:t["guardian phone"],currentClassId:a,currentSectionId:n,rollNumber:t["roll number"]||null}});await k.prisma.enrollment.create({data:{studentId:v.id,classId:a,sectionId:n,academicYear:d,active:!0}}),f.success++}catch(r){f.errors.push(`Row ${e+1}: ${r instanceof Error?r.message:"Unknown error"}`)}return w.NextResponse.json({message:"Bulk import completed",results:f})}catch(e){return console.error("Error importing students:",e),w.NextResponse.json({error:"Internal server error"},{status:500})}}R.version;var sJ=e.i(82131);let sZ=new s.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/admin/students/bulk/route",pathname:"/api/admin/students/bulk",filename:"route",bundlePath:""},distDir:".next",relativeProjectDir:"",resolvedPagePath:"[project]/school-management-system/src/app/api/admin/students/bulk/route.ts",nextConfigOutput:"",userland:sJ}),{workAsyncStorage:sQ,workUnitAsyncStorage:s1,serverHooks:s0}=sZ;function s2(){return(0,c.patchFetch)({workAsyncStorage:sQ,workUnitAsyncStorage:s1})}async function s4(e,r,t){var a;let n="/api/admin/students/bulk/route";n=n.replace(/\/index$/,"")||"/";let s=await sZ.prepare(e,r,{srcPage:n,multiZoneDraftMode:!1});if(!s)return r.statusCode=400,r.end("Bad Request"),null==t.waitUntil||t.waitUntil.call(t,Promise.resolve()),null;let{buildId:c,params:w,nextConfig:A,isDraftMode:S,prerenderManifest:k,routerServerContext:y,isOnDemandRevalidate:C,revalidateOnlyGenerated:R,resolvedPathname:O}=s,x=(0,f.normalizeAppPath)(n),_=!!(k.dynamicRoutes[x]||k.routes[O]);if(_&&!S){let e=!!k.routes[O],r=k.dynamicRoutes[x];if(r&&!1===r.fallback&&!e)throw new b.NoFallbackError}let I=null;!_||sZ.isDev||S||(I="/index"===(I=O)?"/":I);let N=!0===sZ.isDev||!_,D=_&&!N,F=e.method||"GET",P=(0,l.getTracer)(),L=P.getActiveScopeSpan(),M={params:w,prerenderManifest:k,renderOpts:{experimental:{cacheComponents:!!A.experimental.cacheComponents,authInterrupts:!!A.experimental.authInterrupts},supportsDynamicResponse:N,incrementalCache:(0,o.getRequestMeta)(e,"incrementalCache"),cacheLifeProfiles:null==(a=A.experimental)?void 0:a.cacheLife,isRevalidate:D,waitUntil:t.waitUntil,onClose:e=>{r.on("close",e)},onAfterTaskError:void 0,onInstrumentationRequestError:(r,t,a)=>sZ.onRequestError(e,r,a,y)},sharedContext:{buildId:c}},U=new h.NodeNextRequest(e),B=new h.NodeNextResponse(r),H=u.NextRequestAdapter.fromNodeNextRequest(U,(0,u.signalFromNodeResponse)(r));try{let a=async t=>sZ.handle(H,M).finally(()=>{if(!t)return;t.setAttributes({"http.status_code":r.statusCode,"next.rsc":!1});let a=P.getRootSpanAttributes();if(!a)return;if(a.get("next.span_type")!==d.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${a.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let n=a.get("next.route");if(n){let e=`${F} ${n}`;t.setAttributes({"next.route":n,"http.route":n,"next.span_name":e}),t.updateName(e)}else t.updateName(`${F} ${e.url}`)}),s=async s=>{var c,l;let f=async({previousCacheEntry:i})=>{try{if(!(0,o.getRequestMeta)(e,"minimalMode")&&C&&R&&!i)return r.statusCode=404,r.setHeader("x-nextjs-cache","REVALIDATED"),r.end("This page could not be found"),null;let n=await a(s);e.fetchMetrics=M.renderOpts.fetchMetrics;let c=M.renderOpts.pendingWaitUntil;c&&t.waitUntil&&(t.waitUntil(c),c=void 0);let l=M.renderOpts.collectedTags;if(!_)return await (0,m.sendResponse)(U,B,n,M.renderOpts.pendingWaitUntil),null;{let e=await n.blob(),r=(0,g.toNodeOutgoingHttpHeaders)(n.headers);l&&(r[T.NEXT_CACHE_TAGS_HEADER]=l),!r["content-type"]&&e.type&&(r["content-type"]=e.type);let t=void 0!==M.renderOpts.collectedRevalidate&&!(M.renderOpts.collectedRevalidate>=T.INFINITE_CACHE)&&M.renderOpts.collectedRevalidate,a=void 0===M.renderOpts.collectedExpire||M.renderOpts.collectedExpire>=T.INFINITE_CACHE?void 0:M.renderOpts.collectedExpire;return{value:{kind:E.CachedRouteKind.APP_ROUTE,status:n.status,body:Buffer.from(await e.arrayBuffer()),headers:r},cacheControl:{revalidate:t,expire:a}}}}catch(r){throw(null==i?void 0:i.isStale)&&await sZ.onRequestError(e,r,{routerKind:"App Router",routePath:n,routeType:"route",revalidateReason:(0,p.getRevalidateReason)({isRevalidate:D,isOnDemandRevalidate:C})},y),r}},h=await sZ.handleResponse({req:e,nextConfig:A,cacheKey:I,routeKind:i.RouteKind.APP_ROUTE,isFallback:!1,prerenderManifest:k,isRoutePPREnabled:!1,isOnDemandRevalidate:C,revalidateOnlyGenerated:R,responseGenerator:f,waitUntil:t.waitUntil});if(!_)return null;if((null==h||null==(c=h.value)?void 0:c.kind)!==E.CachedRouteKind.APP_ROUTE)throw Object.defineProperty(Error(`Invariant: app-route received invalid cache entry ${null==h||null==(l=h.value)?void 0:l.kind}`),"__NEXT_ERROR_CODE",{value:"E701",enumerable:!1,configurable:!0});(0,o.getRequestMeta)(e,"minimalMode")||r.setHeader("x-nextjs-cache",C?"REVALIDATED":h.isMiss?"MISS":h.isStale?"STALE":"HIT"),S&&r.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");let u=(0,g.fromNodeOutgoingHttpHeaders)(h.value.headers);return(0,o.getRequestMeta)(e,"minimalMode")&&_||u.delete(T.NEXT_CACHE_TAGS_HEADER),!h.cacheControl||r.getHeader("Cache-Control")||u.get("Cache-Control")||u.set("Cache-Control",(0,v.getCacheControlHeader)(h.cacheControl)),await (0,m.sendResponse)(U,B,new Response(h.value.body,{headers:u,status:h.value.status||200})),null};L?await s(L):await P.withPropagatedContext(e.headers,()=>P.trace(d.BaseServerSpan.handleRequest,{spanName:`${F} ${e.url}`,kind:l.SpanKind.SERVER,attributes:{"http.method":F,"http.target":e.url}},s))}catch(r){if(L||r instanceof b.NoFallbackError||await sZ.onRequestError(e,r,{routerKind:"App Router",routePath:x,routeType:"route",revalidateReason:(0,p.getRevalidateReason)({isRevalidate:D,isOnDemandRevalidate:C})}),_)throw r;return await (0,m.sendResponse)(U,B,new Response(null,{status:500})),null}}}];

//# sourceMappingURL=6c5cd_next_dist_esm_build_templates_app-route_e7480310.js.map