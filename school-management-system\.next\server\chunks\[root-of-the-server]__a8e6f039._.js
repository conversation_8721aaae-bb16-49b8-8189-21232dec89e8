module.exports=[18622,(e,t,r)=>{t.exports=e.x("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js"))},56704,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/work-async-storage.external.js",()=>require("next/dist/server/app-render/work-async-storage.external.js"))},32319,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/work-unit-async-storage.external.js",()=>require("next/dist/server/app-render/work-unit-async-storage.external.js"))},20635,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/action-async-storage.external.js",()=>require("next/dist/server/app-render/action-async-storage.external.js"))},24725,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/after-task-async-storage.external.js",()=>require("next/dist/server/app-render/after-task-async-storage.external.js"))},24361,(e,t,r)=>{t.exports=e.x("util",()=>require("util"))},29173,(e,t,r)=>{t.exports=e.x("@prisma/client",()=>require("@prisma/client"))},70406,(e,t,r)=>{t.exports=e.x("next/dist/compiled/@opentelemetry/api",()=>require("next/dist/compiled/@opentelemetry/api"))},93695,(e,t,r)=>{t.exports=e.x("next/dist/shared/lib/no-fallback-error.external.js",()=>require("next/dist/shared/lib/no-fallback-error.external.js"))},61724,(e,t,r)=>{t.exports=e.x("next/dist/compiled/next-server/app-route-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-route-turbo.runtime.prod.js"))},54799,(e,t,r)=>{t.exports=e.x("crypto",()=>require("crypto"))},92509,(e,t,r)=>{t.exports=e.x("url",()=>require("url"))},21517,(e,t,r)=>{t.exports=e.x("http",()=>require("http"))},49719,(e,t,r)=>{t.exports=e.x("assert",()=>require("assert"))},45706,(e,t,r)=>{t.exports=e.x("querystring",()=>require("querystring"))},874,(e,t,r)=>{t.exports=e.x("buffer",()=>require("buffer"))},6461,(e,t,r)=>{t.exports=e.x("zlib",()=>require("zlib"))},24836,(e,t,r)=>{t.exports=e.x("https",()=>require("https"))},27699,(e,t,r)=>{t.exports=e.x("events",()=>require("events"))},12756,e=>{e.v(JSON.parse('{"name":"openid-client","version":"5.7.1","description":"OpenID Connect Relying Party (RP, Client) implementation for Node.js runtime, supports passportjs","keywords":["auth","authentication","basic","certified","client","connect","dynamic","electron","hybrid","identity","implicit","oauth","oauth2","oidc","openid","passport","relying party","strategy"],"homepage":"https://github.com/panva/openid-client","repository":"panva/openid-client","funding":{"url":"https://github.com/sponsors/panva"},"license":"MIT","author":"Filip Skokan <<EMAIL>>","exports":{"types":"./types/index.d.ts","import":"./lib/index.mjs","require":"./lib/index.js"},"main":"./lib/index.js","types":"./types/index.d.ts","files":["lib","types/index.d.ts"],"scripts":{"format":"npx prettier --loglevel silent --write ./lib ./test ./certification ./types","test":"mocha test/**/*.test.js"},"dependencies":{"jose":"^4.15.9","lru-cache":"^6.0.0","object-hash":"^2.2.0","oidc-token-hash":"^5.0.3"},"devDependencies":{"@types/node":"^16.18.106","@types/passport":"^1.0.16","base64url":"^3.0.1","chai":"^4.5.0","mocha":"^10.7.3","nock":"^13.5.5","prettier":"^2.8.8","readable-mock-req":"^0.2.2","sinon":"^9.2.4","timekeeper":"^2.3.1"},"standard-version":{"scripts":{"postchangelog":"sed -i \'\' -e \'s/### \\\\[/## [/g\' CHANGELOG.md"},"types":[{"type":"feat","section":"Features"},{"type":"fix","section":"Fixes"},{"type":"chore","hidden":true},{"type":"docs","hidden":true},{"type":"style","hidden":true},{"type":"refactor","section":"Refactor","hidden":false},{"type":"perf","section":"Performance","hidden":false},{"type":"test","hidden":true}]}}'))},92042,(e,t,r)=>{},92176,e=>{"use strict";e.s(["handler",()=>k,"patchFetch",()=>A,"routeModule",()=>j,"serverHooks",()=>q,"workAsyncStorage",()=>C,"workUnitAsyncStorage",()=>N],92176);var t=e.i(6137),r=e.i(11365),a=e.i(9638),n=e.i(15243),s=e.i(66378),i=e.i(92101),o=e.i(50012),p=e.i(62885),l=e.i(31409),d=e.i(78448),u=e.i(28015),c=e.i(72721),x=e.i(75714),m=e.i(12634),h=e.i(93695);e.i(74732);var y=e.i(66662);e.s(["GET",()=>w,"POST",()=>b],2696);var v=e.i(2835),R=e.i(58356),f=e.i(43382),g=e.i(31279);async function w(e){try{let t=await (0,R.getServerSession)(f.authOptions);if(!t||"ADMIN"!==t.user.role)return v.NextResponse.json({error:"Unauthorized"},{status:401});let{searchParams:r}=new URL(e.url),a=r.get("type");if("terms"===a){let e=await g.prisma.term.findMany({orderBy:{createdAt:"desc"}});return v.NextResponse.json(e)}if("exams"===a){let e=await g.prisma.exam.findMany({include:{term:!0,subject:!0},orderBy:{date:"desc"}});return v.NextResponse.json(e)}return v.NextResponse.json({error:"Invalid type parameter"},{status:400})}catch(e){return console.error("Error fetching exams/terms:",e),v.NextResponse.json({error:"Internal server error"},{status:500})}}async function b(e){try{let t=await (0,R.getServerSession)(f.authOptions);if(!t||"ADMIN"!==t.user.role)return v.NextResponse.json({error:"Unauthorized"},{status:401});let{type:r,data:a}=await e.json();if("term"===r){let e=await g.prisma.term.create({data:{name:a.name,startDate:new Date(a.startDate),endDate:new Date(a.endDate),academicYear:a.academicYear}});return v.NextResponse.json(e)}if("exam"===r){let e=await g.prisma.exam.create({data:{name:a.name,termId:a.termId,subjectId:a.subjectId,maxMarks:a.maxMarks,weightagePercent:a.weightagePercent,date:new Date(a.date)}});return v.NextResponse.json(e)}return v.NextResponse.json({error:"Invalid type parameter"},{status:400})}catch(e){return console.error("Error creating exam/term:",e),v.NextResponse.json({error:"Internal server error"},{status:500})}}var E=e.i(2696);let j=new t.AppRouteRouteModule({definition:{kind:r.RouteKind.APP_ROUTE,page:"/api/admin/exams/route",pathname:"/api/admin/exams",filename:"route",bundlePath:""},distDir:".next",relativeProjectDir:"",resolvedPagePath:"[project]/school-management-system/src/app/api/admin/exams/route.ts",nextConfigOutput:"",userland:E}),{workAsyncStorage:C,workUnitAsyncStorage:N,serverHooks:q}=j;function A(){return(0,a.patchFetch)({workAsyncStorage:C,workUnitAsyncStorage:N})}async function k(e,t,a){var v;let R="/api/admin/exams/route";R=R.replace(/\/index$/,"")||"/";let f=await j.prepare(e,t,{srcPage:R,multiZoneDraftMode:!1});if(!f)return t.statusCode=400,t.end("Bad Request"),null==a.waitUntil||a.waitUntil.call(a,Promise.resolve()),null;let{buildId:g,params:w,nextConfig:b,isDraftMode:E,prerenderManifest:C,routerServerContext:N,isOnDemandRevalidate:q,revalidateOnlyGenerated:A,resolvedPathname:k}=f,P=(0,i.normalizeAppPath)(R),O=!!(C.dynamicRoutes[P]||C.routes[k]);if(O&&!E){let e=!!C.routes[k],t=C.dynamicRoutes[P];if(t&&!1===t.fallback&&!e)throw new h.NoFallbackError}let I=null;!O||j.isDev||E||(I="/index"===(I=k)?"/":I);let S=!0===j.isDev||!O,T=O&&!S,D=e.method||"GET",M=(0,s.getTracer)(),_=M.getActiveScopeSpan(),U={params:w,prerenderManifest:C,renderOpts:{experimental:{cacheComponents:!!b.experimental.cacheComponents,authInterrupts:!!b.experimental.authInterrupts},supportsDynamicResponse:S,incrementalCache:(0,n.getRequestMeta)(e,"incrementalCache"),cacheLifeProfiles:null==(v=b.experimental)?void 0:v.cacheLife,isRevalidate:T,waitUntil:a.waitUntil,onClose:e=>{t.on("close",e)},onAfterTaskError:void 0,onInstrumentationRequestError:(t,r,a)=>j.onRequestError(e,t,a,N)},sharedContext:{buildId:g}},H=new o.NodeNextRequest(e),F=new o.NodeNextResponse(t),$=p.NextRequestAdapter.fromNodeNextRequest(H,(0,p.signalFromNodeResponse)(t));try{let i=async r=>j.handle($,U).finally(()=>{if(!r)return;r.setAttributes({"http.status_code":t.statusCode,"next.rsc":!1});let a=M.getRootSpanAttributes();if(!a)return;if(a.get("next.span_type")!==l.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${a.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let n=a.get("next.route");if(n){let e=`${D} ${n}`;r.setAttributes({"next.route":n,"http.route":n,"next.span_name":e}),r.updateName(e)}else r.updateName(`${D} ${e.url}`)}),o=async s=>{var o,p;let l=async({previousCacheEntry:r})=>{try{if(!(0,n.getRequestMeta)(e,"minimalMode")&&q&&A&&!r)return t.statusCode=404,t.setHeader("x-nextjs-cache","REVALIDATED"),t.end("This page could not be found"),null;let o=await i(s);e.fetchMetrics=U.renderOpts.fetchMetrics;let p=U.renderOpts.pendingWaitUntil;p&&a.waitUntil&&(a.waitUntil(p),p=void 0);let l=U.renderOpts.collectedTags;if(!O)return await (0,u.sendResponse)(H,F,o,U.renderOpts.pendingWaitUntil),null;{let e=await o.blob(),t=(0,c.toNodeOutgoingHttpHeaders)(o.headers);l&&(t[m.NEXT_CACHE_TAGS_HEADER]=l),!t["content-type"]&&e.type&&(t["content-type"]=e.type);let r=void 0!==U.renderOpts.collectedRevalidate&&!(U.renderOpts.collectedRevalidate>=m.INFINITE_CACHE)&&U.renderOpts.collectedRevalidate,a=void 0===U.renderOpts.collectedExpire||U.renderOpts.collectedExpire>=m.INFINITE_CACHE?void 0:U.renderOpts.collectedExpire;return{value:{kind:y.CachedRouteKind.APP_ROUTE,status:o.status,body:Buffer.from(await e.arrayBuffer()),headers:t},cacheControl:{revalidate:r,expire:a}}}}catch(t){throw(null==r?void 0:r.isStale)&&await j.onRequestError(e,t,{routerKind:"App Router",routePath:R,routeType:"route",revalidateReason:(0,d.getRevalidateReason)({isRevalidate:T,isOnDemandRevalidate:q})},N),t}},h=await j.handleResponse({req:e,nextConfig:b,cacheKey:I,routeKind:r.RouteKind.APP_ROUTE,isFallback:!1,prerenderManifest:C,isRoutePPREnabled:!1,isOnDemandRevalidate:q,revalidateOnlyGenerated:A,responseGenerator:l,waitUntil:a.waitUntil});if(!O)return null;if((null==h||null==(o=h.value)?void 0:o.kind)!==y.CachedRouteKind.APP_ROUTE)throw Object.defineProperty(Error(`Invariant: app-route received invalid cache entry ${null==h||null==(p=h.value)?void 0:p.kind}`),"__NEXT_ERROR_CODE",{value:"E701",enumerable:!1,configurable:!0});(0,n.getRequestMeta)(e,"minimalMode")||t.setHeader("x-nextjs-cache",q?"REVALIDATED":h.isMiss?"MISS":h.isStale?"STALE":"HIT"),E&&t.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");let v=(0,c.fromNodeOutgoingHttpHeaders)(h.value.headers);return(0,n.getRequestMeta)(e,"minimalMode")&&O||v.delete(m.NEXT_CACHE_TAGS_HEADER),!h.cacheControl||t.getHeader("Cache-Control")||v.get("Cache-Control")||v.set("Cache-Control",(0,x.getCacheControlHeader)(h.cacheControl)),await (0,u.sendResponse)(H,F,new Response(h.value.body,{headers:v,status:h.value.status||200})),null};_?await o(_):await M.withPropagatedContext(e.headers,()=>M.trace(l.BaseServerSpan.handleRequest,{spanName:`${D} ${e.url}`,kind:s.SpanKind.SERVER,attributes:{"http.method":D,"http.target":e.url}},o))}catch(t){if(_||t instanceof h.NoFallbackError||await j.onRequestError(e,t,{routerKind:"App Router",routePath:P,routeType:"route",revalidateReason:(0,d.getRevalidateReason)({isRevalidate:T,isOnDemandRevalidate:q})}),O)throw t;return await (0,u.sendResponse)(H,F,new Response(null,{status:500})),null}}}];

//# sourceMappingURL=%5Broot-of-the-server%5D__a8e6f039._.js.map