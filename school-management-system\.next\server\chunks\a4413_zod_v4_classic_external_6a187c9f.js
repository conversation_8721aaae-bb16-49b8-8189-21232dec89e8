module.exports=[47504,e=>{"use strict";e.s(["z",()=>lu],47504),e.s(["$brand",()=>r,"$input",()=>n_,"$output",()=>n$,"NEVER",()=>t,"TimePrecision",()=>nG,"ZodAny",()=>uM,"ZodArray",()=>uQ,"ZodBase64",()=>ul,"ZodBase64URL",()=>uc,"ZodBigInt",()=>uN,"ZodBigIntFormat",()=>uP,"ZodBoolean",()=>uO,"ZodCIDRv4",()=>ua,"ZodCIDRv6",()=>uu,"ZodCUID",()=>o6,"ZodCUID2",()=>o1,"ZodCatch",()=>sD,"ZodCodec",()=>sL,"ZodCustom",()=>sY,"ZodCustomStringFormat",()=>uh,"ZodDate",()=>uY,"ZodDefault",()=>sw,"ZodDiscriminatedUnion",()=>u5,"ZodE164",()=>uf,"ZodEmail",()=>oL,"ZodEmoji",()=>oY,"ZodEnum",()=>sc,"ZodError",()=>oy,"ZodFile",()=>sg,"ZodFirstPartyTypeKind",()=>rE,"ZodFunction",()=>sX,"ZodGUID",()=>oF,"ZodIPv4",()=>ut,"ZodIPv6",()=>ur,"ZodISODate",()=>of,"ZodISODateTime",()=>oc,"ZodISODuration",()=>oh,"ZodISOTime",()=>ov,"ZodIntersection",()=>se,"ZodIssueCode",()=>s3,"ZodJWT",()=>uv,"ZodKSUID",()=>o8,"ZodLazy",()=>sB,"ZodLiteral",()=>sp,"ZodMap",()=>su,"ZodNaN",()=>sE,"ZodNanoID",()=>oQ,"ZodNever",()=>uG,"ZodNonOptional",()=>sU,"ZodNull",()=>uF,"ZodNullable",()=>sx,"ZodNumber",()=>ux,"ZodNumberFormat",()=>uI,"ZodObject",()=>u0,"ZodOptional",()=>sy,"ZodPipe",()=>sA,"ZodPrefault",()=>sS,"ZodPromise",()=>sG,"ZodReadonly",()=>sF,"ZodRealError",()=>ob,"ZodRecord",()=>sn,"ZodSet",()=>sl,"ZodString",()=>oT,"ZodStringFormat",()=>oC,"ZodSuccess",()=>sj,"ZodSymbol",()=>uA,"ZodTemplateLiteral",()=>sM,"ZodTransform",()=>s$,"ZodTuple",()=>si,"ZodType",()=>oP,"ZodULID",()=>o9,"ZodURL",()=>oK,"ZodUUID",()=>oM,"ZodUndefined",()=>uL,"ZodUnion",()=>u3,"ZodUnknown",()=>uB,"ZodVoid",()=>uX,"ZodXID",()=>o7,"_ZodString",()=>oE,"_default",()=>sz,"_function",()=>sq,"any",()=>uW,"array",()=>u4,"base64",()=>ud,"base64url",()=>um,"bigint",()=>uD,"boolean",()=>uj,"catch",()=>sP,"check",()=>sH,"cidrv4",()=>uo,"cidrv6",()=>us,"clone",()=>R,"codec",()=>sR,"coerce",()=>lo,"config",()=>u,"core",()=>os,"cuid",()=>o0,"cuid2",()=>o2,"custom",()=>sQ,"date",()=>uH,"decode",()=>oS,"decodeAsync",()=>oU,"discriminatedUnion",()=>u8,"e164",()=>up,"email",()=>oR,"emoji",()=>oH,"encode",()=>oz,"encodeAsync",()=>oZ,"endsWith",()=>aj,"enum",()=>sm,"file",()=>sh,"flattenError",()=>e_,"float32",()=>uz,"float64",()=>uS,"formatError",()=>ey,"function",()=>sq,"getErrorMap",()=>s5,"globalRegistry",()=>nx,"gt",()=>af,"gte",()=>ap,"guid",()=>oJ,"hash",()=>ub,"hex",()=>uy,"hostname",()=>u_,"httpUrl",()=>oq,"includes",()=>aU,"instanceof",()=>s0,"int",()=>uw,"int32",()=>uZ,"int64",()=>uE,"intersection",()=>st,"ipv4",()=>ui,"ipv6",()=>un,"iso",()=>le,"json",()=>s2,"jwt",()=>ug,"keyof",()=>u6,"ksuid",()=>ue,"lazy",()=>sV,"length",()=>aw,"literal",()=>sv,"locales",()=>s8,"looseObject",()=>u9,"lowercase",()=>aS,"lt",()=>ac,"lte",()=>am,"map",()=>ss,"maxLength",()=>ak,"maxSize",()=>ay,"mime",()=>aD,"minLength",()=>aI,"minSize",()=>ab,"multipleOf",()=>a_,"nan",()=>sT,"nanoid",()=>o4,"nativeEnum",()=>sf,"negative",()=>ag,"never",()=>uK,"nonnegative",()=>a$,"nonoptional",()=>sO,"nonpositive",()=>ah,"normalize",()=>aE,"null",()=>uJ,"nullable",()=>sk,"nullish",()=>sI,"number",()=>uk,"object",()=>u1,"optional",()=>sb,"overwrite",()=>aP,"parse",()=>ox,"parseAsync",()=>ok,"partialRecord",()=>so,"pipe",()=>sC,"positive",()=>av,"prefault",()=>sZ,"preprocess",()=>s9,"prettifyError",()=>ek,"promise",()=>sK,"property",()=>aN,"readonly",()=>sJ,"record",()=>sa,"refine",()=>s4,"regex",()=>az,"regexes",()=>ol,"registry",()=>nb,"safeDecode",()=>oj,"safeDecodeAsync",()=>oD,"safeEncode",()=>oO,"safeEncodeAsync",()=>oN,"safeParse",()=>oI,"safeParseAsync",()=>ow,"set",()=>sd,"setErrorMap",()=>s7,"size",()=>ax,"startsWith",()=>aO,"strictObject",()=>u2,"string",()=>oA,"stringFormat",()=>u$,"stringbool",()=>s1,"success",()=>sN,"superRefine",()=>s6,"symbol",()=>uC,"templateLiteral",()=>sW,"toJSONSchema",()=>oo,"toLowerCase",()=>aA,"toUpperCase",()=>aC,"transform",()=>s_,"treeifyError",()=>eb,"trim",()=>aT,"tuple",()=>sr,"uint32",()=>uU,"uint64",()=>uT,"ulid",()=>o3,"undefined",()=>uR,"union",()=>u7,"unknown",()=>uV,"uppercase",()=>aZ,"url",()=>oX,"util",()=>od,"uuid",()=>oW,"uuidv4",()=>oB,"uuidv6",()=>oV,"uuidv7",()=>oG,"void",()=>uq,"xid",()=>o5],61852),e.s([],57564),e.s(["$ZodAsyncError",()=>n,"$ZodEncodeError",()=>a,"$brand",()=>r,"$constructor",()=>i,"NEVER",()=>t,"config",()=>u,"globalConfig",()=>o],90949);let t=Object.freeze({status:"aborted"});function i(e,t,i){function r(i,r){var n;for(let a in Object.defineProperty(i,"_zod",{value:i._zod??{},enumerable:!1}),(n=i._zod).traits??(n.traits=new Set),i._zod.traits.add(e),t(i,r),o.prototype)a in i||Object.defineProperty(i,a,{value:o.prototype[a].bind(i)});i._zod.constr=o,i._zod.def=r}let n=i?.Parent??Object;class a extends n{}function o(e){var t;let n=i?.Parent?new a:this;for(let i of(r(n,e),(t=n._zod).deferred??(t.deferred=[]),n._zod.deferred))i();return n}return Object.defineProperty(a,"name",{value:e}),Object.defineProperty(o,"init",{value:r}),Object.defineProperty(o,Symbol.hasInstance,{value:t=>!!i?.Parent&&t instanceof i.Parent||t?._zod?.traits?.has(e)}),Object.defineProperty(o,"name",{value:e}),o}let r=Symbol("zod_brand");class n extends Error{constructor(){super("Encountered Promise during synchronous parse. Use .parseAsync() instead.")}}class a extends Error{constructor(e){super(`Encountered unidirectional transform during encode: ${e}`),this.name="ZodEncodeError"}}let o={};function u(e){return e&&Object.assign(o,e),o}function s(e){return e}function l(e){return e}function d(e){}function c(e){throw Error()}function m(e){}function f(e){let t=Object.values(e).filter(e=>"number"==typeof e);return Object.entries(e).filter(([e,i])=>-1===t.indexOf(+e)).map(([e,t])=>t)}function p(e,t="|"){return e.map(e=>M(e)).join(t)}function v(e,t){return"bigint"==typeof t?t.toString():t}function g(e){return{get value(){{let t=e();return Object.defineProperty(this,"value",{value:t}),t}}}}function h(e){return null==e}function $(e){let t=+!!e.startsWith("^"),i=e.endsWith("$")?e.length-1:e.length;return e.slice(t,i)}function _(e,t){let i=(e.toString().split(".")[1]||"").length,r=t.toString(),n=(r.split(".")[1]||"").length;if(0===n&&/\d?e-\d?/.test(r)){let e=r.match(/\d?e-(\d?)/);e?.[1]&&(n=Number.parseInt(e[1]))}let a=i>n?i:n;return Number.parseInt(e.toFixed(a).replace(".",""))%Number.parseInt(t.toFixed(a).replace(".",""))/10**a}e.s(["BIGINT_FORMAT_RANGES",()=>V,"Class",()=>ep,"NUMBER_FORMAT_RANGES",()=>B,"aborted",()=>ee,"allowsEval",()=>N,"assert",()=>m,"assertEqual",()=>s,"assertIs",()=>d,"assertNever",()=>c,"assertNotEqual",()=>l,"assignProp",()=>k,"base64ToUint8Array",()=>es,"base64urlToUint8Array",()=>ed,"cached",()=>g,"captureStackTrace",()=>O,"cleanEnum",()=>eu,"cleanRegex",()=>$,"clone",()=>R,"cloneDef",()=>w,"createTransparentProxy",()=>J,"defineLazy",()=>b,"esc",()=>U,"escapeRegex",()=>L,"extend",()=>X,"finalizeIssue",()=>er,"floatSafeRemainder",()=>_,"getElementAtPath",()=>z,"getEnumValues",()=>f,"getLengthableOrigin",()=>ea,"getParsedType",()=>T,"getSizableOrigin",()=>en,"hexToUint8Array",()=>em,"isObject",()=>j,"isPlainObject",()=>D,"issue",()=>eo,"joinValues",()=>p,"jsonStringifyReplacer",()=>v,"merge",()=>Y,"mergeDefs",()=>I,"normalizeParams",()=>F,"nullish",()=>h,"numKeys",()=>E,"objectClone",()=>x,"omit",()=>K,"optionalKeys",()=>W,"partial",()=>H,"pick",()=>G,"prefixIssues",()=>et,"primitiveTypes",()=>C,"promiseAllObject",()=>S,"propertyKeyTypes",()=>A,"randomString",()=>Z,"required",()=>Q,"safeExtend",()=>q,"shallowClone",()=>P,"stringifyPrimitive",()=>M,"uint8ArrayToBase64",()=>el,"uint8ArrayToBase64url",()=>ec,"uint8ArrayToHex",()=>ef,"unwrapMessage",()=>ei],24463);let y=Symbol("evaluating");function b(e,t,i){let r;Object.defineProperty(e,t,{get(){if(r!==y)return void 0===r&&(r=y,r=i()),r},set(i){Object.defineProperty(e,t,{value:i})},configurable:!0})}function x(e){return Object.create(Object.getPrototypeOf(e),Object.getOwnPropertyDescriptors(e))}function k(e,t,i){Object.defineProperty(e,t,{value:i,writable:!0,enumerable:!0,configurable:!0})}function I(...e){let t={};for(let i of e)Object.assign(t,Object.getOwnPropertyDescriptors(i));return Object.defineProperties({},t)}function w(e){return I(e._zod.def)}function z(e,t){return t?t.reduce((e,t)=>e?.[t],e):e}function S(e){let t=Object.keys(e);return Promise.all(t.map(t=>e[t])).then(e=>{let i={};for(let r=0;r<t.length;r++)i[t[r]]=e[r];return i})}function Z(e=10){let t="abcdefghijklmnopqrstuvwxyz",i="";for(let r=0;r<e;r++)i+=t[Math.floor(Math.random()*t.length)];return i}function U(e){return JSON.stringify(e)}let O="captureStackTrace"in Error?Error.captureStackTrace:(...e)=>{};function j(e){return"object"==typeof e&&null!==e&&!Array.isArray(e)}let N=g(()=>{if("undefined"!=typeof navigator&&navigator?.userAgent?.includes("Cloudflare"))return!1;try{return Function(""),!0}catch(e){return!1}});function D(e){if(!1===j(e))return!1;let t=e.constructor;if(void 0===t)return!0;let i=t.prototype;return!1!==j(i)&&!1!==Object.prototype.hasOwnProperty.call(i,"isPrototypeOf")}function P(e){return D(e)?{...e}:e}function E(e){let t=0;for(let i in e)Object.prototype.hasOwnProperty.call(e,i)&&t++;return t}let T=e=>{let t=typeof e;switch(t){case"undefined":return"undefined";case"string":return"string";case"number":return Number.isNaN(e)?"nan":"number";case"boolean":return"boolean";case"function":return"function";case"bigint":return"bigint";case"symbol":return"symbol";case"object":if(Array.isArray(e))return"array";if(null===e)return"null";if(e.then&&"function"==typeof e.then&&e.catch&&"function"==typeof e.catch)return"promise";if("undefined"!=typeof Map&&e instanceof Map)return"map";if("undefined"!=typeof Set&&e instanceof Set)return"set";if("undefined"!=typeof Date&&e instanceof Date)return"date";if("undefined"!=typeof File&&e instanceof File)return"file";return"object";default:throw Error(`Unknown data type: ${t}`)}},A=new Set(["string","number","symbol"]),C=new Set(["string","number","bigint","boolean","symbol","undefined"]);function L(e){return e.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}function R(e,t,i){let r=new e._zod.constr(t??e._zod.def);return(!t||i?.parent)&&(r._zod.parent=e),r}function F(e){if(!e)return{};if("string"==typeof e)return{error:()=>e};if(e?.message!==void 0){if(e?.error!==void 0)throw Error("Cannot specify both `message` and `error` params");e.error=e.message}return(delete e.message,"string"==typeof e.error)?{...e,error:()=>e.error}:e}function J(e){let t;return new Proxy({},{get:(i,r,n)=>(t??(t=e()),Reflect.get(t,r,n)),set:(i,r,n,a)=>(t??(t=e()),Reflect.set(t,r,n,a)),has:(i,r)=>(t??(t=e()),Reflect.has(t,r)),deleteProperty:(i,r)=>(t??(t=e()),Reflect.deleteProperty(t,r)),ownKeys:i=>(t??(t=e()),Reflect.ownKeys(t)),getOwnPropertyDescriptor:(i,r)=>(t??(t=e()),Reflect.getOwnPropertyDescriptor(t,r)),defineProperty:(i,r,n)=>(t??(t=e()),Reflect.defineProperty(t,r,n))})}function M(e){return"bigint"==typeof e?e.toString()+"n":"string"==typeof e?`"${e}"`:`${e}`}function W(e){return Object.keys(e).filter(t=>"optional"===e[t]._zod.optin&&"optional"===e[t]._zod.optout)}let B={safeint:[Number.MIN_SAFE_INTEGER,Number.MAX_SAFE_INTEGER],int32:[-0x80000000,0x7fffffff],uint32:[0,0xffffffff],float32:[-34028234663852886e22,34028234663852886e22],float64:[-Number.MAX_VALUE,Number.MAX_VALUE]},V={int64:[BigInt("-9223372036854775808"),BigInt("9223372036854775807")],uint64:[BigInt(0),BigInt("18446744073709551615")]};function G(e,t){let i=e._zod.def,r=I(e._zod.def,{get shape(){let e={};for(let r in t){if(!(r in i.shape))throw Error(`Unrecognized key: "${r}"`);t[r]&&(e[r]=i.shape[r])}return k(this,"shape",e),e},checks:[]});return R(e,r)}function K(e,t){let i=e._zod.def,r=I(e._zod.def,{get shape(){let r={...e._zod.def.shape};for(let e in t){if(!(e in i.shape))throw Error(`Unrecognized key: "${e}"`);t[e]&&delete r[e]}return k(this,"shape",r),r},checks:[]});return R(e,r)}function X(e,t){if(!D(t))throw Error("Invalid input to extend: expected a plain object");let i=e._zod.def.checks;if(i&&i.length>0)throw Error("Object schemas containing refinements cannot be extended. Use `.safeExtend()` instead.");let r=I(e._zod.def,{get shape(){let i={...e._zod.def.shape,...t};return k(this,"shape",i),i},checks:[]});return R(e,r)}function q(e,t){if(!D(t))throw Error("Invalid input to safeExtend: expected a plain object");let i={...e._zod.def,get shape(){let i={...e._zod.def.shape,...t};return k(this,"shape",i),i},checks:e._zod.def.checks};return R(e,i)}function Y(e,t){let i=I(e._zod.def,{get shape(){let i={...e._zod.def.shape,...t._zod.def.shape};return k(this,"shape",i),i},get catchall(){return t._zod.def.catchall},checks:[]});return R(e,i)}function H(e,t,i){let r=I(t._zod.def,{get shape(){let r=t._zod.def.shape,n={...r};if(i)for(let t in i){if(!(t in r))throw Error(`Unrecognized key: "${t}"`);i[t]&&(n[t]=e?new e({type:"optional",innerType:r[t]}):r[t])}else for(let t in r)n[t]=e?new e({type:"optional",innerType:r[t]}):r[t];return k(this,"shape",n),n},checks:[]});return R(t,r)}function Q(e,t,i){let r=I(t._zod.def,{get shape(){let r=t._zod.def.shape,n={...r};if(i)for(let t in i){if(!(t in n))throw Error(`Unrecognized key: "${t}"`);i[t]&&(n[t]=new e({type:"nonoptional",innerType:r[t]}))}else for(let t in r)n[t]=new e({type:"nonoptional",innerType:r[t]});return k(this,"shape",n),n},checks:[]});return R(t,r)}function ee(e,t=0){if(!0===e.aborted)return!0;for(let i=t;i<e.issues.length;i++)if(e.issues[i]?.continue!==!0)return!0;return!1}function et(e,t){return t.map(t=>(t.path??(t.path=[]),t.path.unshift(e),t))}function ei(e){return"string"==typeof e?e:e?.message}function er(e,t,i){let r={...e,path:e.path??[]};return e.message||(r.message=ei(e.inst?._zod.def?.error?.(e))??ei(t?.error?.(e))??ei(i.customError?.(e))??ei(i.localeError?.(e))??"Invalid input"),delete r.inst,delete r.continue,t?.reportInput||delete r.input,r}function en(e){return e instanceof Set?"set":e instanceof Map?"map":e instanceof File?"file":"unknown"}function ea(e){return Array.isArray(e)?"array":"string"==typeof e?"string":"unknown"}function eo(...e){let[t,i,r]=e;return"string"==typeof t?{message:t,code:"custom",input:i,inst:r}:{...t}}function eu(e){return Object.entries(e).filter(([e,t])=>Number.isNaN(Number.parseInt(e,10))).map(e=>e[1])}function es(e){let t=atob(e),i=new Uint8Array(t.length);for(let e=0;e<t.length;e++)i[e]=t.charCodeAt(e);return i}function el(e){let t="";for(let i=0;i<e.length;i++)t+=String.fromCharCode(e[i]);return btoa(t)}function ed(e){let t=e.replace(/-/g,"+").replace(/_/g,"/"),i="=".repeat((4-t.length%4)%4);return es(t+i)}function ec(e){return el(e).replace(/\+/g,"-").replace(/\//g,"_").replace(/=/g,"")}function em(e){let t=e.replace(/^0x/,"");if(t.length%2!=0)throw Error("Invalid hex string length");let i=new Uint8Array(t.length/2);for(let e=0;e<t.length;e+=2)i[e/2]=Number.parseInt(t.slice(e,e+2),16);return i}function ef(e){return Array.from(e).map(e=>e.toString(16).padStart(2,"0")).join("")}class ep{constructor(...e){}}function ev(){return{localeError:(()=>{let e={string:{unit:"characters",verb:"to have"},file:{unit:"bytes",verb:"to have"},array:{unit:"items",verb:"to have"},set:{unit:"items",verb:"to have"}},t={regex:"input",email:"email address",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO datetime",date:"ISO date",time:"ISO time",duration:"ISO duration",ipv4:"IPv4 address",ipv6:"IPv6 address",cidrv4:"IPv4 range",cidrv6:"IPv6 range",base64:"base64-encoded string",base64url:"base64url-encoded string",json_string:"JSON string",e164:"E.164 number",jwt:"JWT",template_literal:"input"};return i=>{switch(i.code){case"invalid_type":return`Invalid input: expected ${i.expected}, received ${(e=>{let t=typeof e;switch(t){case"number":return Number.isNaN(e)?"NaN":"number";case"object":if(Array.isArray(e))return"array";if(null===e)return"null";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}return t})(i.input)}`;case"invalid_value":if(1===i.values.length)return`Invalid input: expected ${M(i.values[0])}`;return`Invalid option: expected one of ${p(i.values,"|")}`;case"too_big":{let t=i.inclusive?"<=":"<",r=e[i.origin]??null;if(r)return`Too big: expected ${i.origin??"value"} to have ${t}${i.maximum.toString()} ${r.unit??"elements"}`;return`Too big: expected ${i.origin??"value"} to be ${t}${i.maximum.toString()}`}case"too_small":{let t=i.inclusive?">=":">",r=e[i.origin]??null;if(r)return`Too small: expected ${i.origin} to have ${t}${i.minimum.toString()} ${r.unit}`;return`Too small: expected ${i.origin} to be ${t}${i.minimum.toString()}`}case"invalid_format":if("starts_with"===i.format)return`Invalid string: must start with "${i.prefix}"`;if("ends_with"===i.format)return`Invalid string: must end with "${i.suffix}"`;if("includes"===i.format)return`Invalid string: must include "${i.includes}"`;if("regex"===i.format)return`Invalid string: must match pattern ${i.pattern}`;return`Invalid ${t[i.format]??i.format}`;case"not_multiple_of":return`Invalid number: must be a multiple of ${i.divisor}`;case"unrecognized_keys":return`Unrecognized key${i.keys.length>1?"s":""}: ${p(i.keys,", ")}`;case"invalid_key":return`Invalid key in ${i.origin}`;case"invalid_union":default:return"Invalid input";case"invalid_element":return`Invalid value in ${i.origin}`}}})()}}u(ev()),e.i(57564),e.s(["$ZodAny",()=>iG,"$ZodArray",()=>iQ,"$ZodAsyncError",()=>n,"$ZodBase64",()=>iN,"$ZodBase64URL",()=>iP,"$ZodBigInt",()=>iJ,"$ZodBigIntFormat",()=>iM,"$ZodBoolean",()=>iF,"$ZodCIDRv4",()=>iU,"$ZodCIDRv6",()=>iO,"$ZodCUID",()=>i$,"$ZodCUID2",()=>i_,"$ZodCatch",()=>ry,"$ZodCheck",()=>tB,"$ZodCheckBigIntFormat",()=>tY,"$ZodCheckEndsWith",()=>ie,"$ZodCheckGreaterThan",()=>tK,"$ZodCheckIncludes",()=>t5,"$ZodCheckLengthEquals",()=>t1,"$ZodCheckLessThan",()=>tG,"$ZodCheckLowerCase",()=>t3,"$ZodCheckMaxLength",()=>t6,"$ZodCheckMaxSize",()=>tH,"$ZodCheckMimeType",()=>ir,"$ZodCheckMinLength",()=>t0,"$ZodCheckMinSize",()=>tQ,"$ZodCheckMultipleOf",()=>tX,"$ZodCheckNumberFormat",()=>tq,"$ZodCheckOverwrite",()=>ia,"$ZodCheckProperty",()=>ii,"$ZodCheckRegex",()=>t9,"$ZodCheckSizeEquals",()=>t4,"$ZodCheckStartsWith",()=>t8,"$ZodCheckStringFormat",()=>t2,"$ZodCheckUpperCase",()=>t7,"$ZodCodec",()=>rI,"$ZodCustom",()=>rD,"$ZodCustomStringFormat",()=>iC,"$ZodDate",()=>iY,"$ZodDefault",()=>rp,"$ZodDiscriminatedUnion",()=>i7,"$ZodE164",()=>iE,"$ZodEmail",()=>ip,"$ZodEmoji",()=>ig,"$ZodEncodeError",()=>a,"$ZodEnum",()=>ru,"$ZodError",()=>eh,"$ZodFile",()=>rl,"$ZodFunction",()=>rO,"$ZodGUID",()=>ic,"$ZodIPv4",()=>iS,"$ZodIPv6",()=>iZ,"$ZodISODate",()=>iI,"$ZodISODateTime",()=>ik,"$ZodISODuration",()=>iz,"$ZodISOTime",()=>iw,"$ZodIntersection",()=>i5,"$ZodJWT",()=>iA,"$ZodKSUID",()=>ix,"$ZodLazy",()=>rN,"$ZodLiteral",()=>rs,"$ZodMap",()=>rr,"$ZodNaN",()=>rb,"$ZodNanoID",()=>ih,"$ZodNever",()=>iX,"$ZodNonOptional",()=>rh,"$ZodNull",()=>iV,"$ZodNullable",()=>rf,"$ZodNumber",()=>iL,"$ZodNumberFormat",()=>iR,"$ZodObject",()=>i1,"$ZodObjectJIT",()=>i2,"$ZodOptional",()=>rm,"$ZodPipe",()=>rx,"$ZodPrefault",()=>rg,"$ZodPromise",()=>rj,"$ZodReadonly",()=>rS,"$ZodRealError",()=>e$,"$ZodRecord",()=>ri,"$ZodRegistry",()=>ny,"$ZodSet",()=>ra,"$ZodString",()=>il,"$ZodStringFormat",()=>id,"$ZodSuccess",()=>r_,"$ZodSymbol",()=>iW,"$ZodTemplateLiteral",()=>rU,"$ZodTransform",()=>rd,"$ZodTuple",()=>re,"$ZodType",()=>is,"$ZodULID",()=>iy,"$ZodURL",()=>iv,"$ZodUUID",()=>im,"$ZodUndefined",()=>iB,"$ZodUnion",()=>i3,"$ZodUnknown",()=>iK,"$ZodVoid",()=>iq,"$ZodXID",()=>ib,"$brand",()=>r,"$constructor",()=>i,"$input",()=>n_,"$output",()=>n$,"Doc",()=>io,"JSONSchema",()=>ou,"JSONSchemaGenerator",()=>oa,"NEVER",()=>t,"TimePrecision",()=>nG,"_any",()=>an,"_array",()=>aL,"_base64",()=>nM,"_base64url",()=>nW,"_bigint",()=>n7,"_boolean",()=>n9,"_catch",()=>a1,"_check",()=>oi,"_cidrv4",()=>nF,"_cidrv6",()=>nJ,"_coercedBigint",()=>n5,"_coercedBoolean",()=>n3,"_coercedDate",()=>al,"_coercedNumber",()=>nQ,"_coercedString",()=>nI,"_cuid",()=>nP,"_cuid2",()=>nE,"_custom",()=>a8,"_date",()=>as,"_decode",()=>eP,"_decodeAsync",()=>eC,"_default",()=>a4,"_discriminatedUnion",()=>aF,"_e164",()=>nB,"_email",()=>nw,"_emoji",()=>nN,"_encode",()=>eN,"_encodeAsync",()=>eT,"_endsWith",()=>aj,"_enum",()=>aG,"_file",()=>aq,"_float32",()=>n6,"_float64",()=>n0,"_gt",()=>af,"_gte",()=>ap,"_guid",()=>nz,"_includes",()=>aU,"_int",()=>n4,"_int32",()=>n1,"_int64",()=>n8,"_intersection",()=>aJ,"_ipv4",()=>nL,"_ipv6",()=>nR,"_isoDate",()=>nX,"_isoDateTime",()=>nK,"_isoDuration",()=>nY,"_isoTime",()=>nq,"_jwt",()=>nV,"_ksuid",()=>nC,"_lazy",()=>a7,"_length",()=>aw,"_literal",()=>aX,"_lowercase",()=>aS,"_lt",()=>ac,"_lte",()=>am,"_map",()=>aB,"_max",()=>am,"_maxLength",()=>ak,"_maxSize",()=>ay,"_mime",()=>aD,"_min",()=>ap,"_minLength",()=>aI,"_minSize",()=>ab,"_multipleOf",()=>a_,"_nan",()=>ad,"_nanoid",()=>nD,"_nativeEnum",()=>aK,"_negative",()=>ag,"_never",()=>ao,"_nonnegative",()=>a$,"_nonoptional",()=>a6,"_nonpositive",()=>ah,"_normalize",()=>aE,"_null",()=>ar,"_nullable",()=>aQ,"_number",()=>nH,"_optional",()=>aH,"_overwrite",()=>aP,"_parse",()=>eI,"_parseAsync",()=>ez,"_pipe",()=>a2,"_positive",()=>av,"_promise",()=>a5,"_property",()=>aN,"_readonly",()=>a9,"_record",()=>aW,"_refine",()=>oe,"_regex",()=>az,"_safeDecode",()=>eJ,"_safeDecodeAsync",()=>eV,"_safeEncode",()=>eR,"_safeEncodeAsync",()=>eW,"_safeParse",()=>eZ,"_safeParseAsync",()=>eO,"_set",()=>aV,"_size",()=>ax,"_startsWith",()=>aO,"_string",()=>nk,"_stringFormat",()=>on,"_stringbool",()=>or,"_success",()=>a0,"_superRefine",()=>ot,"_symbol",()=>at,"_templateLiteral",()=>a3,"_toLowerCase",()=>aA,"_toUpperCase",()=>aC,"_transform",()=>aY,"_trim",()=>aT,"_tuple",()=>aM,"_uint32",()=>n2,"_uint64",()=>ae,"_ulid",()=>nT,"_undefined",()=>ai,"_union",()=>aR,"_unknown",()=>aa,"_uppercase",()=>aZ,"_url",()=>nj,"_uuid",()=>nS,"_uuidv4",()=>nZ,"_uuidv6",()=>nU,"_uuidv7",()=>nO,"_void",()=>au,"_xid",()=>nA,"clone",()=>R,"config",()=>u,"decode",()=>eE,"decodeAsync",()=>eL,"encode",()=>eD,"encodeAsync",()=>eA,"flattenError",()=>e_,"formatError",()=>ey,"globalConfig",()=>o,"globalRegistry",()=>nx,"isValidBase64",()=>ij,"isValidBase64URL",()=>iD,"isValidJWT",()=>iT,"locales",()=>nh,"parse",()=>ew,"parseAsync",()=>eS,"prettifyError",()=>ek,"regexes",()=>rA,"registry",()=>nb,"safeDecode",()=>eM,"safeDecodeAsync",()=>eG,"safeEncode",()=>eF,"safeEncodeAsync",()=>eB,"safeParse",()=>eU,"safeParseAsync",()=>ej,"toDotPath",()=>ex,"toJSONSchema",()=>oo,"treeifyError",()=>eb,"util",()=>rT,"version",()=>iu],64519),e.s([],77772),e.i(77772),e.i(90949),e.s(["_decode",()=>eP,"_decodeAsync",()=>eC,"_encode",()=>eN,"_encodeAsync",()=>eT,"_parse",()=>eI,"_parseAsync",()=>ez,"_safeDecode",()=>eJ,"_safeDecodeAsync",()=>eV,"_safeEncode",()=>eR,"_safeEncodeAsync",()=>eW,"_safeParse",()=>eZ,"_safeParseAsync",()=>eO,"decode",()=>eE,"decodeAsync",()=>eL,"encode",()=>eD,"encodeAsync",()=>eA,"parse",()=>ew,"parseAsync",()=>eS,"safeDecode",()=>eM,"safeDecodeAsync",()=>eG,"safeEncode",()=>eF,"safeEncodeAsync",()=>eB,"safeParse",()=>eU,"safeParseAsync",()=>ej],10482),e.s(["$ZodError",()=>eh,"$ZodRealError",()=>e$,"flattenError",()=>e_,"formatError",()=>ey,"prettifyError",()=>ek,"toDotPath",()=>ex,"treeifyError",()=>eb],10051);let eg=(e,t)=>{e.name="$ZodError",Object.defineProperty(e,"_zod",{value:e._zod,enumerable:!1}),Object.defineProperty(e,"issues",{value:t,enumerable:!1}),e.message=JSON.stringify(t,v,2),Object.defineProperty(e,"toString",{value:()=>e.message,enumerable:!1})},eh=i("$ZodError",eg),e$=i("$ZodError",eg,{Parent:Error});function e_(e,t=e=>e.message){let i={},r=[];for(let n of e.issues)n.path.length>0?(i[n.path[0]]=i[n.path[0]]||[],i[n.path[0]].push(t(n))):r.push(t(n));return{formErrors:r,fieldErrors:i}}function ey(e,t){let i=t||function(e){return e.message},r={_errors:[]},n=e=>{for(let t of e.issues)if("invalid_union"===t.code&&t.errors.length)t.errors.map(e=>n({issues:e}));else if("invalid_key"===t.code)n({issues:t.issues});else if("invalid_element"===t.code)n({issues:t.issues});else if(0===t.path.length)r._errors.push(i(t));else{let e=r,n=0;for(;n<t.path.length;){let r=t.path[n];n===t.path.length-1?(e[r]=e[r]||{_errors:[]},e[r]._errors.push(i(t))):e[r]=e[r]||{_errors:[]},e=e[r],n++}}};return n(e),r}function eb(e,t){let i=t||function(e){return e.message},r={errors:[]},n=(e,t=[])=>{var a,o;for(let u of e.issues)if("invalid_union"===u.code&&u.errors.length)u.errors.map(e=>n({issues:e},u.path));else if("invalid_key"===u.code)n({issues:u.issues},u.path);else if("invalid_element"===u.code)n({issues:u.issues},u.path);else{let e=[...t,...u.path];if(0===e.length){r.errors.push(i(u));continue}let n=r,s=0;for(;s<e.length;){let t=e[s],r=s===e.length-1;"string"==typeof t?(n.properties??(n.properties={}),(a=n.properties)[t]??(a[t]={errors:[]}),n=n.properties[t]):(n.items??(n.items=[]),(o=n.items)[t]??(o[t]={errors:[]}),n=n.items[t]),r&&n.errors.push(i(u)),s++}}};return n(e),r}function ex(e){let t=[];for(let i of e.map(e=>"object"==typeof e?e.key:e))"number"==typeof i?t.push(`[${i}]`):"symbol"==typeof i?t.push(`[${JSON.stringify(String(i))}]`):/[^\w$]/.test(i)?t.push(`[${JSON.stringify(i)}]`):(t.length&&t.push("."),t.push(i));return t.join("")}function ek(e){let t=[];for(let i of[...e.issues].sort((e,t)=>(e.path??[]).length-(t.path??[]).length))t.push(`✖ ${i.message}`),i.path?.length&&t.push(`  → at ${ex(i.path)}`);return t.join("\n")}let eI=e=>(t,i,r,a)=>{let o=r?Object.assign(r,{async:!1}):{async:!1},s=t._zod.run({value:i,issues:[]},o);if(s instanceof Promise)throw new n;if(s.issues.length){let t=new(a?.Err??e)(s.issues.map(e=>er(e,o,u())));throw O(t,a?.callee),t}return s.value},ew=eI(e$),ez=e=>async(t,i,r,n)=>{let a=r?Object.assign(r,{async:!0}):{async:!0},o=t._zod.run({value:i,issues:[]},a);if(o instanceof Promise&&(o=await o),o.issues.length){let t=new(n?.Err??e)(o.issues.map(e=>er(e,a,u())));throw O(t,n?.callee),t}return o.value},eS=ez(e$),eZ=e=>(t,i,r)=>{let a=r?{...r,async:!1}:{async:!1},o=t._zod.run({value:i,issues:[]},a);if(o instanceof Promise)throw new n;return o.issues.length?{success:!1,error:new(e??eh)(o.issues.map(e=>er(e,a,u())))}:{success:!0,data:o.value}},eU=eZ(e$),eO=e=>async(t,i,r)=>{let n=r?Object.assign(r,{async:!0}):{async:!0},a=t._zod.run({value:i,issues:[]},n);return a instanceof Promise&&(a=await a),a.issues.length?{success:!1,error:new e(a.issues.map(e=>er(e,n,u())))}:{success:!0,data:a.value}},ej=eO(e$),eN=e=>(t,i,r)=>{let n=r?Object.assign(r,{direction:"backward"}):{direction:"backward"};return eI(e)(t,i,n)},eD=eN(e$),eP=e=>(t,i,r)=>eI(e)(t,i,r),eE=eP(e$),eT=e=>async(t,i,r)=>{let n=r?Object.assign(r,{direction:"backward"}):{direction:"backward"};return ez(e)(t,i,n)},eA=eT(e$),eC=e=>async(t,i,r)=>ez(e)(t,i,r),eL=eC(e$),eR=e=>(t,i,r)=>{let n=r?Object.assign(r,{direction:"backward"}):{direction:"backward"};return eZ(e)(t,i,n)},eF=eR(e$),eJ=e=>(t,i,r)=>eZ(e)(t,i,r),eM=eJ(e$),eW=e=>async(t,i,r)=>{let n=r?Object.assign(r,{direction:"backward"}):{direction:"backward"};return eO(e)(t,i,n)},eB=eW(e$),eV=e=>async(t,i,r)=>eO(e)(t,i,r),eG=eV(e$);e.i(10482),e.i(10051),e.s(["$ZodAny",()=>iG,"$ZodArray",()=>iQ,"$ZodBase64",()=>iN,"$ZodBase64URL",()=>iP,"$ZodBigInt",()=>iJ,"$ZodBigIntFormat",()=>iM,"$ZodBoolean",()=>iF,"$ZodCIDRv4",()=>iU,"$ZodCIDRv6",()=>iO,"$ZodCUID",()=>i$,"$ZodCUID2",()=>i_,"$ZodCatch",()=>ry,"$ZodCodec",()=>rI,"$ZodCustom",()=>rD,"$ZodCustomStringFormat",()=>iC,"$ZodDate",()=>iY,"$ZodDefault",()=>rp,"$ZodDiscriminatedUnion",()=>i7,"$ZodE164",()=>iE,"$ZodEmail",()=>ip,"$ZodEmoji",()=>ig,"$ZodEnum",()=>ru,"$ZodFile",()=>rl,"$ZodFunction",()=>rO,"$ZodGUID",()=>ic,"$ZodIPv4",()=>iS,"$ZodIPv6",()=>iZ,"$ZodISODate",()=>iI,"$ZodISODateTime",()=>ik,"$ZodISODuration",()=>iz,"$ZodISOTime",()=>iw,"$ZodIntersection",()=>i5,"$ZodJWT",()=>iA,"$ZodKSUID",()=>ix,"$ZodLazy",()=>rN,"$ZodLiteral",()=>rs,"$ZodMap",()=>rr,"$ZodNaN",()=>rb,"$ZodNanoID",()=>ih,"$ZodNever",()=>iX,"$ZodNonOptional",()=>rh,"$ZodNull",()=>iV,"$ZodNullable",()=>rf,"$ZodNumber",()=>iL,"$ZodNumberFormat",()=>iR,"$ZodObject",()=>i1,"$ZodObjectJIT",()=>i2,"$ZodOptional",()=>rm,"$ZodPipe",()=>rx,"$ZodPrefault",()=>rg,"$ZodPromise",()=>rj,"$ZodReadonly",()=>rS,"$ZodRecord",()=>ri,"$ZodSet",()=>ra,"$ZodString",()=>il,"$ZodStringFormat",()=>id,"$ZodSuccess",()=>r_,"$ZodSymbol",()=>iW,"$ZodTemplateLiteral",()=>rU,"$ZodTransform",()=>rd,"$ZodTuple",()=>re,"$ZodType",()=>is,"$ZodULID",()=>iy,"$ZodURL",()=>iv,"$ZodUUID",()=>im,"$ZodUndefined",()=>iB,"$ZodUnion",()=>i3,"$ZodUnknown",()=>iK,"$ZodVoid",()=>iq,"$ZodXID",()=>ib,"clone",()=>R,"isValidBase64",()=>ij,"isValidBase64URL",()=>iD,"isValidJWT",()=>iT],23851),e.s(["$ZodAny",()=>iG,"$ZodArray",()=>iQ,"$ZodBase64",()=>iN,"$ZodBase64URL",()=>iP,"$ZodBigInt",()=>iJ,"$ZodBigIntFormat",()=>iM,"$ZodBoolean",()=>iF,"$ZodCIDRv4",()=>iU,"$ZodCIDRv6",()=>iO,"$ZodCUID",()=>i$,"$ZodCUID2",()=>i_,"$ZodCatch",()=>ry,"$ZodCodec",()=>rI,"$ZodCustom",()=>rD,"$ZodCustomStringFormat",()=>iC,"$ZodDate",()=>iY,"$ZodDefault",()=>rp,"$ZodDiscriminatedUnion",()=>i7,"$ZodE164",()=>iE,"$ZodEmail",()=>ip,"$ZodEmoji",()=>ig,"$ZodEnum",()=>ru,"$ZodFile",()=>rl,"$ZodFunction",()=>rO,"$ZodGUID",()=>ic,"$ZodIPv4",()=>iS,"$ZodIPv6",()=>iZ,"$ZodISODate",()=>iI,"$ZodISODateTime",()=>ik,"$ZodISODuration",()=>iz,"$ZodISOTime",()=>iw,"$ZodIntersection",()=>i5,"$ZodJWT",()=>iA,"$ZodKSUID",()=>ix,"$ZodLazy",()=>rN,"$ZodLiteral",()=>rs,"$ZodMap",()=>rr,"$ZodNaN",()=>rb,"$ZodNanoID",()=>ih,"$ZodNever",()=>iX,"$ZodNonOptional",()=>rh,"$ZodNull",()=>iV,"$ZodNullable",()=>rf,"$ZodNumber",()=>iL,"$ZodNumberFormat",()=>iR,"$ZodObject",()=>i1,"$ZodObjectJIT",()=>i2,"$ZodOptional",()=>rm,"$ZodPipe",()=>rx,"$ZodPrefault",()=>rg,"$ZodPromise",()=>rj,"$ZodReadonly",()=>rS,"$ZodRecord",()=>ri,"$ZodSet",()=>ra,"$ZodString",()=>il,"$ZodStringFormat",()=>id,"$ZodSuccess",()=>r_,"$ZodSymbol",()=>iW,"$ZodTemplateLiteral",()=>rU,"$ZodTransform",()=>rd,"$ZodTuple",()=>re,"$ZodType",()=>is,"$ZodULID",()=>iy,"$ZodURL",()=>iv,"$ZodUUID",()=>im,"$ZodUndefined",()=>iB,"$ZodUnion",()=>i3,"$ZodUnknown",()=>iK,"$ZodVoid",()=>iq,"$ZodXID",()=>ib,"isValidBase64",()=>ij,"isValidBase64URL",()=>iD,"isValidJWT",()=>iT],78351),e.s(["$ZodCheck",()=>tB,"$ZodCheckBigIntFormat",()=>tY,"$ZodCheckEndsWith",()=>ie,"$ZodCheckGreaterThan",()=>tK,"$ZodCheckIncludes",()=>t5,"$ZodCheckLengthEquals",()=>t1,"$ZodCheckLessThan",()=>tG,"$ZodCheckLowerCase",()=>t3,"$ZodCheckMaxLength",()=>t6,"$ZodCheckMaxSize",()=>tH,"$ZodCheckMimeType",()=>ir,"$ZodCheckMinLength",()=>t0,"$ZodCheckMinSize",()=>tQ,"$ZodCheckMultipleOf",()=>tX,"$ZodCheckNumberFormat",()=>tq,"$ZodCheckOverwrite",()=>ia,"$ZodCheckProperty",()=>ii,"$ZodCheckRegex",()=>t9,"$ZodCheckSizeEquals",()=>t4,"$ZodCheckStartsWith",()=>t8,"$ZodCheckStringFormat",()=>t2,"$ZodCheckUpperCase",()=>t7],80057),e.s(["base64",()=>ts,"base64url",()=>tl,"bigint",()=>t_,"boolean",()=>tx,"browserEmail",()=>ti,"cidrv4",()=>to,"cidrv6",()=>tu,"cuid",()=>eK,"cuid2",()=>eX,"date",()=>tp,"datetime",()=>th,"domain",()=>tc,"duration",()=>e4,"e164",()=>tm,"email",()=>e7,"emoji",()=>tr,"extendedDuration",()=>e6,"guid",()=>e0,"hex",()=>tS,"hostname",()=>td,"html5Email",()=>e5,"idnEmail",()=>tt,"integer",()=>ty,"ipv4",()=>tn,"ipv6",()=>ta,"ksuid",()=>eH,"lowercase",()=>tw,"md5_base64",()=>tj,"md5_base64url",()=>tN,"md5_hex",()=>tO,"nanoid",()=>eQ,"null",()=>tk,"number",()=>tb,"rfc5322Email",()=>e8,"sha1_base64",()=>tP,"sha1_base64url",()=>tE,"sha1_hex",()=>tD,"sha256_base64",()=>tA,"sha256_base64url",()=>tC,"sha256_hex",()=>tT,"sha384_base64",()=>tR,"sha384_base64url",()=>tF,"sha384_hex",()=>tL,"sha512_base64",()=>tM,"sha512_base64url",()=>tW,"sha512_hex",()=>tJ,"string",()=>t$,"time",()=>tg,"ulid",()=>eq,"undefined",()=>tI,"unicodeEmail",()=>te,"uppercase",()=>tz,"uuid",()=>e1,"uuid4",()=>e2,"uuid6",()=>e9,"uuid7",()=>e3,"xid",()=>eY],90657);let eK=/^[cC][^\s-]{8,}$/,eX=/^[0-9a-z]+$/,eq=/^[0-9A-HJKMNP-TV-Za-hjkmnp-tv-z]{26}$/,eY=/^[0-9a-vA-V]{20}$/,eH=/^[A-Za-z0-9]{27}$/,eQ=/^[a-zA-Z0-9_-]{21}$/,e4=/^P(?:(\d+W)|(?!.*W)(?=\d|T\d)(\d+Y)?(\d+M)?(\d+D)?(T(?=\d)(\d+H)?(\d+M)?(\d+([.,]\d+)?S)?)?)$/,e6=/^[-+]?P(?!$)(?:(?:[-+]?\d+Y)|(?:[-+]?\d+[.,]\d+Y$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:(?:[-+]?\d+W)|(?:[-+]?\d+[.,]\d+W$))?(?:(?:[-+]?\d+D)|(?:[-+]?\d+[.,]\d+D$))?(?:T(?=[\d+-])(?:(?:[-+]?\d+H)|(?:[-+]?\d+[.,]\d+H$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:[-+]?\d+(?:[.,]\d+)?S)?)??$/,e0=/^([0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12})$/,e1=e=>e?RegExp(`^([0-9a-fA-F]{8}-[0-9a-fA-F]{4}-${e}[0-9a-fA-F]{3}-[89abAB][0-9a-fA-F]{3}-[0-9a-fA-F]{12})$`):/^([0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[1-8][0-9a-fA-F]{3}-[89abAB][0-9a-fA-F]{3}-[0-9a-fA-F]{12}|00000000-0000-0000-0000-000000000000|ffffffff-ffff-ffff-ffff-ffffffffffff)$/,e2=e1(4),e9=e1(6),e3=e1(7),e7=/^(?!\.)(?!.*\.\.)([A-Za-z0-9_'+\-\.]*)[A-Za-z0-9_+-]@([A-Za-z0-9][A-Za-z0-9\-]*\.)+[A-Za-z]{2,}$/,e5=/^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/,e8=/^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/,te=/^[^\s@"]{1,64}@[^\s@]{1,255}$/u,tt=/^[^\s@"]{1,64}@[^\s@]{1,255}$/u,ti=/^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;function tr(){return RegExp("^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$","u")}let tn=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/,ta=/^(([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}|::|([0-9a-fA-F]{1,4})?::([0-9a-fA-F]{1,4}:?){0,6})$/,to=/^((25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\/([0-9]|[1-2][0-9]|3[0-2])$/,tu=/^(([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}|::|([0-9a-fA-F]{1,4})?::([0-9a-fA-F]{1,4}:?){0,6})\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/,ts=/^$|^(?:[0-9a-zA-Z+/]{4})*(?:(?:[0-9a-zA-Z+/]{2}==)|(?:[0-9a-zA-Z+/]{3}=))?$/,tl=/^[A-Za-z0-9_-]*$/,td=/^(?=.{1,253}\.?$)[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[-0-9a-zA-Z]{0,61}[0-9a-zA-Z])?)*\.?$/,tc=/^([a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?\.)+[a-zA-Z]{2,}$/,tm=/^\+(?:[0-9]){6,14}[0-9]$/,tf="(?:(?:\\d\\d[2468][048]|\\d\\d[13579][26]|\\d\\d0[48]|[02468][048]00|[13579][26]00)-02-29|\\d{4}-(?:(?:0[13578]|1[02])-(?:0[1-9]|[12]\\d|3[01])|(?:0[469]|11)-(?:0[1-9]|[12]\\d|30)|(?:02)-(?:0[1-9]|1\\d|2[0-8])))",tp=RegExp(`^${tf}$`);function tv(e){let t="(?:[01]\\d|2[0-3]):[0-5]\\d";return"number"==typeof e.precision?-1===e.precision?`${t}`:0===e.precision?`${t}:[0-5]\\d`:`${t}:[0-5]\\d\\.\\d{${e.precision}}`:`${t}(?::[0-5]\\d(?:\\.\\d+)?)?`}function tg(e){return RegExp(`^${tv(e)}$`)}function th(e){let t=tv({precision:e.precision}),i=["Z"];e.local&&i.push(""),e.offset&&i.push("([+-](?:[01]\\d|2[0-3]):[0-5]\\d)");let r=`${t}(?:${i.join("|")})`;return RegExp(`^${tf}T(?:${r})$`)}let t$=e=>{let t=e?`[\\s\\S]{${e?.minimum??0},${e?.maximum??""}}`:"[\\s\\S]*";return RegExp(`^${t}$`)},t_=/^\d+n?$/,ty=/^\d+$/,tb=/^-?\d+(?:\.\d+)?/i,tx=/true|false/i,tk=/null/i,tI=/undefined/i,tw=/^[^A-Z]*$/,tz=/^[^a-z]*$/,tS=/^[0-9a-fA-F]*$/;function tZ(e,t){return RegExp(`^[A-Za-z0-9+/]{${e}}${t}$`)}function tU(e){return RegExp(`^[A-Za-z0-9-_]{${e}}$`)}let tO=/^[0-9a-fA-F]{32}$/,tj=tZ(22,"=="),tN=tU(22),tD=/^[0-9a-fA-F]{40}$/,tP=tZ(27,"="),tE=tU(27),tT=/^[0-9a-fA-F]{64}$/,tA=tZ(43,"="),tC=tU(43),tL=/^[0-9a-fA-F]{96}$/,tR=tZ(64,""),tF=tU(64),tJ=/^[0-9a-fA-F]{128}$/,tM=tZ(86,"=="),tW=tU(86),tB=i("$ZodCheck",(e,t)=>{var i;e._zod??(e._zod={}),e._zod.def=t,(i=e._zod).onattach??(i.onattach=[])}),tV={number:"number",bigint:"bigint",object:"date"},tG=i("$ZodCheckLessThan",(e,t)=>{tB.init(e,t);let i=tV[typeof t.value];e._zod.onattach.push(e=>{let i=e._zod.bag,r=(t.inclusive?i.maximum:i.exclusiveMaximum)??1/0;t.value<r&&(t.inclusive?i.maximum=t.value:i.exclusiveMaximum=t.value)}),e._zod.check=r=>{(t.inclusive?r.value<=t.value:r.value<t.value)||r.issues.push({origin:i,code:"too_big",maximum:t.value,input:r.value,inclusive:t.inclusive,inst:e,continue:!t.abort})}}),tK=i("$ZodCheckGreaterThan",(e,t)=>{tB.init(e,t);let i=tV[typeof t.value];e._zod.onattach.push(e=>{let i=e._zod.bag,r=(t.inclusive?i.minimum:i.exclusiveMinimum)??-1/0;t.value>r&&(t.inclusive?i.minimum=t.value:i.exclusiveMinimum=t.value)}),e._zod.check=r=>{(t.inclusive?r.value>=t.value:r.value>t.value)||r.issues.push({origin:i,code:"too_small",minimum:t.value,input:r.value,inclusive:t.inclusive,inst:e,continue:!t.abort})}}),tX=i("$ZodCheckMultipleOf",(e,t)=>{tB.init(e,t),e._zod.onattach.push(e=>{var i;(i=e._zod.bag).multipleOf??(i.multipleOf=t.value)}),e._zod.check=i=>{if(typeof i.value!=typeof t.value)throw Error("Cannot mix number and bigint in multiple_of check.");("bigint"==typeof i.value?i.value%t.value===BigInt(0):0===_(i.value,t.value))||i.issues.push({origin:typeof i.value,code:"not_multiple_of",divisor:t.value,input:i.value,inst:e,continue:!t.abort})}}),tq=i("$ZodCheckNumberFormat",(e,t)=>{tB.init(e,t),t.format=t.format||"float64";let i=t.format?.includes("int"),r=i?"int":"number",[n,a]=B[t.format];e._zod.onattach.push(e=>{let r=e._zod.bag;r.format=t.format,r.minimum=n,r.maximum=a,i&&(r.pattern=ty)}),e._zod.check=o=>{let u=o.value;if(i){if(!Number.isInteger(u))return void o.issues.push({expected:r,format:t.format,code:"invalid_type",continue:!1,input:u,inst:e});if(!Number.isSafeInteger(u))return void(u>0?o.issues.push({input:u,code:"too_big",maximum:Number.MAX_SAFE_INTEGER,note:"Integers must be within the safe integer range.",inst:e,origin:r,continue:!t.abort}):o.issues.push({input:u,code:"too_small",minimum:Number.MIN_SAFE_INTEGER,note:"Integers must be within the safe integer range.",inst:e,origin:r,continue:!t.abort}))}u<n&&o.issues.push({origin:"number",input:u,code:"too_small",minimum:n,inclusive:!0,inst:e,continue:!t.abort}),u>a&&o.issues.push({origin:"number",input:u,code:"too_big",maximum:a,inst:e})}}),tY=i("$ZodCheckBigIntFormat",(e,t)=>{tB.init(e,t);let[i,r]=V[t.format];e._zod.onattach.push(e=>{let n=e._zod.bag;n.format=t.format,n.minimum=i,n.maximum=r}),e._zod.check=n=>{let a=n.value;a<i&&n.issues.push({origin:"bigint",input:a,code:"too_small",minimum:i,inclusive:!0,inst:e,continue:!t.abort}),a>r&&n.issues.push({origin:"bigint",input:a,code:"too_big",maximum:r,inst:e})}}),tH=i("$ZodCheckMaxSize",(e,t)=>{var i;tB.init(e,t),(i=e._zod.def).when??(i.when=e=>{let t=e.value;return!h(t)&&void 0!==t.size}),e._zod.onattach.push(e=>{let i=e._zod.bag.maximum??1/0;t.maximum<i&&(e._zod.bag.maximum=t.maximum)}),e._zod.check=i=>{let r=i.value;r.size<=t.maximum||i.issues.push({origin:en(r),code:"too_big",maximum:t.maximum,inclusive:!0,input:r,inst:e,continue:!t.abort})}}),tQ=i("$ZodCheckMinSize",(e,t)=>{var i;tB.init(e,t),(i=e._zod.def).when??(i.when=e=>{let t=e.value;return!h(t)&&void 0!==t.size}),e._zod.onattach.push(e=>{let i=e._zod.bag.minimum??-1/0;t.minimum>i&&(e._zod.bag.minimum=t.minimum)}),e._zod.check=i=>{let r=i.value;r.size>=t.minimum||i.issues.push({origin:en(r),code:"too_small",minimum:t.minimum,inclusive:!0,input:r,inst:e,continue:!t.abort})}}),t4=i("$ZodCheckSizeEquals",(e,t)=>{var i;tB.init(e,t),(i=e._zod.def).when??(i.when=e=>{let t=e.value;return!h(t)&&void 0!==t.size}),e._zod.onattach.push(e=>{let i=e._zod.bag;i.minimum=t.size,i.maximum=t.size,i.size=t.size}),e._zod.check=i=>{let r=i.value,n=r.size;if(n===t.size)return;let a=n>t.size;i.issues.push({origin:en(r),...a?{code:"too_big",maximum:t.size}:{code:"too_small",minimum:t.size},inclusive:!0,exact:!0,input:i.value,inst:e,continue:!t.abort})}}),t6=i("$ZodCheckMaxLength",(e,t)=>{var i;tB.init(e,t),(i=e._zod.def).when??(i.when=e=>{let t=e.value;return!h(t)&&void 0!==t.length}),e._zod.onattach.push(e=>{let i=e._zod.bag.maximum??1/0;t.maximum<i&&(e._zod.bag.maximum=t.maximum)}),e._zod.check=i=>{let r=i.value;if(r.length<=t.maximum)return;let n=ea(r);i.issues.push({origin:n,code:"too_big",maximum:t.maximum,inclusive:!0,input:r,inst:e,continue:!t.abort})}}),t0=i("$ZodCheckMinLength",(e,t)=>{var i;tB.init(e,t),(i=e._zod.def).when??(i.when=e=>{let t=e.value;return!h(t)&&void 0!==t.length}),e._zod.onattach.push(e=>{let i=e._zod.bag.minimum??-1/0;t.minimum>i&&(e._zod.bag.minimum=t.minimum)}),e._zod.check=i=>{let r=i.value;if(r.length>=t.minimum)return;let n=ea(r);i.issues.push({origin:n,code:"too_small",minimum:t.minimum,inclusive:!0,input:r,inst:e,continue:!t.abort})}}),t1=i("$ZodCheckLengthEquals",(e,t)=>{var i;tB.init(e,t),(i=e._zod.def).when??(i.when=e=>{let t=e.value;return!h(t)&&void 0!==t.length}),e._zod.onattach.push(e=>{let i=e._zod.bag;i.minimum=t.length,i.maximum=t.length,i.length=t.length}),e._zod.check=i=>{let r=i.value,n=r.length;if(n===t.length)return;let a=ea(r),o=n>t.length;i.issues.push({origin:a,...o?{code:"too_big",maximum:t.length}:{code:"too_small",minimum:t.length},inclusive:!0,exact:!0,input:i.value,inst:e,continue:!t.abort})}}),t2=i("$ZodCheckStringFormat",(e,t)=>{var i,r;tB.init(e,t),e._zod.onattach.push(e=>{let i=e._zod.bag;i.format=t.format,t.pattern&&(i.patterns??(i.patterns=new Set),i.patterns.add(t.pattern))}),t.pattern?(i=e._zod).check??(i.check=i=>{t.pattern.lastIndex=0,t.pattern.test(i.value)||i.issues.push({origin:"string",code:"invalid_format",format:t.format,input:i.value,...t.pattern?{pattern:t.pattern.toString()}:{},inst:e,continue:!t.abort})}):(r=e._zod).check??(r.check=()=>{})}),t9=i("$ZodCheckRegex",(e,t)=>{t2.init(e,t),e._zod.check=i=>{t.pattern.lastIndex=0,t.pattern.test(i.value)||i.issues.push({origin:"string",code:"invalid_format",format:"regex",input:i.value,pattern:t.pattern.toString(),inst:e,continue:!t.abort})}}),t3=i("$ZodCheckLowerCase",(e,t)=>{t.pattern??(t.pattern=tw),t2.init(e,t)}),t7=i("$ZodCheckUpperCase",(e,t)=>{t.pattern??(t.pattern=tz),t2.init(e,t)}),t5=i("$ZodCheckIncludes",(e,t)=>{tB.init(e,t);let i=L(t.includes),r=new RegExp("number"==typeof t.position?`^.{${t.position}}${i}`:i);t.pattern=r,e._zod.onattach.push(e=>{let t=e._zod.bag;t.patterns??(t.patterns=new Set),t.patterns.add(r)}),e._zod.check=i=>{i.value.includes(t.includes,t.position)||i.issues.push({origin:"string",code:"invalid_format",format:"includes",includes:t.includes,input:i.value,inst:e,continue:!t.abort})}}),t8=i("$ZodCheckStartsWith",(e,t)=>{tB.init(e,t);let i=RegExp(`^${L(t.prefix)}.*`);t.pattern??(t.pattern=i),e._zod.onattach.push(e=>{let t=e._zod.bag;t.patterns??(t.patterns=new Set),t.patterns.add(i)}),e._zod.check=i=>{i.value.startsWith(t.prefix)||i.issues.push({origin:"string",code:"invalid_format",format:"starts_with",prefix:t.prefix,input:i.value,inst:e,continue:!t.abort})}}),ie=i("$ZodCheckEndsWith",(e,t)=>{tB.init(e,t);let i=RegExp(`.*${L(t.suffix)}$`);t.pattern??(t.pattern=i),e._zod.onattach.push(e=>{let t=e._zod.bag;t.patterns??(t.patterns=new Set),t.patterns.add(i)}),e._zod.check=i=>{i.value.endsWith(t.suffix)||i.issues.push({origin:"string",code:"invalid_format",format:"ends_with",suffix:t.suffix,input:i.value,inst:e,continue:!t.abort})}});function it(e,t,i){e.issues.length&&t.issues.push(...et(i,e.issues))}let ii=i("$ZodCheckProperty",(e,t)=>{tB.init(e,t),e._zod.check=e=>{let i=t.schema._zod.run({value:e.value[t.property],issues:[]},{});if(i instanceof Promise)return i.then(i=>it(i,e,t.property));it(i,e,t.property)}}),ir=i("$ZodCheckMimeType",(e,t)=>{tB.init(e,t);let i=new Set(t.mime);e._zod.onattach.push(e=>{e._zod.bag.mime=t.mime}),e._zod.check=r=>{i.has(r.value.type)||r.issues.push({code:"invalid_value",values:t.mime,input:r.value.type,inst:e,continue:!t.abort})}}),ia=i("$ZodCheckOverwrite",(e,t)=>{tB.init(e,t),e._zod.check=e=>{e.value=t.tx(e.value)}});e.s(["Doc",()=>io],37554);class io{constructor(e=[]){this.content=[],this.indent=0,this&&(this.args=e)}indented(e){this.indent+=1,e(this),this.indent-=1}write(e){if("function"==typeof e){e(this,{execution:"sync"}),e(this,{execution:"async"});return}let t=e.split("\n").filter(e=>e),i=Math.min(...t.map(e=>e.length-e.trimStart().length));for(let e of t.map(e=>e.slice(i)).map(e=>" ".repeat(2*this.indent)+e))this.content.push(e)}compile(){return Function(...this?.args,[...(this?.content??[""]).map(e=>`  ${e}`)].join("\n"))}}e.s(["version",()=>iu],37230);let iu={major:4,minor:1,patch:5},is=i("$ZodType",(e,t)=>{var i;e??(e={}),e._zod.def=t,e._zod.bag=e._zod.bag||{},e._zod.version=iu;let r=[...e._zod.def.checks??[]];for(let t of(e._zod.traits.has("$ZodCheck")&&r.unshift(e),r))for(let i of t._zod.onattach)i(e);if(0===r.length)(i=e._zod).deferred??(i.deferred=[]),e._zod.deferred?.push(()=>{e._zod.run=e._zod.parse});else{let t=(e,t,i)=>{let r,a=ee(e);for(let o of t){if(o._zod.def.when){if(!o._zod.def.when(e))continue}else if(a)continue;let t=e.issues.length,u=o._zod.check(e);if(u instanceof Promise&&i?.async===!1)throw new n;if(r||u instanceof Promise)r=(r??Promise.resolve()).then(async()=>{await u,e.issues.length!==t&&(a||(a=ee(e,t)))});else{if(e.issues.length===t)continue;a||(a=ee(e,t))}}return r?r.then(()=>e):e},i=(i,a,o)=>{if(ee(i))return i.aborted=!0,i;let u=t(a,r,o);if(u instanceof Promise){if(!1===o.async)throw new n;return u.then(t=>e._zod.parse(t,o))}return e._zod.parse(u,o)};e._zod.run=(a,o)=>{if(o.skipChecks)return e._zod.parse(a,o);if("backward"===o.direction){let t=e._zod.parse({value:a.value,issues:[]},{...o,skipChecks:!0});return t instanceof Promise?t.then(e=>i(e,a,o)):i(t,a,o)}let u=e._zod.parse(a,o);if(u instanceof Promise){if(!1===o.async)throw new n;return u.then(e=>t(e,r,o))}return t(u,r,o)}}e["~standard"]={validate:t=>{try{let i=eU(e,t);return i.success?{value:i.data}:{issues:i.error?.issues}}catch(i){return ej(e,t).then(e=>e.success?{value:e.data}:{issues:e.error?.issues})}},vendor:"zod",version:1}}),il=i("$ZodString",(e,t)=>{is.init(e,t),e._zod.pattern=[...e?._zod.bag?.patterns??[]].pop()??t$(e._zod.bag),e._zod.parse=(i,r)=>{if(t.coerce)try{i.value=String(i.value)}catch(e){}return"string"==typeof i.value||i.issues.push({expected:"string",code:"invalid_type",input:i.value,inst:e}),i}}),id=i("$ZodStringFormat",(e,t)=>{t2.init(e,t),il.init(e,t)}),ic=i("$ZodGUID",(e,t)=>{t.pattern??(t.pattern=e0),id.init(e,t)}),im=i("$ZodUUID",(e,t)=>{if(t.version){let e={v1:1,v2:2,v3:3,v4:4,v5:5,v6:6,v7:7,v8:8}[t.version];if(void 0===e)throw Error(`Invalid UUID version: "${t.version}"`);t.pattern??(t.pattern=e1(e))}else t.pattern??(t.pattern=e1());id.init(e,t)}),ip=i("$ZodEmail",(e,t)=>{t.pattern??(t.pattern=e7),id.init(e,t)}),iv=i("$ZodURL",(e,t)=>{id.init(e,t),e._zod.check=i=>{try{let r=i.value.trim(),n=new URL(r);t.hostname&&(t.hostname.lastIndex=0,t.hostname.test(n.hostname)||i.issues.push({code:"invalid_format",format:"url",note:"Invalid hostname",pattern:td.source,input:i.value,inst:e,continue:!t.abort})),t.protocol&&(t.protocol.lastIndex=0,t.protocol.test(n.protocol.endsWith(":")?n.protocol.slice(0,-1):n.protocol)||i.issues.push({code:"invalid_format",format:"url",note:"Invalid protocol",pattern:t.protocol.source,input:i.value,inst:e,continue:!t.abort})),t.normalize?i.value=n.href:i.value=r;return}catch(r){i.issues.push({code:"invalid_format",format:"url",input:i.value,inst:e,continue:!t.abort})}}}),ig=i("$ZodEmoji",(e,t)=>{t.pattern??(t.pattern=tr()),id.init(e,t)}),ih=i("$ZodNanoID",(e,t)=>{t.pattern??(t.pattern=eQ),id.init(e,t)}),i$=i("$ZodCUID",(e,t)=>{t.pattern??(t.pattern=eK),id.init(e,t)}),i_=i("$ZodCUID2",(e,t)=>{t.pattern??(t.pattern=eX),id.init(e,t)}),iy=i("$ZodULID",(e,t)=>{t.pattern??(t.pattern=eq),id.init(e,t)}),ib=i("$ZodXID",(e,t)=>{t.pattern??(t.pattern=eY),id.init(e,t)}),ix=i("$ZodKSUID",(e,t)=>{t.pattern??(t.pattern=eH),id.init(e,t)}),ik=i("$ZodISODateTime",(e,t)=>{t.pattern??(t.pattern=th(t)),id.init(e,t)}),iI=i("$ZodISODate",(e,t)=>{t.pattern??(t.pattern=tp),id.init(e,t)}),iw=i("$ZodISOTime",(e,t)=>{t.pattern??(t.pattern=tg(t)),id.init(e,t)}),iz=i("$ZodISODuration",(e,t)=>{t.pattern??(t.pattern=e4),id.init(e,t)}),iS=i("$ZodIPv4",(e,t)=>{t.pattern??(t.pattern=tn),id.init(e,t),e._zod.onattach.push(e=>{e._zod.bag.format="ipv4"})}),iZ=i("$ZodIPv6",(e,t)=>{t.pattern??(t.pattern=ta),id.init(e,t),e._zod.onattach.push(e=>{e._zod.bag.format="ipv6"}),e._zod.check=i=>{try{new URL(`http://[${i.value}]`)}catch{i.issues.push({code:"invalid_format",format:"ipv6",input:i.value,inst:e,continue:!t.abort})}}}),iU=i("$ZodCIDRv4",(e,t)=>{t.pattern??(t.pattern=to),id.init(e,t)}),iO=i("$ZodCIDRv6",(e,t)=>{t.pattern??(t.pattern=tu),id.init(e,t),e._zod.check=i=>{let[r,n]=i.value.split("/");try{if(!n)throw Error();let e=Number(n);if(`${e}`!==n||e<0||e>128)throw Error();new URL(`http://[${r}]`)}catch{i.issues.push({code:"invalid_format",format:"cidrv6",input:i.value,inst:e,continue:!t.abort})}}});function ij(e){if(""===e)return!0;if(e.length%4!=0)return!1;try{return atob(e),!0}catch{return!1}}let iN=i("$ZodBase64",(e,t)=>{t.pattern??(t.pattern=ts),id.init(e,t),e._zod.onattach.push(e=>{e._zod.bag.contentEncoding="base64"}),e._zod.check=i=>{ij(i.value)||i.issues.push({code:"invalid_format",format:"base64",input:i.value,inst:e,continue:!t.abort})}});function iD(e){if(!tl.test(e))return!1;let t=e.replace(/[-_]/g,e=>"-"===e?"+":"/");return ij(t.padEnd(4*Math.ceil(t.length/4),"="))}let iP=i("$ZodBase64URL",(e,t)=>{t.pattern??(t.pattern=tl),id.init(e,t),e._zod.onattach.push(e=>{e._zod.bag.contentEncoding="base64url"}),e._zod.check=i=>{iD(i.value)||i.issues.push({code:"invalid_format",format:"base64url",input:i.value,inst:e,continue:!t.abort})}}),iE=i("$ZodE164",(e,t)=>{t.pattern??(t.pattern=tm),id.init(e,t)});function iT(e,t=null){try{let i=e.split(".");if(3!==i.length)return!1;let[r]=i;if(!r)return!1;let n=JSON.parse(atob(r));if("typ"in n&&n?.typ!=="JWT"||!n.alg||t&&(!("alg"in n)||n.alg!==t))return!1;return!0}catch{return!1}}let iA=i("$ZodJWT",(e,t)=>{id.init(e,t),e._zod.check=i=>{iT(i.value,t.alg)||i.issues.push({code:"invalid_format",format:"jwt",input:i.value,inst:e,continue:!t.abort})}}),iC=i("$ZodCustomStringFormat",(e,t)=>{id.init(e,t),e._zod.check=i=>{t.fn(i.value)||i.issues.push({code:"invalid_format",format:t.format,input:i.value,inst:e,continue:!t.abort})}}),iL=i("$ZodNumber",(e,t)=>{is.init(e,t),e._zod.pattern=e._zod.bag.pattern??tb,e._zod.parse=(i,r)=>{if(t.coerce)try{i.value=Number(i.value)}catch(e){}let n=i.value;if("number"==typeof n&&!Number.isNaN(n)&&Number.isFinite(n))return i;let a="number"==typeof n?Number.isNaN(n)?"NaN":Number.isFinite(n)?void 0:"Infinity":void 0;return i.issues.push({expected:"number",code:"invalid_type",input:n,inst:e,...a?{received:a}:{}}),i}}),iR=i("$ZodNumber",(e,t)=>{tq.init(e,t),iL.init(e,t)}),iF=i("$ZodBoolean",(e,t)=>{is.init(e,t),e._zod.pattern=tx,e._zod.parse=(i,r)=>{if(t.coerce)try{i.value=!!i.value}catch(e){}let n=i.value;return"boolean"==typeof n||i.issues.push({expected:"boolean",code:"invalid_type",input:n,inst:e}),i}}),iJ=i("$ZodBigInt",(e,t)=>{is.init(e,t),e._zod.pattern=t_,e._zod.parse=(i,r)=>{if(t.coerce)try{i.value=BigInt(i.value)}catch(e){}return"bigint"==typeof i.value||i.issues.push({expected:"bigint",code:"invalid_type",input:i.value,inst:e}),i}}),iM=i("$ZodBigInt",(e,t)=>{tY.init(e,t),iJ.init(e,t)}),iW=i("$ZodSymbol",(e,t)=>{is.init(e,t),e._zod.parse=(t,i)=>{let r=t.value;return"symbol"==typeof r||t.issues.push({expected:"symbol",code:"invalid_type",input:r,inst:e}),t}}),iB=i("$ZodUndefined",(e,t)=>{is.init(e,t),e._zod.pattern=tI,e._zod.values=new Set([void 0]),e._zod.optin="optional",e._zod.optout="optional",e._zod.parse=(t,i)=>{let r=t.value;return void 0===r||t.issues.push({expected:"undefined",code:"invalid_type",input:r,inst:e}),t}}),iV=i("$ZodNull",(e,t)=>{is.init(e,t),e._zod.pattern=tk,e._zod.values=new Set([null]),e._zod.parse=(t,i)=>{let r=t.value;return null===r||t.issues.push({expected:"null",code:"invalid_type",input:r,inst:e}),t}}),iG=i("$ZodAny",(e,t)=>{is.init(e,t),e._zod.parse=e=>e}),iK=i("$ZodUnknown",(e,t)=>{is.init(e,t),e._zod.parse=e=>e}),iX=i("$ZodNever",(e,t)=>{is.init(e,t),e._zod.parse=(t,i)=>(t.issues.push({expected:"never",code:"invalid_type",input:t.value,inst:e}),t)}),iq=i("$ZodVoid",(e,t)=>{is.init(e,t),e._zod.parse=(t,i)=>{let r=t.value;return void 0===r||t.issues.push({expected:"void",code:"invalid_type",input:r,inst:e}),t}}),iY=i("$ZodDate",(e,t)=>{is.init(e,t),e._zod.parse=(i,r)=>{if(t.coerce)try{i.value=new Date(i.value)}catch(e){}let n=i.value,a=n instanceof Date;return a&&!Number.isNaN(n.getTime())||i.issues.push({expected:"date",code:"invalid_type",input:n,...a?{received:"Invalid Date"}:{},inst:e}),i}});function iH(e,t,i){e.issues.length&&t.issues.push(...et(i,e.issues)),t.value[i]=e.value}let iQ=i("$ZodArray",(e,t)=>{is.init(e,t),e._zod.parse=(i,r)=>{let n=i.value;if(!Array.isArray(n))return i.issues.push({expected:"array",code:"invalid_type",input:n,inst:e}),i;i.value=Array(n.length);let a=[];for(let e=0;e<n.length;e++){let o=n[e],u=t.element._zod.run({value:o,issues:[]},r);u instanceof Promise?a.push(u.then(t=>iH(t,i,e))):iH(u,i,e)}return a.length?Promise.all(a).then(()=>i):i}});function i4(e,t,i,r){e.issues.length&&t.issues.push(...et(i,e.issues)),void 0===e.value?i in r&&(t.value[i]=void 0):t.value[i]=e.value}function i6(e){let t=Object.keys(e.shape);for(let i of t)if(!e.shape[i]._zod.traits.has("$ZodType"))throw Error(`Invalid element at key "${i}": expected a Zod schema`);let i=W(e.shape);return{...e,keys:t,keySet:new Set(t),numKeys:t.length,optionalKeys:new Set(i)}}function i0(e,t,i,r,n,a){let o=[],u=n.keySet,s=n.catchall._zod,l=s.def.type;for(let n of Object.keys(t)){if(u.has(n))continue;if("never"===l){o.push(n);continue}let a=s.run({value:t[n],issues:[]},r);a instanceof Promise?e.push(a.then(e=>i4(e,i,n,t))):i4(a,i,n,t)}return(o.length&&i.issues.push({code:"unrecognized_keys",keys:o,input:t,inst:a}),e.length)?Promise.all(e).then(()=>i):i}let i1=i("$ZodObject",(e,t)=>{let i;is.init(e,t);let r=g(()=>i6(t));b(e._zod,"propValues",()=>{let e=t.shape,i={};for(let t in e){let r=e[t]._zod;if(r.values)for(let e of(i[t]??(i[t]=new Set),r.values))i[t].add(e)}return i});let n=t.catchall;e._zod.parse=(t,a)=>{i??(i=r.value);let o=t.value;if(!j(o))return t.issues.push({expected:"object",code:"invalid_type",input:o,inst:e}),t;t.value={};let u=[],s=i.shape;for(let e of i.keys){let i=s[e]._zod.run({value:o[e],issues:[]},a);i instanceof Promise?u.push(i.then(i=>i4(i,t,e,o))):i4(i,t,e,o)}return n?i0(u,o,t,a,r.value,e):u.length?Promise.all(u).then(()=>t):t}}),i2=i("$ZodObjectJIT",(e,t)=>{let i,r;i1.init(e,t);let n=e._zod.parse,a=g(()=>i6(t)),u=!o.jitless,s=u&&N.value,l=t.catchall;e._zod.parse=(o,d)=>{r??(r=a.value);let c=o.value;return j(c)?u&&s&&d?.async===!1&&!0!==d.jitless?(i||(i=(e=>{let t=new io(["shape","payload","ctx"]),i=a.value,r=e=>{let t=U(e);return`shape[${t}]._zod.run({ value: input[${t}], issues: [] }, ctx)`};t.write("const input = payload.value;");let n=Object.create(null),o=0;for(let e of i.keys)n[e]=`key_${o++}`;for(let e of(t.write("const newResult = {}"),i.keys)){let i=n[e],a=U(e);t.write(`const ${i} = ${r(e)};`),t.write(`
        if (${i}.issues.length) {
          payload.issues = payload.issues.concat(${i}.issues.map(iss => ({
            ...iss,
            path: iss.path ? [${a}, ...iss.path] : [${a}]
          })));
        }
        
        if (${i}.value === undefined) {
          if (${a} in input) {
            newResult[${a}] = undefined;
          }
        } else {
          newResult[${a}] = ${i}.value;
        }
      `)}t.write("payload.value = newResult;"),t.write("return payload;");let u=t.compile();return(t,i)=>u(e,t,i)})(t.shape)),o=i(o,d),l)?i0([],c,o,d,r,e):o:n(o,d):(o.issues.push({expected:"object",code:"invalid_type",input:c,inst:e}),o)}});function i9(e,t,i,r){for(let i of e)if(0===i.issues.length)return t.value=i.value,t;let n=e.filter(e=>!ee(e));return 1===n.length?(t.value=n[0].value,n[0]):(t.issues.push({code:"invalid_union",input:t.value,inst:i,errors:e.map(e=>e.issues.map(e=>er(e,r,u())))}),t)}let i3=i("$ZodUnion",(e,t)=>{is.init(e,t),b(e._zod,"optin",()=>t.options.some(e=>"optional"===e._zod.optin)?"optional":void 0),b(e._zod,"optout",()=>t.options.some(e=>"optional"===e._zod.optout)?"optional":void 0),b(e._zod,"values",()=>{if(t.options.every(e=>e._zod.values))return new Set(t.options.flatMap(e=>Array.from(e._zod.values)))}),b(e._zod,"pattern",()=>{if(t.options.every(e=>e._zod.pattern)){let e=t.options.map(e=>e._zod.pattern);return RegExp(`^(${e.map(e=>$(e.source)).join("|")})$`)}});let i=1===t.options.length,r=t.options[0]._zod.run;e._zod.parse=(n,a)=>{if(i)return r(n,a);let o=!1,u=[];for(let e of t.options){let t=e._zod.run({value:n.value,issues:[]},a);if(t instanceof Promise)u.push(t),o=!0;else{if(0===t.issues.length)return t;u.push(t)}}return o?Promise.all(u).then(t=>i9(t,n,e,a)):i9(u,n,e,a)}}),i7=i("$ZodDiscriminatedUnion",(e,t)=>{i3.init(e,t);let i=e._zod.parse;b(e._zod,"propValues",()=>{let e={};for(let i of t.options){let r=i._zod.propValues;if(!r||0===Object.keys(r).length)throw Error(`Invalid discriminated union option at index "${t.options.indexOf(i)}"`);for(let[t,i]of Object.entries(r))for(let r of(e[t]||(e[t]=new Set),i))e[t].add(r)}return e});let r=g(()=>{let e=t.options,i=new Map;for(let r of e){let e=r._zod.propValues?.[t.discriminator];if(!e||0===e.size)throw Error(`Invalid discriminated union option at index "${t.options.indexOf(r)}"`);for(let t of e){if(i.has(t))throw Error(`Duplicate discriminator value "${String(t)}"`);i.set(t,r)}}return i});e._zod.parse=(n,a)=>{let o=n.value;if(!j(o))return n.issues.push({code:"invalid_type",expected:"object",input:o,inst:e}),n;let u=r.value.get(o?.[t.discriminator]);return u?u._zod.run(n,a):t.unionFallback?i(n,a):(n.issues.push({code:"invalid_union",errors:[],note:"No matching discriminator",discriminator:t.discriminator,input:o,path:[t.discriminator],inst:e}),n)}}),i5=i("$ZodIntersection",(e,t)=>{is.init(e,t),e._zod.parse=(e,i)=>{let r=e.value,n=t.left._zod.run({value:r,issues:[]},i),a=t.right._zod.run({value:r,issues:[]},i);return n instanceof Promise||a instanceof Promise?Promise.all([n,a]).then(([t,i])=>i8(e,t,i)):i8(e,n,a)}});function i8(e,t,i){if(t.issues.length&&e.issues.push(...t.issues),i.issues.length&&e.issues.push(...i.issues),ee(e))return e;let r=function e(t,i){if(t===i||t instanceof Date&&i instanceof Date&&+t==+i)return{valid:!0,data:t};if(D(t)&&D(i)){let r=Object.keys(i),n=Object.keys(t).filter(e=>-1!==r.indexOf(e)),a={...t,...i};for(let r of n){let n=e(t[r],i[r]);if(!n.valid)return{valid:!1,mergeErrorPath:[r,...n.mergeErrorPath]};a[r]=n.data}return{valid:!0,data:a}}if(Array.isArray(t)&&Array.isArray(i)){if(t.length!==i.length)return{valid:!1,mergeErrorPath:[]};let r=[];for(let n=0;n<t.length;n++){let a=e(t[n],i[n]);if(!a.valid)return{valid:!1,mergeErrorPath:[n,...a.mergeErrorPath]};r.push(a.data)}return{valid:!0,data:r}}return{valid:!1,mergeErrorPath:[]}}(t.value,i.value);if(!r.valid)throw Error(`Unmergable intersection. Error path: ${JSON.stringify(r.mergeErrorPath)}`);return e.value=r.data,e}let re=i("$ZodTuple",(e,t)=>{is.init(e,t);let i=t.items,r=i.length-[...i].reverse().findIndex(e=>"optional"!==e._zod.optin);e._zod.parse=(n,a)=>{let o=n.value;if(!Array.isArray(o))return n.issues.push({input:o,inst:e,expected:"tuple",code:"invalid_type"}),n;n.value=[];let u=[];if(!t.rest){let t=o.length>i.length,a=o.length<r-1;if(t||a)return n.issues.push({...t?{code:"too_big",maximum:i.length}:{code:"too_small",minimum:i.length},input:o,inst:e,origin:"array"}),n}let s=-1;for(let e of i){if(++s>=o.length&&s>=r)continue;let t=e._zod.run({value:o[s],issues:[]},a);t instanceof Promise?u.push(t.then(e=>rt(e,n,s))):rt(t,n,s)}if(t.rest)for(let e of o.slice(i.length)){s++;let i=t.rest._zod.run({value:e,issues:[]},a);i instanceof Promise?u.push(i.then(e=>rt(e,n,s))):rt(i,n,s)}return u.length?Promise.all(u).then(()=>n):n}});function rt(e,t,i){e.issues.length&&t.issues.push(...et(i,e.issues)),t.value[i]=e.value}let ri=i("$ZodRecord",(e,t)=>{is.init(e,t),e._zod.parse=(i,r)=>{let n=i.value;if(!D(n))return i.issues.push({expected:"record",code:"invalid_type",input:n,inst:e}),i;let a=[];if(t.keyType._zod.values){let o,u=t.keyType._zod.values;for(let e of(i.value={},u))if("string"==typeof e||"number"==typeof e||"symbol"==typeof e){let o=t.valueType._zod.run({value:n[e],issues:[]},r);o instanceof Promise?a.push(o.then(t=>{t.issues.length&&i.issues.push(...et(e,t.issues)),i.value[e]=t.value})):(o.issues.length&&i.issues.push(...et(e,o.issues)),i.value[e]=o.value)}for(let e in n)u.has(e)||(o=o??[]).push(e);o&&o.length>0&&i.issues.push({code:"unrecognized_keys",input:n,inst:e,keys:o})}else for(let o of(i.value={},Reflect.ownKeys(n))){if("__proto__"===o)continue;let s=t.keyType._zod.run({value:o,issues:[]},r);if(s instanceof Promise)throw Error("Async schemas not supported in object keys currently");if(s.issues.length){i.issues.push({code:"invalid_key",origin:"record",issues:s.issues.map(e=>er(e,r,u())),input:o,path:[o],inst:e}),i.value[s.value]=s.value;continue}let l=t.valueType._zod.run({value:n[o],issues:[]},r);l instanceof Promise?a.push(l.then(e=>{e.issues.length&&i.issues.push(...et(o,e.issues)),i.value[s.value]=e.value})):(l.issues.length&&i.issues.push(...et(o,l.issues)),i.value[s.value]=l.value)}return a.length?Promise.all(a).then(()=>i):i}}),rr=i("$ZodMap",(e,t)=>{is.init(e,t),e._zod.parse=(i,r)=>{let n=i.value;if(!(n instanceof Map))return i.issues.push({expected:"map",code:"invalid_type",input:n,inst:e}),i;let a=[];for(let[o,u]of(i.value=new Map,n)){let s=t.keyType._zod.run({value:o,issues:[]},r),l=t.valueType._zod.run({value:u,issues:[]},r);s instanceof Promise||l instanceof Promise?a.push(Promise.all([s,l]).then(([t,a])=>{rn(t,a,i,o,n,e,r)})):rn(s,l,i,o,n,e,r)}return a.length?Promise.all(a).then(()=>i):i}});function rn(e,t,i,r,n,a,o){e.issues.length&&(A.has(typeof r)?i.issues.push(...et(r,e.issues)):i.issues.push({code:"invalid_key",origin:"map",input:n,inst:a,issues:e.issues.map(e=>er(e,o,u()))})),t.issues.length&&(A.has(typeof r)?i.issues.push(...et(r,t.issues)):i.issues.push({origin:"map",code:"invalid_element",input:n,inst:a,key:r,issues:t.issues.map(e=>er(e,o,u()))})),i.value.set(e.value,t.value)}let ra=i("$ZodSet",(e,t)=>{is.init(e,t),e._zod.parse=(i,r)=>{let n=i.value;if(!(n instanceof Set))return i.issues.push({input:n,inst:e,expected:"set",code:"invalid_type"}),i;let a=[];for(let e of(i.value=new Set,n)){let n=t.valueType._zod.run({value:e,issues:[]},r);n instanceof Promise?a.push(n.then(e=>ro(e,i))):ro(n,i)}return a.length?Promise.all(a).then(()=>i):i}});function ro(e,t){e.issues.length&&t.issues.push(...e.issues),t.value.add(e.value)}let ru=i("$ZodEnum",(e,t)=>{is.init(e,t);let i=f(t.entries),r=new Set(i);e._zod.values=r,e._zod.pattern=RegExp(`^(${i.filter(e=>A.has(typeof e)).map(e=>"string"==typeof e?L(e):e.toString()).join("|")})$`),e._zod.parse=(t,n)=>{let a=t.value;return r.has(a)||t.issues.push({code:"invalid_value",values:i,input:a,inst:e}),t}}),rs=i("$ZodLiteral",(e,t)=>{if(is.init(e,t),0===t.values.length)throw Error("Cannot create literal schema with no valid values");e._zod.values=new Set(t.values),e._zod.pattern=RegExp(`^(${t.values.map(e=>"string"==typeof e?L(e):e?L(e.toString()):String(e)).join("|")})$`),e._zod.parse=(i,r)=>{let n=i.value;return e._zod.values.has(n)||i.issues.push({code:"invalid_value",values:t.values,input:n,inst:e}),i}}),rl=i("$ZodFile",(e,t)=>{is.init(e,t),e._zod.parse=(t,i)=>{let r=t.value;return r instanceof File||t.issues.push({expected:"file",code:"invalid_type",input:r,inst:e}),t}}),rd=i("$ZodTransform",(e,t)=>{is.init(e,t),e._zod.parse=(i,r)=>{if("backward"===r.direction)throw new a(e.constructor.name);let o=t.transform(i.value,i);if(r.async)return(o instanceof Promise?o:Promise.resolve(o)).then(e=>(i.value=e,i));if(o instanceof Promise)throw new n;return i.value=o,i}});function rc(e,t){return e.issues.length&&void 0===t?{issues:[],value:void 0}:e}let rm=i("$ZodOptional",(e,t)=>{is.init(e,t),e._zod.optin="optional",e._zod.optout="optional",b(e._zod,"values",()=>t.innerType._zod.values?new Set([...t.innerType._zod.values,void 0]):void 0),b(e._zod,"pattern",()=>{let e=t.innerType._zod.pattern;return e?RegExp(`^(${$(e.source)})?$`):void 0}),e._zod.parse=(e,i)=>{if("optional"===t.innerType._zod.optin){let r=t.innerType._zod.run(e,i);return r instanceof Promise?r.then(t=>rc(t,e.value)):rc(r,e.value)}return void 0===e.value?e:t.innerType._zod.run(e,i)}}),rf=i("$ZodNullable",(e,t)=>{is.init(e,t),b(e._zod,"optin",()=>t.innerType._zod.optin),b(e._zod,"optout",()=>t.innerType._zod.optout),b(e._zod,"pattern",()=>{let e=t.innerType._zod.pattern;return e?RegExp(`^(${$(e.source)}|null)$`):void 0}),b(e._zod,"values",()=>t.innerType._zod.values?new Set([...t.innerType._zod.values,null]):void 0),e._zod.parse=(e,i)=>null===e.value?e:t.innerType._zod.run(e,i)}),rp=i("$ZodDefault",(e,t)=>{is.init(e,t),e._zod.optin="optional",b(e._zod,"values",()=>t.innerType._zod.values),e._zod.parse=(e,i)=>{if("backward"===i.direction)return t.innerType._zod.run(e,i);if(void 0===e.value)return e.value=t.defaultValue,e;let r=t.innerType._zod.run(e,i);return r instanceof Promise?r.then(e=>rv(e,t)):rv(r,t)}});function rv(e,t){return void 0===e.value&&(e.value=t.defaultValue),e}let rg=i("$ZodPrefault",(e,t)=>{is.init(e,t),e._zod.optin="optional",b(e._zod,"values",()=>t.innerType._zod.values),e._zod.parse=(e,i)=>("backward"===i.direction||void 0===e.value&&(e.value=t.defaultValue),t.innerType._zod.run(e,i))}),rh=i("$ZodNonOptional",(e,t)=>{is.init(e,t),b(e._zod,"values",()=>{let e=t.innerType._zod.values;return e?new Set([...e].filter(e=>void 0!==e)):void 0}),e._zod.parse=(i,r)=>{let n=t.innerType._zod.run(i,r);return n instanceof Promise?n.then(t=>r$(t,e)):r$(n,e)}});function r$(e,t){return e.issues.length||void 0!==e.value||e.issues.push({code:"invalid_type",expected:"nonoptional",input:e.value,inst:t}),e}let r_=i("$ZodSuccess",(e,t)=>{is.init(e,t),e._zod.parse=(e,i)=>{if("backward"===i.direction)throw new a("ZodSuccess");let r=t.innerType._zod.run(e,i);return r instanceof Promise?r.then(t=>(e.value=0===t.issues.length,e)):(e.value=0===r.issues.length,e)}}),ry=i("$ZodCatch",(e,t)=>{is.init(e,t),b(e._zod,"optin",()=>t.innerType._zod.optin),b(e._zod,"optout",()=>t.innerType._zod.optout),b(e._zod,"values",()=>t.innerType._zod.values),e._zod.parse=(e,i)=>{if("backward"===i.direction)return t.innerType._zod.run(e,i);let r=t.innerType._zod.run(e,i);return r instanceof Promise?r.then(r=>(e.value=r.value,r.issues.length&&(e.value=t.catchValue({...e,error:{issues:r.issues.map(e=>er(e,i,u()))},input:e.value}),e.issues=[]),e)):(e.value=r.value,r.issues.length&&(e.value=t.catchValue({...e,error:{issues:r.issues.map(e=>er(e,i,u()))},input:e.value}),e.issues=[]),e)}}),rb=i("$ZodNaN",(e,t)=>{is.init(e,t),e._zod.parse=(t,i)=>("number"==typeof t.value&&Number.isNaN(t.value)||t.issues.push({input:t.value,inst:e,expected:"nan",code:"invalid_type"}),t)}),rx=i("$ZodPipe",(e,t)=>{is.init(e,t),b(e._zod,"values",()=>t.in._zod.values),b(e._zod,"optin",()=>t.in._zod.optin),b(e._zod,"optout",()=>t.out._zod.optout),b(e._zod,"propValues",()=>t.in._zod.propValues),e._zod.parse=(e,i)=>{if("backward"===i.direction){let r=t.out._zod.run(e,i);return r instanceof Promise?r.then(e=>rk(e,t.in,i)):rk(r,t.in,i)}let r=t.in._zod.run(e,i);return r instanceof Promise?r.then(e=>rk(e,t.out,i)):rk(r,t.out,i)}});function rk(e,t,i){return e.issues.length?(e.aborted=!0,e):t._zod.run({value:e.value,issues:e.issues},i)}let rI=i("$ZodCodec",(e,t)=>{is.init(e,t),b(e._zod,"values",()=>t.in._zod.values),b(e._zod,"optin",()=>t.in._zod.optin),b(e._zod,"optout",()=>t.out._zod.optout),b(e._zod,"propValues",()=>t.in._zod.propValues),e._zod.parse=(e,i)=>{if("forward"===(i.direction||"forward")){let r=t.in._zod.run(e,i);return r instanceof Promise?r.then(e=>rw(e,t,i)):rw(r,t,i)}{let r=t.out._zod.run(e,i);return r instanceof Promise?r.then(e=>rw(e,t,i)):rw(r,t,i)}}});function rw(e,t,i){if(e.issues.length)return e.aborted=!0,e;if("forward"===(i.direction||"forward")){let r=t.transform(e.value,e);return r instanceof Promise?r.then(r=>rz(e,r,t.out,i)):rz(e,r,t.out,i)}{let r=t.reverseTransform(e.value,e);return r instanceof Promise?r.then(r=>rz(e,r,t.in,i)):rz(e,r,t.in,i)}}function rz(e,t,i,r){return e.issues.length?(e.aborted=!0,e):i._zod.run({value:t,issues:e.issues},r)}let rS=i("$ZodReadonly",(e,t)=>{is.init(e,t),b(e._zod,"propValues",()=>t.innerType._zod.propValues),b(e._zod,"values",()=>t.innerType._zod.values),b(e._zod,"optin",()=>t.innerType._zod.optin),b(e._zod,"optout",()=>t.innerType._zod.optout),e._zod.parse=(e,i)=>{if("backward"===i.direction)return t.innerType._zod.run(e,i);let r=t.innerType._zod.run(e,i);return r instanceof Promise?r.then(rZ):rZ(r)}});function rZ(e){return e.value=Object.freeze(e.value),e}let rU=i("$ZodTemplateLiteral",(e,t)=>{is.init(e,t);let i=[];for(let e of t.parts)if("object"==typeof e&&null!==e){if(!e._zod.pattern)throw Error(`Invalid template literal part, no pattern found: ${[...e._zod.traits].shift()}`);let t=e._zod.pattern instanceof RegExp?e._zod.pattern.source:e._zod.pattern;if(!t)throw Error(`Invalid template literal part: ${e._zod.traits}`);let r=+!!t.startsWith("^"),n=t.endsWith("$")?t.length-1:t.length;i.push(t.slice(r,n))}else if(null===e||C.has(typeof e))i.push(L(`${e}`));else throw Error(`Invalid template literal part: ${e}`);e._zod.pattern=RegExp(`^${i.join("")}$`),e._zod.parse=(i,r)=>("string"!=typeof i.value?i.issues.push({input:i.value,inst:e,expected:"template_literal",code:"invalid_type"}):(e._zod.pattern.lastIndex=0,e._zod.pattern.test(i.value)||i.issues.push({input:i.value,inst:e,code:"invalid_format",format:t.format??"template_literal",pattern:e._zod.pattern.source})),i)}),rO=i("$ZodFunction",(e,t)=>(is.init(e,t),e._def=t,e._zod.def=t,e.implement=t=>{if("function"!=typeof t)throw Error("implement() must be called with a function");return function(...i){let r=Reflect.apply(t,this,e._def.input?ew(e._def.input,i):i);return e._def.output?ew(e._def.output,r):r}},e.implementAsync=t=>{if("function"!=typeof t)throw Error("implementAsync() must be called with a function");return async function(...i){let r=e._def.input?await eS(e._def.input,i):i,n=await Reflect.apply(t,this,r);return e._def.output?await eS(e._def.output,n):n}},e._zod.parse=(t,i)=>("function"!=typeof t.value?t.issues.push({code:"invalid_type",expected:"function",input:t.value,inst:e}):e._def.output&&"promise"===e._def.output._zod.def.type?t.value=e.implementAsync(t.value):t.value=e.implement(t.value),t),e.input=(...t)=>{let i=e.constructor;return new i(Array.isArray(t[0])?{type:"function",input:new re({type:"tuple",items:t[0],rest:t[1]}),output:e._def.output}:{type:"function",input:t[0],output:e._def.output})},e.output=t=>new e.constructor({type:"function",input:e._def.input,output:t}),e)),rj=i("$ZodPromise",(e,t)=>{is.init(e,t),e._zod.parse=(e,i)=>Promise.resolve(e.value).then(e=>t.innerType._zod.run({value:e,issues:[]},i))}),rN=i("$ZodLazy",(e,t)=>{is.init(e,t),b(e._zod,"innerType",()=>t.getter()),b(e._zod,"pattern",()=>e._zod.innerType._zod.pattern),b(e._zod,"propValues",()=>e._zod.innerType._zod.propValues),b(e._zod,"optin",()=>e._zod.innerType._zod.optin??void 0),b(e._zod,"optout",()=>e._zod.innerType._zod.optout??void 0),e._zod.parse=(t,i)=>e._zod.innerType._zod.run(t,i)}),rD=i("$ZodCustom",(e,t)=>{tB.init(e,t),is.init(e,t),e._zod.parse=(e,t)=>e,e._zod.check=i=>{let r=i.value,n=t.fn(r);if(n instanceof Promise)return n.then(t=>rP(t,i,r,e));rP(n,i,r,e)}});function rP(e,t,i,r){if(!e){let e={code:"custom",input:i,inst:r,path:[...r._zod.def.path??[]],continue:!r._zod.def.abort};r._zod.def.params&&(e.params=r._zod.def.params),t.issues.push(eo(e))}}e.i(78351),e.i(23851),e.i(80057),e.i(37230);var rE,rT=e.i(24463),rA=e.i(90657);function rC(){return{localeError:(()=>{let e={string:{unit:"حرف",verb:"أن يحوي"},file:{unit:"بايت",verb:"أن يحوي"},array:{unit:"عنصر",verb:"أن يحوي"},set:{unit:"عنصر",verb:"أن يحوي"}},t={regex:"مدخل",email:"بريد إلكتروني",url:"رابط",emoji:"إيموجي",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"تاريخ ووقت بمعيار ISO",date:"تاريخ بمعيار ISO",time:"وقت بمعيار ISO",duration:"مدة بمعيار ISO",ipv4:"عنوان IPv4",ipv6:"عنوان IPv6",cidrv4:"مدى عناوين بصيغة IPv4",cidrv6:"مدى عناوين بصيغة IPv6",base64:"نَص بترميز base64-encoded",base64url:"نَص بترميز base64url-encoded",json_string:"نَص على هيئة JSON",e164:"رقم هاتف بمعيار E.164",jwt:"JWT",template_literal:"مدخل"};return i=>{switch(i.code){case"invalid_type":return`مدخلات غير مقبولة: يفترض إدخال ${i.expected}، ولكن تم إدخال ${(e=>{let t=typeof e;switch(t){case"number":return Number.isNaN(e)?"NaN":"number";case"object":if(Array.isArray(e))return"array";if(null===e)return"null";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}return t})(i.input)}`;case"invalid_value":if(1===i.values.length)return`مدخلات غير مقبولة: يفترض إدخال ${M(i.values[0])}`;return`اختيار غير مقبول: يتوقع انتقاء أحد هذه الخيارات: ${p(i.values,"|")}`;case"too_big":{let t=i.inclusive?"<=":"<",r=e[i.origin]??null;if(r)return` أكبر من اللازم: يفترض أن تكون ${i.origin??"القيمة"} ${t} ${i.maximum.toString()} ${r.unit??"عنصر"}`;return`أكبر من اللازم: يفترض أن تكون ${i.origin??"القيمة"} ${t} ${i.maximum.toString()}`}case"too_small":{let t=i.inclusive?">=":">",r=e[i.origin]??null;if(r)return`أصغر من اللازم: يفترض لـ ${i.origin} أن يكون ${t} ${i.minimum.toString()} ${r.unit}`;return`أصغر من اللازم: يفترض لـ ${i.origin} أن يكون ${t} ${i.minimum.toString()}`}case"invalid_format":if("starts_with"===i.format)return`نَص غير مقبول: يجب أن يبدأ بـ "${i.prefix}"`;if("ends_with"===i.format)return`نَص غير مقبول: يجب أن ينتهي بـ "${i.suffix}"`;if("includes"===i.format)return`نَص غير مقبول: يجب أن يتضمَّن "${i.includes}"`;if("regex"===i.format)return`نَص غير مقبول: يجب أن يطابق النمط ${i.pattern}`;return`${t[i.format]??i.format} غير مقبول`;case"not_multiple_of":return`رقم غير مقبول: يجب أن يكون من مضاعفات ${i.divisor}`;case"unrecognized_keys":return`معرف${i.keys.length>1?"ات":""} غريب${i.keys.length>1?"ة":""}: ${p(i.keys,"، ")}`;case"invalid_key":return`معرف غير مقبول في ${i.origin}`;case"invalid_union":default:return"مدخل غير مقبول";case"invalid_element":return`مدخل غير مقبول في ${i.origin}`}}})()}}function rL(){return{localeError:(()=>{let e={string:{unit:"simvol",verb:"olmalıdır"},file:{unit:"bayt",verb:"olmalıdır"},array:{unit:"element",verb:"olmalıdır"},set:{unit:"element",verb:"olmalıdır"}},t={regex:"input",email:"email address",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO datetime",date:"ISO date",time:"ISO time",duration:"ISO duration",ipv4:"IPv4 address",ipv6:"IPv6 address",cidrv4:"IPv4 range",cidrv6:"IPv6 range",base64:"base64-encoded string",base64url:"base64url-encoded string",json_string:"JSON string",e164:"E.164 number",jwt:"JWT",template_literal:"input"};return i=>{switch(i.code){case"invalid_type":return`Yanlış dəyər: g\xf6zlənilən ${i.expected}, daxil olan ${(e=>{let t=typeof e;switch(t){case"number":return Number.isNaN(e)?"NaN":"number";case"object":if(Array.isArray(e))return"array";if(null===e)return"null";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}return t})(i.input)}`;case"invalid_value":if(1===i.values.length)return`Yanlış dəyər: g\xf6zlənilən ${M(i.values[0])}`;return`Yanlış se\xe7im: aşağıdakılardan biri olmalıdır: ${p(i.values,"|")}`;case"too_big":{let t=i.inclusive?"<=":"<",r=e[i.origin]??null;if(r)return`\xc7ox b\xf6y\xfck: g\xf6zlənilən ${i.origin??"dəyər"} ${t}${i.maximum.toString()} ${r.unit??"element"}`;return`\xc7ox b\xf6y\xfck: g\xf6zlənilən ${i.origin??"dəyər"} ${t}${i.maximum.toString()}`}case"too_small":{let t=i.inclusive?">=":">",r=e[i.origin]??null;if(r)return`\xc7ox ki\xe7ik: g\xf6zlənilən ${i.origin} ${t}${i.minimum.toString()} ${r.unit}`;return`\xc7ox ki\xe7ik: g\xf6zlənilən ${i.origin} ${t}${i.minimum.toString()}`}case"invalid_format":if("starts_with"===i.format)return`Yanlış mətn: "${i.prefix}" ilə başlamalıdır`;if("ends_with"===i.format)return`Yanlış mətn: "${i.suffix}" ilə bitməlidir`;if("includes"===i.format)return`Yanlış mətn: "${i.includes}" daxil olmalıdır`;if("regex"===i.format)return`Yanlış mətn: ${i.pattern} şablonuna uyğun olmalıdır`;return`Yanlış ${t[i.format]??i.format}`;case"not_multiple_of":return`Yanlış ədəd: ${i.divisor} ilə b\xf6l\xfcnə bilən olmalıdır`;case"unrecognized_keys":return`Tanınmayan a\xe7ar${i.keys.length>1?"lar":""}: ${p(i.keys,", ")}`;case"invalid_key":return`${i.origin} daxilində yanlış a\xe7ar`;case"invalid_union":return"Yanlış dəyər";case"invalid_element":return`${i.origin} daxilində yanlış dəyər`;default:return`Yanlış dəyər`}}})()}}function rR(e,t,i,r){let n=Math.abs(e),a=n%10,o=n%100;return o>=11&&o<=19?r:1===a?t:a>=2&&a<=4?i:r}function rF(){return{localeError:(()=>{let e={string:{unit:{one:"сімвал",few:"сімвалы",many:"сімвалаў"},verb:"мець"},array:{unit:{one:"элемент",few:"элементы",many:"элементаў"},verb:"мець"},set:{unit:{one:"элемент",few:"элементы",many:"элементаў"},verb:"мець"},file:{unit:{one:"байт",few:"байты",many:"байтаў"},verb:"мець"}},t={regex:"увод",email:"email адрас",url:"URL",emoji:"эмодзі",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO дата і час",date:"ISO дата",time:"ISO час",duration:"ISO працягласць",ipv4:"IPv4 адрас",ipv6:"IPv6 адрас",cidrv4:"IPv4 дыяпазон",cidrv6:"IPv6 дыяпазон",base64:"радок у фармаце base64",base64url:"радок у фармаце base64url",json_string:"JSON радок",e164:"нумар E.164",jwt:"JWT",template_literal:"увод"};return i=>{switch(i.code){case"invalid_type":return`Няправільны ўвод: чакаўся ${i.expected}, атрымана ${(e=>{let t=typeof e;switch(t){case"number":return Number.isNaN(e)?"NaN":"лік";case"object":if(Array.isArray(e))return"масіў";if(null===e)return"null";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}return t})(i.input)}`;case"invalid_value":if(1===i.values.length)return`Няправільны ўвод: чакалася ${M(i.values[0])}`;return`Няправільны варыянт: чакаўся адзін з ${p(i.values,"|")}`;case"too_big":{let t=i.inclusive?"<=":"<",r=e[i.origin]??null;if(r){let e=rR(Number(i.maximum),r.unit.one,r.unit.few,r.unit.many);return`Занадта вялікі: чакалася, што ${i.origin??"значэнне"} павінна ${r.verb} ${t}${i.maximum.toString()} ${e}`}return`Занадта вялікі: чакалася, што ${i.origin??"значэнне"} павінна быць ${t}${i.maximum.toString()}`}case"too_small":{let t=i.inclusive?">=":">",r=e[i.origin]??null;if(r){let e=rR(Number(i.minimum),r.unit.one,r.unit.few,r.unit.many);return`Занадта малы: чакалася, што ${i.origin} павінна ${r.verb} ${t}${i.minimum.toString()} ${e}`}return`Занадта малы: чакалася, што ${i.origin} павінна быць ${t}${i.minimum.toString()}`}case"invalid_format":if("starts_with"===i.format)return`Няправільны радок: павінен пачынацца з "${i.prefix}"`;if("ends_with"===i.format)return`Няправільны радок: павінен заканчвацца на "${i.suffix}"`;if("includes"===i.format)return`Няправільны радок: павінен змяшчаць "${i.includes}"`;if("regex"===i.format)return`Няправільны радок: павінен адпавядаць шаблону ${i.pattern}`;return`Няправільны ${t[i.format]??i.format}`;case"not_multiple_of":return`Няправільны лік: павінен быць кратным ${i.divisor}`;case"unrecognized_keys":return`Нераспазнаны ${i.keys.length>1?"ключы":"ключ"}: ${p(i.keys,", ")}`;case"invalid_key":return`Няправільны ключ у ${i.origin}`;case"invalid_union":return"Няправільны ўвод";case"invalid_element":return`Няправільнае значэнне ў ${i.origin}`;default:return`Няправільны ўвод`}}})()}}function rJ(){return{localeError:(()=>{let e={string:{unit:"caràcters",verb:"contenir"},file:{unit:"bytes",verb:"contenir"},array:{unit:"elements",verb:"contenir"},set:{unit:"elements",verb:"contenir"}},t={regex:"entrada",email:"adreça electrònica",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"data i hora ISO",date:"data ISO",time:"hora ISO",duration:"durada ISO",ipv4:"adreça IPv4",ipv6:"adreça IPv6",cidrv4:"rang IPv4",cidrv6:"rang IPv6",base64:"cadena codificada en base64",base64url:"cadena codificada en base64url",json_string:"cadena JSON",e164:"número E.164",jwt:"JWT",template_literal:"entrada"};return i=>{switch(i.code){case"invalid_type":return`Tipus inv\xe0lid: s'esperava ${i.expected}, s'ha rebut ${(e=>{let t=typeof e;switch(t){case"number":return Number.isNaN(e)?"NaN":"number";case"object":if(Array.isArray(e))return"array";if(null===e)return"null";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}return t})(i.input)}`;case"invalid_value":if(1===i.values.length)return`Valor inv\xe0lid: s'esperava ${M(i.values[0])}`;return`Opci\xf3 inv\xe0lida: s'esperava una de ${p(i.values," o ")}`;case"too_big":{let t=i.inclusive?"com a màxim":"menys de",r=e[i.origin]??null;if(r)return`Massa gran: s'esperava que ${i.origin??"el valor"} contingu\xe9s ${t} ${i.maximum.toString()} ${r.unit??"elements"}`;return`Massa gran: s'esperava que ${i.origin??"el valor"} fos ${t} ${i.maximum.toString()}`}case"too_small":{let t=i.inclusive?"com a mínim":"més de",r=e[i.origin]??null;if(r)return`Massa petit: s'esperava que ${i.origin} contingu\xe9s ${t} ${i.minimum.toString()} ${r.unit}`;return`Massa petit: s'esperava que ${i.origin} fos ${t} ${i.minimum.toString()}`}case"invalid_format":if("starts_with"===i.format)return`Format inv\xe0lid: ha de comen\xe7ar amb "${i.prefix}"`;if("ends_with"===i.format)return`Format inv\xe0lid: ha d'acabar amb "${i.suffix}"`;if("includes"===i.format)return`Format inv\xe0lid: ha d'incloure "${i.includes}"`;if("regex"===i.format)return`Format inv\xe0lid: ha de coincidir amb el patr\xf3 ${i.pattern}`;return`Format inv\xe0lid per a ${t[i.format]??i.format}`;case"not_multiple_of":return`N\xfamero inv\xe0lid: ha de ser m\xfaltiple de ${i.divisor}`;case"unrecognized_keys":return`Clau${i.keys.length>1?"s":""} no reconeguda${i.keys.length>1?"s":""}: ${p(i.keys,", ")}`;case"invalid_key":return`Clau inv\xe0lida a ${i.origin}`;case"invalid_union":return"Entrada invàlida";case"invalid_element":return`Element inv\xe0lid a ${i.origin}`;default:return`Entrada inv\xe0lida`}}})()}}function rM(){return{localeError:(()=>{let e={string:{unit:"znaků",verb:"mít"},file:{unit:"bajtů",verb:"mít"},array:{unit:"prvků",verb:"mít"},set:{unit:"prvků",verb:"mít"}},t={regex:"regulární výraz",email:"e-mailová adresa",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"datum a čas ve formátu ISO",date:"datum ve formátu ISO",time:"čas ve formátu ISO",duration:"doba trvání ISO",ipv4:"IPv4 adresa",ipv6:"IPv6 adresa",cidrv4:"rozsah IPv4",cidrv6:"rozsah IPv6",base64:"řetězec zakódovaný ve formátu base64",base64url:"řetězec zakódovaný ve formátu base64url",json_string:"řetězec ve formátu JSON",e164:"číslo E.164",jwt:"JWT",template_literal:"vstup"};return i=>{switch(i.code){case"invalid_type":return`Neplatn\xfd vstup: oček\xe1v\xe1no ${i.expected}, obdrženo ${(e=>{let t=typeof e;switch(t){case"number":return Number.isNaN(e)?"NaN":"číslo";case"string":return"řetězec";case"boolean":return"boolean";case"bigint":return"bigint";case"function":return"funkce";case"symbol":return"symbol";case"undefined":return"undefined";case"object":if(Array.isArray(e))return"pole";if(null===e)return"null";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}return t})(i.input)}`;case"invalid_value":if(1===i.values.length)return`Neplatn\xfd vstup: oček\xe1v\xe1no ${M(i.values[0])}`;return`Neplatn\xe1 možnost: oček\xe1v\xe1na jedna z hodnot ${p(i.values,"|")}`;case"too_big":{let t=i.inclusive?"<=":"<",r=e[i.origin]??null;if(r)return`Hodnota je př\xedliš velk\xe1: ${i.origin??"hodnota"} mus\xed m\xedt ${t}${i.maximum.toString()} ${r.unit??"prvků"}`;return`Hodnota je př\xedliš velk\xe1: ${i.origin??"hodnota"} mus\xed b\xfdt ${t}${i.maximum.toString()}`}case"too_small":{let t=i.inclusive?">=":">",r=e[i.origin]??null;if(r)return`Hodnota je př\xedliš mal\xe1: ${i.origin??"hodnota"} mus\xed m\xedt ${t}${i.minimum.toString()} ${r.unit??"prvků"}`;return`Hodnota je př\xedliš mal\xe1: ${i.origin??"hodnota"} mus\xed b\xfdt ${t}${i.minimum.toString()}`}case"invalid_format":if("starts_with"===i.format)return`Neplatn\xfd řetězec: mus\xed zač\xednat na "${i.prefix}"`;if("ends_with"===i.format)return`Neplatn\xfd řetězec: mus\xed končit na "${i.suffix}"`;if("includes"===i.format)return`Neplatn\xfd řetězec: mus\xed obsahovat "${i.includes}"`;if("regex"===i.format)return`Neplatn\xfd řetězec: mus\xed odpov\xeddat vzoru ${i.pattern}`;return`Neplatn\xfd form\xe1t ${t[i.format]??i.format}`;case"not_multiple_of":return`Neplatn\xe9 č\xedslo: mus\xed b\xfdt n\xe1sobkem ${i.divisor}`;case"unrecognized_keys":return`Nezn\xe1m\xe9 kl\xedče: ${p(i.keys,", ")}`;case"invalid_key":return`Neplatn\xfd kl\xedč v ${i.origin}`;case"invalid_union":return"Neplatný vstup";case"invalid_element":return`Neplatn\xe1 hodnota v ${i.origin}`;default:return`Neplatn\xfd vstup`}}})()}}function rW(){return{localeError:(()=>{let e={string:{unit:"tegn",verb:"havde"},file:{unit:"bytes",verb:"havde"},array:{unit:"elementer",verb:"indeholdt"},set:{unit:"elementer",verb:"indeholdt"}},t={string:"streng",number:"tal",boolean:"boolean",array:"liste",object:"objekt",set:"sæt",file:"fil"},i={regex:"input",email:"e-mailadresse",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO dato- og klokkeslæt",date:"ISO-dato",time:"ISO-klokkeslæt",duration:"ISO-varighed",ipv4:"IPv4-område",ipv6:"IPv6-område",cidrv4:"IPv4-spektrum",cidrv6:"IPv6-spektrum",base64:"base64-kodet streng",base64url:"base64url-kodet streng",json_string:"JSON-streng",e164:"E.164-nummer",jwt:"JWT",template_literal:"input"};return r=>{var n,a,o,u;switch(r.code){case"invalid_type":return`Ugyldigt input: forventede ${t[n=r.expected]??n}, fik ${t[a=(e=>{let t=typeof e;switch(t){case"number":return Number.isNaN(e)?"NaN":"tal";case"object":if(Array.isArray(e))return"liste";if(null===e)return"null";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name;return"objekt"}return t})(r.input)]??a}`;case"invalid_value":if(1===r.values.length)return`Ugyldig v\xe6rdi: forventede ${M(r.values[0])}`;return`Ugyldigt valg: forventede en af f\xf8lgende ${p(r.values,"|")}`;case"too_big":{let i=r.inclusive?"<=":"<",n=e[r.origin]??null,a=t[o=r.origin]??o;if(n)return`For stor: forventede ${a??"value"} ${n.verb} ${i} ${r.maximum.toString()} ${n.unit??"elementer"}`;return`For stor: forventede ${a??"value"} havde ${i} ${r.maximum.toString()}`}case"too_small":{let i=r.inclusive?">=":">",n=e[r.origin]??null,a=t[u=r.origin]??u;if(n)return`For lille: forventede ${a} ${n.verb} ${i} ${r.minimum.toString()} ${n.unit}`;return`For lille: forventede ${a} havde ${i} ${r.minimum.toString()}`}case"invalid_format":if("starts_with"===r.format)return`Ugyldig streng: skal starte med "${r.prefix}"`;if("ends_with"===r.format)return`Ugyldig streng: skal ende med "${r.suffix}"`;if("includes"===r.format)return`Ugyldig streng: skal indeholde "${r.includes}"`;if("regex"===r.format)return`Ugyldig streng: skal matche m\xf8nsteret ${r.pattern}`;return`Ugyldig ${i[r.format]??r.format}`;case"not_multiple_of":return`Ugyldigt tal: skal v\xe6re deleligt med ${r.divisor}`;case"unrecognized_keys":return`${r.keys.length>1?"Ukendte nøgler":"Ukendt nøgle"}: ${p(r.keys,", ")}`;case"invalid_key":return`Ugyldig n\xf8gle i ${r.origin}`;case"invalid_union":return"Ugyldigt input: matcher ingen af de tilladte typer";case"invalid_element":return`Ugyldig v\xe6rdi i ${r.origin}`;default:return"Ugyldigt input"}}})()}}function rB(){return{localeError:(()=>{let e={string:{unit:"Zeichen",verb:"zu haben"},file:{unit:"Bytes",verb:"zu haben"},array:{unit:"Elemente",verb:"zu haben"},set:{unit:"Elemente",verb:"zu haben"}},t={regex:"Eingabe",email:"E-Mail-Adresse",url:"URL",emoji:"Emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO-Datum und -Uhrzeit",date:"ISO-Datum",time:"ISO-Uhrzeit",duration:"ISO-Dauer",ipv4:"IPv4-Adresse",ipv6:"IPv6-Adresse",cidrv4:"IPv4-Bereich",cidrv6:"IPv6-Bereich",base64:"Base64-codierter String",base64url:"Base64-URL-codierter String",json_string:"JSON-String",e164:"E.164-Nummer",jwt:"JWT",template_literal:"Eingabe"};return i=>{switch(i.code){case"invalid_type":return`Ung\xfcltige Eingabe: erwartet ${i.expected}, erhalten ${(e=>{let t=typeof e;switch(t){case"number":return Number.isNaN(e)?"NaN":"Zahl";case"object":if(Array.isArray(e))return"Array";if(null===e)return"null";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}return t})(i.input)}`;case"invalid_value":if(1===i.values.length)return`Ung\xfcltige Eingabe: erwartet ${M(i.values[0])}`;return`Ung\xfcltige Option: erwartet eine von ${p(i.values,"|")}`;case"too_big":{let t=i.inclusive?"<=":"<",r=e[i.origin]??null;if(r)return`Zu gro\xdf: erwartet, dass ${i.origin??"Wert"} ${t}${i.maximum.toString()} ${r.unit??"Elemente"} hat`;return`Zu gro\xdf: erwartet, dass ${i.origin??"Wert"} ${t}${i.maximum.toString()} ist`}case"too_small":{let t=i.inclusive?">=":">",r=e[i.origin]??null;if(r)return`Zu klein: erwartet, dass ${i.origin} ${t}${i.minimum.toString()} ${r.unit} hat`;return`Zu klein: erwartet, dass ${i.origin} ${t}${i.minimum.toString()} ist`}case"invalid_format":if("starts_with"===i.format)return`Ung\xfcltiger String: muss mit "${i.prefix}" beginnen`;if("ends_with"===i.format)return`Ung\xfcltiger String: muss mit "${i.suffix}" enden`;if("includes"===i.format)return`Ung\xfcltiger String: muss "${i.includes}" enthalten`;if("regex"===i.format)return`Ung\xfcltiger String: muss dem Muster ${i.pattern} entsprechen`;return`Ung\xfcltig: ${t[i.format]??i.format}`;case"not_multiple_of":return`Ung\xfcltige Zahl: muss ein Vielfaches von ${i.divisor} sein`;case"unrecognized_keys":return`${i.keys.length>1?"Unbekannte Schlüssel":"Unbekannter Schlüssel"}: ${p(i.keys,", ")}`;case"invalid_key":return`Ung\xfcltiger Schl\xfcssel in ${i.origin}`;case"invalid_union":return"Ungültige Eingabe";case"invalid_element":return`Ung\xfcltiger Wert in ${i.origin}`;default:return`Ung\xfcltige Eingabe`}}})()}}function rV(){return{localeError:(()=>{let e={string:{unit:"karaktrojn",verb:"havi"},file:{unit:"bajtojn",verb:"havi"},array:{unit:"elementojn",verb:"havi"},set:{unit:"elementojn",verb:"havi"}},t={regex:"enigo",email:"retadreso",url:"URL",emoji:"emoĝio",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO-datotempo",date:"ISO-dato",time:"ISO-tempo",duration:"ISO-daŭro",ipv4:"IPv4-adreso",ipv6:"IPv6-adreso",cidrv4:"IPv4-rango",cidrv6:"IPv6-rango",base64:"64-ume kodita karaktraro",base64url:"URL-64-ume kodita karaktraro",json_string:"JSON-karaktraro",e164:"E.164-nombro",jwt:"JWT",template_literal:"enigo"};return i=>{switch(i.code){case"invalid_type":return`Nevalida enigo: atendiĝis ${i.expected}, riceviĝis ${(e=>{let t=typeof e;switch(t){case"number":return Number.isNaN(e)?"NaN":"nombro";case"object":if(Array.isArray(e))return"tabelo";if(null===e)return"senvalora";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}return t})(i.input)}`;case"invalid_value":if(1===i.values.length)return`Nevalida enigo: atendiĝis ${M(i.values[0])}`;return`Nevalida opcio: atendiĝis unu el ${p(i.values,"|")}`;case"too_big":{let t=i.inclusive?"<=":"<",r=e[i.origin]??null;if(r)return`Tro granda: atendiĝis ke ${i.origin??"valoro"} havu ${t}${i.maximum.toString()} ${r.unit??"elementojn"}`;return`Tro granda: atendiĝis ke ${i.origin??"valoro"} havu ${t}${i.maximum.toString()}`}case"too_small":{let t=i.inclusive?">=":">",r=e[i.origin]??null;if(r)return`Tro malgranda: atendiĝis ke ${i.origin} havu ${t}${i.minimum.toString()} ${r.unit}`;return`Tro malgranda: atendiĝis ke ${i.origin} estu ${t}${i.minimum.toString()}`}case"invalid_format":if("starts_with"===i.format)return`Nevalida karaktraro: devas komenciĝi per "${i.prefix}"`;if("ends_with"===i.format)return`Nevalida karaktraro: devas finiĝi per "${i.suffix}"`;if("includes"===i.format)return`Nevalida karaktraro: devas inkluzivi "${i.includes}"`;if("regex"===i.format)return`Nevalida karaktraro: devas kongrui kun la modelo ${i.pattern}`;return`Nevalida ${t[i.format]??i.format}`;case"not_multiple_of":return`Nevalida nombro: devas esti oblo de ${i.divisor}`;case"unrecognized_keys":return`Nekonata${i.keys.length>1?"j":""} ŝlosilo${i.keys.length>1?"j":""}: ${p(i.keys,", ")}`;case"invalid_key":return`Nevalida ŝlosilo en ${i.origin}`;case"invalid_union":default:return"Nevalida enigo";case"invalid_element":return`Nevalida valoro en ${i.origin}`}}})()}}function rG(){return{localeError:(()=>{let e={string:{unit:"caracteres",verb:"tener"},file:{unit:"bytes",verb:"tener"},array:{unit:"elementos",verb:"tener"},set:{unit:"elementos",verb:"tener"}},t={regex:"entrada",email:"dirección de correo electrónico",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"fecha y hora ISO",date:"fecha ISO",time:"hora ISO",duration:"duración ISO",ipv4:"dirección IPv4",ipv6:"dirección IPv6",cidrv4:"rango IPv4",cidrv6:"rango IPv6",base64:"cadena codificada en base64",base64url:"URL codificada en base64",json_string:"cadena JSON",e164:"número E.164",jwt:"JWT",template_literal:"entrada"};return i=>{switch(i.code){case"invalid_type":return`Entrada inv\xe1lida: se esperaba ${i.expected}, recibido ${(e=>{let t=typeof e;switch(t){case"number":return Number.isNaN(e)?"NaN":"número";case"object":if(Array.isArray(e))return"arreglo";if(null===e)return"nulo";if(Object.getPrototypeOf(e)!==Object.prototype)return e.constructor.name}return t})(i.input)}`;case"invalid_value":if(1===i.values.length)return`Entrada inv\xe1lida: se esperaba ${M(i.values[0])}`;return`Opci\xf3n inv\xe1lida: se esperaba una de ${p(i.values,"|")}`;case"too_big":{let t=i.inclusive?"<=":"<",r=e[i.origin]??null;if(r)return`Demasiado grande: se esperaba que ${i.origin??"valor"} tuviera ${t}${i.maximum.toString()} ${r.unit??"elementos"}`;return`Demasiado grande: se esperaba que ${i.origin??"valor"} fuera ${t}${i.maximum.toString()}`}case"too_small":{let t=i.inclusive?">=":">",r=e[i.origin]??null;if(r)return`Demasiado peque\xf1o: se esperaba que ${i.origin} tuviera ${t}${i.minimum.toString()} ${r.unit}`;return`Demasiado peque\xf1o: se esperaba que ${i.origin} fuera ${t}${i.minimum.toString()}`}case"invalid_format":if("starts_with"===i.format)return`Cadena inv\xe1lida: debe comenzar con "${i.prefix}"`;if("ends_with"===i.format)return`Cadena inv\xe1lida: debe terminar en "${i.suffix}"`;if("includes"===i.format)return`Cadena inv\xe1lida: debe incluir "${i.includes}"`;if("regex"===i.format)return`Cadena inv\xe1lida: debe coincidir con el patr\xf3n ${i.pattern}`;return`Inv\xe1lido ${t[i.format]??i.format}`;case"not_multiple_of":return`N\xfamero inv\xe1lido: debe ser m\xfaltiplo de ${i.divisor}`;case"unrecognized_keys":return`Llave${i.keys.length>1?"s":""} desconocida${i.keys.length>1?"s":""}: ${p(i.keys,", ")}`;case"invalid_key":return`Llave inv\xe1lida en ${i.origin}`;case"invalid_union":return"Entrada inválida";case"invalid_element":return`Valor inv\xe1lido en ${i.origin}`;default:return`Entrada inv\xe1lida`}}})()}}function rK(){return{localeError:(()=>{let e={string:{unit:"کاراکتر",verb:"داشته باشد"},file:{unit:"بایت",verb:"داشته باشد"},array:{unit:"آیتم",verb:"داشته باشد"},set:{unit:"آیتم",verb:"داشته باشد"}},t={regex:"ورودی",email:"آدرس ایمیل",url:"URL",emoji:"ایموجی",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"تاریخ و زمان ایزو",date:"تاریخ ایزو",time:"زمان ایزو",duration:"مدت زمان ایزو",ipv4:"IPv4 آدرس",ipv6:"IPv6 آدرس",cidrv4:"IPv4 دامنه",cidrv6:"IPv6 دامنه",base64:"base64-encoded رشته",base64url:"base64url-encoded رشته",json_string:"JSON رشته",e164:"E.164 عدد",jwt:"JWT",template_literal:"ورودی"};return i=>{switch(i.code){case"invalid_type":return`ورودی نامعتبر: می‌بایست ${i.expected} می‌بود، ${(e=>{let t=typeof e;switch(t){case"number":return Number.isNaN(e)?"NaN":"عدد";case"object":if(Array.isArray(e))return"آرایه";if(null===e)return"null";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}return t})(i.input)} دریافت شد`;case"invalid_value":if(1===i.values.length)return`ورودی نامعتبر: می‌بایست ${M(i.values[0])} می‌بود`;return`گزینه نامعتبر: می‌بایست یکی از ${p(i.values,"|")} می‌بود`;case"too_big":{let t=i.inclusive?"<=":"<",r=e[i.origin]??null;if(r)return`خیلی بزرگ: ${i.origin??"مقدار"} باید ${t}${i.maximum.toString()} ${r.unit??"عنصر"} باشد`;return`خیلی بزرگ: ${i.origin??"مقدار"} باید ${t}${i.maximum.toString()} باشد`}case"too_small":{let t=i.inclusive?">=":">",r=e[i.origin]??null;if(r)return`خیلی کوچک: ${i.origin} باید ${t}${i.minimum.toString()} ${r.unit} باشد`;return`خیلی کوچک: ${i.origin} باید ${t}${i.minimum.toString()} باشد`}case"invalid_format":if("starts_with"===i.format)return`رشته نامعتبر: باید با "${i.prefix}" شروع شود`;if("ends_with"===i.format)return`رشته نامعتبر: باید با "${i.suffix}" تمام شود`;if("includes"===i.format)return`رشته نامعتبر: باید شامل "${i.includes}" باشد`;if("regex"===i.format)return`رشته نامعتبر: باید با الگوی ${i.pattern} مطابقت داشته باشد`;return`${t[i.format]??i.format} نامعتبر`;case"not_multiple_of":return`عدد نامعتبر: باید مضرب ${i.divisor} باشد`;case"unrecognized_keys":return`کلید${i.keys.length>1?"های":""} ناشناس: ${p(i.keys,", ")}`;case"invalid_key":return`کلید ناشناس در ${i.origin}`;case"invalid_union":default:return`ورودی نامعتبر`;case"invalid_element":return`مقدار نامعتبر در ${i.origin}`}}})()}}function rX(){return{localeError:(()=>{let e={string:{unit:"merkkiä",subject:"merkkijonon"},file:{unit:"tavua",subject:"tiedoston"},array:{unit:"alkiota",subject:"listan"},set:{unit:"alkiota",subject:"joukon"},number:{unit:"",subject:"luvun"},bigint:{unit:"",subject:"suuren kokonaisluvun"},int:{unit:"",subject:"kokonaisluvun"},date:{unit:"",subject:"päivämäärän"}},t={regex:"säännöllinen lauseke",email:"sähköpostiosoite",url:"URL-osoite",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO-aikaleima",date:"ISO-päivämäärä",time:"ISO-aika",duration:"ISO-kesto",ipv4:"IPv4-osoite",ipv6:"IPv6-osoite",cidrv4:"IPv4-alue",cidrv6:"IPv6-alue",base64:"base64-koodattu merkkijono",base64url:"base64url-koodattu merkkijono",json_string:"JSON-merkkijono",e164:"E.164-luku",jwt:"JWT",template_literal:"templaattimerkkijono"};return i=>{switch(i.code){case"invalid_type":return`Virheellinen tyyppi: odotettiin ${i.expected}, oli ${(e=>{let t=typeof e;switch(t){case"number":return Number.isNaN(e)?"NaN":"number";case"object":if(Array.isArray(e))return"array";if(null===e)return"null";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}return t})(i.input)}`;case"invalid_value":if(1===i.values.length)return`Virheellinen sy\xf6te: t\xe4ytyy olla ${M(i.values[0])}`;return`Virheellinen valinta: t\xe4ytyy olla yksi seuraavista: ${p(i.values,"|")}`;case"too_big":{let t=i.inclusive?"<=":"<",r=e[i.origin]??null;if(r)return`Liian suuri: ${r.subject} t\xe4ytyy olla ${t}${i.maximum.toString()} ${r.unit}`.trim();return`Liian suuri: arvon t\xe4ytyy olla ${t}${i.maximum.toString()}`}case"too_small":{let t=i.inclusive?">=":">",r=e[i.origin]??null;if(r)return`Liian pieni: ${r.subject} t\xe4ytyy olla ${t}${i.minimum.toString()} ${r.unit}`.trim();return`Liian pieni: arvon t\xe4ytyy olla ${t}${i.minimum.toString()}`}case"invalid_format":if("starts_with"===i.format)return`Virheellinen sy\xf6te: t\xe4ytyy alkaa "${i.prefix}"`;if("ends_with"===i.format)return`Virheellinen sy\xf6te: t\xe4ytyy loppua "${i.suffix}"`;if("includes"===i.format)return`Virheellinen sy\xf6te: t\xe4ytyy sis\xe4lt\xe4\xe4 "${i.includes}"`;if("regex"===i.format)return`Virheellinen sy\xf6te: t\xe4ytyy vastata s\xe4\xe4nn\xf6llist\xe4 lauseketta ${i.pattern}`;return`Virheellinen ${t[i.format]??i.format}`;case"not_multiple_of":return`Virheellinen luku: t\xe4ytyy olla luvun ${i.divisor} monikerta`;case"unrecognized_keys":return`${i.keys.length>1?"Tuntemattomat avaimet":"Tuntematon avain"}: ${p(i.keys,", ")}`;case"invalid_key":return"Virheellinen avain tietueessa";case"invalid_union":return"Virheellinen unioni";case"invalid_element":return"Virheellinen arvo joukossa";default:return`Virheellinen sy\xf6te`}}})()}}function rq(){return{localeError:(()=>{let e={string:{unit:"caractères",verb:"avoir"},file:{unit:"octets",verb:"avoir"},array:{unit:"éléments",verb:"avoir"},set:{unit:"éléments",verb:"avoir"}},t={regex:"entrée",email:"adresse e-mail",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"date et heure ISO",date:"date ISO",time:"heure ISO",duration:"durée ISO",ipv4:"adresse IPv4",ipv6:"adresse IPv6",cidrv4:"plage IPv4",cidrv6:"plage IPv6",base64:"chaîne encodée en base64",base64url:"chaîne encodée en base64url",json_string:"chaîne JSON",e164:"numéro E.164",jwt:"JWT",template_literal:"entrée"};return i=>{switch(i.code){case"invalid_type":return`Entr\xe9e invalide : ${i.expected} attendu, ${(e=>{let t=typeof e;switch(t){case"number":return Number.isNaN(e)?"NaN":"nombre";case"object":if(Array.isArray(e))return"tableau";if(null===e)return"null";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}return t})(i.input)} re\xe7u`;case"invalid_value":if(1===i.values.length)return`Entr\xe9e invalide : ${M(i.values[0])} attendu`;return`Option invalide : une valeur parmi ${p(i.values,"|")} attendue`;case"too_big":{let t=i.inclusive?"<=":"<",r=e[i.origin]??null;if(r)return`Trop grand : ${i.origin??"valeur"} doit ${r.verb} ${t}${i.maximum.toString()} ${r.unit??"élément(s)"}`;return`Trop grand : ${i.origin??"valeur"} doit \xeatre ${t}${i.maximum.toString()}`}case"too_small":{let t=i.inclusive?">=":">",r=e[i.origin]??null;if(r)return`Trop petit : ${i.origin} doit ${r.verb} ${t}${i.minimum.toString()} ${r.unit}`;return`Trop petit : ${i.origin} doit \xeatre ${t}${i.minimum.toString()}`}case"invalid_format":if("starts_with"===i.format)return`Cha\xeene invalide : doit commencer par "${i.prefix}"`;if("ends_with"===i.format)return`Cha\xeene invalide : doit se terminer par "${i.suffix}"`;if("includes"===i.format)return`Cha\xeene invalide : doit inclure "${i.includes}"`;if("regex"===i.format)return`Cha\xeene invalide : doit correspondre au mod\xe8le ${i.pattern}`;return`${t[i.format]??i.format} invalide`;case"not_multiple_of":return`Nombre invalide : doit \xeatre un multiple de ${i.divisor}`;case"unrecognized_keys":return`Cl\xe9${i.keys.length>1?"s":""} non reconnue${i.keys.length>1?"s":""} : ${p(i.keys,", ")}`;case"invalid_key":return`Cl\xe9 invalide dans ${i.origin}`;case"invalid_union":return"Entrée invalide";case"invalid_element":return`Valeur invalide dans ${i.origin}`;default:return`Entr\xe9e invalide`}}})()}}function rY(){return{localeError:(()=>{let e={string:{unit:"caractères",verb:"avoir"},file:{unit:"octets",verb:"avoir"},array:{unit:"éléments",verb:"avoir"},set:{unit:"éléments",verb:"avoir"}},t={regex:"entrée",email:"adresse courriel",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"date-heure ISO",date:"date ISO",time:"heure ISO",duration:"durée ISO",ipv4:"adresse IPv4",ipv6:"adresse IPv6",cidrv4:"plage IPv4",cidrv6:"plage IPv6",base64:"chaîne encodée en base64",base64url:"chaîne encodée en base64url",json_string:"chaîne JSON",e164:"numéro E.164",jwt:"JWT",template_literal:"entrée"};return i=>{switch(i.code){case"invalid_type":return`Entr\xe9e invalide : attendu ${i.expected}, re\xe7u ${(e=>{let t=typeof e;switch(t){case"number":return Number.isNaN(e)?"NaN":"number";case"object":if(Array.isArray(e))return"array";if(null===e)return"null";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}return t})(i.input)}`;case"invalid_value":if(1===i.values.length)return`Entr\xe9e invalide : attendu ${M(i.values[0])}`;return`Option invalide : attendu l'une des valeurs suivantes ${p(i.values,"|")}`;case"too_big":{let t=i.inclusive?"≤":"<",r=e[i.origin]??null;if(r)return`Trop grand : attendu que ${i.origin??"la valeur"} ait ${t}${i.maximum.toString()} ${r.unit}`;return`Trop grand : attendu que ${i.origin??"la valeur"} soit ${t}${i.maximum.toString()}`}case"too_small":{let t=i.inclusive?"≥":">",r=e[i.origin]??null;if(r)return`Trop petit : attendu que ${i.origin} ait ${t}${i.minimum.toString()} ${r.unit}`;return`Trop petit : attendu que ${i.origin} soit ${t}${i.minimum.toString()}`}case"invalid_format":if("starts_with"===i.format)return`Cha\xeene invalide : doit commencer par "${i.prefix}"`;if("ends_with"===i.format)return`Cha\xeene invalide : doit se terminer par "${i.suffix}"`;if("includes"===i.format)return`Cha\xeene invalide : doit inclure "${i.includes}"`;if("regex"===i.format)return`Cha\xeene invalide : doit correspondre au motif ${i.pattern}`;return`${t[i.format]??i.format} invalide`;case"not_multiple_of":return`Nombre invalide : doit \xeatre un multiple de ${i.divisor}`;case"unrecognized_keys":return`Cl\xe9${i.keys.length>1?"s":""} non reconnue${i.keys.length>1?"s":""} : ${p(i.keys,", ")}`;case"invalid_key":return`Cl\xe9 invalide dans ${i.origin}`;case"invalid_union":return"Entrée invalide";case"invalid_element":return`Valeur invalide dans ${i.origin}`;default:return`Entr\xe9e invalide`}}})()}}function rH(){return{localeError:(()=>{let e={string:{unit:"אותיות",verb:"לכלול"},file:{unit:"בייטים",verb:"לכלול"},array:{unit:"פריטים",verb:"לכלול"},set:{unit:"פריטים",verb:"לכלול"}},t={regex:"קלט",email:"כתובת אימייל",url:"כתובת רשת",emoji:"אימוג'י",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"תאריך וזמן ISO",date:"תאריך ISO",time:"זמן ISO",duration:"משך זמן ISO",ipv4:"כתובת IPv4",ipv6:"כתובת IPv6",cidrv4:"טווח IPv4",cidrv6:"טווח IPv6",base64:"מחרוזת בבסיס 64",base64url:"מחרוזת בבסיס 64 לכתובות רשת",json_string:"מחרוזת JSON",e164:"מספר E.164",jwt:"JWT",template_literal:"קלט"};return i=>{switch(i.code){case"invalid_type":return`קלט לא תקין: צריך ${i.expected}, התקבל ${(e=>{let t=typeof e;switch(t){case"number":return Number.isNaN(e)?"NaN":"number";case"object":if(Array.isArray(e))return"array";if(null===e)return"null";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}return t})(i.input)}`;case"invalid_value":if(1===i.values.length)return`קלט לא תקין: צריך ${M(i.values[0])}`;return`קלט לא תקין: צריך אחת מהאפשרויות  ${p(i.values,"|")}`;case"too_big":{let t=i.inclusive?"<=":"<",r=e[i.origin]??null;if(r)return`גדול מדי: ${i.origin??"value"} צריך להיות ${t}${i.maximum.toString()} ${r.unit??"elements"}`;return`גדול מדי: ${i.origin??"value"} צריך להיות ${t}${i.maximum.toString()}`}case"too_small":{let t=i.inclusive?">=":">",r=e[i.origin]??null;if(r)return`קטן מדי: ${i.origin} צריך להיות ${t}${i.minimum.toString()} ${r.unit}`;return`קטן מדי: ${i.origin} צריך להיות ${t}${i.minimum.toString()}`}case"invalid_format":if("starts_with"===i.format)return`מחרוזת לא תקינה: חייבת להתחיל ב"${i.prefix}"`;if("ends_with"===i.format)return`מחרוזת לא תקינה: חייבת להסתיים ב "${i.suffix}"`;if("includes"===i.format)return`מחרוזת לא תקינה: חייבת לכלול "${i.includes}"`;if("regex"===i.format)return`מחרוזת לא תקינה: חייבת להתאים לתבנית ${i.pattern}`;return`${t[i.format]??i.format} לא תקין`;case"not_multiple_of":return`מספר לא תקין: חייב להיות מכפלה של ${i.divisor}`;case"unrecognized_keys":return`מפתח${i.keys.length>1?"ות":""} לא מזוה${i.keys.length>1?"ים":"ה"}: ${p(i.keys,", ")}`;case"invalid_key":return`מפתח לא תקין ב${i.origin}`;case"invalid_union":return"קלט לא תקין";case"invalid_element":return`ערך לא תקין ב${i.origin}`;default:return`קלט לא תקין`}}})()}}function rQ(){return{localeError:(()=>{let e={string:{unit:"karakter",verb:"legyen"},file:{unit:"byte",verb:"legyen"},array:{unit:"elem",verb:"legyen"},set:{unit:"elem",verb:"legyen"}},t={regex:"bemenet",email:"email cím",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO időbélyeg",date:"ISO dátum",time:"ISO idő",duration:"ISO időintervallum",ipv4:"IPv4 cím",ipv6:"IPv6 cím",cidrv4:"IPv4 tartomány",cidrv6:"IPv6 tartomány",base64:"base64-kódolt string",base64url:"base64url-kódolt string",json_string:"JSON string",e164:"E.164 szám",jwt:"JWT",template_literal:"bemenet"};return i=>{switch(i.code){case"invalid_type":return`\xc9rv\xe9nytelen bemenet: a v\xe1rt \xe9rt\xe9k ${i.expected}, a kapott \xe9rt\xe9k ${(e=>{let t=typeof e;switch(t){case"number":return Number.isNaN(e)?"NaN":"szám";case"object":if(Array.isArray(e))return"tömb";if(null===e)return"null";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}return t})(i.input)}`;case"invalid_value":if(1===i.values.length)return`\xc9rv\xe9nytelen bemenet: a v\xe1rt \xe9rt\xe9k ${M(i.values[0])}`;return`\xc9rv\xe9nytelen opci\xf3: valamelyik \xe9rt\xe9k v\xe1rt ${p(i.values,"|")}`;case"too_big":{let t=i.inclusive?"<=":"<",r=e[i.origin]??null;if(r)return`T\xfal nagy: ${i.origin??"érték"} m\xe9rete t\xfal nagy ${t}${i.maximum.toString()} ${r.unit??"elem"}`;return`T\xfal nagy: a bemeneti \xe9rt\xe9k ${i.origin??"érték"} t\xfal nagy: ${t}${i.maximum.toString()}`}case"too_small":{let t=i.inclusive?">=":">",r=e[i.origin]??null;if(r)return`T\xfal kicsi: a bemeneti \xe9rt\xe9k ${i.origin} m\xe9rete t\xfal kicsi ${t}${i.minimum.toString()} ${r.unit}`;return`T\xfal kicsi: a bemeneti \xe9rt\xe9k ${i.origin} t\xfal kicsi ${t}${i.minimum.toString()}`}case"invalid_format":if("starts_with"===i.format)return`\xc9rv\xe9nytelen string: "${i.prefix}" \xe9rt\xe9kkel kell kezdődnie`;if("ends_with"===i.format)return`\xc9rv\xe9nytelen string: "${i.suffix}" \xe9rt\xe9kkel kell v\xe9gződnie`;if("includes"===i.format)return`\xc9rv\xe9nytelen string: "${i.includes}" \xe9rt\xe9ket kell tartalmaznia`;if("regex"===i.format)return`\xc9rv\xe9nytelen string: ${i.pattern} mint\xe1nak kell megfelelnie`;return`\xc9rv\xe9nytelen ${t[i.format]??i.format}`;case"not_multiple_of":return`\xc9rv\xe9nytelen sz\xe1m: ${i.divisor} t\xf6bbsz\xf6r\xf6s\xe9nek kell lennie`;case"unrecognized_keys":return`Ismeretlen kulcs${i.keys.length>1?"s":""}: ${p(i.keys,", ")}`;case"invalid_key":return`\xc9rv\xe9nytelen kulcs ${i.origin}`;case"invalid_union":return"Érvénytelen bemenet";case"invalid_element":return`\xc9rv\xe9nytelen \xe9rt\xe9k: ${i.origin}`;default:return`\xc9rv\xe9nytelen bemenet`}}})()}}function r4(){return{localeError:(()=>{let e={string:{unit:"karakter",verb:"memiliki"},file:{unit:"byte",verb:"memiliki"},array:{unit:"item",verb:"memiliki"},set:{unit:"item",verb:"memiliki"}},t={regex:"input",email:"alamat email",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"tanggal dan waktu format ISO",date:"tanggal format ISO",time:"jam format ISO",duration:"durasi format ISO",ipv4:"alamat IPv4",ipv6:"alamat IPv6",cidrv4:"rentang alamat IPv4",cidrv6:"rentang alamat IPv6",base64:"string dengan enkode base64",base64url:"string dengan enkode base64url",json_string:"string JSON",e164:"angka E.164",jwt:"JWT",template_literal:"input"};return i=>{switch(i.code){case"invalid_type":return`Input tidak valid: diharapkan ${i.expected}, diterima ${(e=>{let t=typeof e;switch(t){case"number":return Number.isNaN(e)?"NaN":"number";case"object":if(Array.isArray(e))return"array";if(null===e)return"null";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}return t})(i.input)}`;case"invalid_value":if(1===i.values.length)return`Input tidak valid: diharapkan ${M(i.values[0])}`;return`Pilihan tidak valid: diharapkan salah satu dari ${p(i.values,"|")}`;case"too_big":{let t=i.inclusive?"<=":"<",r=e[i.origin]??null;if(r)return`Terlalu besar: diharapkan ${i.origin??"value"} memiliki ${t}${i.maximum.toString()} ${r.unit??"elemen"}`;return`Terlalu besar: diharapkan ${i.origin??"value"} menjadi ${t}${i.maximum.toString()}`}case"too_small":{let t=i.inclusive?">=":">",r=e[i.origin]??null;if(r)return`Terlalu kecil: diharapkan ${i.origin} memiliki ${t}${i.minimum.toString()} ${r.unit}`;return`Terlalu kecil: diharapkan ${i.origin} menjadi ${t}${i.minimum.toString()}`}case"invalid_format":if("starts_with"===i.format)return`String tidak valid: harus dimulai dengan "${i.prefix}"`;if("ends_with"===i.format)return`String tidak valid: harus berakhir dengan "${i.suffix}"`;if("includes"===i.format)return`String tidak valid: harus menyertakan "${i.includes}"`;if("regex"===i.format)return`String tidak valid: harus sesuai pola ${i.pattern}`;return`${t[i.format]??i.format} tidak valid`;case"not_multiple_of":return`Angka tidak valid: harus kelipatan dari ${i.divisor}`;case"unrecognized_keys":return`Kunci tidak dikenali ${i.keys.length>1?"s":""}: ${p(i.keys,", ")}`;case"invalid_key":return`Kunci tidak valid di ${i.origin}`;case"invalid_union":default:return"Input tidak valid";case"invalid_element":return`Nilai tidak valid di ${i.origin}`}}})()}}function r6(){return{localeError:(()=>{let e={string:{unit:"stafi",verb:"að hafa"},file:{unit:"bæti",verb:"að hafa"},array:{unit:"hluti",verb:"að hafa"},set:{unit:"hluti",verb:"að hafa"}},t={regex:"gildi",email:"netfang",url:"vefslóð",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO dagsetning og tími",date:"ISO dagsetning",time:"ISO tími",duration:"ISO tímalengd",ipv4:"IPv4 address",ipv6:"IPv6 address",cidrv4:"IPv4 range",cidrv6:"IPv6 range",base64:"base64-encoded strengur",base64url:"base64url-encoded strengur",json_string:"JSON strengur",e164:"E.164 tölugildi",jwt:"JWT",template_literal:"gildi"};return i=>{switch(i.code){case"invalid_type":return`Rangt gildi: \xde\xfa sl\xf3st inn ${(e=>{let t=typeof e;switch(t){case"number":return Number.isNaN(e)?"NaN":"númer";case"object":if(Array.isArray(e))return"fylki";if(null===e)return"null";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}return t})(i.input)} \xfear sem \xe1 a\xf0 vera ${i.expected}`;case"invalid_value":if(1===i.values.length)return`Rangt gildi: gert r\xe1\xf0 fyrir ${M(i.values[0])}`;return`\xd3gilt val: m\xe1 vera eitt af eftirfarandi ${p(i.values,"|")}`;case"too_big":{let t=i.inclusive?"<=":"<",r=e[i.origin]??null;if(r)return`Of st\xf3rt: gert er r\xe1\xf0 fyrir a\xf0 ${i.origin??"gildi"} hafi ${t}${i.maximum.toString()} ${r.unit??"hluti"}`;return`Of st\xf3rt: gert er r\xe1\xf0 fyrir a\xf0 ${i.origin??"gildi"} s\xe9 ${t}${i.maximum.toString()}`}case"too_small":{let t=i.inclusive?">=":">",r=e[i.origin]??null;if(r)return`Of l\xedti\xf0: gert er r\xe1\xf0 fyrir a\xf0 ${i.origin} hafi ${t}${i.minimum.toString()} ${r.unit}`;return`Of l\xedti\xf0: gert er r\xe1\xf0 fyrir a\xf0 ${i.origin} s\xe9 ${t}${i.minimum.toString()}`}case"invalid_format":if("starts_with"===i.format)return`\xd3gildur strengur: ver\xf0ur a\xf0 byrja \xe1 "${i.prefix}"`;if("ends_with"===i.format)return`\xd3gildur strengur: ver\xf0ur a\xf0 enda \xe1 "${i.suffix}"`;if("includes"===i.format)return`\xd3gildur strengur: ver\xf0ur a\xf0 innihalda "${i.includes}"`;if("regex"===i.format)return`\xd3gildur strengur: ver\xf0ur a\xf0 fylgja mynstri ${i.pattern}`;return`Rangt ${t[i.format]??i.format}`;case"not_multiple_of":return`R\xf6ng tala: ver\xf0ur a\xf0 vera margfeldi af ${i.divisor}`;case"unrecognized_keys":return`\xd3\xfeekkt ${i.keys.length>1?"ir lyklar":"ur lykill"}: ${p(i.keys,", ")}`;case"invalid_key":return`Rangur lykill \xed ${i.origin}`;case"invalid_union":default:return"Rangt gildi";case"invalid_element":return`Rangt gildi \xed ${i.origin}`}}})()}}function r0(){return{localeError:(()=>{let e={string:{unit:"caratteri",verb:"avere"},file:{unit:"byte",verb:"avere"},array:{unit:"elementi",verb:"avere"},set:{unit:"elementi",verb:"avere"}},t={regex:"input",email:"indirizzo email",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"data e ora ISO",date:"data ISO",time:"ora ISO",duration:"durata ISO",ipv4:"indirizzo IPv4",ipv6:"indirizzo IPv6",cidrv4:"intervallo IPv4",cidrv6:"intervallo IPv6",base64:"stringa codificata in base64",base64url:"URL codificata in base64",json_string:"stringa JSON",e164:"numero E.164",jwt:"JWT",template_literal:"input"};return i=>{switch(i.code){case"invalid_type":return`Input non valido: atteso ${i.expected}, ricevuto ${(e=>{let t=typeof e;switch(t){case"number":return Number.isNaN(e)?"NaN":"numero";case"object":if(Array.isArray(e))return"vettore";if(null===e)return"null";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}return t})(i.input)}`;case"invalid_value":if(1===i.values.length)return`Input non valido: atteso ${M(i.values[0])}`;return`Opzione non valida: atteso uno tra ${p(i.values,"|")}`;case"too_big":{let t=i.inclusive?"<=":"<",r=e[i.origin]??null;if(r)return`Troppo grande: ${i.origin??"valore"} deve avere ${t}${i.maximum.toString()} ${r.unit??"elementi"}`;return`Troppo grande: ${i.origin??"valore"} deve essere ${t}${i.maximum.toString()}`}case"too_small":{let t=i.inclusive?">=":">",r=e[i.origin]??null;if(r)return`Troppo piccolo: ${i.origin} deve avere ${t}${i.minimum.toString()} ${r.unit}`;return`Troppo piccolo: ${i.origin} deve essere ${t}${i.minimum.toString()}`}case"invalid_format":if("starts_with"===i.format)return`Stringa non valida: deve iniziare con "${i.prefix}"`;if("ends_with"===i.format)return`Stringa non valida: deve terminare con "${i.suffix}"`;if("includes"===i.format)return`Stringa non valida: deve includere "${i.includes}"`;if("regex"===i.format)return`Stringa non valida: deve corrispondere al pattern ${i.pattern}`;return`Invalid ${t[i.format]??i.format}`;case"not_multiple_of":return`Numero non valido: deve essere un multiplo di ${i.divisor}`;case"unrecognized_keys":return`Chiav${i.keys.length>1?"i":"e"} non riconosciut${i.keys.length>1?"e":"a"}: ${p(i.keys,", ")}`;case"invalid_key":return`Chiave non valida in ${i.origin}`;case"invalid_union":default:return"Input non valido";case"invalid_element":return`Valore non valido in ${i.origin}`}}})()}}function r1(){return{localeError:(()=>{let e={string:{unit:"文字",verb:"である"},file:{unit:"バイト",verb:"である"},array:{unit:"要素",verb:"である"},set:{unit:"要素",verb:"である"}},t={regex:"入力値",email:"メールアドレス",url:"URL",emoji:"絵文字",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO日時",date:"ISO日付",time:"ISO時刻",duration:"ISO期間",ipv4:"IPv4アドレス",ipv6:"IPv6アドレス",cidrv4:"IPv4範囲",cidrv6:"IPv6範囲",base64:"base64エンコード文字列",base64url:"base64urlエンコード文字列",json_string:"JSON文字列",e164:"E.164番号",jwt:"JWT",template_literal:"入力値"};return i=>{switch(i.code){case"invalid_type":return`無効な入力: ${i.expected}が期待されましたが、${(e=>{let t=typeof e;switch(t){case"number":return Number.isNaN(e)?"NaN":"数値";case"object":if(Array.isArray(e))return"配列";if(null===e)return"null";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}return t})(i.input)}が入力されました`;case"invalid_value":if(1===i.values.length)return`無効な入力: ${M(i.values[0])}が期待されました`;return`無効な選択: ${p(i.values,"、")}のいずれかである必要があります`;case"too_big":{let t=i.inclusive?"以下である":"より小さい",r=e[i.origin]??null;if(r)return`大きすぎる値: ${i.origin??"値"}は${i.maximum.toString()}${r.unit??"要素"}${t}必要があります`;return`大きすぎる値: ${i.origin??"値"}は${i.maximum.toString()}${t}必要があります`}case"too_small":{let t=i.inclusive?"以上である":"より大きい",r=e[i.origin]??null;if(r)return`小さすぎる値: ${i.origin}は${i.minimum.toString()}${r.unit}${t}必要があります`;return`小さすぎる値: ${i.origin}は${i.minimum.toString()}${t}必要があります`}case"invalid_format":if("starts_with"===i.format)return`無効な文字列: "${i.prefix}"で始まる必要があります`;if("ends_with"===i.format)return`無効な文字列: "${i.suffix}"で終わる必要があります`;if("includes"===i.format)return`無効な文字列: "${i.includes}"を含む必要があります`;if("regex"===i.format)return`無効な文字列: パターン${i.pattern}に一致する必要があります`;return`無効な${t[i.format]??i.format}`;case"not_multiple_of":return`無効な数値: ${i.divisor}の倍数である必要があります`;case"unrecognized_keys":return`認識されていないキー${i.keys.length>1?"群":""}: ${p(i.keys,"、")}`;case"invalid_key":return`${i.origin}内の無効なキー`;case"invalid_union":return"無効な入力";case"invalid_element":return`${i.origin}内の無効な値`;default:return`無効な入力`}}})()}}function r2(){return{localeError:(()=>{let e={string:{unit:"តួអក្សរ",verb:"គួរមាន"},file:{unit:"បៃ",verb:"គួរមាន"},array:{unit:"ធាតុ",verb:"គួរមាន"},set:{unit:"ធាតុ",verb:"គួរមាន"}},t={regex:"ទិន្នន័យបញ្ចូល",email:"អាសយដ្ឋានអ៊ីមែល",url:"URL",emoji:"សញ្ញាអារម្មណ៍",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"កាលបរិច្ឆេទ និងម៉ោង ISO",date:"កាលបរិច្ឆេទ ISO",time:"ម៉ោង ISO",duration:"រយៈពេល ISO",ipv4:"អាសយដ្ឋាន IPv4",ipv6:"អាសយដ្ឋាន IPv6",cidrv4:"ដែនអាសយដ្ឋាន IPv4",cidrv6:"ដែនអាសយដ្ឋាន IPv6",base64:"ខ្សែអក្សរអ៊ិកូដ base64",base64url:"ខ្សែអក្សរអ៊ិកូដ base64url",json_string:"ខ្សែអក្សរ JSON",e164:"លេខ E.164",jwt:"JWT",template_literal:"ទិន្នន័យបញ្ចូល"};return i=>{switch(i.code){case"invalid_type":return`ទិន្នន័យបញ្ចូលមិនត្រឹមត្រូវ៖ ត្រូវការ ${i.expected} ប៉ុន្តែទទួលបាន ${(e=>{let t=typeof e;switch(t){case"number":return Number.isNaN(e)?"មិនមែនជាលេខ (NaN)":"លេខ";case"object":if(Array.isArray(e))return"អារេ (Array)";if(null===e)return"គ្មានតម្លៃ (null)";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}return t})(i.input)}`;case"invalid_value":if(1===i.values.length)return`ទិន្នន័យបញ្ចូលមិនត្រឹមត្រូវ៖ ត្រូវការ ${M(i.values[0])}`;return`ជម្រើសមិនត្រឹមត្រូវ៖ ត្រូវជាមួយក្នុងចំណោម ${p(i.values,"|")}`;case"too_big":{let t=i.inclusive?"<=":"<",r=e[i.origin]??null;if(r)return`ធំពេក៖ ត្រូវការ ${i.origin??"តម្លៃ"} ${t} ${i.maximum.toString()} ${r.unit??"ធាតុ"}`;return`ធំពេក៖ ត្រូវការ ${i.origin??"តម្លៃ"} ${t} ${i.maximum.toString()}`}case"too_small":{let t=i.inclusive?">=":">",r=e[i.origin]??null;if(r)return`តូចពេក៖ ត្រូវការ ${i.origin} ${t} ${i.minimum.toString()} ${r.unit}`;return`តូចពេក៖ ត្រូវការ ${i.origin} ${t} ${i.minimum.toString()}`}case"invalid_format":if("starts_with"===i.format)return`ខ្សែអក្សរមិនត្រឹមត្រូវ៖ ត្រូវចាប់ផ្តើមដោយ "${i.prefix}"`;if("ends_with"===i.format)return`ខ្សែអក្សរមិនត្រឹមត្រូវ៖ ត្រូវបញ្ចប់ដោយ "${i.suffix}"`;if("includes"===i.format)return`ខ្សែអក្សរមិនត្រឹមត្រូវ៖ ត្រូវមាន "${i.includes}"`;if("regex"===i.format)return`ខ្សែអក្សរមិនត្រឹមត្រូវ៖ ត្រូវតែផ្គូផ្គងនឹងទម្រង់ដែលបានកំណត់ ${i.pattern}`;return`មិនត្រឹមត្រូវ៖ ${t[i.format]??i.format}`;case"not_multiple_of":return`លេខមិនត្រឹមត្រូវ៖ ត្រូវតែជាពហុគុណនៃ ${i.divisor}`;case"unrecognized_keys":return`រកឃើញសោមិនស្គាល់៖ ${p(i.keys,", ")}`;case"invalid_key":return`សោមិនត្រឹមត្រូវនៅក្នុង ${i.origin}`;case"invalid_union":default:return`ទិន្នន័យមិនត្រឹមត្រូវ`;case"invalid_element":return`ទិន្នន័យមិនត្រឹមត្រូវនៅក្នុង ${i.origin}`}}})()}}function r9(){return{localeError:(()=>{let e={string:{unit:"문자",verb:"to have"},file:{unit:"바이트",verb:"to have"},array:{unit:"개",verb:"to have"},set:{unit:"개",verb:"to have"}},t={regex:"입력",email:"이메일 주소",url:"URL",emoji:"이모지",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO 날짜시간",date:"ISO 날짜",time:"ISO 시간",duration:"ISO 기간",ipv4:"IPv4 주소",ipv6:"IPv6 주소",cidrv4:"IPv4 범위",cidrv6:"IPv6 범위",base64:"base64 인코딩 문자열",base64url:"base64url 인코딩 문자열",json_string:"JSON 문자열",e164:"E.164 번호",jwt:"JWT",template_literal:"입력"};return i=>{switch(i.code){case"invalid_type":return`잘못된 입력: 예상 타입은 ${i.expected}, 받은 타입은 ${(e=>{let t=typeof e;switch(t){case"number":return Number.isNaN(e)?"NaN":"number";case"object":if(Array.isArray(e))return"array";if(null===e)return"null";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}return t})(i.input)}입니다`;case"invalid_value":if(1===i.values.length)return`잘못된 입력: 값은 ${M(i.values[0])} 이어야 합니다`;return`잘못된 옵션: ${p(i.values,"또는 ")} 중 하나여야 합니다`;case"too_big":{let t=i.inclusive?"이하":"미만",r="미만"===t?"이어야 합니다":"여야 합니다",n=e[i.origin]??null,a=n?.unit??"요소";if(n)return`${i.origin??"값"}이 너무 큽니다: ${i.maximum.toString()}${a} ${t}${r}`;return`${i.origin??"값"}이 너무 큽니다: ${i.maximum.toString()} ${t}${r}`}case"too_small":{let t=i.inclusive?"이상":"초과",r="이상"===t?"이어야 합니다":"여야 합니다",n=e[i.origin]??null,a=n?.unit??"요소";if(n)return`${i.origin??"값"}이 너무 작습니다: ${i.minimum.toString()}${a} ${t}${r}`;return`${i.origin??"값"}이 너무 작습니다: ${i.minimum.toString()} ${t}${r}`}case"invalid_format":if("starts_with"===i.format)return`잘못된 문자열: "${i.prefix}"(으)로 시작해야 합니다`;if("ends_with"===i.format)return`잘못된 문자열: "${i.suffix}"(으)로 끝나야 합니다`;if("includes"===i.format)return`잘못된 문자열: "${i.includes}"을(를) 포함해야 합니다`;if("regex"===i.format)return`잘못된 문자열: 정규식 ${i.pattern} 패턴과 일치해야 합니다`;return`잘못된 ${t[i.format]??i.format}`;case"not_multiple_of":return`잘못된 숫자: ${i.divisor}의 배수여야 합니다`;case"unrecognized_keys":return`인식할 수 없는 키: ${p(i.keys,", ")}`;case"invalid_key":return`잘못된 키: ${i.origin}`;case"invalid_union":default:return`잘못된 입력`;case"invalid_element":return`잘못된 값: ${i.origin}`}}})()}}function r3(){return{localeError:(()=>{let e={string:{unit:"знаци",verb:"да имаат"},file:{unit:"бајти",verb:"да имаат"},array:{unit:"ставки",verb:"да имаат"},set:{unit:"ставки",verb:"да имаат"}},t={regex:"внес",email:"адреса на е-пошта",url:"URL",emoji:"емоџи",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO датум и време",date:"ISO датум",time:"ISO време",duration:"ISO времетраење",ipv4:"IPv4 адреса",ipv6:"IPv6 адреса",cidrv4:"IPv4 опсег",cidrv6:"IPv6 опсег",base64:"base64-енкодирана низа",base64url:"base64url-енкодирана низа",json_string:"JSON низа",e164:"E.164 број",jwt:"JWT",template_literal:"внес"};return i=>{switch(i.code){case"invalid_type":return`Грешен внес: се очекува ${i.expected}, примено ${(e=>{let t=typeof e;switch(t){case"number":return Number.isNaN(e)?"NaN":"број";case"object":if(Array.isArray(e))return"низа";if(null===e)return"null";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}return t})(i.input)}`;case"invalid_value":if(1===i.values.length)return`Invalid input: expected ${M(i.values[0])}`;return`Грешана опција: се очекува една ${p(i.values,"|")}`;case"too_big":{let t=i.inclusive?"<=":"<",r=e[i.origin]??null;if(r)return`Премногу голем: се очекува ${i.origin??"вредноста"} да има ${t}${i.maximum.toString()} ${r.unit??"елементи"}`;return`Премногу голем: се очекува ${i.origin??"вредноста"} да биде ${t}${i.maximum.toString()}`}case"too_small":{let t=i.inclusive?">=":">",r=e[i.origin]??null;if(r)return`Премногу мал: се очекува ${i.origin} да има ${t}${i.minimum.toString()} ${r.unit}`;return`Премногу мал: се очекува ${i.origin} да биде ${t}${i.minimum.toString()}`}case"invalid_format":if("starts_with"===i.format)return`Неважечка низа: мора да започнува со "${i.prefix}"`;if("ends_with"===i.format)return`Неважечка низа: мора да завршува со "${i.suffix}"`;if("includes"===i.format)return`Неважечка низа: мора да вклучува "${i.includes}"`;if("regex"===i.format)return`Неважечка низа: мора да одгоара на патернот ${i.pattern}`;return`Invalid ${t[i.format]??i.format}`;case"not_multiple_of":return`Грешен број: мора да биде делив со ${i.divisor}`;case"unrecognized_keys":return`${i.keys.length>1?"Непрепознаени клучеви":"Непрепознаен клуч"}: ${p(i.keys,", ")}`;case"invalid_key":return`Грешен клуч во ${i.origin}`;case"invalid_union":return"Грешен внес";case"invalid_element":return`Грешна вредност во ${i.origin}`;default:return`Грешен внес`}}})()}}function r7(){return{localeError:(()=>{let e={string:{unit:"aksara",verb:"mempunyai"},file:{unit:"bait",verb:"mempunyai"},array:{unit:"elemen",verb:"mempunyai"},set:{unit:"elemen",verb:"mempunyai"}},t={regex:"input",email:"alamat e-mel",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"tarikh masa ISO",date:"tarikh ISO",time:"masa ISO",duration:"tempoh ISO",ipv4:"alamat IPv4",ipv6:"alamat IPv6",cidrv4:"julat IPv4",cidrv6:"julat IPv6",base64:"string dikodkan base64",base64url:"string dikodkan base64url",json_string:"string JSON",e164:"nombor E.164",jwt:"JWT",template_literal:"input"};return i=>{switch(i.code){case"invalid_type":return`Input tidak sah: dijangka ${i.expected}, diterima ${(e=>{let t=typeof e;switch(t){case"number":return Number.isNaN(e)?"NaN":"nombor";case"object":if(Array.isArray(e))return"array";if(null===e)return"null";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}return t})(i.input)}`;case"invalid_value":if(1===i.values.length)return`Input tidak sah: dijangka ${M(i.values[0])}`;return`Pilihan tidak sah: dijangka salah satu daripada ${p(i.values,"|")}`;case"too_big":{let t=i.inclusive?"<=":"<",r=e[i.origin]??null;if(r)return`Terlalu besar: dijangka ${i.origin??"nilai"} ${r.verb} ${t}${i.maximum.toString()} ${r.unit??"elemen"}`;return`Terlalu besar: dijangka ${i.origin??"nilai"} adalah ${t}${i.maximum.toString()}`}case"too_small":{let t=i.inclusive?">=":">",r=e[i.origin]??null;if(r)return`Terlalu kecil: dijangka ${i.origin} ${r.verb} ${t}${i.minimum.toString()} ${r.unit}`;return`Terlalu kecil: dijangka ${i.origin} adalah ${t}${i.minimum.toString()}`}case"invalid_format":if("starts_with"===i.format)return`String tidak sah: mesti bermula dengan "${i.prefix}"`;if("ends_with"===i.format)return`String tidak sah: mesti berakhir dengan "${i.suffix}"`;if("includes"===i.format)return`String tidak sah: mesti mengandungi "${i.includes}"`;if("regex"===i.format)return`String tidak sah: mesti sepadan dengan corak ${i.pattern}`;return`${t[i.format]??i.format} tidak sah`;case"not_multiple_of":return`Nombor tidak sah: perlu gandaan ${i.divisor}`;case"unrecognized_keys":return`Kunci tidak dikenali: ${p(i.keys,", ")}`;case"invalid_key":return`Kunci tidak sah dalam ${i.origin}`;case"invalid_union":default:return"Input tidak sah";case"invalid_element":return`Nilai tidak sah dalam ${i.origin}`}}})()}}function r5(){return{localeError:(()=>{let e={string:{unit:"tekens"},file:{unit:"bytes"},array:{unit:"elementen"},set:{unit:"elementen"}},t={regex:"invoer",email:"emailadres",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO datum en tijd",date:"ISO datum",time:"ISO tijd",duration:"ISO duur",ipv4:"IPv4-adres",ipv6:"IPv6-adres",cidrv4:"IPv4-bereik",cidrv6:"IPv6-bereik",base64:"base64-gecodeerde tekst",base64url:"base64 URL-gecodeerde tekst",json_string:"JSON string",e164:"E.164-nummer",jwt:"JWT",template_literal:"invoer"};return i=>{switch(i.code){case"invalid_type":return`Ongeldige invoer: verwacht ${i.expected}, ontving ${(e=>{let t=typeof e;switch(t){case"number":return Number.isNaN(e)?"NaN":"getal";case"object":if(Array.isArray(e))return"array";if(null===e)return"null";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}return t})(i.input)}`;case"invalid_value":if(1===i.values.length)return`Ongeldige invoer: verwacht ${M(i.values[0])}`;return`Ongeldige optie: verwacht \xe9\xe9n van ${p(i.values,"|")}`;case"too_big":{let t=i.inclusive?"<=":"<",r=e[i.origin]??null;if(r)return`Te lang: verwacht dat ${i.origin??"waarde"} ${t}${i.maximum.toString()} ${r.unit??"elementen"} bevat`;return`Te lang: verwacht dat ${i.origin??"waarde"} ${t}${i.maximum.toString()} is`}case"too_small":{let t=i.inclusive?">=":">",r=e[i.origin]??null;if(r)return`Te kort: verwacht dat ${i.origin} ${t}${i.minimum.toString()} ${r.unit} bevat`;return`Te kort: verwacht dat ${i.origin} ${t}${i.minimum.toString()} is`}case"invalid_format":if("starts_with"===i.format)return`Ongeldige tekst: moet met "${i.prefix}" beginnen`;if("ends_with"===i.format)return`Ongeldige tekst: moet op "${i.suffix}" eindigen`;if("includes"===i.format)return`Ongeldige tekst: moet "${i.includes}" bevatten`;if("regex"===i.format)return`Ongeldige tekst: moet overeenkomen met patroon ${i.pattern}`;return`Ongeldig: ${t[i.format]??i.format}`;case"not_multiple_of":return`Ongeldig getal: moet een veelvoud van ${i.divisor} zijn`;case"unrecognized_keys":return`Onbekende key${i.keys.length>1?"s":""}: ${p(i.keys,", ")}`;case"invalid_key":return`Ongeldige key in ${i.origin}`;case"invalid_union":default:return"Ongeldige invoer";case"invalid_element":return`Ongeldige waarde in ${i.origin}`}}})()}}function r8(){return{localeError:(()=>{let e={string:{unit:"tegn",verb:"å ha"},file:{unit:"bytes",verb:"å ha"},array:{unit:"elementer",verb:"å inneholde"},set:{unit:"elementer",verb:"å inneholde"}},t={regex:"input",email:"e-postadresse",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO dato- og klokkeslett",date:"ISO-dato",time:"ISO-klokkeslett",duration:"ISO-varighet",ipv4:"IPv4-område",ipv6:"IPv6-område",cidrv4:"IPv4-spekter",cidrv6:"IPv6-spekter",base64:"base64-enkodet streng",base64url:"base64url-enkodet streng",json_string:"JSON-streng",e164:"E.164-nummer",jwt:"JWT",template_literal:"input"};return i=>{switch(i.code){case"invalid_type":return`Ugyldig input: forventet ${i.expected}, fikk ${(e=>{let t=typeof e;switch(t){case"number":return Number.isNaN(e)?"NaN":"tall";case"object":if(Array.isArray(e))return"liste";if(null===e)return"null";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}return t})(i.input)}`;case"invalid_value":if(1===i.values.length)return`Ugyldig verdi: forventet ${M(i.values[0])}`;return`Ugyldig valg: forventet en av ${p(i.values,"|")}`;case"too_big":{let t=i.inclusive?"<=":"<",r=e[i.origin]??null;if(r)return`For stor(t): forventet ${i.origin??"value"} til \xe5 ha ${t}${i.maximum.toString()} ${r.unit??"elementer"}`;return`For stor(t): forventet ${i.origin??"value"} til \xe5 ha ${t}${i.maximum.toString()}`}case"too_small":{let t=i.inclusive?">=":">",r=e[i.origin]??null;if(r)return`For lite(n): forventet ${i.origin} til \xe5 ha ${t}${i.minimum.toString()} ${r.unit}`;return`For lite(n): forventet ${i.origin} til \xe5 ha ${t}${i.minimum.toString()}`}case"invalid_format":if("starts_with"===i.format)return`Ugyldig streng: m\xe5 starte med "${i.prefix}"`;if("ends_with"===i.format)return`Ugyldig streng: m\xe5 ende med "${i.suffix}"`;if("includes"===i.format)return`Ugyldig streng: m\xe5 inneholde "${i.includes}"`;if("regex"===i.format)return`Ugyldig streng: m\xe5 matche m\xf8nsteret ${i.pattern}`;return`Ugyldig ${t[i.format]??i.format}`;case"not_multiple_of":return`Ugyldig tall: m\xe5 v\xe6re et multiplum av ${i.divisor}`;case"unrecognized_keys":return`${i.keys.length>1?"Ukjente nøkler":"Ukjent nøkkel"}: ${p(i.keys,", ")}`;case"invalid_key":return`Ugyldig n\xf8kkel i ${i.origin}`;case"invalid_union":default:return"Ugyldig input";case"invalid_element":return`Ugyldig verdi i ${i.origin}`}}})()}}function ne(){return{localeError:(()=>{let e={string:{unit:"harf",verb:"olmalıdır"},file:{unit:"bayt",verb:"olmalıdır"},array:{unit:"unsur",verb:"olmalıdır"},set:{unit:"unsur",verb:"olmalıdır"}},t={regex:"giren",email:"epostagâh",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO hengâmı",date:"ISO tarihi",time:"ISO zamanı",duration:"ISO müddeti",ipv4:"IPv4 nişânı",ipv6:"IPv6 nişânı",cidrv4:"IPv4 menzili",cidrv6:"IPv6 menzili",base64:"base64-şifreli metin",base64url:"base64url-şifreli metin",json_string:"JSON metin",e164:"E.164 sayısı",jwt:"JWT",template_literal:"giren"};return i=>{switch(i.code){case"invalid_type":return`F\xe2sit giren: umulan ${i.expected}, alınan ${(e=>{let t=typeof e;switch(t){case"number":return Number.isNaN(e)?"NaN":"numara";case"object":if(Array.isArray(e))return"saf";if(null===e)return"gayb";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}return t})(i.input)}`;case"invalid_value":if(1===i.values.length)return`F\xe2sit giren: umulan ${M(i.values[0])}`;return`F\xe2sit tercih: m\xfbteberler ${p(i.values,"|")}`;case"too_big":{let t=i.inclusive?"<=":"<",r=e[i.origin]??null;if(r)return`Fazla b\xfcy\xfck: ${i.origin??"value"}, ${t}${i.maximum.toString()} ${r.unit??"elements"} sahip olmalıydı.`;return`Fazla b\xfcy\xfck: ${i.origin??"value"}, ${t}${i.maximum.toString()} olmalıydı.`}case"too_small":{let t=i.inclusive?">=":">",r=e[i.origin]??null;if(r)return`Fazla k\xfc\xe7\xfck: ${i.origin}, ${t}${i.minimum.toString()} ${r.unit} sahip olmalıydı.`;return`Fazla k\xfc\xe7\xfck: ${i.origin}, ${t}${i.minimum.toString()} olmalıydı.`}case"invalid_format":if("starts_with"===i.format)return`F\xe2sit metin: "${i.prefix}" ile başlamalı.`;if("ends_with"===i.format)return`F\xe2sit metin: "${i.suffix}" ile bitmeli.`;if("includes"===i.format)return`F\xe2sit metin: "${i.includes}" ihtiv\xe2 etmeli.`;if("regex"===i.format)return`F\xe2sit metin: ${i.pattern} nakşına uymalı.`;return`F\xe2sit ${t[i.format]??i.format}`;case"not_multiple_of":return`F\xe2sit sayı: ${i.divisor} katı olmalıydı.`;case"unrecognized_keys":return`Tanınmayan anahtar ${i.keys.length>1?"s":""}: ${p(i.keys,", ")}`;case"invalid_key":return`${i.origin} i\xe7in tanınmayan anahtar var.`;case"invalid_union":return"Giren tanınamadı.";case"invalid_element":return`${i.origin} i\xe7in tanınmayan kıymet var.`;default:return`Kıymet tanınamadı.`}}})()}}function nt(){return{localeError:(()=>{let e={string:{unit:"توکي",verb:"ولري"},file:{unit:"بایټس",verb:"ولري"},array:{unit:"توکي",verb:"ولري"},set:{unit:"توکي",verb:"ولري"}},t={regex:"ورودي",email:"بریښنالیک",url:"یو آر ال",emoji:"ایموجي",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"نیټه او وخت",date:"نېټه",time:"وخت",duration:"موده",ipv4:"د IPv4 پته",ipv6:"د IPv6 پته",cidrv4:"د IPv4 ساحه",cidrv6:"د IPv6 ساحه",base64:"base64-encoded متن",base64url:"base64url-encoded متن",json_string:"JSON متن",e164:"د E.164 شمېره",jwt:"JWT",template_literal:"ورودي"};return i=>{switch(i.code){case"invalid_type":return`ناسم ورودي: باید ${i.expected} وای, مګر ${(e=>{let t=typeof e;switch(t){case"number":return Number.isNaN(e)?"NaN":"عدد";case"object":if(Array.isArray(e))return"ارې";if(null===e)return"null";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}return t})(i.input)} ترلاسه شو`;case"invalid_value":if(1===i.values.length)return`ناسم ورودي: باید ${M(i.values[0])} وای`;return`ناسم انتخاب: باید یو له ${p(i.values,"|")} څخه وای`;case"too_big":{let t=i.inclusive?"<=":"<",r=e[i.origin]??null;if(r)return`ډیر لوی: ${i.origin??"ارزښت"} باید ${t}${i.maximum.toString()} ${r.unit??"عنصرونه"} ولري`;return`ډیر لوی: ${i.origin??"ارزښت"} باید ${t}${i.maximum.toString()} وي`}case"too_small":{let t=i.inclusive?">=":">",r=e[i.origin]??null;if(r)return`ډیر کوچنی: ${i.origin} باید ${t}${i.minimum.toString()} ${r.unit} ولري`;return`ډیر کوچنی: ${i.origin} باید ${t}${i.minimum.toString()} وي`}case"invalid_format":if("starts_with"===i.format)return`ناسم متن: باید د "${i.prefix}" سره پیل شي`;if("ends_with"===i.format)return`ناسم متن: باید د "${i.suffix}" سره پای ته ورسيږي`;if("includes"===i.format)return`ناسم متن: باید "${i.includes}" ولري`;if("regex"===i.format)return`ناسم متن: باید د ${i.pattern} سره مطابقت ولري`;return`${t[i.format]??i.format} ناسم دی`;case"not_multiple_of":return`ناسم عدد: باید د ${i.divisor} مضرب وي`;case"unrecognized_keys":return`ناسم ${i.keys.length>1?"کلیډونه":"کلیډ"}: ${p(i.keys,", ")}`;case"invalid_key":return`ناسم کلیډ په ${i.origin} کې`;case"invalid_union":default:return`ناسمه ورودي`;case"invalid_element":return`ناسم عنصر په ${i.origin} کې`}}})()}}function ni(){return{localeError:(()=>{let e={string:{unit:"znaków",verb:"mieć"},file:{unit:"bajtów",verb:"mieć"},array:{unit:"elementów",verb:"mieć"},set:{unit:"elementów",verb:"mieć"}},t={regex:"wyrażenie",email:"adres email",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"data i godzina w formacie ISO",date:"data w formacie ISO",time:"godzina w formacie ISO",duration:"czas trwania ISO",ipv4:"adres IPv4",ipv6:"adres IPv6",cidrv4:"zakres IPv4",cidrv6:"zakres IPv6",base64:"ciąg znaków zakodowany w formacie base64",base64url:"ciąg znaków zakodowany w formacie base64url",json_string:"ciąg znaków w formacie JSON",e164:"liczba E.164",jwt:"JWT",template_literal:"wejście"};return i=>{switch(i.code){case"invalid_type":return`Nieprawidłowe dane wejściowe: oczekiwano ${i.expected}, otrzymano ${(e=>{let t=typeof e;switch(t){case"number":return Number.isNaN(e)?"NaN":"liczba";case"object":if(Array.isArray(e))return"tablica";if(null===e)return"null";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}return t})(i.input)}`;case"invalid_value":if(1===i.values.length)return`Nieprawidłowe dane wejściowe: oczekiwano ${M(i.values[0])}`;return`Nieprawidłowa opcja: oczekiwano jednej z wartości ${p(i.values,"|")}`;case"too_big":{let t=i.inclusive?"<=":"<",r=e[i.origin]??null;if(r)return`Za duża wartość: oczekiwano, że ${i.origin??"wartość"} będzie mieć ${t}${i.maximum.toString()} ${r.unit??"elementów"}`;return`Zbyt duż(y/a/e): oczekiwano, że ${i.origin??"wartość"} będzie wynosić ${t}${i.maximum.toString()}`}case"too_small":{let t=i.inclusive?">=":">",r=e[i.origin]??null;if(r)return`Za mała wartość: oczekiwano, że ${i.origin??"wartość"} będzie mieć ${t}${i.minimum.toString()} ${r.unit??"elementów"}`;return`Zbyt mał(y/a/e): oczekiwano, że ${i.origin??"wartość"} będzie wynosić ${t}${i.minimum.toString()}`}case"invalid_format":if("starts_with"===i.format)return`Nieprawidłowy ciąg znak\xf3w: musi zaczynać się od "${i.prefix}"`;if("ends_with"===i.format)return`Nieprawidłowy ciąg znak\xf3w: musi kończyć się na "${i.suffix}"`;if("includes"===i.format)return`Nieprawidłowy ciąg znak\xf3w: musi zawierać "${i.includes}"`;if("regex"===i.format)return`Nieprawidłowy ciąg znak\xf3w: musi odpowiadać wzorcowi ${i.pattern}`;return`Nieprawidłow(y/a/e) ${t[i.format]??i.format}`;case"not_multiple_of":return`Nieprawidłowa liczba: musi być wielokrotnością ${i.divisor}`;case"unrecognized_keys":return`Nierozpoznane klucze${i.keys.length>1?"s":""}: ${p(i.keys,", ")}`;case"invalid_key":return`Nieprawidłowy klucz w ${i.origin}`;case"invalid_union":return"Nieprawidłowe dane wejściowe";case"invalid_element":return`Nieprawidłowa wartość w ${i.origin}`;default:return`Nieprawidłowe dane wejściowe`}}})()}}function nr(){return{localeError:(()=>{let e={string:{unit:"caracteres",verb:"ter"},file:{unit:"bytes",verb:"ter"},array:{unit:"itens",verb:"ter"},set:{unit:"itens",verb:"ter"}},t={regex:"padrão",email:"endereço de e-mail",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"data e hora ISO",date:"data ISO",time:"hora ISO",duration:"duração ISO",ipv4:"endereço IPv4",ipv6:"endereço IPv6",cidrv4:"faixa de IPv4",cidrv6:"faixa de IPv6",base64:"texto codificado em base64",base64url:"URL codificada em base64",json_string:"texto JSON",e164:"número E.164",jwt:"JWT",template_literal:"entrada"};return i=>{switch(i.code){case"invalid_type":return`Tipo inv\xe1lido: esperado ${i.expected}, recebido ${(e=>{let t=typeof e;switch(t){case"number":return Number.isNaN(e)?"NaN":"número";case"object":if(Array.isArray(e))return"array";if(null===e)return"nulo";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}return t})(i.input)}`;case"invalid_value":if(1===i.values.length)return`Entrada inv\xe1lida: esperado ${M(i.values[0])}`;return`Op\xe7\xe3o inv\xe1lida: esperada uma das ${p(i.values,"|")}`;case"too_big":{let t=i.inclusive?"<=":"<",r=e[i.origin]??null;if(r)return`Muito grande: esperado que ${i.origin??"valor"} tivesse ${t}${i.maximum.toString()} ${r.unit??"elementos"}`;return`Muito grande: esperado que ${i.origin??"valor"} fosse ${t}${i.maximum.toString()}`}case"too_small":{let t=i.inclusive?">=":">",r=e[i.origin]??null;if(r)return`Muito pequeno: esperado que ${i.origin} tivesse ${t}${i.minimum.toString()} ${r.unit}`;return`Muito pequeno: esperado que ${i.origin} fosse ${t}${i.minimum.toString()}`}case"invalid_format":if("starts_with"===i.format)return`Texto inv\xe1lido: deve come\xe7ar com "${i.prefix}"`;if("ends_with"===i.format)return`Texto inv\xe1lido: deve terminar com "${i.suffix}"`;if("includes"===i.format)return`Texto inv\xe1lido: deve incluir "${i.includes}"`;if("regex"===i.format)return`Texto inv\xe1lido: deve corresponder ao padr\xe3o ${i.pattern}`;return`${t[i.format]??i.format} inv\xe1lido`;case"not_multiple_of":return`N\xfamero inv\xe1lido: deve ser m\xfaltiplo de ${i.divisor}`;case"unrecognized_keys":return`Chave${i.keys.length>1?"s":""} desconhecida${i.keys.length>1?"s":""}: ${p(i.keys,", ")}`;case"invalid_key":return`Chave inv\xe1lida em ${i.origin}`;case"invalid_union":return"Entrada inválida";case"invalid_element":return`Valor inv\xe1lido em ${i.origin}`;default:return`Campo inv\xe1lido`}}})()}}function nn(e,t,i,r){let n=Math.abs(e),a=n%10,o=n%100;return o>=11&&o<=19?r:1===a?t:a>=2&&a<=4?i:r}function na(){return{localeError:(()=>{let e={string:{unit:{one:"символ",few:"символа",many:"символов"},verb:"иметь"},file:{unit:{one:"байт",few:"байта",many:"байт"},verb:"иметь"},array:{unit:{one:"элемент",few:"элемента",many:"элементов"},verb:"иметь"},set:{unit:{one:"элемент",few:"элемента",many:"элементов"},verb:"иметь"}},t={regex:"ввод",email:"email адрес",url:"URL",emoji:"эмодзи",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO дата и время",date:"ISO дата",time:"ISO время",duration:"ISO длительность",ipv4:"IPv4 адрес",ipv6:"IPv6 адрес",cidrv4:"IPv4 диапазон",cidrv6:"IPv6 диапазон",base64:"строка в формате base64",base64url:"строка в формате base64url",json_string:"JSON строка",e164:"номер E.164",jwt:"JWT",template_literal:"ввод"};return i=>{switch(i.code){case"invalid_type":return`Неверный ввод: ожидалось ${i.expected}, получено ${(e=>{let t=typeof e;switch(t){case"number":return Number.isNaN(e)?"NaN":"число";case"object":if(Array.isArray(e))return"массив";if(null===e)return"null";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}return t})(i.input)}`;case"invalid_value":if(1===i.values.length)return`Неверный ввод: ожидалось ${M(i.values[0])}`;return`Неверный вариант: ожидалось одно из ${p(i.values,"|")}`;case"too_big":{let t=i.inclusive?"<=":"<",r=e[i.origin]??null;if(r){let e=nn(Number(i.maximum),r.unit.one,r.unit.few,r.unit.many);return`Слишком большое значение: ожидалось, что ${i.origin??"значение"} будет иметь ${t}${i.maximum.toString()} ${e}`}return`Слишком большое значение: ожидалось, что ${i.origin??"значение"} будет ${t}${i.maximum.toString()}`}case"too_small":{let t=i.inclusive?">=":">",r=e[i.origin]??null;if(r){let e=nn(Number(i.minimum),r.unit.one,r.unit.few,r.unit.many);return`Слишком маленькое значение: ожидалось, что ${i.origin} будет иметь ${t}${i.minimum.toString()} ${e}`}return`Слишком маленькое значение: ожидалось, что ${i.origin} будет ${t}${i.minimum.toString()}`}case"invalid_format":if("starts_with"===i.format)return`Неверная строка: должна начинаться с "${i.prefix}"`;if("ends_with"===i.format)return`Неверная строка: должна заканчиваться на "${i.suffix}"`;if("includes"===i.format)return`Неверная строка: должна содержать "${i.includes}"`;if("regex"===i.format)return`Неверная строка: должна соответствовать шаблону ${i.pattern}`;return`Неверный ${t[i.format]??i.format}`;case"not_multiple_of":return`Неверное число: должно быть кратным ${i.divisor}`;case"unrecognized_keys":return`Нераспознанн${i.keys.length>1?"ые":"ый"} ключ${i.keys.length>1?"и":""}: ${p(i.keys,", ")}`;case"invalid_key":return`Неверный ключ в ${i.origin}`;case"invalid_union":return"Неверные входные данные";case"invalid_element":return`Неверное значение в ${i.origin}`;default:return`Неверные входные данные`}}})()}}function no(){return{localeError:(()=>{let e={string:{unit:"znakov",verb:"imeti"},file:{unit:"bajtov",verb:"imeti"},array:{unit:"elementov",verb:"imeti"},set:{unit:"elementov",verb:"imeti"}},t={regex:"vnos",email:"e-poštni naslov",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO datum in čas",date:"ISO datum",time:"ISO čas",duration:"ISO trajanje",ipv4:"IPv4 naslov",ipv6:"IPv6 naslov",cidrv4:"obseg IPv4",cidrv6:"obseg IPv6",base64:"base64 kodiran niz",base64url:"base64url kodiran niz",json_string:"JSON niz",e164:"E.164 številka",jwt:"JWT",template_literal:"vnos"};return i=>{switch(i.code){case"invalid_type":return`Neveljaven vnos: pričakovano ${i.expected}, prejeto ${(e=>{let t=typeof e;switch(t){case"number":return Number.isNaN(e)?"NaN":"število";case"object":if(Array.isArray(e))return"tabela";if(null===e)return"null";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}return t})(i.input)}`;case"invalid_value":if(1===i.values.length)return`Neveljaven vnos: pričakovano ${M(i.values[0])}`;return`Neveljavna možnost: pričakovano eno izmed ${p(i.values,"|")}`;case"too_big":{let t=i.inclusive?"<=":"<",r=e[i.origin]??null;if(r)return`Preveliko: pričakovano, da bo ${i.origin??"vrednost"} imelo ${t}${i.maximum.toString()} ${r.unit??"elementov"}`;return`Preveliko: pričakovano, da bo ${i.origin??"vrednost"} ${t}${i.maximum.toString()}`}case"too_small":{let t=i.inclusive?">=":">",r=e[i.origin]??null;if(r)return`Premajhno: pričakovano, da bo ${i.origin} imelo ${t}${i.minimum.toString()} ${r.unit}`;return`Premajhno: pričakovano, da bo ${i.origin} ${t}${i.minimum.toString()}`}case"invalid_format":if("starts_with"===i.format)return`Neveljaven niz: mora se začeti z "${i.prefix}"`;if("ends_with"===i.format)return`Neveljaven niz: mora se končati z "${i.suffix}"`;if("includes"===i.format)return`Neveljaven niz: mora vsebovati "${i.includes}"`;if("regex"===i.format)return`Neveljaven niz: mora ustrezati vzorcu ${i.pattern}`;return`Neveljaven ${t[i.format]??i.format}`;case"not_multiple_of":return`Neveljavno število: mora biti večkratnik ${i.divisor}`;case"unrecognized_keys":return`Neprepoznan${i.keys.length>1?"i ključi":" ključ"}: ${p(i.keys,", ")}`;case"invalid_key":return`Neveljaven ključ v ${i.origin}`;case"invalid_union":default:return"Neveljaven vnos";case"invalid_element":return`Neveljavna vrednost v ${i.origin}`}}})()}}function nu(){return{localeError:(()=>{let e={string:{unit:"tecken",verb:"att ha"},file:{unit:"bytes",verb:"att ha"},array:{unit:"objekt",verb:"att innehålla"},set:{unit:"objekt",verb:"att innehålla"}},t={regex:"reguljärt uttryck",email:"e-postadress",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO-datum och tid",date:"ISO-datum",time:"ISO-tid",duration:"ISO-varaktighet",ipv4:"IPv4-intervall",ipv6:"IPv6-intervall",cidrv4:"IPv4-spektrum",cidrv6:"IPv6-spektrum",base64:"base64-kodad sträng",base64url:"base64url-kodad sträng",json_string:"JSON-sträng",e164:"E.164-nummer",jwt:"JWT",template_literal:"mall-literal"};return i=>{switch(i.code){case"invalid_type":return`Ogiltig inmatning: f\xf6rv\xe4ntat ${i.expected}, fick ${(e=>{let t=typeof e;switch(t){case"number":return Number.isNaN(e)?"NaN":"antal";case"object":if(Array.isArray(e))return"lista";if(null===e)return"null";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}return t})(i.input)}`;case"invalid_value":if(1===i.values.length)return`Ogiltig inmatning: f\xf6rv\xe4ntat ${M(i.values[0])}`;return`Ogiltigt val: f\xf6rv\xe4ntade en av ${p(i.values,"|")}`;case"too_big":{let t=i.inclusive?"<=":"<",r=e[i.origin]??null;if(r)return`F\xf6r stor(t): f\xf6rv\xe4ntade ${i.origin??"värdet"} att ha ${t}${i.maximum.toString()} ${r.unit??"element"}`;return`F\xf6r stor(t): f\xf6rv\xe4ntat ${i.origin??"värdet"} att ha ${t}${i.maximum.toString()}`}case"too_small":{let t=i.inclusive?">=":">",r=e[i.origin]??null;if(r)return`F\xf6r lite(t): f\xf6rv\xe4ntade ${i.origin??"värdet"} att ha ${t}${i.minimum.toString()} ${r.unit}`;return`F\xf6r lite(t): f\xf6rv\xe4ntade ${i.origin??"värdet"} att ha ${t}${i.minimum.toString()}`}case"invalid_format":if("starts_with"===i.format)return`Ogiltig str\xe4ng: m\xe5ste b\xf6rja med "${i.prefix}"`;if("ends_with"===i.format)return`Ogiltig str\xe4ng: m\xe5ste sluta med "${i.suffix}"`;if("includes"===i.format)return`Ogiltig str\xe4ng: m\xe5ste inneh\xe5lla "${i.includes}"`;if("regex"===i.format)return`Ogiltig str\xe4ng: m\xe5ste matcha m\xf6nstret "${i.pattern}"`;return`Ogiltig(t) ${t[i.format]??i.format}`;case"not_multiple_of":return`Ogiltigt tal: m\xe5ste vara en multipel av ${i.divisor}`;case"unrecognized_keys":return`${i.keys.length>1?"Okända nycklar":"Okänd nyckel"}: ${p(i.keys,", ")}`;case"invalid_key":return`Ogiltig nyckel i ${i.origin??"värdet"}`;case"invalid_union":default:return"Ogiltig input";case"invalid_element":return`Ogiltigt v\xe4rde i ${i.origin??"värdet"}`}}})()}}function ns(){return{localeError:(()=>{let e={string:{unit:"எழுத்துக்கள்",verb:"கொண்டிருக்க வேண்டும்"},file:{unit:"பைட்டுகள்",verb:"கொண்டிருக்க வேண்டும்"},array:{unit:"உறுப்புகள்",verb:"கொண்டிருக்க வேண்டும்"},set:{unit:"உறுப்புகள்",verb:"கொண்டிருக்க வேண்டும்"}},t={regex:"உள்ளீடு",email:"மின்னஞ்சல் முகவரி",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO தேதி நேரம்",date:"ISO தேதி",time:"ISO நேரம்",duration:"ISO கால அளவு",ipv4:"IPv4 முகவரி",ipv6:"IPv6 முகவரி",cidrv4:"IPv4 வரம்பு",cidrv6:"IPv6 வரம்பு",base64:"base64-encoded சரம்",base64url:"base64url-encoded சரம்",json_string:"JSON சரம்",e164:"E.164 எண்",jwt:"JWT",template_literal:"input"};return i=>{switch(i.code){case"invalid_type":return`தவறான உள்ளீடு: எதிர்பார்க்கப்பட்டது ${i.expected}, பெறப்பட்டது ${(e=>{let t=typeof e;switch(t){case"number":return Number.isNaN(e)?"எண் அல்லாதது":"எண்";case"object":if(Array.isArray(e))return"அணி";if(null===e)return"வெறுமை";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}return t})(i.input)}`;case"invalid_value":if(1===i.values.length)return`தவறான உள்ளீடு: எதிர்பார்க்கப்பட்டது ${M(i.values[0])}`;return`தவறான விருப்பம்: எதிர்பார்க்கப்பட்டது ${p(i.values,"|")} இல் ஒன்று`;case"too_big":{let t=i.inclusive?"<=":"<",r=e[i.origin]??null;if(r)return`மிக பெரியது: எதிர்பார்க்கப்பட்டது ${i.origin??"மதிப்பு"} ${t}${i.maximum.toString()} ${r.unit??"உறுப்புகள்"} ஆக இருக்க வேண்டும்`;return`மிக பெரியது: எதிர்பார்க்கப்பட்டது ${i.origin??"மதிப்பு"} ${t}${i.maximum.toString()} ஆக இருக்க வேண்டும்`}case"too_small":{let t=i.inclusive?">=":">",r=e[i.origin]??null;if(r)return`மிகச் சிறியது: எதிர்பார்க்கப்பட்டது ${i.origin} ${t}${i.minimum.toString()} ${r.unit} ஆக இருக்க வேண்டும்`;return`மிகச் சிறியது: எதிர்பார்க்கப்பட்டது ${i.origin} ${t}${i.minimum.toString()} ஆக இருக்க வேண்டும்`}case"invalid_format":if("starts_with"===i.format)return`தவறான சரம்: "${i.prefix}" இல் தொடங்க வேண்டும்`;if("ends_with"===i.format)return`தவறான சரம்: "${i.suffix}" இல் முடிவடைய வேண்டும்`;if("includes"===i.format)return`தவறான சரம்: "${i.includes}" ஐ உள்ளடக்க வேண்டும்`;if("regex"===i.format)return`தவறான சரம்: ${i.pattern} முறைபாட்டுடன் பொருந்த வேண்டும்`;return`தவறான ${t[i.format]??i.format}`;case"not_multiple_of":return`தவறான எண்: ${i.divisor} இன் பலமாக இருக்க வேண்டும்`;case"unrecognized_keys":return`அடையாளம் தெரியாத விசை${i.keys.length>1?"கள்":""}: ${p(i.keys,", ")}`;case"invalid_key":return`${i.origin} இல் தவறான விசை`;case"invalid_union":return"தவறான உள்ளீடு";case"invalid_element":return`${i.origin} இல் தவறான மதிப்பு`;default:return`தவறான உள்ளீடு`}}})()}}function nl(){return{localeError:(()=>{let e={string:{unit:"ตัวอักษร",verb:"ควรมี"},file:{unit:"ไบต์",verb:"ควรมี"},array:{unit:"รายการ",verb:"ควรมี"},set:{unit:"รายการ",verb:"ควรมี"}},t={regex:"ข้อมูลที่ป้อน",email:"ที่อยู่อีเมล",url:"URL",emoji:"อิโมจิ",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"วันที่เวลาแบบ ISO",date:"วันที่แบบ ISO",time:"เวลาแบบ ISO",duration:"ช่วงเวลาแบบ ISO",ipv4:"ที่อยู่ IPv4",ipv6:"ที่อยู่ IPv6",cidrv4:"ช่วง IP แบบ IPv4",cidrv6:"ช่วง IP แบบ IPv6",base64:"ข้อความแบบ Base64",base64url:"ข้อความแบบ Base64 สำหรับ URL",json_string:"ข้อความแบบ JSON",e164:"เบอร์โทรศัพท์ระหว่างประเทศ (E.164)",jwt:"โทเคน JWT",template_literal:"ข้อมูลที่ป้อน"};return i=>{switch(i.code){case"invalid_type":return`ประเภทข้อมูลไม่ถูกต้อง: ควรเป็น ${i.expected} แต่ได้รับ ${(e=>{let t=typeof e;switch(t){case"number":return Number.isNaN(e)?"ไม่ใช่ตัวเลข (NaN)":"ตัวเลข";case"object":if(Array.isArray(e))return"อาร์เรย์ (Array)";if(null===e)return"ไม่มีค่า (null)";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}return t})(i.input)}`;case"invalid_value":if(1===i.values.length)return`ค่าไม่ถูกต้อง: ควรเป็น ${M(i.values[0])}`;return`ตัวเลือกไม่ถูกต้อง: ควรเป็นหนึ่งใน ${p(i.values,"|")}`;case"too_big":{let t=i.inclusive?"ไม่เกิน":"น้อยกว่า",r=e[i.origin]??null;if(r)return`เกินกำหนด: ${i.origin??"ค่า"} ควรมี${t} ${i.maximum.toString()} ${r.unit??"รายการ"}`;return`เกินกำหนด: ${i.origin??"ค่า"} ควรมี${t} ${i.maximum.toString()}`}case"too_small":{let t=i.inclusive?"อย่างน้อย":"มากกว่า",r=e[i.origin]??null;if(r)return`น้อยกว่ากำหนด: ${i.origin} ควรมี${t} ${i.minimum.toString()} ${r.unit}`;return`น้อยกว่ากำหนด: ${i.origin} ควรมี${t} ${i.minimum.toString()}`}case"invalid_format":if("starts_with"===i.format)return`รูปแบบไม่ถูกต้อง: ข้อความต้องขึ้นต้นด้วย "${i.prefix}"`;if("ends_with"===i.format)return`รูปแบบไม่ถูกต้อง: ข้อความต้องลงท้ายด้วย "${i.suffix}"`;if("includes"===i.format)return`รูปแบบไม่ถูกต้อง: ข้อความต้องมี "${i.includes}" อยู่ในข้อความ`;if("regex"===i.format)return`รูปแบบไม่ถูกต้อง: ต้องตรงกับรูปแบบที่กำหนด ${i.pattern}`;return`รูปแบบไม่ถูกต้อง: ${t[i.format]??i.format}`;case"not_multiple_of":return`ตัวเลขไม่ถูกต้อง: ต้องเป็นจำนวนที่หารด้วย ${i.divisor} ได้ลงตัว`;case"unrecognized_keys":return`พบคีย์ที่ไม่รู้จัก: ${p(i.keys,", ")}`;case"invalid_key":return`คีย์ไม่ถูกต้องใน ${i.origin}`;case"invalid_union":return"ข้อมูลไม่ถูกต้อง: ไม่ตรงกับรูปแบบยูเนียนที่กำหนดไว้";case"invalid_element":return`ข้อมูลไม่ถูกต้องใน ${i.origin}`;default:return`ข้อมูลไม่ถูกต้อง`}}})()}}function nd(){return{localeError:(()=>{let e={string:{unit:"karakter",verb:"olmalı"},file:{unit:"bayt",verb:"olmalı"},array:{unit:"öğe",verb:"olmalı"},set:{unit:"öğe",verb:"olmalı"}},t={regex:"girdi",email:"e-posta adresi",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO tarih ve saat",date:"ISO tarih",time:"ISO saat",duration:"ISO süre",ipv4:"IPv4 adresi",ipv6:"IPv6 adresi",cidrv4:"IPv4 aralığı",cidrv6:"IPv6 aralığı",base64:"base64 ile şifrelenmiş metin",base64url:"base64url ile şifrelenmiş metin",json_string:"JSON dizesi",e164:"E.164 sayısı",jwt:"JWT",template_literal:"Şablon dizesi"};return i=>{switch(i.code){case"invalid_type":return`Ge\xe7ersiz değer: beklenen ${i.expected}, alınan ${(e=>{let t=typeof e;switch(t){case"number":return Number.isNaN(e)?"NaN":"number";case"object":if(Array.isArray(e))return"array";if(null===e)return"null";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}return t})(i.input)}`;case"invalid_value":if(1===i.values.length)return`Ge\xe7ersiz değer: beklenen ${M(i.values[0])}`;return`Ge\xe7ersiz se\xe7enek: aşağıdakilerden biri olmalı: ${p(i.values,"|")}`;case"too_big":{let t=i.inclusive?"<=":"<",r=e[i.origin]??null;if(r)return`\xc7ok b\xfcy\xfck: beklenen ${i.origin??"değer"} ${t}${i.maximum.toString()} ${r.unit??"öğe"}`;return`\xc7ok b\xfcy\xfck: beklenen ${i.origin??"değer"} ${t}${i.maximum.toString()}`}case"too_small":{let t=i.inclusive?">=":">",r=e[i.origin]??null;if(r)return`\xc7ok k\xfc\xe7\xfck: beklenen ${i.origin} ${t}${i.minimum.toString()} ${r.unit}`;return`\xc7ok k\xfc\xe7\xfck: beklenen ${i.origin} ${t}${i.minimum.toString()}`}case"invalid_format":if("starts_with"===i.format)return`Ge\xe7ersiz metin: "${i.prefix}" ile başlamalı`;if("ends_with"===i.format)return`Ge\xe7ersiz metin: "${i.suffix}" ile bitmeli`;if("includes"===i.format)return`Ge\xe7ersiz metin: "${i.includes}" i\xe7ermeli`;if("regex"===i.format)return`Ge\xe7ersiz metin: ${i.pattern} desenine uymalı`;return`Ge\xe7ersiz ${t[i.format]??i.format}`;case"not_multiple_of":return`Ge\xe7ersiz sayı: ${i.divisor} ile tam b\xf6l\xfcnebilmeli`;case"unrecognized_keys":return`Tanınmayan anahtar${i.keys.length>1?"lar":""}: ${p(i.keys,", ")}`;case"invalid_key":return`${i.origin} i\xe7inde ge\xe7ersiz anahtar`;case"invalid_union":return"Geçersiz değer";case"invalid_element":return`${i.origin} i\xe7inde ge\xe7ersiz değer`;default:return`Ge\xe7ersiz değer`}}})()}}function nc(){return{localeError:(()=>{let e={string:{unit:"символів",verb:"матиме"},file:{unit:"байтів",verb:"матиме"},array:{unit:"елементів",verb:"матиме"},set:{unit:"елементів",verb:"матиме"}},t={regex:"вхідні дані",email:"адреса електронної пошти",url:"URL",emoji:"емодзі",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"дата та час ISO",date:"дата ISO",time:"час ISO",duration:"тривалість ISO",ipv4:"адреса IPv4",ipv6:"адреса IPv6",cidrv4:"діапазон IPv4",cidrv6:"діапазон IPv6",base64:"рядок у кодуванні base64",base64url:"рядок у кодуванні base64url",json_string:"рядок JSON",e164:"номер E.164",jwt:"JWT",template_literal:"вхідні дані"};return i=>{switch(i.code){case"invalid_type":return`Неправильні вхідні дані: очікується ${i.expected}, отримано ${(e=>{let t=typeof e;switch(t){case"number":return Number.isNaN(e)?"NaN":"число";case"object":if(Array.isArray(e))return"масив";if(null===e)return"null";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}return t})(i.input)}`;case"invalid_value":if(1===i.values.length)return`Неправильні вхідні дані: очікується ${M(i.values[0])}`;return`Неправильна опція: очікується одне з ${p(i.values,"|")}`;case"too_big":{let t=i.inclusive?"<=":"<",r=e[i.origin]??null;if(r)return`Занадто велике: очікується, що ${i.origin??"значення"} ${r.verb} ${t}${i.maximum.toString()} ${r.unit??"елементів"}`;return`Занадто велике: очікується, що ${i.origin??"значення"} буде ${t}${i.maximum.toString()}`}case"too_small":{let t=i.inclusive?">=":">",r=e[i.origin]??null;if(r)return`Занадто мале: очікується, що ${i.origin} ${r.verb} ${t}${i.minimum.toString()} ${r.unit}`;return`Занадто мале: очікується, що ${i.origin} буде ${t}${i.minimum.toString()}`}case"invalid_format":if("starts_with"===i.format)return`Неправильний рядок: повинен починатися з "${i.prefix}"`;if("ends_with"===i.format)return`Неправильний рядок: повинен закінчуватися на "${i.suffix}"`;if("includes"===i.format)return`Неправильний рядок: повинен містити "${i.includes}"`;if("regex"===i.format)return`Неправильний рядок: повинен відповідати шаблону ${i.pattern}`;return`Неправильний ${t[i.format]??i.format}`;case"not_multiple_of":return`Неправильне число: повинно бути кратним ${i.divisor}`;case"unrecognized_keys":return`Нерозпізнаний ключ${i.keys.length>1?"і":""}: ${p(i.keys,", ")}`;case"invalid_key":return`Неправильний ключ у ${i.origin}`;case"invalid_union":return"Неправильні вхідні дані";case"invalid_element":return`Неправильне значення у ${i.origin}`;default:return`Неправильні вхідні дані`}}})()}}function nm(){return{localeError:(()=>{let e={string:{unit:"حروف",verb:"ہونا"},file:{unit:"بائٹس",verb:"ہونا"},array:{unit:"آئٹمز",verb:"ہونا"},set:{unit:"آئٹمز",verb:"ہونا"}},t={regex:"ان پٹ",email:"ای میل ایڈریس",url:"یو آر ایل",emoji:"ایموجی",uuid:"یو یو آئی ڈی",uuidv4:"یو یو آئی ڈی وی 4",uuidv6:"یو یو آئی ڈی وی 6",nanoid:"نینو آئی ڈی",guid:"جی یو آئی ڈی",cuid:"سی یو آئی ڈی",cuid2:"سی یو آئی ڈی 2",ulid:"یو ایل آئی ڈی",xid:"ایکس آئی ڈی",ksuid:"کے ایس یو آئی ڈی",datetime:"آئی ایس او ڈیٹ ٹائم",date:"آئی ایس او تاریخ",time:"آئی ایس او وقت",duration:"آئی ایس او مدت",ipv4:"آئی پی وی 4 ایڈریس",ipv6:"آئی پی وی 6 ایڈریس",cidrv4:"آئی پی وی 4 رینج",cidrv6:"آئی پی وی 6 رینج",base64:"بیس 64 ان کوڈڈ سٹرنگ",base64url:"بیس 64 یو آر ایل ان کوڈڈ سٹرنگ",json_string:"جے ایس او این سٹرنگ",e164:"ای 164 نمبر",jwt:"جے ڈبلیو ٹی",template_literal:"ان پٹ"};return i=>{switch(i.code){case"invalid_type":return`غلط ان پٹ: ${i.expected} متوقع تھا، ${(e=>{let t=typeof e;switch(t){case"number":return Number.isNaN(e)?"NaN":"نمبر";case"object":if(Array.isArray(e))return"آرے";if(null===e)return"نل";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}return t})(i.input)} موصول ہوا`;case"invalid_value":if(1===i.values.length)return`غلط ان پٹ: ${M(i.values[0])} متوقع تھا`;return`غلط آپشن: ${p(i.values,"|")} میں سے ایک متوقع تھا`;case"too_big":{let t=i.inclusive?"<=":"<",r=e[i.origin]??null;if(r)return`بہت بڑا: ${i.origin??"ویلیو"} کے ${t}${i.maximum.toString()} ${r.unit??"عناصر"} ہونے متوقع تھے`;return`بہت بڑا: ${i.origin??"ویلیو"} کا ${t}${i.maximum.toString()} ہونا متوقع تھا`}case"too_small":{let t=i.inclusive?">=":">",r=e[i.origin]??null;if(r)return`بہت چھوٹا: ${i.origin} کے ${t}${i.minimum.toString()} ${r.unit} ہونے متوقع تھے`;return`بہت چھوٹا: ${i.origin} کا ${t}${i.minimum.toString()} ہونا متوقع تھا`}case"invalid_format":if("starts_with"===i.format)return`غلط سٹرنگ: "${i.prefix}" سے شروع ہونا چاہیے`;if("ends_with"===i.format)return`غلط سٹرنگ: "${i.suffix}" پر ختم ہونا چاہیے`;if("includes"===i.format)return`غلط سٹرنگ: "${i.includes}" شامل ہونا چاہیے`;if("regex"===i.format)return`غلط سٹرنگ: پیٹرن ${i.pattern} سے میچ ہونا چاہیے`;return`غلط ${t[i.format]??i.format}`;case"not_multiple_of":return`غلط نمبر: ${i.divisor} کا مضاعف ہونا چاہیے`;case"unrecognized_keys":return`غیر تسلیم شدہ کی${i.keys.length>1?"ز":""}: ${p(i.keys,"، ")}`;case"invalid_key":return`${i.origin} میں غلط کی`;case"invalid_union":return"غلط ان پٹ";case"invalid_element":return`${i.origin} میں غلط ویلیو`;default:return`غلط ان پٹ`}}})()}}function nf(){return{localeError:(()=>{let e={string:{unit:"ký tự",verb:"có"},file:{unit:"byte",verb:"có"},array:{unit:"phần tử",verb:"có"},set:{unit:"phần tử",verb:"có"}},t={regex:"đầu vào",email:"địa chỉ email",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ngày giờ ISO",date:"ngày ISO",time:"giờ ISO",duration:"khoảng thời gian ISO",ipv4:"địa chỉ IPv4",ipv6:"địa chỉ IPv6",cidrv4:"dải IPv4",cidrv6:"dải IPv6",base64:"chuỗi mã hóa base64",base64url:"chuỗi mã hóa base64url",json_string:"chuỗi JSON",e164:"số E.164",jwt:"JWT",template_literal:"đầu vào"};return i=>{switch(i.code){case"invalid_type":return`Đầu v\xe0o kh\xf4ng hợp lệ: mong đợi ${i.expected}, nhận được ${(e=>{let t=typeof e;switch(t){case"number":return Number.isNaN(e)?"NaN":"số";case"object":if(Array.isArray(e))return"mảng";if(null===e)return"null";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}return t})(i.input)}`;case"invalid_value":if(1===i.values.length)return`Đầu v\xe0o kh\xf4ng hợp lệ: mong đợi ${M(i.values[0])}`;return`T\xf9y chọn kh\xf4ng hợp lệ: mong đợi một trong c\xe1c gi\xe1 trị ${p(i.values,"|")}`;case"too_big":{let t=i.inclusive?"<=":"<",r=e[i.origin]??null;if(r)return`Qu\xe1 lớn: mong đợi ${i.origin??"giá trị"} ${r.verb} ${t}${i.maximum.toString()} ${r.unit??"phần tử"}`;return`Qu\xe1 lớn: mong đợi ${i.origin??"giá trị"} ${t}${i.maximum.toString()}`}case"too_small":{let t=i.inclusive?">=":">",r=e[i.origin]??null;if(r)return`Qu\xe1 nhỏ: mong đợi ${i.origin} ${r.verb} ${t}${i.minimum.toString()} ${r.unit}`;return`Qu\xe1 nhỏ: mong đợi ${i.origin} ${t}${i.minimum.toString()}`}case"invalid_format":if("starts_with"===i.format)return`Chuỗi kh\xf4ng hợp lệ: phải bắt đầu bằng "${i.prefix}"`;if("ends_with"===i.format)return`Chuỗi kh\xf4ng hợp lệ: phải kết th\xfac bằng "${i.suffix}"`;if("includes"===i.format)return`Chuỗi kh\xf4ng hợp lệ: phải bao gồm "${i.includes}"`;if("regex"===i.format)return`Chuỗi kh\xf4ng hợp lệ: phải khớp với mẫu ${i.pattern}`;return`${t[i.format]??i.format} kh\xf4ng hợp lệ`;case"not_multiple_of":return`Số kh\xf4ng hợp lệ: phải l\xe0 bội số của ${i.divisor}`;case"unrecognized_keys":return`Kh\xf3a kh\xf4ng được nhận dạng: ${p(i.keys,", ")}`;case"invalid_key":return`Kh\xf3a kh\xf4ng hợp lệ trong ${i.origin}`;case"invalid_union":return"Đầu vào không hợp lệ";case"invalid_element":return`Gi\xe1 trị kh\xf4ng hợp lệ trong ${i.origin}`;default:return`Đầu v\xe0o kh\xf4ng hợp lệ`}}})()}}function np(){return{localeError:(()=>{let e={string:{unit:"字符",verb:"包含"},file:{unit:"字节",verb:"包含"},array:{unit:"项",verb:"包含"},set:{unit:"项",verb:"包含"}},t={regex:"输入",email:"电子邮件",url:"URL",emoji:"表情符号",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO日期时间",date:"ISO日期",time:"ISO时间",duration:"ISO时长",ipv4:"IPv4地址",ipv6:"IPv6地址",cidrv4:"IPv4网段",cidrv6:"IPv6网段",base64:"base64编码字符串",base64url:"base64url编码字符串",json_string:"JSON字符串",e164:"E.164号码",jwt:"JWT",template_literal:"输入"};return i=>{switch(i.code){case"invalid_type":return`无效输入：期望 ${i.expected}，实际接收 ${(e=>{let t=typeof e;switch(t){case"number":return Number.isNaN(e)?"非数字(NaN)":"数字";case"object":if(Array.isArray(e))return"数组";if(null===e)return"空值(null)";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}return t})(i.input)}`;case"invalid_value":if(1===i.values.length)return`无效输入：期望 ${M(i.values[0])}`;return`无效选项：期望以下之一 ${p(i.values,"|")}`;case"too_big":{let t=i.inclusive?"<=":"<",r=e[i.origin]??null;if(r)return`数值过大：期望 ${i.origin??"值"} ${t}${i.maximum.toString()} ${r.unit??"个元素"}`;return`数值过大：期望 ${i.origin??"值"} ${t}${i.maximum.toString()}`}case"too_small":{let t=i.inclusive?">=":">",r=e[i.origin]??null;if(r)return`数值过小：期望 ${i.origin} ${t}${i.minimum.toString()} ${r.unit}`;return`数值过小：期望 ${i.origin} ${t}${i.minimum.toString()}`}case"invalid_format":if("starts_with"===i.format)return`无效字符串：必须以 "${i.prefix}" 开头`;if("ends_with"===i.format)return`无效字符串：必须以 "${i.suffix}" 结尾`;if("includes"===i.format)return`无效字符串：必须包含 "${i.includes}"`;if("regex"===i.format)return`无效字符串：必须满足正则表达式 ${i.pattern}`;return`无效${t[i.format]??i.format}`;case"not_multiple_of":return`无效数字：必须是 ${i.divisor} 的倍数`;case"unrecognized_keys":return`出现未知的键(key): ${p(i.keys,", ")}`;case"invalid_key":return`${i.origin} 中的键(key)无效`;case"invalid_union":return"无效输入";case"invalid_element":return`${i.origin} 中包含无效值(value)`;default:return`无效输入`}}})()}}function nv(){return{localeError:(()=>{let e={string:{unit:"字元",verb:"擁有"},file:{unit:"位元組",verb:"擁有"},array:{unit:"項目",verb:"擁有"},set:{unit:"項目",verb:"擁有"}},t={regex:"輸入",email:"郵件地址",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO 日期時間",date:"ISO 日期",time:"ISO 時間",duration:"ISO 期間",ipv4:"IPv4 位址",ipv6:"IPv6 位址",cidrv4:"IPv4 範圍",cidrv6:"IPv6 範圍",base64:"base64 編碼字串",base64url:"base64url 編碼字串",json_string:"JSON 字串",e164:"E.164 數值",jwt:"JWT",template_literal:"輸入"};return i=>{switch(i.code){case"invalid_type":return`無效的輸入值：預期為 ${i.expected}，但收到 ${(e=>{let t=typeof e;switch(t){case"number":return Number.isNaN(e)?"NaN":"number";case"object":if(Array.isArray(e))return"array";if(null===e)return"null";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}return t})(i.input)}`;case"invalid_value":if(1===i.values.length)return`無效的輸入值：預期為 ${M(i.values[0])}`;return`無效的選項：預期為以下其中之一 ${p(i.values,"|")}`;case"too_big":{let t=i.inclusive?"<=":"<",r=e[i.origin]??null;if(r)return`數值過大：預期 ${i.origin??"值"} 應為 ${t}${i.maximum.toString()} ${r.unit??"個元素"}`;return`數值過大：預期 ${i.origin??"值"} 應為 ${t}${i.maximum.toString()}`}case"too_small":{let t=i.inclusive?">=":">",r=e[i.origin]??null;if(r)return`數值過小：預期 ${i.origin} 應為 ${t}${i.minimum.toString()} ${r.unit}`;return`數值過小：預期 ${i.origin} 應為 ${t}${i.minimum.toString()}`}case"invalid_format":if("starts_with"===i.format)return`無效的字串：必須以 "${i.prefix}" 開頭`;if("ends_with"===i.format)return`無效的字串：必須以 "${i.suffix}" 結尾`;if("includes"===i.format)return`無效的字串：必須包含 "${i.includes}"`;if("regex"===i.format)return`無效的字串：必須符合格式 ${i.pattern}`;return`無效的 ${t[i.format]??i.format}`;case"not_multiple_of":return`無效的數字：必須為 ${i.divisor} 的倍數`;case"unrecognized_keys":return`無法識別的鍵值${i.keys.length>1?"們":""}：${p(i.keys,"、")}`;case"invalid_key":return`${i.origin} 中有無效的鍵值`;case"invalid_union":return"無效的輸入值";case"invalid_element":return`${i.origin} 中有無效的值`;default:return`無效的輸入值`}}})()}}function ng(){return{localeError:(()=>{let e={string:{unit:"àmi",verb:"ní"},file:{unit:"bytes",verb:"ní"},array:{unit:"nkan",verb:"ní"},set:{unit:"nkan",verb:"ní"}},t={regex:"ẹ̀rọ ìbáwọlé",email:"àdírẹ́sì ìmẹ́lì",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"àkókò ISO",date:"ọjọ́ ISO",time:"àkókò ISO",duration:"àkókò tó pé ISO",ipv4:"àdírẹ́sì IPv4",ipv6:"àdírẹ́sì IPv6",cidrv4:"àgbègbè IPv4",cidrv6:"àgbègbè IPv6",base64:"ọ̀rọ̀ tí a kọ́ ní base64",base64url:"ọ̀rọ̀ base64url",json_string:"ọ̀rọ̀ JSON",e164:"nọ́mbà E.164",jwt:"JWT",template_literal:"ẹ̀rọ ìbáwọlé"};return i=>{switch(i.code){case"invalid_type":return`\xccb\xe1wọl\xe9 aṣ\xecṣe: a n\xed l\xe1ti fi ${i.expected}, \xe0mọ̀ a r\xed ${(e=>{let t=typeof e;switch(t){case"number":return Number.isNaN(e)?"NaN":"nọ́mbà";case"object":if(Array.isArray(e))return"akopọ";if(null===e)return"null";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}return t})(i.input)}`;case"invalid_value":if(1===i.values.length)return`\xccb\xe1wọl\xe9 aṣ\xecṣe: a n\xed l\xe1ti fi ${M(i.values[0])}`;return`\xc0ṣ\xe0y\xe0n aṣ\xecṣe: yan ọ̀kan l\xe1ra ${p(i.values,"|")}`;case"too_big":{let t=i.inclusive?"<=":"<",r=e[i.origin]??null;if(r)return`T\xf3 pọ̀ j\xf9: a n\xed l\xe1ti jẹ́ p\xe9 ${i.origin??"iye"} ${r.verb} ${t}${i.maximum} ${r.unit}`;return`T\xf3 pọ̀ j\xf9: a n\xed l\xe1ti jẹ́ ${t}${i.maximum}`}case"too_small":{let t=i.inclusive?">=":">",r=e[i.origin]??null;if(r)return`K\xe9r\xe9 ju: a n\xed l\xe1ti jẹ́ p\xe9 ${i.origin} ${r.verb} ${t}${i.minimum} ${r.unit}`;return`K\xe9r\xe9 ju: a n\xed l\xe1ti jẹ́ ${t}${i.minimum}`}case"invalid_format":if("starts_with"===i.format)return`Ọ̀rọ̀ aṣ\xecṣe: gbọ́dọ̀ bẹ̀rẹ̀ pẹ̀l\xfa "${i.prefix}"`;if("ends_with"===i.format)return`Ọ̀rọ̀ aṣ\xecṣe: gbọ́dọ̀ par\xed pẹ̀l\xfa "${i.suffix}"`;if("includes"===i.format)return`Ọ̀rọ̀ aṣ\xecṣe: gbọ́dọ̀ n\xed "${i.includes}"`;if("regex"===i.format)return`Ọ̀rọ̀ aṣ\xecṣe: gbọ́dọ̀ b\xe1 \xe0pẹẹrẹ mu ${i.pattern}`;return`Aṣ\xecṣe: ${t[i.format]??i.format}`;case"not_multiple_of":return`Nọ́mb\xe0 aṣ\xecṣe: gbọ́dọ̀ jẹ́ \xe8y\xe0 p\xedp\xedn ti ${i.divisor}`;case"unrecognized_keys":return`Bọt\xecn\xec \xe0\xecmọ̀: ${p(i.keys,", ")}`;case"invalid_key":return`Bọt\xecn\xec aṣ\xecṣe n\xedn\xfa ${i.origin}`;case"invalid_union":default:return"Ìbáwọlé aṣìṣe";case"invalid_element":return`Iye aṣ\xecṣe n\xedn\xfa ${i.origin}`}}})()}}e.s(["ar",()=>rC,"az",()=>rL,"be",()=>rF,"ca",()=>rJ,"cs",()=>rM,"da",()=>rW,"de",()=>rB,"en",()=>ev,"eo",()=>rV,"es",()=>rG,"fa",()=>rK,"fi",()=>rX,"fr",()=>rq,"frCA",()=>rY,"he",()=>rH,"hu",()=>rQ,"id",()=>r4,"is",()=>r6,"it",()=>r0,"ja",()=>r1,"kh",()=>r2,"ko",()=>r9,"mk",()=>r3,"ms",()=>r7,"nl",()=>r5,"no",()=>r8,"ota",()=>ne,"pl",()=>ni,"ps",()=>nt,"pt",()=>nr,"ru",()=>na,"sl",()=>no,"sv",()=>nu,"ta",()=>ns,"th",()=>nl,"tr",()=>nd,"ua",()=>nc,"ur",()=>nm,"vi",()=>nf,"yo",()=>ng,"zhCN",()=>np,"zhTW",()=>nv],69964),e.s([],81527),e.i(81527);var nh=e.i(69964);e.s(["$ZodRegistry",()=>ny,"$input",()=>n_,"$output",()=>n$,"globalRegistry",()=>nx,"registry",()=>nb],66486);let n$=Symbol("ZodOutput"),n_=Symbol("ZodInput");class ny{constructor(){this._map=new Map,this._idmap=new Map}add(e,...t){let i=t[0];if(this._map.set(e,i),i&&"object"==typeof i&&"id"in i){if(this._idmap.has(i.id))throw Error(`ID ${i.id} already exists in the registry`);this._idmap.set(i.id,e)}return this}clear(){return this._map=new Map,this._idmap=new Map,this}remove(e){let t=this._map.get(e);return t&&"object"==typeof t&&"id"in t&&this._idmap.delete(t.id),this._map.delete(e),this}get(e){let t=e._zod.parent;if(t){let i={...this.get(t)??{}};delete i.id;let r={...i,...this._map.get(e)};return Object.keys(r).length?r:void 0}return this._map.get(e)}has(e){return this._map.has(e)}}function nb(){return new ny}let nx=nb();function nk(e,t){return new e({type:"string",...F(t)})}function nI(e,t){return new e({type:"string",coerce:!0,...F(t)})}function nw(e,t){return new e({type:"string",format:"email",check:"string_format",abort:!1,...F(t)})}function nz(e,t){return new e({type:"string",format:"guid",check:"string_format",abort:!1,...F(t)})}function nS(e,t){return new e({type:"string",format:"uuid",check:"string_format",abort:!1,...F(t)})}function nZ(e,t){return new e({type:"string",format:"uuid",check:"string_format",abort:!1,version:"v4",...F(t)})}function nU(e,t){return new e({type:"string",format:"uuid",check:"string_format",abort:!1,version:"v6",...F(t)})}function nO(e,t){return new e({type:"string",format:"uuid",check:"string_format",abort:!1,version:"v7",...F(t)})}function nj(e,t){return new e({type:"string",format:"url",check:"string_format",abort:!1,...F(t)})}function nN(e,t){return new e({type:"string",format:"emoji",check:"string_format",abort:!1,...F(t)})}function nD(e,t){return new e({type:"string",format:"nanoid",check:"string_format",abort:!1,...F(t)})}function nP(e,t){return new e({type:"string",format:"cuid",check:"string_format",abort:!1,...F(t)})}function nE(e,t){return new e({type:"string",format:"cuid2",check:"string_format",abort:!1,...F(t)})}function nT(e,t){return new e({type:"string",format:"ulid",check:"string_format",abort:!1,...F(t)})}function nA(e,t){return new e({type:"string",format:"xid",check:"string_format",abort:!1,...F(t)})}function nC(e,t){return new e({type:"string",format:"ksuid",check:"string_format",abort:!1,...F(t)})}function nL(e,t){return new e({type:"string",format:"ipv4",check:"string_format",abort:!1,...F(t)})}function nR(e,t){return new e({type:"string",format:"ipv6",check:"string_format",abort:!1,...F(t)})}function nF(e,t){return new e({type:"string",format:"cidrv4",check:"string_format",abort:!1,...F(t)})}function nJ(e,t){return new e({type:"string",format:"cidrv6",check:"string_format",abort:!1,...F(t)})}function nM(e,t){return new e({type:"string",format:"base64",check:"string_format",abort:!1,...F(t)})}function nW(e,t){return new e({type:"string",format:"base64url",check:"string_format",abort:!1,...F(t)})}function nB(e,t){return new e({type:"string",format:"e164",check:"string_format",abort:!1,...F(t)})}function nV(e,t){return new e({type:"string",format:"jwt",check:"string_format",abort:!1,...F(t)})}e.i(66486),e.i(37554),e.s(["TimePrecision",()=>nG,"_any",()=>an,"_array",()=>aL,"_base64",()=>nM,"_base64url",()=>nW,"_bigint",()=>n7,"_boolean",()=>n9,"_catch",()=>a1,"_check",()=>oi,"_cidrv4",()=>nF,"_cidrv6",()=>nJ,"_coercedBigint",()=>n5,"_coercedBoolean",()=>n3,"_coercedDate",()=>al,"_coercedNumber",()=>nQ,"_coercedString",()=>nI,"_cuid",()=>nP,"_cuid2",()=>nE,"_custom",()=>a8,"_date",()=>as,"_default",()=>a4,"_discriminatedUnion",()=>aF,"_e164",()=>nB,"_email",()=>nw,"_emoji",()=>nN,"_endsWith",()=>aj,"_enum",()=>aG,"_file",()=>aq,"_float32",()=>n6,"_float64",()=>n0,"_gt",()=>af,"_gte",()=>ap,"_guid",()=>nz,"_includes",()=>aU,"_int",()=>n4,"_int32",()=>n1,"_int64",()=>n8,"_intersection",()=>aJ,"_ipv4",()=>nL,"_ipv6",()=>nR,"_isoDate",()=>nX,"_isoDateTime",()=>nK,"_isoDuration",()=>nY,"_isoTime",()=>nq,"_jwt",()=>nV,"_ksuid",()=>nC,"_lazy",()=>a7,"_length",()=>aw,"_literal",()=>aX,"_lowercase",()=>aS,"_lt",()=>ac,"_lte",()=>am,"_map",()=>aB,"_max",()=>am,"_maxLength",()=>ak,"_maxSize",()=>ay,"_mime",()=>aD,"_min",()=>ap,"_minLength",()=>aI,"_minSize",()=>ab,"_multipleOf",()=>a_,"_nan",()=>ad,"_nanoid",()=>nD,"_nativeEnum",()=>aK,"_negative",()=>ag,"_never",()=>ao,"_nonnegative",()=>a$,"_nonoptional",()=>a6,"_nonpositive",()=>ah,"_normalize",()=>aE,"_null",()=>ar,"_nullable",()=>aQ,"_number",()=>nH,"_optional",()=>aH,"_overwrite",()=>aP,"_pipe",()=>a2,"_positive",()=>av,"_promise",()=>a5,"_property",()=>aN,"_readonly",()=>a9,"_record",()=>aW,"_refine",()=>oe,"_regex",()=>az,"_set",()=>aV,"_size",()=>ax,"_startsWith",()=>aO,"_string",()=>nk,"_stringFormat",()=>on,"_stringbool",()=>or,"_success",()=>a0,"_superRefine",()=>ot,"_symbol",()=>at,"_templateLiteral",()=>a3,"_toLowerCase",()=>aA,"_toUpperCase",()=>aC,"_transform",()=>aY,"_trim",()=>aT,"_tuple",()=>aM,"_uint32",()=>n2,"_uint64",()=>ae,"_ulid",()=>nT,"_undefined",()=>ai,"_union",()=>aR,"_unknown",()=>aa,"_uppercase",()=>aZ,"_url",()=>nj,"_uuid",()=>nS,"_uuidv4",()=>nZ,"_uuidv6",()=>nU,"_uuidv7",()=>nO,"_void",()=>au,"_xid",()=>nA],44260);let nG={Any:null,Minute:-1,Second:0,Millisecond:3,Microsecond:6};function nK(e,t){return new e({type:"string",format:"datetime",check:"string_format",offset:!1,local:!1,precision:null,...F(t)})}function nX(e,t){return new e({type:"string",format:"date",check:"string_format",...F(t)})}function nq(e,t){return new e({type:"string",format:"time",check:"string_format",precision:null,...F(t)})}function nY(e,t){return new e({type:"string",format:"duration",check:"string_format",...F(t)})}function nH(e,t){return new e({type:"number",checks:[],...F(t)})}function nQ(e,t){return new e({type:"number",coerce:!0,checks:[],...F(t)})}function n4(e,t){return new e({type:"number",check:"number_format",abort:!1,format:"safeint",...F(t)})}function n6(e,t){return new e({type:"number",check:"number_format",abort:!1,format:"float32",...F(t)})}function n0(e,t){return new e({type:"number",check:"number_format",abort:!1,format:"float64",...F(t)})}function n1(e,t){return new e({type:"number",check:"number_format",abort:!1,format:"int32",...F(t)})}function n2(e,t){return new e({type:"number",check:"number_format",abort:!1,format:"uint32",...F(t)})}function n9(e,t){return new e({type:"boolean",...F(t)})}function n3(e,t){return new e({type:"boolean",coerce:!0,...F(t)})}function n7(e,t){return new e({type:"bigint",...F(t)})}function n5(e,t){return new e({type:"bigint",coerce:!0,...F(t)})}function n8(e,t){return new e({type:"bigint",check:"bigint_format",abort:!1,format:"int64",...F(t)})}function ae(e,t){return new e({type:"bigint",check:"bigint_format",abort:!1,format:"uint64",...F(t)})}function at(e,t){return new e({type:"symbol",...F(t)})}function ai(e,t){return new e({type:"undefined",...F(t)})}function ar(e,t){return new e({type:"null",...F(t)})}function an(e){return new e({type:"any"})}function aa(e){return new e({type:"unknown"})}function ao(e,t){return new e({type:"never",...F(t)})}function au(e,t){return new e({type:"void",...F(t)})}function as(e,t){return new e({type:"date",...F(t)})}function al(e,t){return new e({type:"date",coerce:!0,...F(t)})}function ad(e,t){return new e({type:"nan",...F(t)})}function ac(e,t){return new tG({check:"less_than",...F(t),value:e,inclusive:!1})}function am(e,t){return new tG({check:"less_than",...F(t),value:e,inclusive:!0})}function af(e,t){return new tK({check:"greater_than",...F(t),value:e,inclusive:!1})}function ap(e,t){return new tK({check:"greater_than",...F(t),value:e,inclusive:!0})}function av(e){return af(0,e)}function ag(e){return ac(0,e)}function ah(e){return am(0,e)}function a$(e){return ap(0,e)}function a_(e,t){return new tX({check:"multiple_of",...F(t),value:e})}function ay(e,t){return new tH({check:"max_size",...F(t),maximum:e})}function ab(e,t){return new tQ({check:"min_size",...F(t),minimum:e})}function ax(e,t){return new t4({check:"size_equals",...F(t),size:e})}function ak(e,t){return new t6({check:"max_length",...F(t),maximum:e})}function aI(e,t){return new t0({check:"min_length",...F(t),minimum:e})}function aw(e,t){return new t1({check:"length_equals",...F(t),length:e})}function az(e,t){return new t9({check:"string_format",format:"regex",...F(t),pattern:e})}function aS(e){return new t3({check:"string_format",format:"lowercase",...F(e)})}function aZ(e){return new t7({check:"string_format",format:"uppercase",...F(e)})}function aU(e,t){return new t5({check:"string_format",format:"includes",...F(t),includes:e})}function aO(e,t){return new t8({check:"string_format",format:"starts_with",...F(t),prefix:e})}function aj(e,t){return new ie({check:"string_format",format:"ends_with",...F(t),suffix:e})}function aN(e,t,i){return new ii({check:"property",property:e,schema:t,...F(i)})}function aD(e,t){return new ir({check:"mime_type",mime:e,...F(t)})}function aP(e){return new ia({check:"overwrite",tx:e})}function aE(e){return aP(t=>t.normalize(e))}function aT(){return aP(e=>e.trim())}function aA(){return aP(e=>e.toLowerCase())}function aC(){return aP(e=>e.toUpperCase())}function aL(e,t,i){return new e({type:"array",element:t,...F(i)})}function aR(e,t,i){return new e({type:"union",options:t,...F(i)})}function aF(e,t,i,r){return new e({type:"union",options:i,discriminator:t,...F(r)})}function aJ(e,t,i){return new e({type:"intersection",left:t,right:i})}function aM(e,t,i,r){let n=i instanceof is,a=n?r:i;return new e({type:"tuple",items:t,rest:n?i:null,...F(a)})}function aW(e,t,i,r){return new e({type:"record",keyType:t,valueType:i,...F(r)})}function aB(e,t,i,r){return new e({type:"map",keyType:t,valueType:i,...F(r)})}function aV(e,t,i){return new e({type:"set",valueType:t,...F(i)})}function aG(e,t,i){return new e({type:"enum",entries:Array.isArray(t)?Object.fromEntries(t.map(e=>[e,e])):t,...F(i)})}function aK(e,t,i){return new e({type:"enum",entries:t,...F(i)})}function aX(e,t,i){return new e({type:"literal",values:Array.isArray(t)?t:[t],...F(i)})}function aq(e,t){return new e({type:"file",...F(t)})}function aY(e,t){return new e({type:"transform",transform:t})}function aH(e,t){return new e({type:"optional",innerType:t})}function aQ(e,t){return new e({type:"nullable",innerType:t})}function a4(e,t,i){return new e({type:"default",innerType:t,get defaultValue(){return"function"==typeof i?i():P(i)}})}function a6(e,t,i){return new e({type:"nonoptional",innerType:t,...F(i)})}function a0(e,t){return new e({type:"success",innerType:t})}function a1(e,t,i){return new e({type:"catch",innerType:t,catchValue:"function"==typeof i?i:()=>i})}function a2(e,t,i){return new e({type:"pipe",in:t,out:i})}function a9(e,t){return new e({type:"readonly",innerType:t})}function a3(e,t,i){return new e({type:"template_literal",parts:t,...F(i)})}function a7(e,t){return new e({type:"lazy",getter:t})}function a5(e,t){return new e({type:"promise",innerType:t})}function a8(e,t,i){let r=F(i);return r.abort??(r.abort=!0),new e({type:"custom",check:"custom",fn:t,...r})}function oe(e,t,i){return new e({type:"custom",check:"custom",fn:t,...F(i)})}function ot(e){let t=oi(i=>(i.addIssue=e=>{"string"==typeof e?i.issues.push(eo(e,i.value,t._zod.def)):(e.fatal&&(e.continue=!1),e.code??(e.code="custom"),e.input??(e.input=i.value),e.inst??(e.inst=t),e.continue??(e.continue=!t._zod.def.abort),i.issues.push(eo(e)))},e(i.value,i)));return t}function oi(e,t){let i=new tB({check:"custom",...F(t)});return i._zod.check=e,i}function or(e,t){let i=F(t),r=i.truthy??["true","1","yes","on","y","enabled"],n=i.falsy??["false","0","no","off","n","disabled"];"sensitive"!==i.case&&(r=r.map(e=>"string"==typeof e?e.toLowerCase():e),n=n.map(e=>"string"==typeof e?e.toLowerCase():e));let a=new Set(r),o=new Set(n),u=e.Codec??rI,s=e.Boolean??iF,l=new u({type:"pipe",in:new(e.String??il)({type:"string",error:i.error}),out:new s({type:"boolean",error:i.error}),transform:(e,t)=>{let r=e;return"sensitive"!==i.case&&(r=r.toLowerCase()),!!a.has(r)||!o.has(r)&&(t.issues.push({code:"invalid_value",expected:"stringbool",values:[...a,...o],input:t.value,inst:l,continue:!1}),{})},reverseTransform:(e,t)=>!0===e?r[0]||"true":n[0]||"false",error:i.error});return l}function on(e,t,i,r={}){let n=F(r),a={...F(r),check:"string_format",type:"string",format:t,fn:"function"==typeof i?i:e=>i.test(e),...n};return i instanceof RegExp&&(a.pattern=i),new e(a)}e.i(44260),e.s(["JSONSchemaGenerator",()=>oa,"toJSONSchema",()=>oo],15737);class oa{constructor(e){this.counter=0,this.metadataRegistry=e?.metadata??nx,this.target=e?.target??"draft-2020-12",this.unrepresentable=e?.unrepresentable??"throw",this.override=e?.override??(()=>{}),this.io=e?.io??"output",this.seen=new Map}process(e,t={path:[],schemaPath:[]}){var i;let r=e._zod.def,n=this.seen.get(e);if(n)return n.count++,t.schemaPath.includes(e)&&(n.cycle=t.path),n.schema;let a={schema:{},count:1,cycle:void 0,path:t.path};this.seen.set(e,a);let o=e._zod.toJSONSchema?.();if(o)a.schema=o;else{let i={...t,schemaPath:[...t.schemaPath,e],path:t.path},n=e._zod.parent;if(n)a.ref=n,this.process(n,i),this.seen.get(n).isParent=!0;else{let t=a.schema;switch(r.type){case"string":{t.type="string";let{minimum:i,maximum:r,format:n,patterns:o,contentEncoding:u}=e._zod.bag;if("number"==typeof i&&(t.minLength=i),"number"==typeof r&&(t.maxLength=r),n&&(t.format=({guid:"uuid",url:"uri",datetime:"date-time",json_string:"json-string",regex:""})[n]??n,""===t.format&&delete t.format),u&&(t.contentEncoding=u),o&&o.size>0){let e=[...o];1===e.length?t.pattern=e[0].source:e.length>1&&(a.schema.allOf=[...e.map(e=>({..."draft-7"===this.target||"draft-4"===this.target||"openapi-3.0"===this.target?{type:"string"}:{},pattern:e.source}))])}break}case"number":{let{minimum:i,maximum:r,format:n,multipleOf:a,exclusiveMaximum:o,exclusiveMinimum:u}=e._zod.bag;"string"==typeof n&&n.includes("int")?t.type="integer":t.type="number","number"==typeof u&&("draft-4"===this.target||"openapi-3.0"===this.target?(t.minimum=u,t.exclusiveMinimum=!0):t.exclusiveMinimum=u),"number"==typeof i&&(t.minimum=i,"number"==typeof u&&"draft-4"!==this.target&&(u>=i?delete t.minimum:delete t.exclusiveMinimum)),"number"==typeof o&&("draft-4"===this.target||"openapi-3.0"===this.target?(t.maximum=o,t.exclusiveMaximum=!0):t.exclusiveMaximum=o),"number"==typeof r&&(t.maximum=r,"number"==typeof o&&"draft-4"!==this.target&&(o<=r?delete t.maximum:delete t.exclusiveMaximum)),"number"==typeof a&&(t.multipleOf=a);break}case"boolean":case"success":t.type="boolean";break;case"bigint":if("throw"===this.unrepresentable)throw Error("BigInt cannot be represented in JSON Schema");break;case"symbol":if("throw"===this.unrepresentable)throw Error("Symbols cannot be represented in JSON Schema");break;case"null":"openapi-3.0"===this.target?(t.type="string",t.nullable=!0,t.enum=[null]):t.type="null";break;case"any":case"unknown":break;case"undefined":if("throw"===this.unrepresentable)throw Error("Undefined cannot be represented in JSON Schema");break;case"void":if("throw"===this.unrepresentable)throw Error("Void cannot be represented in JSON Schema");break;case"never":t.not={};break;case"date":if("throw"===this.unrepresentable)throw Error("Date cannot be represented in JSON Schema");break;case"array":{let{minimum:n,maximum:a}=e._zod.bag;"number"==typeof n&&(t.minItems=n),"number"==typeof a&&(t.maxItems=a),t.type="array",t.items=this.process(r.element,{...i,path:[...i.path,"items"]});break}case"object":{t.type="object",t.properties={};let e=r.shape;for(let r in e)t.properties[r]=this.process(e[r],{...i,path:[...i.path,"properties",r]});let n=new Set([...new Set(Object.keys(e))].filter(e=>{let t=r.shape[e]._zod;return"input"===this.io?void 0===t.optin:void 0===t.optout}));n.size>0&&(t.required=Array.from(n)),r.catchall?._zod.def.type==="never"?t.additionalProperties=!1:r.catchall?r.catchall&&(t.additionalProperties=this.process(r.catchall,{...i,path:[...i.path,"additionalProperties"]})):"output"===this.io&&(t.additionalProperties=!1);break}case"union":t.anyOf=r.options.map((e,t)=>this.process(e,{...i,path:[...i.path,"anyOf",t]}));break;case"intersection":{let e=this.process(r.left,{...i,path:[...i.path,"allOf",0]}),n=this.process(r.right,{...i,path:[...i.path,"allOf",1]}),a=e=>"allOf"in e&&1===Object.keys(e).length;t.allOf=[...a(e)?e.allOf:[e],...a(n)?n.allOf:[n]];break}case"tuple":{t.type="array";let n="draft-2020-12"===this.target?"prefixItems":"items",a="draft-2020-12"===this.target||"openapi-3.0"===this.target?"items":"additionalItems",o=r.items.map((e,t)=>this.process(e,{...i,path:[...i.path,n,t]})),u=r.rest?this.process(r.rest,{...i,path:[...i.path,a,..."openapi-3.0"===this.target?[r.items.length]:[]]}):null;"draft-2020-12"===this.target?(t.prefixItems=o,u&&(t.items=u)):"openapi-3.0"===this.target?(t.items={anyOf:o},u&&t.items.anyOf.push(u),t.minItems=o.length,u||(t.maxItems=o.length)):(t.items=o,u&&(t.additionalItems=u));let{minimum:s,maximum:l}=e._zod.bag;"number"==typeof s&&(t.minItems=s),"number"==typeof l&&(t.maxItems=l);break}case"record":t.type="object",("draft-7"===this.target||"draft-2020-12"===this.target)&&(t.propertyNames=this.process(r.keyType,{...i,path:[...i.path,"propertyNames"]})),t.additionalProperties=this.process(r.valueType,{...i,path:[...i.path,"additionalProperties"]});break;case"map":if("throw"===this.unrepresentable)throw Error("Map cannot be represented in JSON Schema");break;case"set":if("throw"===this.unrepresentable)throw Error("Set cannot be represented in JSON Schema");break;case"enum":{let e=f(r.entries);e.every(e=>"number"==typeof e)&&(t.type="number"),e.every(e=>"string"==typeof e)&&(t.type="string"),t.enum=e;break}case"literal":{let e=[];for(let t of r.values)if(void 0===t){if("throw"===this.unrepresentable)throw Error("Literal `undefined` cannot be represented in JSON Schema")}else if("bigint"==typeof t)if("throw"===this.unrepresentable)throw Error("BigInt literals cannot be represented in JSON Schema");else e.push(Number(t));else e.push(t);if(0===e.length);else if(1===e.length){let i=e[0];t.type=null===i?"null":typeof i,"draft-4"===this.target||"openapi-3.0"===this.target?t.enum=[i]:t.const=i}else e.every(e=>"number"==typeof e)&&(t.type="number"),e.every(e=>"string"==typeof e)&&(t.type="string"),e.every(e=>"boolean"==typeof e)&&(t.type="string"),e.every(e=>null===e)&&(t.type="null"),t.enum=e;break}case"file":{let i={type:"string",format:"binary",contentEncoding:"binary"},{minimum:r,maximum:n,mime:a}=e._zod.bag;void 0!==r&&(i.minLength=r),void 0!==n&&(i.maxLength=n),a?1===a.length?(i.contentMediaType=a[0],Object.assign(t,i)):t.anyOf=a.map(e=>({...i,contentMediaType:e})):Object.assign(t,i);break}case"transform":if("throw"===this.unrepresentable)throw Error("Transforms cannot be represented in JSON Schema");break;case"nullable":{let e=this.process(r.innerType,i);"openapi-3.0"===this.target?(a.ref=r.innerType,t.nullable=!0):t.anyOf=[e,{type:"null"}];break}case"nonoptional":case"promise":case"optional":this.process(r.innerType,i),a.ref=r.innerType;break;case"default":this.process(r.innerType,i),a.ref=r.innerType,t.default=JSON.parse(JSON.stringify(r.defaultValue));break;case"prefault":this.process(r.innerType,i),a.ref=r.innerType,"input"===this.io&&(t._prefault=JSON.parse(JSON.stringify(r.defaultValue)));break;case"catch":{let e;this.process(r.innerType,i),a.ref=r.innerType;try{e=r.catchValue(void 0)}catch{throw Error("Dynamic catch values are not supported in JSON Schema")}t.default=e;break}case"nan":if("throw"===this.unrepresentable)throw Error("NaN cannot be represented in JSON Schema");break;case"template_literal":{let i=e._zod.pattern;if(!i)throw Error("Pattern not found in template literal");t.type="string",t.pattern=i.source;break}case"pipe":{let e="input"===this.io?"transform"===r.in._zod.def.type?r.out:r.in:r.out;this.process(e,i),a.ref=e;break}case"readonly":this.process(r.innerType,i),a.ref=r.innerType,t.readOnly=!0;break;case"lazy":{let t=e._zod.innerType;this.process(t,i),a.ref=t;break}case"custom":if("throw"===this.unrepresentable)throw Error("Custom types cannot be represented in JSON Schema");break;case"function":if("throw"===this.unrepresentable)throw Error("Function types cannot be represented in JSON Schema")}}}let u=this.metadataRegistry.get(e);return u&&Object.assign(a.schema,u),"input"===this.io&&function e(t,i){let r=i??{seen:new Set};if(r.seen.has(t))return!1;r.seen.add(t);let n=t._zod.def;switch(n.type){case"string":case"number":case"bigint":case"boolean":case"date":case"symbol":case"undefined":case"null":case"any":case"unknown":case"never":case"void":case"literal":case"enum":case"nan":case"file":case"template_literal":case"custom":case"success":case"catch":case"function":return!1;case"array":return e(n.element,r);case"object":for(let t in n.shape)if(e(n.shape[t],r))return!0;return!1;case"union":for(let t of n.options)if(e(t,r))return!0;return!1;case"intersection":return e(n.left,r)||e(n.right,r);case"tuple":for(let t of n.items)if(e(t,r))return!0;if(n.rest&&e(n.rest,r))return!0;return!1;case"record":case"map":return e(n.keyType,r)||e(n.valueType,r);case"set":return e(n.valueType,r);case"promise":case"optional":case"nonoptional":case"nullable":case"readonly":case"default":case"prefault":return e(n.innerType,r);case"lazy":return e(n.getter(),r);case"transform":return!0;case"pipe":return e(n.in,r)||e(n.out,r)}throw Error(`Unknown schema type: ${n.type}`)}(e)&&(delete a.schema.examples,delete a.schema.default),"input"===this.io&&a.schema._prefault&&((i=a.schema).default??(i.default=a.schema._prefault)),delete a.schema._prefault,this.seen.get(e).schema}emit(e,t){let i={cycles:t?.cycles??"ref",reused:t?.reused??"inline",external:t?.external??void 0},r=this.seen.get(e);if(!r)throw Error("Unprocessed schema. This is a bug in Zod.");let n=e=>{let t="draft-2020-12"===this.target?"$defs":"definitions";if(i.external){let r=i.external.registry.get(e[0])?.id,n=i.external.uri??(e=>e);if(r)return{ref:n(r)};let a=e[1].defId??e[1].schema.id??`schema${this.counter++}`;return e[1].defId=a,{defId:a,ref:`${n("__shared")}#/${t}/${a}`}}if(e[1]===r)return{ref:"#"};let n=`#/${t}/`,a=e[1].schema.id??`__schema${this.counter++}`;return{defId:a,ref:n+a}},a=e=>{if(e[1].schema.$ref)return;let t=e[1],{ref:i,defId:r}=n(e);t.def={...t.schema},r&&(t.defId=r);let a=t.schema;for(let e in a)delete a[e];a.$ref=i};if("throw"===i.cycles)for(let e of this.seen.entries()){let t=e[1];if(t.cycle)throw Error(`Cycle detected: #/${t.cycle?.join("/")}/<root>

Set the \`cycles\` parameter to \`"ref"\` to resolve cyclical schemas with defs.`)}for(let t of this.seen.entries()){let r=t[1];if(e===t[0]){a(t);continue}if(i.external){let r=i.external.registry.get(t[0])?.id;if(e!==t[0]&&r){a(t);continue}}if(this.metadataRegistry.get(t[0])?.id||r.cycle||r.count>1&&"ref"===i.reused){a(t);continue}}let o=(e,t)=>{let i=this.seen.get(e),r=i.def??i.schema,n={...r};if(null===i.ref)return;let a=i.ref;if(i.ref=null,a){o(a,t);let e=this.seen.get(a).schema;e.$ref&&("draft-7"===t.target||"draft-4"===t.target||"openapi-3.0"===t.target)?(r.allOf=r.allOf??[],r.allOf.push(e)):(Object.assign(r,e),Object.assign(r,n))}i.isParent||this.override({zodSchema:e,jsonSchema:r,path:i.path??[]})};for(let e of[...this.seen.entries()].reverse())o(e[0],{target:this.target});let u={};if("draft-2020-12"===this.target?u.$schema="https://json-schema.org/draft/2020-12/schema":"draft-7"===this.target?u.$schema="http://json-schema.org/draft-07/schema#":"draft-4"===this.target?u.$schema="http://json-schema.org/draft-04/schema#":"openapi-3.0"===this.target||console.warn(`Invalid target: ${this.target}`),i.external?.uri){let t=i.external.registry.get(e)?.id;if(!t)throw Error("Schema is missing an `id` property");u.$id=i.external.uri(t)}Object.assign(u,r.def);let s=i.external?.defs??{};for(let e of this.seen.entries()){let t=e[1];t.def&&t.defId&&(s[t.defId]=t.def)}i.external||Object.keys(s).length>0&&("draft-2020-12"===this.target?u.$defs=s:u.definitions=s);try{return JSON.parse(JSON.stringify(u))}catch(e){throw Error("Error converting schema to JSON.")}}}function oo(e,t){if(e instanceof ny){let i=new oa(t),r={};for(let t of e._idmap.entries()){let[e,r]=t;i.process(r)}let n={},a={registry:e,uri:t?.uri,defs:r};for(let r of e._idmap.entries()){let[e,o]=r;n[e]=i.emit(o,{...t,external:a})}return Object.keys(r).length>0&&(n.__shared={["draft-2020-12"===i.target?"$defs":"definitions"]:r}),{schemas:n}}let i=new oa(t);return i.process(e),i.emit(e,t)}e.i(15737),e.s([],92246);var ou=e.i(92246),os=e.i(64519);e.s(["ZodAny",()=>uM,"ZodArray",()=>uQ,"ZodBase64",()=>ul,"ZodBase64URL",()=>uc,"ZodBigInt",()=>uN,"ZodBigIntFormat",()=>uP,"ZodBoolean",()=>uO,"ZodCIDRv4",()=>ua,"ZodCIDRv6",()=>uu,"ZodCUID",()=>o6,"ZodCUID2",()=>o1,"ZodCatch",()=>sD,"ZodCodec",()=>sL,"ZodCustom",()=>sY,"ZodCustomStringFormat",()=>uh,"ZodDate",()=>uY,"ZodDefault",()=>sw,"ZodDiscriminatedUnion",()=>u5,"ZodE164",()=>uf,"ZodEmail",()=>oL,"ZodEmoji",()=>oY,"ZodEnum",()=>sc,"ZodFile",()=>sg,"ZodFunction",()=>sX,"ZodGUID",()=>oF,"ZodIPv4",()=>ut,"ZodIPv6",()=>ur,"ZodIntersection",()=>se,"ZodJWT",()=>uv,"ZodKSUID",()=>o8,"ZodLazy",()=>sB,"ZodLiteral",()=>sp,"ZodMap",()=>su,"ZodNaN",()=>sE,"ZodNanoID",()=>oQ,"ZodNever",()=>uG,"ZodNonOptional",()=>sU,"ZodNull",()=>uF,"ZodNullable",()=>sx,"ZodNumber",()=>ux,"ZodNumberFormat",()=>uI,"ZodObject",()=>u0,"ZodOptional",()=>sy,"ZodPipe",()=>sA,"ZodPrefault",()=>sS,"ZodPromise",()=>sG,"ZodReadonly",()=>sF,"ZodRecord",()=>sn,"ZodSet",()=>sl,"ZodString",()=>oT,"ZodStringFormat",()=>oC,"ZodSuccess",()=>sj,"ZodSymbol",()=>uA,"ZodTemplateLiteral",()=>sM,"ZodTransform",()=>s$,"ZodTuple",()=>si,"ZodType",()=>oP,"ZodULID",()=>o9,"ZodURL",()=>oK,"ZodUUID",()=>oM,"ZodUndefined",()=>uL,"ZodUnion",()=>u3,"ZodUnknown",()=>uB,"ZodVoid",()=>uX,"ZodXID",()=>o7,"_ZodString",()=>oE,"_default",()=>sz,"_function",()=>sq,"any",()=>uW,"array",()=>u4,"base64",()=>ud,"base64url",()=>um,"bigint",()=>uD,"boolean",()=>uj,"catch",()=>sP,"check",()=>sH,"cidrv4",()=>uo,"cidrv6",()=>us,"codec",()=>sR,"cuid",()=>o0,"cuid2",()=>o2,"custom",()=>sQ,"date",()=>uH,"discriminatedUnion",()=>u8,"e164",()=>up,"email",()=>oR,"emoji",()=>oH,"enum",()=>sm,"file",()=>sh,"float32",()=>uz,"float64",()=>uS,"function",()=>sq,"guid",()=>oJ,"hash",()=>ub,"hex",()=>uy,"hostname",()=>u_,"httpUrl",()=>oq,"instanceof",()=>s0,"int",()=>uw,"int32",()=>uZ,"int64",()=>uE,"intersection",()=>st,"ipv4",()=>ui,"ipv6",()=>un,"json",()=>s2,"jwt",()=>ug,"keyof",()=>u6,"ksuid",()=>ue,"lazy",()=>sV,"literal",()=>sv,"looseObject",()=>u9,"map",()=>ss,"nan",()=>sT,"nanoid",()=>o4,"nativeEnum",()=>sf,"never",()=>uK,"nonoptional",()=>sO,"null",()=>uJ,"nullable",()=>sk,"nullish",()=>sI,"number",()=>uk,"object",()=>u1,"optional",()=>sb,"partialRecord",()=>so,"pipe",()=>sC,"prefault",()=>sZ,"preprocess",()=>s9,"promise",()=>sK,"readonly",()=>sJ,"record",()=>sa,"refine",()=>s4,"set",()=>sd,"strictObject",()=>u2,"string",()=>oA,"stringFormat",()=>u$,"stringbool",()=>s1,"success",()=>sN,"superRefine",()=>s6,"symbol",()=>uC,"templateLiteral",()=>sW,"transform",()=>s_,"tuple",()=>sr,"uint32",()=>uU,"uint64",()=>uT,"ulid",()=>o3,"undefined",()=>uR,"union",()=>u7,"unknown",()=>uV,"url",()=>oX,"uuid",()=>oW,"uuidv4",()=>oB,"uuidv6",()=>oV,"uuidv7",()=>oG,"void",()=>uq,"xid",()=>o5],44493);var ol=rA,od=rT;e.s(["ZodISODate",()=>of,"ZodISODateTime",()=>oc,"ZodISODuration",()=>oh,"ZodISOTime",()=>ov,"date",()=>op,"datetime",()=>om,"duration",()=>o$,"time",()=>og],97514);let oc=i("ZodISODateTime",(e,t)=>{ik.init(e,t),oC.init(e,t)});function om(e){return nK(oc,e)}let of=i("ZodISODate",(e,t)=>{iI.init(e,t),oC.init(e,t)});function op(e){return nX(of,e)}let ov=i("ZodISOTime",(e,t)=>{iw.init(e,t),oC.init(e,t)});function og(e){return nq(ov,e)}let oh=i("ZodISODuration",(e,t)=>{iz.init(e,t),oC.init(e,t)});function o$(e){return nY(oh,e)}e.s(["decode",()=>oS,"decodeAsync",()=>oU,"encode",()=>oz,"encodeAsync",()=>oZ,"parse",()=>ox,"parseAsync",()=>ok,"safeDecode",()=>oj,"safeDecodeAsync",()=>oD,"safeEncode",()=>oO,"safeEncodeAsync",()=>oN,"safeParse",()=>oI,"safeParseAsync",()=>ow],57905),e.s(["ZodError",()=>oy,"ZodRealError",()=>ob],65155);let o_=(e,t)=>{eh.init(e,t),e.name="ZodError",Object.defineProperties(e,{format:{value:t=>ey(e,t)},flatten:{value:t=>e_(e,t)},addIssue:{value:t=>{e.issues.push(t),e.message=JSON.stringify(e.issues,v,2)}},addIssues:{value:t=>{e.issues.push(...t),e.message=JSON.stringify(e.issues,v,2)}},isEmpty:{get:()=>0===e.issues.length}})},oy=i("ZodError",o_),ob=i("ZodError",o_,{Parent:Error}),ox=eI(ob),ok=ez(ob),oI=eZ(ob),ow=eO(ob),oz=eN(ob),oS=eP(ob),oZ=eT(ob),oU=eC(ob),oO=eR(ob),oj=eJ(ob),oN=eW(ob),oD=eV(ob),oP=i("ZodType",(e,t)=>(is.init(e,t),e.def=t,e.type=t.type,Object.defineProperty(e,"_def",{value:t}),e.check=(...i)=>e.clone({...t,checks:[...t.checks??[],...i.map(e=>"function"==typeof e?{_zod:{check:e,def:{check:"custom"},onattach:[]}}:e)]}),e.clone=(t,i)=>R(e,t,i),e.brand=()=>e,e.register=(t,i)=>(t.add(e,i),e),e.parse=(t,i)=>ox(e,t,i,{callee:e.parse}),e.safeParse=(t,i)=>oI(e,t,i),e.parseAsync=async(t,i)=>ok(e,t,i,{callee:e.parseAsync}),e.safeParseAsync=async(t,i)=>ow(e,t,i),e.spa=e.safeParseAsync,e.encode=(t,i)=>oz(e,t,i),e.decode=(t,i)=>oS(e,t,i),e.encodeAsync=async(t,i)=>oZ(e,t,i),e.decodeAsync=async(t,i)=>oU(e,t,i),e.safeEncode=(t,i)=>oO(e,t,i),e.safeDecode=(t,i)=>oj(e,t,i),e.safeEncodeAsync=async(t,i)=>oN(e,t,i),e.safeDecodeAsync=async(t,i)=>oD(e,t,i),e.refine=(t,i)=>e.check(s4(t,i)),e.superRefine=t=>e.check(ot(t)),e.overwrite=t=>e.check(aP(t)),e.optional=()=>sb(e),e.nullable=()=>sk(e),e.nullish=()=>sb(sk(e)),e.nonoptional=t=>sO(e,t),e.array=()=>u4(e),e.or=t=>u7([e,t]),e.and=t=>st(e,t),e.transform=t=>sC(e,s_(t)),e.default=t=>sz(e,t),e.prefault=t=>sZ(e,t),e.catch=t=>sP(e,t),e.pipe=t=>sC(e,t),e.readonly=()=>sJ(e),e.describe=t=>{let i=e.clone();return nx.add(i,{description:t}),i},Object.defineProperty(e,"description",{get:()=>nx.get(e)?.description,configurable:!0}),e.meta=(...t)=>{if(0===t.length)return nx.get(e);let i=e.clone();return nx.add(i,t[0]),i},e.isOptional=()=>e.safeParse(void 0).success,e.isNullable=()=>e.safeParse(null).success,e)),oE=i("_ZodString",(e,t)=>{il.init(e,t),oP.init(e,t);let i=e._zod.bag;e.format=i.format??null,e.minLength=i.minimum??null,e.maxLength=i.maximum??null,e.regex=(...t)=>e.check(az(...t)),e.includes=(...t)=>e.check(aU(...t)),e.startsWith=(...t)=>e.check(aO(...t)),e.endsWith=(...t)=>e.check(aj(...t)),e.min=(...t)=>e.check(aI(...t)),e.max=(...t)=>e.check(ak(...t)),e.length=(...t)=>e.check(aw(...t)),e.nonempty=(...t)=>e.check(aI(1,...t)),e.lowercase=t=>e.check(aS(t)),e.uppercase=t=>e.check(aZ(t)),e.trim=()=>e.check(aT()),e.normalize=(...t)=>e.check(aE(...t)),e.toLowerCase=()=>e.check(aA()),e.toUpperCase=()=>e.check(aC())}),oT=i("ZodString",(e,t)=>{il.init(e,t),oE.init(e,t),e.email=t=>e.check(nw(oL,t)),e.url=t=>e.check(nj(oK,t)),e.jwt=t=>e.check(nV(uv,t)),e.emoji=t=>e.check(nN(oY,t)),e.guid=t=>e.check(nz(oF,t)),e.uuid=t=>e.check(nS(oM,t)),e.uuidv4=t=>e.check(nZ(oM,t)),e.uuidv6=t=>e.check(nU(oM,t)),e.uuidv7=t=>e.check(nO(oM,t)),e.nanoid=t=>e.check(nD(oQ,t)),e.guid=t=>e.check(nz(oF,t)),e.cuid=t=>e.check(nP(o6,t)),e.cuid2=t=>e.check(nE(o1,t)),e.ulid=t=>e.check(nT(o9,t)),e.base64=t=>e.check(nM(ul,t)),e.base64url=t=>e.check(nW(uc,t)),e.xid=t=>e.check(nA(o7,t)),e.ksuid=t=>e.check(nC(o8,t)),e.ipv4=t=>e.check(nL(ut,t)),e.ipv6=t=>e.check(nR(ur,t)),e.cidrv4=t=>e.check(nF(ua,t)),e.cidrv6=t=>e.check(nJ(uu,t)),e.e164=t=>e.check(nB(uf,t)),e.datetime=t=>e.check(om(t)),e.date=t=>e.check(op(t)),e.time=t=>e.check(og(t)),e.duration=t=>e.check(o$(t))});function oA(e){return nk(oT,e)}let oC=i("ZodStringFormat",(e,t)=>{id.init(e,t),oE.init(e,t)}),oL=i("ZodEmail",(e,t)=>{ip.init(e,t),oC.init(e,t)});function oR(e){return nw(oL,e)}let oF=i("ZodGUID",(e,t)=>{ic.init(e,t),oC.init(e,t)});function oJ(e){return nz(oF,e)}let oM=i("ZodUUID",(e,t)=>{im.init(e,t),oC.init(e,t)});function oW(e){return nS(oM,e)}function oB(e){return nZ(oM,e)}function oV(e){return nU(oM,e)}function oG(e){return nO(oM,e)}let oK=i("ZodURL",(e,t)=>{iv.init(e,t),oC.init(e,t)});function oX(e){return nj(oK,e)}function oq(e){return nj(oK,{protocol:/^https?$/,hostname:ol.domain,...od.normalizeParams(e)})}let oY=i("ZodEmoji",(e,t)=>{ig.init(e,t),oC.init(e,t)});function oH(e){return nN(oY,e)}let oQ=i("ZodNanoID",(e,t)=>{ih.init(e,t),oC.init(e,t)});function o4(e){return nD(oQ,e)}let o6=i("ZodCUID",(e,t)=>{i$.init(e,t),oC.init(e,t)});function o0(e){return nP(o6,e)}let o1=i("ZodCUID2",(e,t)=>{i_.init(e,t),oC.init(e,t)});function o2(e){return nE(o1,e)}let o9=i("ZodULID",(e,t)=>{iy.init(e,t),oC.init(e,t)});function o3(e){return nT(o9,e)}let o7=i("ZodXID",(e,t)=>{ib.init(e,t),oC.init(e,t)});function o5(e){return nA(o7,e)}let o8=i("ZodKSUID",(e,t)=>{ix.init(e,t),oC.init(e,t)});function ue(e){return nC(o8,e)}let ut=i("ZodIPv4",(e,t)=>{iS.init(e,t),oC.init(e,t)});function ui(e){return nL(ut,e)}let ur=i("ZodIPv6",(e,t)=>{iZ.init(e,t),oC.init(e,t)});function un(e){return nR(ur,e)}let ua=i("ZodCIDRv4",(e,t)=>{iU.init(e,t),oC.init(e,t)});function uo(e){return nF(ua,e)}let uu=i("ZodCIDRv6",(e,t)=>{iO.init(e,t),oC.init(e,t)});function us(e){return nJ(uu,e)}let ul=i("ZodBase64",(e,t)=>{iN.init(e,t),oC.init(e,t)});function ud(e){return nM(ul,e)}let uc=i("ZodBase64URL",(e,t)=>{iP.init(e,t),oC.init(e,t)});function um(e){return nW(uc,e)}let uf=i("ZodE164",(e,t)=>{iE.init(e,t),oC.init(e,t)});function up(e){return nB(uf,e)}let uv=i("ZodJWT",(e,t)=>{iA.init(e,t),oC.init(e,t)});function ug(e){return nV(uv,e)}let uh=i("ZodCustomStringFormat",(e,t)=>{iC.init(e,t),oC.init(e,t)});function u$(e,t,i={}){return on(uh,e,t,i)}function u_(e){return on(uh,"hostname",ol.hostname,e)}function uy(e){return on(uh,"hex",ol.hex,e)}function ub(e,t){let i=t?.enc??"hex",r=`${e}_${i}`,n=ol[r];if(!n)throw Error(`Unrecognized hash format: ${r}`);return on(uh,r,n,t)}let ux=i("ZodNumber",(e,t)=>{iL.init(e,t),oP.init(e,t),e.gt=(t,i)=>e.check(af(t,i)),e.gte=(t,i)=>e.check(ap(t,i)),e.min=(t,i)=>e.check(ap(t,i)),e.lt=(t,i)=>e.check(ac(t,i)),e.lte=(t,i)=>e.check(am(t,i)),e.max=(t,i)=>e.check(am(t,i)),e.int=t=>e.check(uw(t)),e.safe=t=>e.check(uw(t)),e.positive=t=>e.check(af(0,t)),e.nonnegative=t=>e.check(ap(0,t)),e.negative=t=>e.check(ac(0,t)),e.nonpositive=t=>e.check(am(0,t)),e.multipleOf=(t,i)=>e.check(a_(t,i)),e.step=(t,i)=>e.check(a_(t,i)),e.finite=()=>e;let i=e._zod.bag;e.minValue=Math.max(i.minimum??-1/0,i.exclusiveMinimum??-1/0)??null,e.maxValue=Math.min(i.maximum??1/0,i.exclusiveMaximum??1/0)??null,e.isInt=(i.format??"").includes("int")||Number.isSafeInteger(i.multipleOf??.5),e.isFinite=!0,e.format=i.format??null});function uk(e){return nH(ux,e)}let uI=i("ZodNumberFormat",(e,t)=>{iR.init(e,t),ux.init(e,t)});function uw(e){return n4(uI,e)}function uz(e){return n6(uI,e)}function uS(e){return n0(uI,e)}function uZ(e){return n1(uI,e)}function uU(e){return n2(uI,e)}let uO=i("ZodBoolean",(e,t)=>{iF.init(e,t),oP.init(e,t)});function uj(e){return n9(uO,e)}let uN=i("ZodBigInt",(e,t)=>{iJ.init(e,t),oP.init(e,t),e.gte=(t,i)=>e.check(ap(t,i)),e.min=(t,i)=>e.check(ap(t,i)),e.gt=(t,i)=>e.check(af(t,i)),e.gte=(t,i)=>e.check(ap(t,i)),e.min=(t,i)=>e.check(ap(t,i)),e.lt=(t,i)=>e.check(ac(t,i)),e.lte=(t,i)=>e.check(am(t,i)),e.max=(t,i)=>e.check(am(t,i)),e.positive=t=>e.check(af(BigInt(0),t)),e.negative=t=>e.check(ac(BigInt(0),t)),e.nonpositive=t=>e.check(am(BigInt(0),t)),e.nonnegative=t=>e.check(ap(BigInt(0),t)),e.multipleOf=(t,i)=>e.check(a_(t,i));let i=e._zod.bag;e.minValue=i.minimum??null,e.maxValue=i.maximum??null,e.format=i.format??null});function uD(e){return n7(uN,e)}let uP=i("ZodBigIntFormat",(e,t)=>{iM.init(e,t),uN.init(e,t)});function uE(e){return n8(uP,e)}function uT(e){return ae(uP,e)}let uA=i("ZodSymbol",(e,t)=>{iW.init(e,t),oP.init(e,t)});function uC(e){return at(uA,e)}let uL=i("ZodUndefined",(e,t)=>{iB.init(e,t),oP.init(e,t)});function uR(e){return ai(uL,e)}let uF=i("ZodNull",(e,t)=>{iV.init(e,t),oP.init(e,t)});function uJ(e){return ar(uF,e)}let uM=i("ZodAny",(e,t)=>{iG.init(e,t),oP.init(e,t)});function uW(){return an(uM)}let uB=i("ZodUnknown",(e,t)=>{iK.init(e,t),oP.init(e,t)});function uV(){return aa(uB)}let uG=i("ZodNever",(e,t)=>{iX.init(e,t),oP.init(e,t)});function uK(e){return ao(uG,e)}let uX=i("ZodVoid",(e,t)=>{iq.init(e,t),oP.init(e,t)});function uq(e){return au(uX,e)}let uY=i("ZodDate",(e,t)=>{iY.init(e,t),oP.init(e,t),e.min=(t,i)=>e.check(ap(t,i)),e.max=(t,i)=>e.check(am(t,i));let i=e._zod.bag;e.minDate=i.minimum?new Date(i.minimum):null,e.maxDate=i.maximum?new Date(i.maximum):null});function uH(e){return as(uY,e)}let uQ=i("ZodArray",(e,t)=>{iQ.init(e,t),oP.init(e,t),e.element=t.element,e.min=(t,i)=>e.check(aI(t,i)),e.nonempty=t=>e.check(aI(1,t)),e.max=(t,i)=>e.check(ak(t,i)),e.length=(t,i)=>e.check(aw(t,i)),e.unwrap=()=>e.element});function u4(e,t){return aL(uQ,e,t)}function u6(e){return sm(Object.keys(e._zod.def.shape))}let u0=i("ZodObject",(e,t)=>{i2.init(e,t),oP.init(e,t),od.defineLazy(e,"shape",()=>t.shape),e.keyof=()=>sm(Object.keys(e._zod.def.shape)),e.catchall=t=>e.clone({...e._zod.def,catchall:t}),e.passthrough=()=>e.clone({...e._zod.def,catchall:uV()}),e.loose=()=>e.clone({...e._zod.def,catchall:uV()}),e.strict=()=>e.clone({...e._zod.def,catchall:uK()}),e.strip=()=>e.clone({...e._zod.def,catchall:void 0}),e.extend=t=>od.extend(e,t),e.safeExtend=t=>od.safeExtend(e,t),e.merge=t=>od.merge(e,t),e.pick=t=>od.pick(e,t),e.omit=t=>od.omit(e,t),e.partial=(...t)=>od.partial(sy,e,t[0]),e.required=(...t)=>od.required(sU,e,t[0])});function u1(e,t){return new u0({type:"object",get shape(){return od.assignProp(this,"shape",e?od.objectClone(e):{}),this.shape},...od.normalizeParams(t)})}function u2(e,t){return new u0({type:"object",get shape(){return od.assignProp(this,"shape",od.objectClone(e)),this.shape},catchall:uK(),...od.normalizeParams(t)})}function u9(e,t){return new u0({type:"object",get shape(){return od.assignProp(this,"shape",od.objectClone(e)),this.shape},catchall:uV(),...od.normalizeParams(t)})}let u3=i("ZodUnion",(e,t)=>{i3.init(e,t),oP.init(e,t),e.options=t.options});function u7(e,t){return new u3({type:"union",options:e,...od.normalizeParams(t)})}let u5=i("ZodDiscriminatedUnion",(e,t)=>{u3.init(e,t),i7.init(e,t)});function u8(e,t,i){return new u5({type:"union",options:t,discriminator:e,...od.normalizeParams(i)})}let se=i("ZodIntersection",(e,t)=>{i5.init(e,t),oP.init(e,t)});function st(e,t){return new se({type:"intersection",left:e,right:t})}let si=i("ZodTuple",(e,t)=>{re.init(e,t),oP.init(e,t),e.rest=t=>e.clone({...e._zod.def,rest:t})});function sr(e,t,i){let r=t instanceof is,n=r?i:t;return new si({type:"tuple",items:e,rest:r?t:null,...od.normalizeParams(n)})}let sn=i("ZodRecord",(e,t)=>{ri.init(e,t),oP.init(e,t),e.keyType=t.keyType,e.valueType=t.valueType});function sa(e,t,i){return new sn({type:"record",keyType:e,valueType:t,...od.normalizeParams(i)})}function so(e,t,i){let r=R(e);return r._zod.values=void 0,new sn({type:"record",keyType:r,valueType:t,...od.normalizeParams(i)})}let su=i("ZodMap",(e,t)=>{rr.init(e,t),oP.init(e,t),e.keyType=t.keyType,e.valueType=t.valueType});function ss(e,t,i){return new su({type:"map",keyType:e,valueType:t,...od.normalizeParams(i)})}let sl=i("ZodSet",(e,t)=>{ra.init(e,t),oP.init(e,t),e.min=(...t)=>e.check(ab(...t)),e.nonempty=t=>e.check(ab(1,t)),e.max=(...t)=>e.check(ay(...t)),e.size=(...t)=>e.check(ax(...t))});function sd(e,t){return new sl({type:"set",valueType:e,...od.normalizeParams(t)})}let sc=i("ZodEnum",(e,t)=>{ru.init(e,t),oP.init(e,t),e.enum=t.entries,e.options=Object.values(t.entries);let i=new Set(Object.keys(t.entries));e.extract=(e,r)=>{let n={};for(let r of e)if(i.has(r))n[r]=t.entries[r];else throw Error(`Key ${r} not found in enum`);return new sc({...t,checks:[],...od.normalizeParams(r),entries:n})},e.exclude=(e,r)=>{let n={...t.entries};for(let t of e)if(i.has(t))delete n[t];else throw Error(`Key ${t} not found in enum`);return new sc({...t,checks:[],...od.normalizeParams(r),entries:n})}});function sm(e,t){return new sc({type:"enum",entries:Array.isArray(e)?Object.fromEntries(e.map(e=>[e,e])):e,...od.normalizeParams(t)})}function sf(e,t){return new sc({type:"enum",entries:e,...od.normalizeParams(t)})}let sp=i("ZodLiteral",(e,t)=>{rs.init(e,t),oP.init(e,t),e.values=new Set(t.values),Object.defineProperty(e,"value",{get(){if(t.values.length>1)throw Error("This schema contains multiple valid literal values. Use `.values` instead.");return t.values[0]}})});function sv(e,t){return new sp({type:"literal",values:Array.isArray(e)?e:[e],...od.normalizeParams(t)})}let sg=i("ZodFile",(e,t)=>{rl.init(e,t),oP.init(e,t),e.min=(t,i)=>e.check(ab(t,i)),e.max=(t,i)=>e.check(ay(t,i)),e.mime=(t,i)=>e.check(aD(Array.isArray(t)?t:[t],i))});function sh(e){return aq(sg,e)}let s$=i("ZodTransform",(e,t)=>{rd.init(e,t),oP.init(e,t),e._zod.parse=(i,r)=>{if("backward"===r.direction)throw new a(e.constructor.name);i.addIssue=r=>{"string"==typeof r?i.issues.push(od.issue(r,i.value,t)):(r.fatal&&(r.continue=!1),r.code??(r.code="custom"),r.input??(r.input=i.value),r.inst??(r.inst=e),i.issues.push(od.issue(r)))};let n=t.transform(i.value,i);return n instanceof Promise?n.then(e=>(i.value=e,i)):(i.value=n,i)}});function s_(e){return new s$({type:"transform",transform:e})}let sy=i("ZodOptional",(e,t)=>{rm.init(e,t),oP.init(e,t),e.unwrap=()=>e._zod.def.innerType});function sb(e){return new sy({type:"optional",innerType:e})}let sx=i("ZodNullable",(e,t)=>{rf.init(e,t),oP.init(e,t),e.unwrap=()=>e._zod.def.innerType});function sk(e){return new sx({type:"nullable",innerType:e})}function sI(e){return sb(sk(e))}let sw=i("ZodDefault",(e,t)=>{rp.init(e,t),oP.init(e,t),e.unwrap=()=>e._zod.def.innerType,e.removeDefault=e.unwrap});function sz(e,t){return new sw({type:"default",innerType:e,get defaultValue(){return"function"==typeof t?t():od.shallowClone(t)}})}let sS=i("ZodPrefault",(e,t)=>{rg.init(e,t),oP.init(e,t),e.unwrap=()=>e._zod.def.innerType});function sZ(e,t){return new sS({type:"prefault",innerType:e,get defaultValue(){return"function"==typeof t?t():od.shallowClone(t)}})}let sU=i("ZodNonOptional",(e,t)=>{rh.init(e,t),oP.init(e,t),e.unwrap=()=>e._zod.def.innerType});function sO(e,t){return new sU({type:"nonoptional",innerType:e,...od.normalizeParams(t)})}let sj=i("ZodSuccess",(e,t)=>{r_.init(e,t),oP.init(e,t),e.unwrap=()=>e._zod.def.innerType});function sN(e){return new sj({type:"success",innerType:e})}let sD=i("ZodCatch",(e,t)=>{ry.init(e,t),oP.init(e,t),e.unwrap=()=>e._zod.def.innerType,e.removeCatch=e.unwrap});function sP(e,t){return new sD({type:"catch",innerType:e,catchValue:"function"==typeof t?t:()=>t})}let sE=i("ZodNaN",(e,t)=>{rb.init(e,t),oP.init(e,t)});function sT(e){return ad(sE,e)}let sA=i("ZodPipe",(e,t)=>{rx.init(e,t),oP.init(e,t),e.in=t.in,e.out=t.out});function sC(e,t){return new sA({type:"pipe",in:e,out:t})}let sL=i("ZodCodec",(e,t)=>{sA.init(e,t),rI.init(e,t)});function sR(e,t,i){return new sL({type:"pipe",in:e,out:t,transform:i.decode,reverseTransform:i.encode})}let sF=i("ZodReadonly",(e,t)=>{rS.init(e,t),oP.init(e,t),e.unwrap=()=>e._zod.def.innerType});function sJ(e){return new sF({type:"readonly",innerType:e})}let sM=i("ZodTemplateLiteral",(e,t)=>{rU.init(e,t),oP.init(e,t)});function sW(e,t){return new sM({type:"template_literal",parts:e,...od.normalizeParams(t)})}let sB=i("ZodLazy",(e,t)=>{rN.init(e,t),oP.init(e,t),e.unwrap=()=>e._zod.def.getter()});function sV(e){return new sB({type:"lazy",getter:e})}let sG=i("ZodPromise",(e,t)=>{rj.init(e,t),oP.init(e,t),e.unwrap=()=>e._zod.def.innerType});function sK(e){return new sG({type:"promise",innerType:e})}let sX=i("ZodFunction",(e,t)=>{rO.init(e,t),oP.init(e,t)});function sq(e){return new sX({type:"function",input:Array.isArray(e?.input)?sr(e?.input):e?.input??u4(uV()),output:e?.output??uV()})}let sY=i("ZodCustom",(e,t)=>{rD.init(e,t),oP.init(e,t)});function sH(e){let t=new tB({check:"custom"});return t._zod.check=e,t}function sQ(e,t){return a8(sY,e??(()=>!0),t)}function s4(e,t={}){return oe(sY,e,t)}function s6(e){return ot(e)}function s0(e,t={error:`Input not instance of ${e.name}`}){let i=new sY({type:"custom",check:"custom",fn:t=>t instanceof e,abort:!0,...od.normalizeParams(t)});return i._zod.bag.Class=e,i}let s1=(...e)=>or({Codec:sL,Boolean:uO,String:oT},...e);function s2(e){let t=sV(()=>u7([oA(e),uk(),uj(),uJ(),u4(t),sa(oA(),t)]));return t}function s9(e,t){return sC(s_(e),t)}e.i(44493),e.s(["endsWith",()=>aj,"gt",()=>af,"gte",()=>ap,"includes",()=>aU,"length",()=>aw,"lowercase",()=>aS,"lt",()=>ac,"lte",()=>am,"maxLength",()=>ak,"maxSize",()=>ay,"mime",()=>aD,"minLength",()=>aI,"minSize",()=>ab,"multipleOf",()=>a_,"negative",()=>ag,"nonnegative",()=>a$,"nonpositive",()=>ah,"normalize",()=>aE,"overwrite",()=>aP,"positive",()=>av,"property",()=>aN,"regex",()=>az,"size",()=>ax,"startsWith",()=>aO,"toLowerCase",()=>aA,"toUpperCase",()=>aC,"trim",()=>aT,"uppercase",()=>aZ],32694),e.s([],5087),e.i(5087),e.i(32694),e.i(65155),e.i(57905),e.s(["$brand",()=>r,"ZodFirstPartyTypeKind",()=>rE,"ZodIssueCode",()=>s3,"config",()=>u,"getErrorMap",()=>s5,"setErrorMap",()=>s7],12929),e.s(["ZodFirstPartyTypeKind",()=>rE,"ZodIssueCode",()=>s3,"getErrorMap",()=>s5,"setErrorMap",()=>s7],63965);let s3={invalid_type:"invalid_type",too_big:"too_big",too_small:"too_small",invalid_format:"invalid_format",not_multiple_of:"not_multiple_of",unrecognized_keys:"unrecognized_keys",invalid_union:"invalid_union",invalid_key:"invalid_key",invalid_element:"invalid_element",invalid_value:"invalid_value",custom:"custom"};function s7(e){u({customError:e})}function s5(){return u().customError}rE||(rE={}),e.i(63965),e.i(12929);var ol=rA,od=rT,s8=nh,le=e.i(97514);function lt(e){return nI(oT,e)}function li(e){return nQ(ux,e)}function lr(e){return n3(uO,e)}function ln(e){return n5(uN,e)}function la(e){return al(uY,e)}e.s(["bigint",()=>ln,"boolean",()=>lr,"date",()=>la,"number",()=>li,"string",()=>lt],9099);var lo=e.i(9099),lu=e.i(61852)}];

//# sourceMappingURL=a4413_zod_v4_classic_external_6a187c9f.js.map