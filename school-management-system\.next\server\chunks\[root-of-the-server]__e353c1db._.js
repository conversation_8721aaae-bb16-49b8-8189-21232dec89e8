module.exports=[18622,(e,t,r)=>{t.exports=e.x("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js"))},56704,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/work-async-storage.external.js",()=>require("next/dist/server/app-render/work-async-storage.external.js"))},32319,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/work-unit-async-storage.external.js",()=>require("next/dist/server/app-render/work-unit-async-storage.external.js"))},20635,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/action-async-storage.external.js",()=>require("next/dist/server/app-render/action-async-storage.external.js"))},24725,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/after-task-async-storage.external.js",()=>require("next/dist/server/app-render/after-task-async-storage.external.js"))},29173,(e,t,r)=>{t.exports=e.x("@prisma/client",()=>require("@prisma/client"))},70406,(e,t,r)=>{t.exports=e.x("next/dist/compiled/@opentelemetry/api",()=>require("next/dist/compiled/@opentelemetry/api"))},93695,(e,t,r)=>{t.exports=e.x("next/dist/shared/lib/no-fallback-error.external.js",()=>require("next/dist/shared/lib/no-fallback-error.external.js"))},61724,(e,t,r)=>{t.exports=e.x("next/dist/compiled/next-server/app-route-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-route-turbo.runtime.prod.js"))},62902,(e,t,r)=>{},39063,e=>{"use strict";e.s(["handler",()=>N,"patchFetch",()=>j,"routeModule",()=>E,"serverHooks",()=>b,"workAsyncStorage",()=>C,"workUnitAsyncStorage",()=>A],39063);var t=e.i(6137),r=e.i(11365),a=e.i(9638),n=e.i(15243),s=e.i(66378),o=e.i(92101),i=e.i(50012),l=e.i(62885),d=e.i(31409),u=e.i(78448),c=e.i(28015),p=e.i(72721),x=e.i(75714),h=e.i(12634),R=e.i(93695);e.i(74732);var m=e.i(66662);e.s(["GET",()=>f,"POST",()=>w],30404);var v=e.i(2835),g=e.i(31279);async function f(e){try{let{searchParams:t}=new URL(e.url),r=t.get("date"),a=t.get("classId"),n=t.get("sectionId"),s={};r&&(s.date=new Date(r)),a&&"all"!==a&&(s.classId=a),n&&"all"!==n&&(s.sectionId=n);let o=await g.prisma.attendance.findMany({where:s,include:{student:{include:{user:!0}},class:!0,section:!0,subject:!0,takenByTeacher:{include:{user:!0}}},orderBy:{date:"desc"}});return v.NextResponse.json(o)}catch(e){return console.error("Error fetching attendance:",e),v.NextResponse.json({error:"Internal server error"},{status:500})}}async function w(e){try{let{studentId:t,date:r,status:a,remarks:n,classId:s,sectionId:o,subjectId:i,takenByTeacherId:l}=await e.json(),d=await g.prisma.attendance.create({data:{studentId:t,date:new Date(r),status:a,remarks:n,classId:s,sectionId:o,subjectId:i,takenByTeacherId:l}});return v.NextResponse.json(d)}catch(e){return console.error("Error creating attendance:",e),v.NextResponse.json({error:"Internal server error"},{status:500})}}var y=e.i(30404);let E=new t.AppRouteRouteModule({definition:{kind:r.RouteKind.APP_ROUTE,page:"/api/admin/attendance/route",pathname:"/api/admin/attendance",filename:"route",bundlePath:""},distDir:".next",relativeProjectDir:"",resolvedPagePath:"[project]/school-management-system/src/app/api/admin/attendance/route.ts",nextConfigOutput:"",userland:y}),{workAsyncStorage:C,workUnitAsyncStorage:A,serverHooks:b}=E;function j(){return(0,a.patchFetch)({workAsyncStorage:C,workUnitAsyncStorage:A})}async function N(e,t,a){var v;let g="/api/admin/attendance/route";g=g.replace(/\/index$/,"")||"/";let f=await E.prepare(e,t,{srcPage:g,multiZoneDraftMode:!1});if(!f)return t.statusCode=400,t.end("Bad Request"),null==a.waitUntil||a.waitUntil.call(a,Promise.resolve()),null;let{buildId:w,params:y,nextConfig:C,isDraftMode:A,prerenderManifest:b,routerServerContext:j,isOnDemandRevalidate:N,revalidateOnlyGenerated:T,resolvedPathname:q}=f,P=(0,o.normalizeAppPath)(g),k=!!(b.dynamicRoutes[P]||b.routes[q]);if(k&&!A){let e=!!b.routes[q],t=b.dynamicRoutes[P];if(t&&!1===t.fallback&&!e)throw new R.NoFallbackError}let O=null;!k||E.isDev||A||(O="/index"===(O=q)?"/":O);let I=!0===E.isDev||!k,_=k&&!I,S=e.method||"GET",H=(0,s.getTracer)(),U=H.getActiveScopeSpan(),M={params:y,prerenderManifest:b,renderOpts:{experimental:{cacheComponents:!!C.experimental.cacheComponents,authInterrupts:!!C.experimental.authInterrupts},supportsDynamicResponse:I,incrementalCache:(0,n.getRequestMeta)(e,"incrementalCache"),cacheLifeProfiles:null==(v=C.experimental)?void 0:v.cacheLife,isRevalidate:_,waitUntil:a.waitUntil,onClose:e=>{t.on("close",e)},onAfterTaskError:void 0,onInstrumentationRequestError:(t,r,a)=>E.onRequestError(e,t,a,j)},sharedContext:{buildId:w}},D=new i.NodeNextRequest(e),$=new i.NodeNextResponse(t),F=l.NextRequestAdapter.fromNodeNextRequest(D,(0,l.signalFromNodeResponse)(t));try{let o=async r=>E.handle(F,M).finally(()=>{if(!r)return;r.setAttributes({"http.status_code":t.statusCode,"next.rsc":!1});let a=H.getRootSpanAttributes();if(!a)return;if(a.get("next.span_type")!==d.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${a.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let n=a.get("next.route");if(n){let e=`${S} ${n}`;r.setAttributes({"next.route":n,"http.route":n,"next.span_name":e}),r.updateName(e)}else r.updateName(`${S} ${e.url}`)}),i=async s=>{var i,l;let d=async({previousCacheEntry:r})=>{try{if(!(0,n.getRequestMeta)(e,"minimalMode")&&N&&T&&!r)return t.statusCode=404,t.setHeader("x-nextjs-cache","REVALIDATED"),t.end("This page could not be found"),null;let i=await o(s);e.fetchMetrics=M.renderOpts.fetchMetrics;let l=M.renderOpts.pendingWaitUntil;l&&a.waitUntil&&(a.waitUntil(l),l=void 0);let d=M.renderOpts.collectedTags;if(!k)return await (0,c.sendResponse)(D,$,i,M.renderOpts.pendingWaitUntil),null;{let e=await i.blob(),t=(0,p.toNodeOutgoingHttpHeaders)(i.headers);d&&(t[h.NEXT_CACHE_TAGS_HEADER]=d),!t["content-type"]&&e.type&&(t["content-type"]=e.type);let r=void 0!==M.renderOpts.collectedRevalidate&&!(M.renderOpts.collectedRevalidate>=h.INFINITE_CACHE)&&M.renderOpts.collectedRevalidate,a=void 0===M.renderOpts.collectedExpire||M.renderOpts.collectedExpire>=h.INFINITE_CACHE?void 0:M.renderOpts.collectedExpire;return{value:{kind:m.CachedRouteKind.APP_ROUTE,status:i.status,body:Buffer.from(await e.arrayBuffer()),headers:t},cacheControl:{revalidate:r,expire:a}}}}catch(t){throw(null==r?void 0:r.isStale)&&await E.onRequestError(e,t,{routerKind:"App Router",routePath:g,routeType:"route",revalidateReason:(0,u.getRevalidateReason)({isRevalidate:_,isOnDemandRevalidate:N})},j),t}},R=await E.handleResponse({req:e,nextConfig:C,cacheKey:O,routeKind:r.RouteKind.APP_ROUTE,isFallback:!1,prerenderManifest:b,isRoutePPREnabled:!1,isOnDemandRevalidate:N,revalidateOnlyGenerated:T,responseGenerator:d,waitUntil:a.waitUntil});if(!k)return null;if((null==R||null==(i=R.value)?void 0:i.kind)!==m.CachedRouteKind.APP_ROUTE)throw Object.defineProperty(Error(`Invariant: app-route received invalid cache entry ${null==R||null==(l=R.value)?void 0:l.kind}`),"__NEXT_ERROR_CODE",{value:"E701",enumerable:!1,configurable:!0});(0,n.getRequestMeta)(e,"minimalMode")||t.setHeader("x-nextjs-cache",N?"REVALIDATED":R.isMiss?"MISS":R.isStale?"STALE":"HIT"),A&&t.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");let v=(0,p.fromNodeOutgoingHttpHeaders)(R.value.headers);return(0,n.getRequestMeta)(e,"minimalMode")&&k||v.delete(h.NEXT_CACHE_TAGS_HEADER),!R.cacheControl||t.getHeader("Cache-Control")||v.get("Cache-Control")||v.set("Cache-Control",(0,x.getCacheControlHeader)(R.cacheControl)),await (0,c.sendResponse)(D,$,new Response(R.value.body,{headers:v,status:R.value.status||200})),null};U?await i(U):await H.withPropagatedContext(e.headers,()=>H.trace(d.BaseServerSpan.handleRequest,{spanName:`${S} ${e.url}`,kind:s.SpanKind.SERVER,attributes:{"http.method":S,"http.target":e.url}},i))}catch(t){if(U||t instanceof R.NoFallbackError||await E.onRequestError(e,t,{routerKind:"App Router",routePath:P,routeType:"route",revalidateReason:(0,u.getRevalidateReason)({isRevalidate:_,isOnDemandRevalidate:N})}),k)throw t;return await (0,c.sendResponse)(D,$,new Response(null,{status:500})),null}}}];

//# sourceMappingURL=%5Broot-of-the-server%5D__e353c1db._.js.map