{"version": 3, "sources": ["turbopack:///[project]/school-management-system/src/app/(dash)/teacher/marks/[examId]/view/page.tsx", "turbopack:///[project]/school-management-system/src/components/marks/marks-table.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { usePara<PERSON>, useRouter } from 'next/navigation'\nimport { useSession } from 'next-auth/react'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Button } from '@/components/ui/button'\nimport DashboardLayout from '@/components/layout/dashboard-layout'\nimport { teacherNavigation } from '@/lib/navigation'\nimport MarksTable from '@/components/marks/marks-table'\nimport {\n  Calendar,\n  Award,\n  BookOpen,\n  Users,\n  ArrowLeft,\n  Edit,\n  Download,\n  AlertCircle,\n  CheckCircle\n} from 'lucide-react'\nimport Link from 'next/link'\n\ninterface Student {\n  id: string\n  admissionNo: string\n  rollNumber: string\n  firstName: string\n  lastName: string\n  email: string\n  className: string\n  sectionName: string\n  currentMark: {\n    id: string\n    obtainedMarks: number\n    remarks?: string\n    createdAt: string\n    updatedAt: string\n  } | null\n  hasMarks: boolean\n}\n\ninterface Exam {\n  id: string\n  name: string\n  maxMarks: number\n  date: string\n  subject: {\n    id: string\n    name: string\n    code: string\n    class: {\n      id: string\n      name: string\n    }\n  }\n  term: {\n    id: string\n    name: string\n  }\n}\n\ninterface ExamData {\n  exam: Exam\n  students: Student[]\n}\n\n\n\nexport default function TeacherMarksViewPage() {\n  const params = useParams()\n  const router = useRouter()\n  const { data: session } = useSession()\n  const examId = params.examId as string\n\n  const [examData, setExamData] = useState<ExamData | null>(null)\n  const [loading, setLoading] = useState(true)\n\n  useEffect(() => {\n    fetchExamData()\n  }, [examId])\n\n  const fetchExamData = async () => {\n    try {\n      setLoading(true)\n      const response = await fetch(`/api/teacher/exams/${examId}/students`)\n      if (response.ok) {\n        const data = await response.json()\n        setExamData(data)\n      } else {\n        console.error('Failed to fetch exam data')\n      }\n    } catch (error) {\n      console.error('Error fetching exam data:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handleEdit = (examId: string) => {\n    router.push(`/teacher/marks/${examId}`)\n  }\n\n\n\n  if (loading) {\n    return (\n      <DashboardLayout title=\"View Marks\" navigation={teacherNavigation}>\n        <div className=\"text-center py-8\">\n          <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto\"></div>\n          <p className=\"mt-2 text-gray-600\">Loading exam data...</p>\n        </div>\n      </DashboardLayout>\n    )\n  }\n\n  if (!examData) {\n    return (\n      <DashboardLayout title=\"View Marks\" navigation={teacherNavigation}>\n        <div className=\"text-center py-8\">\n          <AlertCircle className=\"h-12 w-12 text-red-400 mx-auto mb-4\" />\n          <p className=\"text-gray-600\">Exam not found</p>\n          <Link href=\"/teacher/marks\">\n            <Button className=\"mt-4\">\n              <ArrowLeft className=\"w-4 h-4 mr-2\" />\n              Back to Marks\n            </Button>\n          </Link>\n        </div>\n      </DashboardLayout>\n    )\n  }\n\n  const studentsWithMarks = examData.students.filter(s => s.hasMarks)\n  const studentsWithoutMarks = examData.students.filter(s => !s.hasMarks)\n  \n  const stats = {\n    totalStudents: examData.students.length,\n    gradedStudents: studentsWithMarks.length,\n    pendingStudents: studentsWithoutMarks.length,\n    averageMarks: studentsWithMarks.length > 0 \n      ? Math.round(studentsWithMarks.reduce((sum, s) => sum + (s.currentMark?.obtainedMarks || 0), 0) / studentsWithMarks.length * 100) / 100\n      : 0,\n    averagePercentage: studentsWithMarks.length > 0 \n      ? Math.round(studentsWithMarks.reduce((sum, s) => sum + ((s.currentMark?.obtainedMarks || 0) / examData.exam.maxMarks * 100), 0) / studentsWithMarks.length * 100) / 100\n      : 0\n  }\n\n  return (\n    <DashboardLayout title=\"View Marks\" navigation={teacherNavigation}>\n      <div className=\"space-y-6\">\n        {/* Header */}\n        <div className=\"flex flex-col space-y-4 sm:flex-row sm:justify-between sm:items-center sm:space-y-0\">\n          <div>\n            <div className=\"flex items-center space-x-2 mb-2\">\n              <Link href=\"/teacher/marks\">\n                <Button variant=\"outline\" size=\"sm\">\n                  <ArrowLeft className=\"w-4 h-4 mr-1\" />\n                  Back\n                </Button>\n              </Link>\n            </div>\n            <h1 className=\"text-xl sm:text-2xl font-bold text-gray-900\">View Marks</h1>\n            <p className=\"text-sm sm:text-base text-gray-600\">\n              {examData.exam.name} - {examData.exam.subject.name} ({examData.exam.subject.class.name})\n            </p>\n          </div>\n          <div className=\"flex space-x-2\">\n            <Link href={`/teacher/marks/${examId}`}>\n              <Button variant=\"outline\">\n                <Edit className=\"w-4 h-4 mr-2\" />\n                Edit Marks\n              </Button>\n            </Link>\n            <Button variant=\"outline\">\n              <Download className=\"w-4 h-4 mr-2\" />\n              Export\n            </Button>\n          </div>\n        </div>\n\n        {/* Stats Cards */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6\">\n          <Card>\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n              <CardTitle className=\"text-sm font-medium\">Total Students</CardTitle>\n              <Users className=\"h-4 w-4 text-muted-foreground\" />\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-2xl font-bold\">{stats.totalStudents}</div>\n            </CardContent>\n          </Card>\n          <Card>\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n              <CardTitle className=\"text-sm font-medium\">Graded</CardTitle>\n              <CheckCircle className=\"h-4 w-4 text-green-600\" />\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-2xl font-bold text-green-600\">{stats.gradedStudents}</div>\n            </CardContent>\n          </Card>\n          <Card>\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n              <CardTitle className=\"text-sm font-medium\">Pending</CardTitle>\n              <AlertCircle className=\"h-4 w-4 text-orange-600\" />\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-2xl font-bold text-orange-600\">{stats.pendingStudents}</div>\n            </CardContent>\n          </Card>\n          <Card>\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n              <CardTitle className=\"text-sm font-medium\">Average Marks</CardTitle>\n              <Award className=\"h-4 w-4 text-blue-600\" />\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-2xl font-bold text-blue-600\">{stats.averageMarks}</div>\n            </CardContent>\n          </Card>\n          <Card>\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n              <CardTitle className=\"text-sm font-medium\">Average %</CardTitle>\n              <Award className=\"h-4 w-4 text-blue-600\" />\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-2xl font-bold text-blue-600\">{stats.averagePercentage}%</div>\n            </CardContent>\n          </Card>\n        </div>\n\n        {/* Exam Info */}\n        <Card>\n          <CardHeader>\n            <CardTitle className=\"flex items-center space-x-2\">\n              <BookOpen className=\"w-5 h-5\" />\n              <span>Exam Details</span>\n            </CardTitle>\n          </CardHeader>\n          <CardContent>\n            <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4 text-sm\">\n              <div>\n                <span className=\"font-medium text-gray-700\">Subject:</span>\n                <p className=\"text-gray-600\">{examData.exam.subject.name}</p>\n              </div>\n              <div>\n                <span className=\"font-medium text-gray-700\">Class:</span>\n                <p className=\"text-gray-600\">{examData.exam.subject.class.name}</p>\n              </div>\n              <div>\n                <span className=\"font-medium text-gray-700\">Max Marks:</span>\n                <p className=\"text-gray-600\">{examData.exam.maxMarks}</p>\n              </div>\n              <div>\n                <span className=\"font-medium text-gray-700\">Date:</span>\n                <p className=\"text-gray-600\">{new Date(examData.exam.date).toLocaleDateString()}</p>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n\n        {/* Marks Table */}\n        <MarksTable\n          exam={examData.exam}\n          students={examData.students}\n          onEdit={handleEdit}\n          showActions={true}\n        />\n      </div>\n    </DashboardLayout>\n  )\n}\n", "'use client'\n\nimport { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Button } from '@/components/ui/button'\nimport { \n  Users,\n  CheckCircle,\n  AlertCircle,\n  Eye,\n  Edit\n} from 'lucide-react'\n\ninterface Student {\n  id: string\n  admissionNo: string\n  rollNumber: string\n  firstName: string\n  lastName: string\n  email: string\n  className: string\n  sectionName: string\n  currentMark: {\n    id: string\n    obtainedMarks: number\n    remarks?: string\n    createdAt: string\n    updatedAt: string\n  } | null\n  hasMarks: boolean\n}\n\ninterface Exam {\n  id: string\n  name: string\n  maxMarks: number\n  date: string\n  subject: {\n    id: string\n    name: string\n    code: string\n    class: {\n      id: string\n      name: string\n    }\n  }\n  term: {\n    id: string\n    name: string\n  }\n}\n\ninterface MarksTableProps {\n  exam: Exam\n  students: Student[]\n  onEdit?: (examId: string) => void\n  showActions?: boolean\n}\n\nexport default function MarksTable({ exam, students, onEdit, showActions = true }: MarksTableProps) {\n  const getGradeColor = (percentage: number) => {\n    if (percentage >= 90) return 'bg-green-100 text-green-800'\n    if (percentage >= 80) return 'bg-blue-100 text-blue-800'\n    if (percentage >= 70) return 'bg-yellow-100 text-yellow-800'\n    if (percentage >= 60) return 'bg-orange-100 text-orange-800'\n    return 'bg-red-100 text-red-800'\n  }\n\n  const getGrade = (percentage: number) => {\n    if (percentage >= 90) return 'A+'\n    if (percentage >= 80) return 'A'\n    if (percentage >= 70) return 'B+'\n    if (percentage >= 60) return 'B'\n    if (percentage >= 50) return 'C+'\n    if (percentage >= 40) return 'C'\n    if (percentage >= 30) return 'D'\n    return 'F'\n  }\n\n  const studentsWithMarks = students.filter(s => s.hasMarks)\n  const studentsWithoutMarks = students.filter(s => !s.hasMarks)\n  \n  const stats = {\n    totalStudents: students.length,\n    gradedStudents: studentsWithMarks.length,\n    pendingStudents: studentsWithoutMarks.length,\n    averageMarks: studentsWithMarks.length > 0 \n      ? Math.round(studentsWithMarks.reduce((sum, s) => sum + (s.currentMark?.obtainedMarks || 0), 0) / studentsWithMarks.length * 100) / 100\n      : 0,\n    averagePercentage: studentsWithMarks.length > 0 \n      ? Math.round(studentsWithMarks.reduce((sum, s) => sum + ((s.currentMark?.obtainedMarks || 0) / exam.maxMarks * 100), 0) / studentsWithMarks.length * 100) / 100\n      : 0\n  }\n\n  return (\n    <Card>\n      <CardHeader>\n        <CardTitle className=\"flex items-center justify-between\">\n          <div className=\"flex items-center space-x-2\">\n            <Users className=\"w-5 h-5\" />\n            <span>Student Marks</span>\n          </div>\n          {showActions && onEdit && (\n            <Button variant=\"outline\" onClick={() => onEdit(exam.id)}>\n              <Edit className=\"w-4 h-4 mr-2\" />\n              Edit Marks\n            </Button>\n          )}\n        </CardTitle>\n        <CardDescription>\n          Marks for {exam.name} - {exam.subject.name} ({exam.subject.class.name})\n        </CardDescription>\n        \n        {/* Quick Stats */}\n        <div className=\"grid grid-cols-2 md:grid-cols-5 gap-4 text-sm\">\n          <div className=\"text-center p-2 bg-gray-50 rounded\">\n            <div className=\"font-semibold text-gray-900\">{stats.totalStudents}</div>\n            <div className=\"text-gray-600\">Total</div>\n          </div>\n          <div className=\"text-center p-2 bg-green-50 rounded\">\n            <div className=\"font-semibold text-green-800\">{stats.gradedStudents}</div>\n            <div className=\"text-green-600\">Graded</div>\n          </div>\n          <div className=\"text-center p-2 bg-orange-50 rounded\">\n            <div className=\"font-semibold text-orange-800\">{stats.pendingStudents}</div>\n            <div className=\"text-orange-600\">Pending</div>\n          </div>\n          <div className=\"text-center p-2 bg-blue-50 rounded\">\n            <div className=\"font-semibold text-blue-800\">{stats.averageMarks}</div>\n            <div className=\"text-blue-600\">Avg Marks</div>\n          </div>\n          <div className=\"text-center p-2 bg-blue-50 rounded\">\n            <div className=\"font-semibold text-blue-800\">{stats.averagePercentage}%</div>\n            <div className=\"text-blue-600\">Avg %</div>\n          </div>\n        </div>\n      </CardHeader>\n      <CardContent>\n        {students.length === 0 ? (\n          <div className=\"text-center py-8\">\n            <Users className=\"h-12 w-12 text-gray-400 mx-auto mb-4\" />\n            <p className=\"text-gray-600\">No students found for this exam</p>\n          </div>\n        ) : (\n          <div className=\"space-y-4\">\n            {/* Desktop Table */}\n            <div className=\"hidden lg:block overflow-x-auto\">\n              <table className=\"min-w-full divide-y divide-gray-200\">\n                <thead className=\"bg-gray-50\">\n                  <tr>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      Student\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      Roll No.\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      Section\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      Marks\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      Percentage\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      Grade\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      Remarks\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      Status\n                    </th>\n                  </tr>\n                </thead>\n                <tbody className=\"bg-white divide-y divide-gray-200\">\n                  {students.map((student) => {\n                    const percentage = student.currentMark \n                      ? Math.round((student.currentMark.obtainedMarks / exam.maxMarks) * 100 * 100) / 100\n                      : 0\n                    const grade = student.currentMark ? getGrade(percentage) : '-'\n                    \n                    return (\n                      <tr key={student.id}>\n                        <td className=\"px-6 py-4 whitespace-nowrap\">\n                          <div>\n                            <div className=\"text-sm font-medium text-gray-900\">\n                              {student.firstName} {student.lastName}\n                            </div>\n                            <div className=\"text-sm text-gray-500\">\n                              {student.admissionNo}\n                            </div>\n                          </div>\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                          {student.rollNumber || '-'}\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                          {student.sectionName || '-'}\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                          {student.currentMark \n                            ? `${student.currentMark.obtainedMarks}/${exam.maxMarks}`\n                            : '-'\n                          }\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                          {student.currentMark ? `${percentage}%` : '-'}\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap\">\n                          {student.currentMark ? (\n                            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getGradeColor(percentage)}`}>\n                              {grade}\n                            </span>\n                          ) : (\n                            <span className=\"text-gray-400\">-</span>\n                          )}\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                          {student.currentMark?.remarks || '-'}\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap\">\n                          {student.hasMarks ? (\n                            <span className=\"inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800\">\n                              <CheckCircle className=\"w-3 h-3 mr-1\" />\n                              Graded\n                            </span>\n                          ) : (\n                            <span className=\"inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800\">\n                              <AlertCircle className=\"w-3 h-3 mr-1\" />\n                              Pending\n                            </span>\n                          )}\n                        </td>\n                      </tr>\n                    )\n                  })}\n                </tbody>\n              </table>\n            </div>\n\n            {/* Mobile Cards */}\n            <div className=\"lg:hidden space-y-4\">\n              {students.map((student) => {\n                const percentage = student.currentMark \n                  ? Math.round((student.currentMark.obtainedMarks / exam.maxMarks) * 100 * 100) / 100\n                  : 0\n                const grade = student.currentMark ? getGrade(percentage) : '-'\n                \n                return (\n                  <Card key={student.id} className=\"p-4\">\n                    <div className=\"space-y-3\">\n                      <div className=\"flex items-start justify-between\">\n                        <div className=\"flex-1 min-w-0\">\n                          <h3 className=\"text-lg font-medium text-gray-900 truncate\">\n                            {student.firstName} {student.lastName}\n                          </h3>\n                          <p className=\"text-sm text-gray-500\">\n                            {student.admissionNo} • Roll: {student.rollNumber || '-'} • Section: {student.sectionName || '-'}\n                          </p>\n                        </div>\n                        <div className=\"ml-4\">\n                          {student.hasMarks ? (\n                            <span className=\"inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800\">\n                              <CheckCircle className=\"w-3 h-3 mr-1\" />\n                              Graded\n                            </span>\n                          ) : (\n                            <span className=\"inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800\">\n                              <AlertCircle className=\"w-3 h-3 mr-1\" />\n                              Pending\n                            </span>\n                          )}\n                        </div>\n                      </div>\n                      \n                      {student.currentMark ? (\n                        <div className=\"grid grid-cols-2 gap-4 text-sm\">\n                          <div>\n                            <span className=\"font-medium text-gray-700\">Marks:</span>\n                            <p className=\"text-gray-600\">{student.currentMark.obtainedMarks}/{exam.maxMarks}</p>\n                          </div>\n                          <div>\n                            <span className=\"font-medium text-gray-700\">Percentage:</span>\n                            <p className=\"text-gray-600 font-semibold\">{percentage}%</p>\n                          </div>\n                          <div>\n                            <span className=\"font-medium text-gray-700\">Grade:</span>\n                            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getGradeColor(percentage)}`}>\n                              {grade}\n                            </span>\n                          </div>\n                          <div>\n                            <span className=\"font-medium text-gray-700\">Remarks:</span>\n                            <p className=\"text-gray-600\">{student.currentMark.remarks || '-'}</p>\n                          </div>\n                        </div>\n                      ) : (\n                        <div className=\"text-center py-4 text-gray-500\">\n                          <AlertCircle className=\"h-8 w-8 mx-auto mb-2\" />\n                          <p>No marks entered yet</p>\n                        </div>\n                      )}\n                    </div>\n                  </Card>\n                )\n              })}\n            </div>\n          </div>\n        )}\n      </CardContent>\n    </Card>\n  )\n}\n"], "names": [], "mappings": "qFAEA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,MACA,EAAA,EAAA,CAAA,CAAA,MACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OCJA,EAAA,EAAA,CAAA,CAAA,OAAA,EAAA,EAAA,CAAA,CAAA,OAAA,EAAA,EAAA,CAAA,CAAA,OAAA,EAAA,EAAA,CAAA,CAAA,OAsDe,SAAS,EAAW,MAAE,CAAI,CAAE,UAAQ,QAAE,CAAM,CAAE,eAAc,CAAI,CAAmB,EAChG,IAAM,EAAgB,AAAC,GACrB,AAAI,GAAc,GAAW,CAAP,6BAClB,GAAc,GAAW,CAAP,2BAClB,GAAc,GAAW,CAAP,+BAClB,GAAc,GAAW,CAAP,+BACf,0BAGH,EAAW,AAAC,GAChB,AAAI,GAAc,GAAW,CAAP,IAClB,GAAc,GAAW,CAAP,GAClB,GAAc,GAAW,CAAP,IAClB,GAAc,GAAW,CAAP,GAClB,GAAc,GAAW,CAAP,IAClB,GAAc,GAAW,CAAP,GAClB,GAAc,GAAW,CAAP,GACf,IAGH,EAAoB,EAAS,MAAM,CAAC,GAAK,EAAE,QAAQ,EACnD,EAAuB,EAAS,MAAM,CAAC,GAAK,CAAC,EAAE,QAAQ,EAEvD,EAAQ,CACZ,cAAe,EAAS,MAAM,CAC9B,eAAgB,EAAkB,MAAM,CACxC,gBAAiB,EAAqB,MAAM,CAC5C,aAAc,EAAkB,MAAM,CAAG,EACrC,KAAK,KAAK,CAAC,EAAkB,MAAM,CAAC,CAAC,EAAK,IAAM,GAAO,EAAE,CAAH,UAAc,EAAE,gBAAiB,CAAC,CAAG,GAAK,EAAkB,MAAM,CAAG,KAAO,IAClI,EACJ,kBAAmB,EAAkB,MAAM,CAAG,EAC1C,KAAK,KAAK,CAAC,EAAkB,MAAM,CAAC,CAAC,EAAK,IAAM,EAAO,CAAC,EAAE,WAAW,EAAE,gBAAiB,CAAC,CAAI,EAAK,QAAQ,CAAG,IAAM,GAAK,EAAkB,MAAM,CAAG,KAAO,IAC1J,CACN,EAEA,MACE,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,IAAI,CAAA,WACH,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,UAAU,CAAA,WACT,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,SAAS,CAAA,CAAC,UAAU,8CACnB,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,wCACb,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,KAAK,CAAA,CAAC,UAAU,YACjB,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,UAAK,qBAEP,GAAe,GACd,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,MAAM,CAAA,CAAC,QAAQ,UAAU,QAAS,IAAM,EAAO,EAAK,EAAE,YACrD,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,IAAI,CAAA,CAAC,UAAU,iBAAiB,mBAKvC,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,eAAe,CAAA,WAAC,aACJ,EAAK,IAAI,CAAC,MAAI,EAAK,OAAO,CAAC,IAAI,CAAC,KAAG,EAAK,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,OAIxE,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,0DACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,+CACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,uCAA+B,EAAM,aAAa,GACjE,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,yBAAgB,aAEjC,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,gDACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,wCAAgC,EAAM,cAAc,GACnE,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,0BAAiB,cAElC,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,iDACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,yCAAiC,EAAM,eAAe,GACrE,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,2BAAkB,eAEnC,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,+CACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,uCAA+B,EAAM,YAAY,GAChE,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,yBAAgB,iBAEjC,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,+CACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,wCAA+B,EAAM,iBAAiB,CAAC,OACtE,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,yBAAgB,mBAIrC,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,WAAW,CAAA,UACW,IAApB,EAAS,MAAM,CACd,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,6BACb,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,KAAK,CAAA,CAAC,UAAU,yCACjB,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,yBAAgB,uCAG/B,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,sBAEb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,2CACb,CAAA,EAAA,EAAA,IAAA,EAAC,QAAA,CAAM,UAAU,gDACf,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CAAM,UAAU,sBACf,CAAA,EAAA,EAAA,IAAA,EAAC,KAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,0FAAiF,YAG/F,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,0FAAiF,aAG/F,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,0FAAiF,YAG/F,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,0FAAiF,UAG/F,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,0FAAiF,eAG/F,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,0FAAiF,UAG/F,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,0FAAiF,YAG/F,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,0FAAiF,gBAKnG,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CAAM,UAAU,6CACd,EAAS,GAAG,CAAC,AAAC,IACb,IAAM,EAAa,EAAQ,WAAW,CAClC,KAAK,KAAK,GAAU,WAAW,CAAC,aAAa,CAAG,EAAK,QAAQ,CAAjD,EAAqD,GAAa,GAAP,CACvE,EACE,EAAQ,EAAQ,WAAW,CAAG,EAAS,GAAc,IAE3D,MACE,CAAA,EAAA,EAAA,IAAA,EAAC,KAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,uCACZ,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,WACC,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,8CACZ,EAAQ,SAAS,CAAC,IAAE,EAAQ,QAAQ,IAEvC,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,iCACZ,EAAQ,WAAW,QAI1B,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,6DACX,EAAQ,UAAU,EAAI,MAEzB,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,6DACX,EAAQ,WAAW,EAAI,MAE1B,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,6DACX,EAAQ,WAAW,CAChB,CAAA,EAAG,EAAQ,WAAW,CAAC,aAAa,CAAC,CAAC,EAAE,EAAK,QAAQ,CAAA,CAAE,CACvD,MAGN,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,6DACX,EAAQ,WAAW,CAAG,CAAA,EAAG,EAAW,CAAC,CAAC,CAAG,MAE5C,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,uCACX,EAAQ,WAAW,CAClB,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAW,CAAC,yDAAyD,EAAE,EAAc,GAAA,CAAa,UACrG,IAGH,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAU,yBAAgB,QAGpC,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,6DACX,EAAQ,WAAW,EAAE,SAAW,MAEnC,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,uCACX,EAAQ,QAAQ,CACf,CAAA,EAAA,EAAA,IAAA,EAAC,OAAA,CAAK,UAAU,8GACd,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,WAAW,CAAA,CAAC,UAAU,iBAAiB,YAI1C,CAAA,EAAA,EAAA,IAAA,EAAC,OAAA,CAAK,UAAU,4GACd,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,WAAW,CAAA,CAAC,UAAU,iBAAiB,iBA9CvC,EAAQ,EAAE,CAqDvB,UAMN,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,+BACZ,EAAS,GAAG,CAAC,AAAC,IACb,IAAM,EAAa,EAAQ,WAAW,CAClC,KAAK,KAAK,CAAE,EAAQ,WAAW,CAAC,aAAa,CAAG,EAAK,QAAQ,GAAI,GAAa,GAAP,CACvE,EACE,EAAQ,EAAQ,WAAW,CAAG,EAAS,GAAc,IAE3D,MACE,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,IAAI,CAAA,CAAkB,UAAU,eAC/B,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,sBACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,6CACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,2BACb,CAAA,EAAA,EAAA,IAAA,EAAC,KAAA,CAAG,UAAU,uDACX,EAAQ,SAAS,CAAC,IAAE,EAAQ,QAAQ,IAEvC,CAAA,EAAA,EAAA,IAAA,EAAC,IAAA,CAAE,UAAU,kCACV,EAAQ,WAAW,CAAC,YAAU,EAAQ,UAAU,EAAI,IAAI,eAAa,EAAQ,WAAW,EAAI,UAGjG,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,gBACZ,EAAQ,QAAQ,CACf,CAAA,EAAA,EAAA,IAAA,EAAC,OAAA,CAAK,UAAU,8GACd,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,WAAW,CAAA,CAAC,UAAU,iBAAiB,YAI1C,CAAA,EAAA,EAAA,IAAA,EAAC,OAAA,CAAK,UAAU,4GACd,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,WAAW,CAAA,CAAC,UAAU,iBAAiB,kBAO/C,EAAQ,WAAW,CAClB,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,2CACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAU,qCAA4B,WAC5C,CAAA,EAAA,EAAA,IAAA,EAAC,IAAA,CAAE,UAAU,0BAAiB,EAAQ,WAAW,CAAC,aAAa,CAAC,IAAE,EAAK,QAAQ,OAEjF,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAU,qCAA4B,gBAC5C,CAAA,EAAA,EAAA,IAAA,EAAC,IAAA,CAAE,UAAU,wCAA+B,EAAW,UAEzD,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAU,qCAA4B,WAC5C,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAW,CAAC,yDAAyD,EAAE,EAAc,GAAA,CAAa,UACrG,OAGL,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAU,qCAA4B,aAC5C,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,yBAAiB,EAAQ,WAAW,CAAC,OAAO,EAAI,YAIjE,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,2CACb,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,WAAW,CAAA,CAAC,UAAU,yBACvB,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,UAAE,gCAlDA,EAAQ,EAAE,CAwDzB,YAOd,CD/SA,IAAA,EAAA,EAAA,CAAA,CAAA,OAAA,EAAA,EAAA,CAAA,CAAA,OAAA,EAAA,EAAA,CAAA,CAAA,OAAA,EAAA,EAAA,CAAA,CAAA,OAWA,EAAA,EAAA,CAAA,CAAA,OAgDe,SAAS,IACtB,IAAM,EAAS,CAAA,EAAA,EAAA,SAAA,AAAS,IAClB,EAAS,CAAA,EAAA,EAAA,SAAA,AAAS,IAClB,CAAE,KAAM,CAAO,CAAE,CAAG,CAAA,EAAA,EAAA,UAAA,AAAU,IAC9B,EAAS,EAAO,MAAM,CAEtB,CAAC,EAAU,EAAY,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,EAAkB,MACpD,CAAC,EAAS,EAAW,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,EAAC,IAEvC,CAAA,EAAA,EAAA,SAAA,AAAS,EAAC,KACR,GACF,EAAG,CAAC,EAAO,EAEX,IAAM,EAAgB,UACpB,GAAI,CACF,EAAW,IACX,IAAM,EAAW,MAAM,MAAM,CAAC,mBAAmB,EAAE,EAAO,SAAS,CAAC,EACpE,GAAI,EAAS,EAAE,CAAE,CACf,IAAM,EAAO,MAAM,EAAS,IAAI,GAChC,EAAY,EACd,MACE,CADK,OACG,KAAK,CAAC,4BAElB,CAAE,MAAO,EAAO,CACd,QAAQ,KAAK,CAAC,4BAA6B,EAC7C,QAAU,CACR,GAAW,EACb,CACF,EAQA,GAAI,EACF,MACE,CAFS,AAET,EAAA,EAAA,GAAA,EAAC,EAAA,OAAe,CAAA,CAAC,MAAM,aAAa,WAAY,EAAA,iBAAiB,UAC/D,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,6BACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,yEACf,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,8BAAqB,8BAM1C,GAAI,CAAC,EACH,MACE,CAAA,CAFW,CAEX,EAAA,GAAA,EAAC,EAAA,OAAe,CAAA,CAAC,MAAM,aAAa,WAAY,EAAA,iBAAiB,UAC/D,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,6BACb,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,WAAW,CAAA,CAAC,UAAU,wCACvB,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,yBAAgB,mBAC7B,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,OAAI,CAAA,CAAC,KAAK,0BACT,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,MAAM,CAAA,CAAC,UAAU,iBAChB,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,SAAS,CAAA,CAAC,UAAU,iBAAiB,0BASlD,IAAM,EAAoB,EAAS,QAAQ,CAAC,MAAM,CAAC,GAAK,EAAE,QAAQ,EAC5D,EAAuB,EAAS,QAAQ,CAAC,MAAM,CAAC,GAAK,CAAC,EAAE,QAAQ,EAEhE,EAAQ,CACZ,cAAe,EAAS,QAAQ,CAAC,MAAM,CACvC,eAAgB,EAAkB,MAAM,CACxC,gBAAiB,EAAqB,MAAM,CAC5C,aAAc,EAAkB,MAAM,CAAG,EACrC,KAAK,KAAK,CAAC,EAAkB,MAAM,CAAC,CAAC,EAAK,IAAM,GAAO,EAAE,CAAH,UAAc,EAAE,gBAAiB,CAAC,CAAG,GAAK,EAAkB,MAAM,CAAG,KAAO,IAClI,EACJ,kBAAmB,EAAkB,MAAM,CAAG,EAC1C,KAAK,KAAK,CAAC,EAAkB,MAAM,CAAC,CAAC,EAAK,IAAM,EAAO,CAAC,EAAE,WAAW,EAAE,gBAAiB,CAAC,CAAI,EAAS,IAAI,CAAC,QAAQ,CAAG,IAAM,GAAK,EAAkB,MAAM,CAAG,KAAO,IACnK,CACN,EAEA,MACE,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,OAAe,CAAA,CAAC,MAAM,aAAa,WAAY,EAAA,iBAAiB,UAC/D,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,sBAEb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,gGACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,4CACb,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,OAAI,CAAA,CAAC,KAAK,0BACT,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,MAAM,CAAA,CAAC,QAAQ,UAAU,KAAK,eAC7B,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,SAAS,CAAA,CAAC,UAAU,iBAAiB,cAK5C,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,uDAA8C,eAC5D,CAAA,EAAA,EAAA,IAAA,EAAC,IAAA,CAAE,UAAU,+CACV,EAAS,IAAI,CAAC,IAAI,CAAC,MAAI,EAAS,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAG,EAAS,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,UAG3F,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,2BACb,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,OAAI,CAAA,CAAC,KAAM,CAAC,eAAe,EAAE,EAAA,CAAQ,UACpC,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,MAAM,CAAA,CAAC,QAAQ,oBACd,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,IAAI,CAAA,CAAC,UAAU,iBAAiB,kBAIrC,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,MAAM,CAAA,CAAC,QAAQ,oBACd,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,QAAQ,CAAA,CAAC,UAAU,iBAAiB,kBAO3C,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,iEACb,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,IAAI,CAAA,WACH,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,UAAU,CAAA,CAAC,UAAU,sEACpB,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,SAAS,CAAA,CAAC,UAAU,+BAAsB,mBAC3C,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,KAAK,CAAA,CAAC,UAAU,qCAEnB,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,WAAW,CAAA,UACV,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,8BAAsB,EAAM,aAAa,QAG5D,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,IAAI,CAAA,WACH,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,UAAU,CAAA,CAAC,UAAU,sEACpB,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,SAAS,CAAA,CAAC,UAAU,+BAAsB,WAC3C,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,WAAW,CAAA,CAAC,UAAU,8BAEzB,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,WAAW,CAAA,UACV,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,6CAAqC,EAAM,cAAc,QAG5E,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,IAAI,CAAA,WACH,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,UAAU,CAAA,CAAC,UAAU,sEACpB,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,SAAS,CAAA,CAAC,UAAU,+BAAsB,YAC3C,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,WAAW,CAAA,CAAC,UAAU,+BAEzB,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,WAAW,CAAA,UACV,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,8CAAsC,EAAM,eAAe,QAG9E,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,IAAI,CAAA,WACH,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,UAAU,CAAA,CAAC,UAAU,sEACpB,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,SAAS,CAAA,CAAC,UAAU,+BAAsB,kBAC3C,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,KAAK,CAAA,CAAC,UAAU,6BAEnB,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,WAAW,CAAA,UACV,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,4CAAoC,EAAM,YAAY,QAGzE,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,IAAI,CAAA,WACH,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,UAAU,CAAA,CAAC,UAAU,sEACpB,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,SAAS,CAAA,CAAC,UAAU,+BAAsB,cAC3C,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,KAAK,CAAA,CAAC,UAAU,6BAEnB,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,WAAW,CAAA,UACV,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,6CAAoC,EAAM,iBAAiB,CAAC,eAMjF,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,IAAI,CAAA,WACH,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,UAAU,CAAA,UACT,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,SAAS,CAAA,CAAC,UAAU,wCACnB,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,QAAQ,CAAA,CAAC,UAAU,YACpB,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,UAAK,sBAGV,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,WAAW,CAAA,UACV,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,0DACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAU,qCAA4B,aAC5C,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,yBAAiB,EAAS,IAAI,CAAC,OAAO,CAAC,IAAI,MAE1D,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAU,qCAA4B,WAC5C,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,yBAAiB,EAAS,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,MAEhE,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAU,qCAA4B,eAC5C,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,yBAAiB,EAAS,IAAI,CAAC,QAAQ,MAEtD,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAU,qCAA4B,UAC5C,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,yBAAiB,IAAI,KAAK,EAAS,IAAI,CAAC,IAAI,EAAE,kBAAkB,gBAOrF,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,CACC,KAAM,EAAS,IAAI,CACnB,SAAU,EAAS,QAAQ,CAC3B,OArKW,AAAC,CAqKJ,GApKd,EAAO,IAAI,CAAC,CAAC,eAAe,EAAE,EAAA,CAAQ,CACxC,EAoKQ,aAAa,QAKvB"}