{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\r\nimport { twMerge } from \"tailwind-merge\"\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs))\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,IAAA,sQAAO,EAAC,IAAA,gOAAI,EAAC;AACtB", "debugId": null}}, {"offset": {"line": 19, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Card = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\r\n      \"rounded-lg border border-gray-200 dark:border-gray-800 bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 shadow-sm\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nCard.displayName = \"Card\"\r\n\r\nconst CardHeader = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardHeader.displayName = \"CardHeader\"\r\n\r\nconst CardTitle = React.forwardRef<\r\n  HTMLParagraphElement,\r\n  React.HTMLAttributes<HTMLHeadingElement>\r\n>(({ className, ...props }, ref) => (\r\n  <h3\r\n    ref={ref}\r\n    className={cn(\r\n      \"text-2xl font-semibold leading-none tracking-tight\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nCardTitle.displayName = \"CardTitle\"\r\n\r\nconst CardDescription = React.forwardRef<\r\n  HTMLParagraphElement,\r\n  React.HTMLAttributes<HTMLParagraphElement>\r\n>(({ className, ...props }, ref) => (\r\n  <p\r\n    ref={ref}\r\n    className={cn(\"text-sm text-gray-600 dark:text-gray-400\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardDescription.displayName = \"CardDescription\"\r\n\r\nconst CardContent = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\r\n))\r\nCardContent.displayName = \"CardContent\"\r\n\r\nconst CardFooter = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"flex items-center p-6 pt-0\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardFooter.displayName = \"CardFooter\"\r\n\r\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,oWAAgB,CAG3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,+XAAC;QACC,KAAK;QACL,WAAW,IAAA,2JAAE,EACX,+HACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,oWAAgB,CAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,+XAAC;QACC,KAAK;QACL,WAAW,IAAA,2JAAE,EAAC,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,oWAAgB,CAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,+XAA<PERSON>;QACC,KAAK;QACL,WAAW,IAAA,2JAAE,EACX,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,oWAAgB,CAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,+XAAC;QACC,KAAK;QACL,WAAW,IAAA,2JAAE,EAAC,4CAA4C;QACzD,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,oWAAgB,CAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,+XAAC;QAAI,KAAK;QAAK,WAAW,IAAA,2JAAE,EAAC,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,oWAAgB,CAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,+XAAC;QACC,KAAK;QACL,WAAW,IAAA,2JAAE,EAAC,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 104, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst buttonVariants = cva(\r\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: \"bg-blue-600 text-white hover:bg-blue-700 dark:bg-blue-600 dark:hover:bg-blue-700\",\r\n        destructive:\r\n          \"bg-red-600 text-white hover:bg-red-700 dark:bg-red-600 dark:hover:bg-red-700\",\r\n        outline:\r\n          \"border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-900 hover:bg-gray-50 dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-gray-100\",\r\n        secondary:\r\n          \"bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-gray-100 hover:bg-gray-200 dark:hover:bg-gray-700\",\r\n        ghost: \"hover:bg-gray-100 dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-gray-100\",\r\n        link: \"text-blue-600 dark:text-blue-400 underline-offset-4 hover:underline\",\r\n      },\r\n      size: {\r\n        default: \"h-10 px-4 py-2\",\r\n        sm: \"h-9 rounded-md px-3\",\r\n        lg: \"h-11 rounded-md px-8\",\r\n        icon: \"h-10 w-10\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n      size: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nexport interface ButtonProps\r\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\r\n    VariantProps<typeof buttonVariants> {\r\n  asChild?: boolean\r\n}\r\n\r\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\r\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\r\n    const Comp = asChild ? Slot : \"button\"\r\n    return (\r\n      <Comp\r\n        className={cn(buttonVariants({ variant, size, className }))}\r\n        ref={ref}\r\n        {...props}\r\n      />\r\n    )\r\n  }\r\n)\r\nButton.displayName = \"Button\"\r\n\r\nexport { Button, buttonVariants }\r\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;;;;;;AAEA,MAAM,iBAAiB,IAAA,oRAAG,EACxB,uQACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,oWAAgB,CAC7B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,+UAAI,GAAG;IAC9B,qBACE,+XAAC;QACC,WAAW,IAAA,2JAAE,EAAC,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 164, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/components/ui/label.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst labelVariants = cva(\r\n  \"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\"\r\n)\r\n\r\nconst Label = React.forwardRef<\r\n  React.ElementRef<typeof LabelPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root> &\r\n    VariantProps<typeof labelVariants>\r\n>(({ className, ...props }, ref) => (\r\n  <LabelPrimitive.Root\r\n    ref={ref}\r\n    className={cn(labelVariants(), className)}\r\n    {...props}\r\n  />\r\n))\r\nLabel.displayName = LabelPrimitive.Root.displayName\r\n\r\nexport { Label }\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;;;;;;AAEA,MAAM,gBAAgB,IAAA,oRAAG,EACvB;AAGF,MAAM,sBAAQ,oWAAgB,CAI5B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,+XAAC,wTAAmB;QAClB,KAAK;QACL,WAAW,IAAA,2JAAE,EAAC,iBAAiB;QAC9B,GAAG,KAAK;;;;;;AAGb,MAAM,WAAW,GAAG,wTAAmB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 212, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/components/ui/dropdown-menu.tsx"], "sourcesContent": ["'use client'\n\nimport * as React from 'react'\nimport * as DropdownMenuPrimitive from '@radix-ui/react-dropdown-menu'\nimport { Check, ChevronRight, Circle } from 'lucide-react'\nimport { cn } from '@/lib/utils'\n\nconst DropdownMenu = DropdownMenuPrimitive.Root\n\nconst DropdownMenuTrigger = DropdownMenuPrimitive.Trigger\n\nconst DropdownMenuGroup = DropdownMenuPrimitive.Group\n\nconst DropdownMenuPortal = DropdownMenuPrimitive.Portal\n\nconst DropdownMenuSub = DropdownMenuPrimitive.Sub\n\nconst DropdownMenuRadioGroup = DropdownMenuPrimitive.RadioGroup\n\nconst DropdownMenuSubTrigger = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.SubTrigger>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.SubTrigger> & {\n    inset?: boolean\n  }\n>(({ className, inset, children, ...props }, ref) => (\n  <DropdownMenuPrimitive.SubTrigger\n    ref={ref}\n    className={cn(\n      'flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent',\n      inset && 'pl-8',\n      className\n    )}\n    {...props}\n  >\n    {children}\n    <ChevronRight className=\"ml-auto h-4 w-4\" />\n  </DropdownMenuPrimitive.SubTrigger>\n))\nDropdownMenuSubTrigger.displayName = DropdownMenuPrimitive.SubTrigger.displayName\n\nconst DropdownMenuSubContent = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.SubContent>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.SubContent>\n>(({ className, ...props }, ref) => (\n  <DropdownMenuPrimitive.SubContent\n    ref={ref}\n    className={cn(\n      'z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2',\n      className\n    )}\n    {...props}\n  />\n))\nDropdownMenuSubContent.displayName = DropdownMenuPrimitive.SubContent.displayName\n\nconst DropdownMenuContent = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Content>\n>(({ className, sideOffset = 4, ...props }, ref) => (\n  <DropdownMenuPrimitive.Portal>\n    <DropdownMenuPrimitive.Content\n      ref={ref}\n      sideOffset={sideOffset}\n      className={cn(\n        'z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2',\n        className\n      )}\n      {...props}\n    />\n  </DropdownMenuPrimitive.Portal>\n))\nDropdownMenuContent.displayName = DropdownMenuPrimitive.Content.displayName\n\nconst DropdownMenuItem = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Item> & {\n    inset?: boolean\n  }\n>(({ className, inset, ...props }, ref) => (\n  <DropdownMenuPrimitive.Item\n    ref={ref}\n    className={cn(\n      'relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50',\n      inset && 'pl-8',\n      className\n    )}\n    {...props}\n  />\n))\nDropdownMenuItem.displayName = DropdownMenuPrimitive.Item.displayName\n\nconst DropdownMenuCheckboxItem = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.CheckboxItem>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.CheckboxItem>\n>(({ className, children, checked, ...props }, ref) => (\n  <DropdownMenuPrimitive.CheckboxItem\n    ref={ref}\n    className={cn(\n      'relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50',\n      className\n    )}\n    checked={checked}\n    {...props}\n  >\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\n      <DropdownMenuPrimitive.ItemIndicator>\n        <Check className=\"h-4 w-4\" />\n      </DropdownMenuPrimitive.ItemIndicator>\n    </span>\n    {children}\n  </DropdownMenuPrimitive.CheckboxItem>\n))\nDropdownMenuCheckboxItem.displayName = DropdownMenuPrimitive.CheckboxItem.displayName\n\nconst DropdownMenuRadioItem = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.RadioItem>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.RadioItem>\n>(({ className, children, ...props }, ref) => (\n  <DropdownMenuPrimitive.RadioItem\n    ref={ref}\n    className={cn(\n      'relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50',\n      className\n    )}\n    {...props}\n  >\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\n      <DropdownMenuPrimitive.ItemIndicator>\n        <Circle className=\"h-2 w-2 fill-current\" />\n      </DropdownMenuPrimitive.ItemIndicator>\n    </span>\n    {children}\n  </DropdownMenuPrimitive.RadioItem>\n))\nDropdownMenuRadioItem.displayName = DropdownMenuPrimitive.RadioItem.displayName\n\nconst DropdownMenuLabel = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Label>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Label> & {\n    inset?: boolean\n  }\n>(({ className, inset, ...props }, ref) => (\n  <DropdownMenuPrimitive.Label\n    ref={ref}\n    className={cn(\n      'px-2 py-1.5 text-sm font-semibold',\n      inset && 'pl-8',\n      className\n    )}\n    {...props}\n  />\n))\nDropdownMenuLabel.displayName = DropdownMenuPrimitive.Label.displayName\n\nconst DropdownMenuSeparator = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Separator>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Separator>\n>(({ className, ...props }, ref) => (\n  <DropdownMenuPrimitive.Separator\n    ref={ref}\n    className={cn('-mx-1 my-1 h-px bg-muted', className)}\n    {...props}\n  />\n))\nDropdownMenuSeparator.displayName = DropdownMenuPrimitive.Separator.displayName\n\nconst DropdownMenuShortcut = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLSpanElement>) => {\n  return (\n    <span\n      className={cn('ml-auto text-xs tracking-widest opacity-60', className)}\n      {...props}\n    />\n  )\n}\nDropdownMenuShortcut.displayName = 'DropdownMenuShortcut'\n\nexport {\n  DropdownMenu,\n  DropdownMenuTrigger,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuCheckboxItem,\n  DropdownMenuRadioItem,\n  DropdownMenuLabel,\n  DropdownMenuSeparator,\n  DropdownMenuShortcut,\n  DropdownMenuGroup,\n  DropdownMenuPortal,\n  DropdownMenuSub,\n  DropdownMenuSubContent,\n  DropdownMenuSubTrigger,\n  DropdownMenuRadioGroup,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA;AACA;AACA;AAAA;AAAA;AACA;AALA;;;;;;AAOA,MAAM,eAAe,6TAA0B;AAE/C,MAAM,sBAAsB,gUAA6B;AAEzD,MAAM,oBAAoB,8TAA2B;AAErD,MAAM,qBAAqB,+TAA4B;AAEvD,MAAM,kBAAkB,4TAAyB;AAEjD,MAAM,yBAAyB,mUAAgC;AAE/D,MAAM,uCAAyB,oWAAgB,CAK7C,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAC3C,+XAAC,mUAAgC;QAC/B,KAAK;QACL,WAAW,IAAA,2JAAE,EACX,wIACA,SAAS,QACT;QAED,GAAG,KAAK;;YAER;0BACD,+XAAC,4VAAY;gBAAC,WAAU;;;;;;;;;;;;AAG5B,uBAAuB,WAAW,GAAG,mUAAgC,CAAC,WAAW;AAEjF,MAAM,uCAAyB,oWAAgB,CAG7C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,+XAAC,mUAAgC;QAC/B,KAAK;QACL,WAAW,IAAA,2JAAE,EACX,ybACA;QAED,GAAG,KAAK;;;;;;AAGb,uBAAuB,WAAW,GAAG,mUAAgC,CAAC,WAAW;AAEjF,MAAM,oCAAsB,oWAAgB,CAG1C,CAAC,EAAE,SAAS,EAAE,aAAa,CAAC,EAAE,GAAG,OAAO,EAAE,oBAC1C,+XAAC,+TAA4B;kBAC3B,cAAA,+XAAC,gUAA6B;YAC5B,KAAK;YACL,YAAY;YACZ,WAAW,IAAA,2JAAE,EACX,ybACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIf,oBAAoB,WAAW,GAAG,gUAA6B,CAAC,WAAW;AAE3E,MAAM,iCAAmB,oWAAgB,CAKvC,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE,oBACjC,+XAAC,6TAA0B;QACzB,KAAK;QACL,WAAW,IAAA,2JAAE,EACX,mOACA,SAAS,QACT;QAED,GAAG,KAAK;;;;;;AAGb,iBAAiB,WAAW,GAAG,6TAA0B,CAAC,WAAW;AAErE,MAAM,yCAA2B,oWAAgB,CAG/C,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,OAAO,EAAE,oBAC7C,+XAAC,qUAAkC;QACjC,KAAK;QACL,WAAW,IAAA,2JAAE,EACX,wOACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,+XAAC;gBAAK,WAAU;0BACd,cAAA,+XAAC,sUAAmC;8BAClC,cAAA,+XAAC,mUAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGpB;;;;;;;AAGL,yBAAyB,WAAW,GAAG,qUAAkC,CAAC,WAAW;AAErF,MAAM,sCAAwB,oWAAgB,CAG5C,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,+XAAC,kUAA+B;QAC9B,KAAK;QACL,WAAW,IAAA,2JAAE,EACX,wOACA;QAED,GAAG,KAAK;;0BAET,+XAAC;gBAAK,WAAU;0BACd,cAAA,+XAAC,sUAAmC;8BAClC,cAAA,+XAAC,sUAAM;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGrB;;;;;;;AAGL,sBAAsB,WAAW,GAAG,kUAA+B,CAAC,WAAW;AAE/E,MAAM,kCAAoB,oWAAgB,CAKxC,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE,oBACjC,+XAAC,8TAA2B;QAC1B,KAAK;QACL,WAAW,IAAA,2JAAE,EACX,qCACA,SAAS,QACT;QAED,GAAG,KAAK;;;;;;AAGb,kBAAkB,WAAW,GAAG,8TAA2B,CAAC,WAAW;AAEvE,MAAM,sCAAwB,oWAAgB,CAG5C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,+XAAC,kUAA+B;QAC9B,KAAK;QACL,WAAW,IAAA,2JAAE,EAAC,4BAA4B;QACzC,GAAG,KAAK;;;;;;AAGb,sBAAsB,WAAW,GAAG,kUAA+B,CAAC,WAAW;AAE/E,MAAM,uBAAuB,CAAC,EAC5B,SAAS,EACT,GAAG,OACmC;IACtC,qBACE,+XAAC;QACC,WAAW,IAAA,2JAAE,EAAC,8CAA8C;QAC3D,GAAG,KAAK;;;;;;AAGf;AACA,qBAAqB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 423, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/components/ui/theme-toggle.tsx"], "sourcesContent": ["'use client'\n\nimport { <PERSON>, Sun, Monitor } from 'lucide-react'\nimport { Button } from './button'\nimport { useTheme } from '@/components/providers/theme-provider'\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuTrigger,\n} from './dropdown-menu'\n\nexport function ThemeToggle() {\n  const { theme, actualTheme, mounted, setTheme } = useTheme()\n\n  const toggleTheme = () => {\n    if (!mounted) return\n    const next = actualTheme === 'light' ? 'dark' : 'light'\n    setTheme(next)\n  }\n\n  return (\n    <DropdownMenu>\n      <DropdownMenuTrigger asChild>\n        <Button\n          variant=\"ghost\"\n          size=\"icon\"\n          className=\"h-9 w-9\"\n          onClick={toggleTheme}\n          title={`Switch to ${actualTheme === 'light' ? 'dark' : 'light'} mode`}\n        >\n          <Sun className=\"h-4 w-4 rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0\" />\n          <Moon className=\"absolute h-4 w-4 rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100\" />\n          <span className=\"sr-only\">Toggle theme</span>\n        </Button>\n      </DropdownMenuTrigger>\n      <DropdownMenuContent align=\"end\">\n        <DropdownMenuItem onClick={() => setTheme('light')}>\n          <Sun className=\"mr-2 h-4 w-4\" />\n          <span>Light</span>\n        </DropdownMenuItem>\n        <DropdownMenuItem onClick={() => setTheme('dark')}>\n          <Moon className=\"mr-2 h-4 w-4\" />\n          <span>Dark</span>\n        </DropdownMenuItem>\n        <DropdownMenuItem onClick={() => setTheme('system')}>\n          <Monitor className=\"mr-2 h-4 w-4\" />\n          <span>System</span>\n        </DropdownMenuItem>\n      </DropdownMenuContent>\n    </DropdownMenu>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAAA;AAAA;AACA;AACA;AACA;AALA;;;;;;AAYO,SAAS;IACd,MAAM,EAAE,KAAK,EAAE,WAAW,EAAE,OAAO,EAAE,QAAQ,EAAE,GAAG,IAAA,kMAAQ;IAE1D,MAAM,cAAc;QAClB,IAAI,CAAC,SAAS;QACd,MAAM,OAAO,gBAAgB,UAAU,SAAS;QAChD,SAAS;IACX;IAEA,qBACE,+XAAC,8LAAY;;0BACX,+XAAC,qMAAmB;gBAAC,OAAO;0BAC1B,cAAA,+XAAC,8KAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,WAAU;oBACV,SAAS;oBACT,OAAO,CAAC,UAAU,EAAE,gBAAgB,UAAU,SAAS,QAAQ,KAAK,CAAC;;sCAErE,+XAAC,6TAAG;4BAAC,WAAU;;;;;;sCACf,+XAAC,gUAAI;4BAAC,WAAU;;;;;;sCAChB,+XAAC;4BAAK,WAAU;sCAAU;;;;;;;;;;;;;;;;;0BAG9B,+XAAC,qMAAmB;gBAAC,OAAM;;kCACzB,+XAAC,kMAAgB;wBAAC,SAAS,IAAM,SAAS;;0CACxC,+XAAC,6TAAG;gCAAC,WAAU;;;;;;0CACf,+XAAC;0CAAK;;;;;;;;;;;;kCAER,+XAAC,kMAAgB;wBAAC,SAAS,IAAM,SAAS;;0CACxC,+XAAC,gUAAI;gCAAC,WAAU;;;;;;0CAChB,+XAAC;0CAAK;;;;;;;;;;;;kCAER,+XAAC,kMAAgB;wBAAC,SAAS,IAAM,SAAS;;0CACxC,+XAAC,yUAAO;gCAAC,WAAU;;;;;;0CACnB,+XAAC;0CAAK;;;;;;;;;;;;;;;;;;;;;;;;AAKhB", "debugId": null}}, {"offset": {"line": 580, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/components/layout/dashboard-layout.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { useSession, signOut } from 'next-auth/react'\nimport { useRouter } from 'next/navigation'\nimport { Button } from '@/components/ui/button'\n\nimport { ThemeToggle } from '@/components/ui/theme-toggle'\nimport {\n  Menu,\n  X,\n  User,\n  LogOut,\n  Settings,\n  Bell,\n  Search,\n  School,\n  Plus,\n  Upload,\n  Download,\n  Users,\n  BookOpen,\n  GraduationCap,\n  FileText,\n  BarChart3,\n  Calendar,\n  Home,\n  Edit,\n  ClipboardList,\n  Award\n} from 'lucide-react'\n\ninterface DashboardLayoutProps {\n  children: React.ReactNode\n  title: string\n  navigation: {\n    name: string\n    href: string\n    icon: string\n  }[]\n}\n\n// Icon mapping object\nconst iconMap: Record<string, React.ComponentType<{ className?: string }>> = {\n  Plus,\n  Upload,\n  Download,\n  Users,\n  BookOpen,\n  GraduationCap,\n  FileText,\n  BarChart3,\n  Calendar,\n  Home,\n  Settings,\n  Bell,\n  User,\n  Edit,\n  ClipboardList,\n  Award\n}\n\nexport default function DashboardLayout({ children, title, navigation }: DashboardLayoutProps) {\n  const { data: session } = useSession()\n  const router = useRouter()\n  const [sidebarOpen, setSidebarOpen] = useState(false)\n\n  const handleSignOut = async () => {\n    await signOut({ callbackUrl: '/' })\n  }\n\n  const getIcon = (iconName: string) => {\n    const IconComponent = iconMap[iconName]\n    return IconComponent || Home // fallback to Home icon if not found\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 dark:bg-gray-950\">\n      {/* Mobile sidebar */}\n      <div className={`fixed inset-0 z-50 lg:hidden ${sidebarOpen ? 'block' : 'hidden'}`}>\n        <div className=\"fixed inset-0 bg-gray-600 bg-opacity-75\" onClick={() => setSidebarOpen(false)} />\n        <div className=\"fixed inset-y-0 left-0 flex w-64 flex-col bg-white dark:bg-gray-900\">\n          <div className=\"flex h-16 items-center justify-between px-4\">\n            <div className=\"flex items-center\">\n              <School className=\"h-8 w-8 text-blue-600\" />\n              <span className=\"ml-2 text-lg font-semibold\">SMS</span>\n            </div>\n            <Button\n              variant=\"ghost\"\n              size=\"sm\"\n              onClick={() => setSidebarOpen(false)}\n            >\n              <X className=\"h-5 w-5\" />\n            </Button>\n          </div>\n          <nav className=\"flex-1 space-y-1 px-2 py-4\">\n            {navigation.map((item) => {\n              const IconComponent = getIcon(item.icon)\n              return (\n                <Button\n                  key={`mobile-${item.name}`}\n                  variant=\"ghost\"\n                  className=\"w-full justify-start\"\n                  onClick={() => {\n                    router.push(item.href)\n                    setSidebarOpen(false)\n                  }}\n                >\n                  <IconComponent className=\"mr-3 h-5 w-5\" />\n                  {item.name}\n                </Button>\n              )\n            })}\n          </nav>\n        </div>\n      </div>\n\n      {/* Desktop sidebar */}\n      <div className=\"hidden lg:fixed lg:inset-y-0 lg:flex lg:w-64 lg:flex-col\">\n        <div className=\"flex flex-col flex-grow bg-white dark:bg-gray-900 border-r border-gray-200 dark:border-gray-800\">\n          <div className=\"flex h-16 items-center px-4\">\n            <School className=\"h-8 w-8 text-blue-600\" />\n            <span className=\"ml-2 text-lg font-semibold hidden xl:inline\">School Management System</span>\n            <span className=\"ml-2 text-lg font-semibold xl:hidden\">SMS</span>\n          </div>\n          <nav className=\"flex-1 space-y-1 px-2 py-4\">\n            {navigation.map((item) => {\n              const IconComponent = getIcon(item.icon)\n              return (\n                <Button\n                  key={`desktop-${item.name}`}\n                  variant=\"ghost\"\n                  className=\"w-full justify-start\"\n                  onClick={() => router.push(item.href)}\n                >\n                  <IconComponent className=\"mr-3 h-5 w-5\" />\n                  {item.name}\n                </Button>\n              )\n            })}\n          </nav>\n        </div>\n      </div>\n\n      {/* Main content */}\n      <div className=\"lg:pl-64\">\n        {/* Top bar */}\n        <div className=\"sticky top-0 z-40 flex h-16 shrink-0 items-center gap-x-4 border-b border-gray-200 dark:border-gray-800 bg-white dark:bg-gray-900 px-4 shadow-sm sm:gap-x-6 sm:px-6 lg:px-8\">\n          <Button\n            variant=\"ghost\"\n            size=\"sm\"\n            className=\"lg:hidden\"\n            onClick={() => setSidebarOpen(true)}\n          >\n            <Menu className=\"h-5 w-5\" />\n          </Button>\n\n          <div className=\"flex flex-1 gap-x-4 self-stretch lg:gap-x-6\">\n            <div className=\"relative flex flex-1\">\n              <div className=\"pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3\">\n                <Search className=\"h-5 w-5 text-gray-400\" />\n              </div>\n              <input\n                type=\"text\"\n                placeholder=\"Search...\"\n                className=\"block h-full w-full border-0 py-0 pl-10 pr-0 text-gray-900 dark:text-gray-100 placeholder:text-gray-400 dark:placeholder:text-gray-500 focus:ring-0 sm:text-sm bg-transparent\"\n              />\n            </div>\n          </div>\n\n          <div className=\"flex items-center gap-x-4 lg:gap-x-6\">\n            <ThemeToggle />\n\n            <Button variant=\"ghost\" size=\"sm\">\n              <Bell className=\"h-5 w-5\" />\n            </Button>\n\n            <div className=\"relative\">\n              <div className=\"flex items-center gap-x-3\">\n                <div className=\"text-sm hidden sm:block\">\n                  <p className=\"font-medium text-gray-900 dark:text-gray-100\">\n                    {session?.user?.firstName} {session?.user?.lastName}\n                  </p>\n                  <p className=\"text-gray-500 dark:text-gray-400 capitalize\">\n                    {session?.user?.role?.toLowerCase()}\n                  </p>\n                </div>\n                <div className=\"flex items-center gap-x-2\">\n                  <Button\n                    variant=\"ghost\"\n                    size=\"sm\"\n                    onClick={handleSignOut}\n                    className=\"flex items-center gap-2\"\n                  >\n                    <LogOut className=\"h-4 w-4\" />\n                    <span className=\"hidden sm:inline\">Sign Out</span>\n                  </Button>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Page content */}\n        <main className=\"py-6\">\n          <div className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\n            <div className=\"mb-6\">\n              <h1 className=\"text-2xl font-bold text-gray-900 dark:text-gray-100\">{title}</h1>\n            </div>\n            {children}\n          </div>\n        </main>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AARA;;;;;;;;AA0CA,sBAAsB;AACtB,MAAM,UAAuE;IAC3E,MAAA,gUAAI;IACJ,QAAA,sUAAM;IACN,UAAA,4UAAQ;IACR,OAAA,mUAAK;IACL,UAAA,gVAAQ;IACR,eAAA,+VAAa;IACb,UAAA,gVAAQ;IACR,WAAA,qVAAS;IACT,UAAA,4UAAQ;IACR,MAAA,iUAAI;IACJ,UAAA,4UAAQ;IACR,MAAA,gUAAI;IACJ,MAAA,gUAAI;IACJ,MAAA,yUAAI;IACJ,eAAA,+VAAa;IACb,OAAA,mUAAK;AACP;AAEe,SAAS,gBAAgB,EAAE,QAAQ,EAAE,KAAK,EAAE,UAAU,EAAwB;IAC3F,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,IAAA,0SAAU;IACpC,MAAM,SAAS,IAAA,gSAAS;IACxB,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,kWAAQ,EAAC;IAE/C,MAAM,gBAAgB;QACpB,MAAM,IAAA,uSAAO,EAAC;YAAE,aAAa;QAAI;IACnC;IAEA,MAAM,UAAU,CAAC;QACf,MAAM,gBAAgB,OAAO,CAAC,SAAS;QACvC,OAAO,iBAAiB,iUAAI,CAAC,qCAAqC;;IACpE;IAEA,qBACE,+XAAC;QAAI,WAAU;;0BAEb,+XAAC;gBAAI,WAAW,CAAC,6BAA6B,EAAE,cAAc,UAAU,UAAU;;kCAChF,+XAAC;wBAAI,WAAU;wBAA0C,SAAS,IAAM,eAAe;;;;;;kCACvF,+XAAC;wBAAI,WAAU;;0CACb,+XAAC;gCAAI,WAAU;;kDACb,+XAAC;wCAAI,WAAU;;0DACb,+XAAC,sUAAM;gDAAC,WAAU;;;;;;0DAClB,+XAAC;gDAAK,WAAU;0DAA6B;;;;;;;;;;;;kDAE/C,+XAAC,8KAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,eAAe;kDAE9B,cAAA,+XAAC,uTAAC;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAGjB,+XAAC;gCAAI,WAAU;0CACZ,WAAW,GAAG,CAAC,CAAC;oCACf,MAAM,gBAAgB,QAAQ,KAAK,IAAI;oCACvC,qBACE,+XAAC,8KAAM;wCAEL,SAAQ;wCACR,WAAU;wCACV,SAAS;4CACP,OAAO,IAAI,CAAC,KAAK,IAAI;4CACrB,eAAe;wCACjB;;0DAEA,+XAAC;gDAAc,WAAU;;;;;;4CACxB,KAAK,IAAI;;uCATL,CAAC,OAAO,EAAE,KAAK,IAAI,EAAE;;;;;gCAYhC;;;;;;;;;;;;;;;;;;0BAMN,+XAAC;gBAAI,WAAU;0BACb,cAAA,+XAAC;oBAAI,WAAU;;sCACb,+XAAC;4BAAI,WAAU;;8CACb,+XAAC,sUAAM;oCAAC,WAAU;;;;;;8CAClB,+XAAC;oCAAK,WAAU;8CAA8C;;;;;;8CAC9D,+XAAC;oCAAK,WAAU;8CAAuC;;;;;;;;;;;;sCAEzD,+XAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAC;gCACf,MAAM,gBAAgB,QAAQ,KAAK,IAAI;gCACvC,qBACE,+XAAC,8KAAM;oCAEL,SAAQ;oCACR,WAAU;oCACV,SAAS,IAAM,OAAO,IAAI,CAAC,KAAK,IAAI;;sDAEpC,+XAAC;4CAAc,WAAU;;;;;;wCACxB,KAAK,IAAI;;mCANL,CAAC,QAAQ,EAAE,KAAK,IAAI,EAAE;;;;;4BASjC;;;;;;;;;;;;;;;;;0BAMN,+XAAC;gBAAI,WAAU;;kCAEb,+XAAC;wBAAI,WAAU;;0CACb,+XAAC,8KAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,eAAe;0CAE9B,cAAA,+XAAC,gUAAI;oCAAC,WAAU;;;;;;;;;;;0CAGlB,+XAAC;gCAAI,WAAU;0CACb,cAAA,+XAAC;oCAAI,WAAU;;sDACb,+XAAC;4CAAI,WAAU;sDACb,cAAA,+XAAC,sUAAM;gDAAC,WAAU;;;;;;;;;;;sDAEpB,+XAAC;4CACC,MAAK;4CACL,aAAY;4CACZ,WAAU;;;;;;;;;;;;;;;;;0CAKhB,+XAAC;gCAAI,WAAU;;kDACb,+XAAC,4LAAW;;;;;kDAEZ,+XAAC,8KAAM;wCAAC,SAAQ;wCAAQ,MAAK;kDAC3B,cAAA,+XAAC,gUAAI;4CAAC,WAAU;;;;;;;;;;;kDAGlB,+XAAC;wCAAI,WAAU;kDACb,cAAA,+XAAC;4CAAI,WAAU;;8DACb,+XAAC;oDAAI,WAAU;;sEACb,+XAAC;4DAAE,WAAU;;gEACV,SAAS,MAAM;gEAAU;gEAAE,SAAS,MAAM;;;;;;;sEAE7C,+XAAC;4DAAE,WAAU;sEACV,SAAS,MAAM,MAAM;;;;;;;;;;;;8DAG1B,+XAAC;oDAAI,WAAU;8DACb,cAAA,+XAAC,8KAAM;wDACL,SAAQ;wDACR,MAAK;wDACL,SAAS;wDACT,WAAU;;0EAEV,+XAAC,0UAAM;gEAAC,WAAU;;;;;;0EAClB,+XAAC;gEAAK,WAAU;0EAAmB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAS/C,+XAAC;wBAAK,WAAU;kCACd,cAAA,+XAAC;4BAAI,WAAU;;8CACb,+XAAC;oCAAI,WAAU;8CACb,cAAA,+XAAC;wCAAG,WAAU;kDAAuD;;;;;;;;;;;gCAEtE;;;;;;;;;;;;;;;;;;;;;;;;AAMb", "debugId": null}}, {"offset": {"line": 1064, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/lib/grading.ts"], "sourcesContent": ["// Grade mapping configuration\r\nexport interface GradeConfig {\r\n  A_PLUS: number; // >= 90\r\n  A: number;     // >= 80\r\n  B_PLUS: number; // >= 70\r\n  B: number;     // >= 60\r\n  C: number;     // >= 50\r\n  D: number;     // >= 40\r\n  E: number;     // < 40\r\n}\r\n\r\n// Default grade configuration\r\nexport const DEFAULT_GRADE_CONFIG: GradeConfig = {\r\n  A_PLUS: 90,\r\n  A: 80,\r\n  B_PLUS: 70,\r\n  B: 60,\r\n  C: 50,\r\n  D: 40,\r\n  E: 0\r\n}\r\n\r\n/**\r\n * Calculate grade based on percentage\r\n */\r\nexport function calculateGrade(percentage: number, config: GradeConfig = DEFAULT_GRADE_CONFIG): string {\r\n  if (percentage >= config.A_PLUS) return 'A+'\r\n  if (percentage >= config.A) return 'A'\r\n  if (percentage >= config.B_PLUS) return 'B+'\r\n  if (percentage >= config.B) return 'B'\r\n  if (percentage >= config.C) return 'C'\r\n  if (percentage >= config.D) return 'D'\r\n  return 'E'\r\n}\r\n\r\n/**\r\n * Calculate percentage from obtained marks and max marks\r\n */\r\nexport function calculatePercentage(obtainedMarks: number, maxMarks: number): number {\r\n  if (maxMarks === 0) return 0\r\n  return Math.round((obtainedMarks / maxMarks) * 100 * 100) / 100 // Round to 2 decimal places\r\n}\r\n\r\n/**\r\n * Calculate GPA based on grades\r\n */\r\nexport function calculateGPA(grades: string[], config: GradeConfig = DEFAULT_GRADE_CONFIG): number {\r\n  if (grades.length === 0) return 0\r\n\r\n  const gradePoints = grades.map(grade => {\r\n    switch (grade) {\r\n      case 'A+': return 4.0\r\n      case 'A': return 3.7\r\n      case 'B+': return 3.3\r\n      case 'B': return 3.0\r\n      case 'C': return 2.0\r\n      case 'D': return 1.0\r\n      case 'E': return 0.0\r\n      default: return 0.0\r\n    }\r\n  })\r\n\r\n  const totalPoints = gradePoints.reduce((sum, points) => sum + points, 0 as number)\r\n  return Math.round((totalPoints / grades.length) * 100) / 100 // Round to 2 decimal places\r\n}\r\n\r\n/**\r\n * Calculate weighted GPA based on subject credits\r\n */\r\nexport function calculateWeightedGPA(\r\n  grades: string[], \r\n  credits: number[] = [], \r\n  config: GradeConfig = DEFAULT_GRADE_CONFIG\r\n): number {\r\n  if (grades.length === 0) return 0\r\n\r\n  // If no credits provided, use equal weight (default credit = 1)\r\n  const subjectCredits = credits.length === grades.length ? credits : grades.map(() => 1)\r\n\r\n  const gradePoints = grades.map(grade => {\r\n    switch (grade) {\r\n      case 'A+': return 4.0\r\n      case 'A': return 3.7\r\n      case 'B+': return 3.3\r\n      case 'B': return 3.0\r\n      case 'C': return 2.0\r\n      case 'D': return 1.0\r\n      case 'E': return 0.0\r\n      default: return 0.0\r\n    }\r\n  })\r\n\r\n  const totalWeightedPoints = gradePoints.reduce((sum, points, index) => {\r\n    return sum + (points * subjectCredits[index])\r\n  }, 0 as number)\r\n\r\n  const totalCredits = subjectCredits.reduce((sum, credit) => sum + credit, 0)\r\n  \r\n  return Math.round((totalWeightedPoints / totalCredits) * 100) / 100 // Round to 2 decimal places\r\n}\r\n\r\n/**\r\n * Calculate attendance percentage\r\n */\r\nexport function calculateAttendancePercentage(presentDays: number, totalDays: number): number {\r\n  if (totalDays === 0) return 0\r\n  return Math.round((presentDays / totalDays) * 100 * 100) / 100 // Round to 2 decimal places\r\n}\r\n\r\n/**\r\n * Get grade color for UI display (text color)\r\n */\r\nexport function getGradeColor(grade: string): string {\r\n  switch (grade) {\r\n    case 'A+':\r\n    case 'A':\r\n      return 'text-green-600'\r\n    case 'B+':\r\n    case 'B':\r\n      return 'text-blue-600'\r\n    case 'C+':\r\n    case 'C':\r\n      return 'text-yellow-600'\r\n    case 'D':\r\n      return 'text-orange-600'\r\n    case 'E':\r\n    case 'F':\r\n      return 'text-red-600'\r\n    default:\r\n      return 'text-gray-600'\r\n  }\r\n}\r\n\r\n/**\r\n * Get grade badge color for UI display (background + text + border)\r\n */\r\nexport function getGradeBadgeColor(grade: string): string {\r\n  switch (grade) {\r\n    case 'A+':\r\n    case 'A':\r\n      return 'bg-green-100 text-green-800 border-green-200'\r\n    case 'B+':\r\n    case 'B':\r\n      return 'bg-blue-100 text-blue-800 border-blue-200'\r\n    case 'C+':\r\n    case 'C':\r\n      return 'bg-yellow-100 text-yellow-800 border-yellow-200'\r\n    case 'D':\r\n      return 'bg-orange-100 text-orange-800 border-orange-200'\r\n    case 'E':\r\n    case 'F':\r\n      return 'bg-red-100 text-red-800 border-red-200'\r\n    default:\r\n      return 'bg-gray-100 text-gray-800 border-gray-200'\r\n  }\r\n}\r\n\r\n/**\r\n * Get attendance status color for UI display\r\n */\r\nexport function getAttendanceColor(status: 'PRESENT' | 'ABSENT' | 'LATE' | 'HALF_DAY'): string {\r\n  switch (status) {\r\n    case 'PRESENT':\r\n      return 'text-green-600'\r\n    case 'ABSENT':\r\n      return 'text-red-600'\r\n    case 'LATE':\r\n      return 'text-yellow-600'\r\n    case 'HALF_DAY':\r\n      return 'text-orange-600'\r\n    default:\r\n      return 'text-gray-600'\r\n  }\r\n}\r\n"], "names": [], "mappings": "AAAA,8BAA8B;;;;;;;;;;;;;;;;;;;;;AAYvB,MAAM,uBAAoC;IAC/C,QAAQ;IACR,GAAG;IACH,QAAQ;IACR,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;AACL;AAKO,SAAS,eAAe,UAAkB,EAAE,SAAsB,oBAAoB;IAC3F,IAAI,cAAc,OAAO,MAAM,EAAE,OAAO;IACxC,IAAI,cAAc,OAAO,CAAC,EAAE,OAAO;IACnC,IAAI,cAAc,OAAO,MAAM,EAAE,OAAO;IACxC,IAAI,cAAc,OAAO,CAAC,EAAE,OAAO;IACnC,IAAI,cAAc,OAAO,CAAC,EAAE,OAAO;IACnC,IAAI,cAAc,OAAO,CAAC,EAAE,OAAO;IACnC,OAAO;AACT;AAKO,SAAS,oBAAoB,aAAqB,EAAE,QAAgB;IACzE,IAAI,aAAa,GAAG,OAAO;IAC3B,OAAO,KAAK,KAAK,CAAC,AAAC,gBAAgB,WAAY,MAAM,OAAO,IAAI,4BAA4B;;AAC9F;AAKO,SAAS,aAAa,MAAgB,EAAE,SAAsB,oBAAoB;IACvF,IAAI,OAAO,MAAM,KAAK,GAAG,OAAO;IAEhC,MAAM,cAAc,OAAO,GAAG,CAAC,CAAA;QAC7B,OAAQ;YACN,KAAK;gBAAM,OAAO;YAClB,KAAK;gBAAK,OAAO;YACjB,KAAK;gBAAM,OAAO;YAClB,KAAK;gBAAK,OAAO;YACjB,KAAK;gBAAK,OAAO;YACjB,KAAK;gBAAK,OAAO;YACjB,KAAK;gBAAK,OAAO;YACjB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,cAAc,YAAY,MAAM,CAAC,CAAC,KAAK,SAAW,MAAM,QAAQ;IACtE,OAAO,KAAK,KAAK,CAAC,AAAC,cAAc,OAAO,MAAM,GAAI,OAAO,IAAI,4BAA4B;;AAC3F;AAKO,SAAS,qBACd,MAAgB,EAChB,UAAoB,EAAE,EACtB,SAAsB,oBAAoB;IAE1C,IAAI,OAAO,MAAM,KAAK,GAAG,OAAO;IAEhC,gEAAgE;IAChE,MAAM,iBAAiB,QAAQ,MAAM,KAAK,OAAO,MAAM,GAAG,UAAU,OAAO,GAAG,CAAC,IAAM;IAErF,MAAM,cAAc,OAAO,GAAG,CAAC,CAAA;QAC7B,OAAQ;YACN,KAAK;gBAAM,OAAO;YAClB,KAAK;gBAAK,OAAO;YACjB,KAAK;gBAAM,OAAO;YAClB,KAAK;gBAAK,OAAO;YACjB,KAAK;gBAAK,OAAO;YACjB,KAAK;gBAAK,OAAO;YACjB,KAAK;gBAAK,OAAO;YACjB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,sBAAsB,YAAY,MAAM,CAAC,CAAC,KAAK,QAAQ;QAC3D,OAAO,MAAO,SAAS,cAAc,CAAC,MAAM;IAC9C,GAAG;IAEH,MAAM,eAAe,eAAe,MAAM,CAAC,CAAC,KAAK,SAAW,MAAM,QAAQ;IAE1E,OAAO,KAAK,KAAK,CAAC,AAAC,sBAAsB,eAAgB,OAAO,IAAI,4BAA4B;;AAClG;AAKO,SAAS,8BAA8B,WAAmB,EAAE,SAAiB;IAClF,IAAI,cAAc,GAAG,OAAO;IAC5B,OAAO,KAAK,KAAK,CAAC,AAAC,cAAc,YAAa,MAAM,OAAO,IAAI,4BAA4B;;AAC7F;AAKO,SAAS,cAAc,KAAa;IACzC,OAAQ;QACN,KAAK;QACL,KAAK;YACH,OAAO;QACT,KAAK;QACL,KAAK;YACH,OAAO;QACT,KAAK;QACL,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;QACL,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF;AAKO,SAAS,mBAAmB,KAAa;IAC9C,OAAQ;QACN,KAAK;QACL,KAAK;YACH,OAAO;QACT,KAAK;QACL,KAAK;YACH,OAAO;QACT,KAAK;QACL,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;QACL,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF;AAKO,SAAS,mBAAmB,MAAkD;IACnF,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF", "debugId": null}}, {"offset": {"line": 1228, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/lib/navigation.ts"], "sourcesContent": ["// Shared navigation configurations for different user roles\n\nexport const adminNavigation = [\n  { name: 'Dashboard', href: '/admin', icon: 'BarChart3' },\n  { name: 'Students', href: '/admin/students', icon: 'Users' },\n  { name: 'Teachers', href: '/admin/teachers', icon: 'GraduationCap' },\n  { name: 'Classes & Sections', href: '/admin/classes', icon: 'BookOpen' },\n  { name: 'Subjects', href: '/admin/subjects', icon: 'FileText' },\n  { name: 'Terms & Exams', href: '/admin/exams', icon: 'Calendar' },\n  { name: 'Attendance', href: '/admin/attendance', icon: 'ClipboardList' },\n  { name: 'Marks', href: '/admin/marks', icon: 'Award' },\n  { name: 'Reports', href: '/admin/reports', icon: 'Download' },\n  { name: 'Settings', href: '/admin/settings', icon: 'Settings' },\n];\n\nexport const teacherNavigation = [\n  { name: 'Dashboard', href: '/teacher', icon: 'BarChart3' },\n  { name: 'My Classes', href: '/teacher/classes', icon: 'BookOpen' },\n  { name: 'Attendance', href: '/teacher/attendance', icon: 'ClipboardList' },\n  { name: 'Marks', href: '/teacher/marks', icon: 'Award' },\n  { name: 'Students', href: '/teacher/students', icon: 'Users' },\n  { name: 'Reports', href: '/teacher/reports', icon: 'FileText' },\n  { name: 'Profile', href: '/teacher/profile', icon: 'User' },\n];\n\nexport const studentNavigation = [\n  { name: 'Dashboard', href: '/student', icon: 'BarChart3' },\n  { name: 'My Classes', href: '/student/classes', icon: 'BookOpen' },\n  { name: 'Attendance', href: '/student/attendance', icon: 'ClipboardList' },\n  { name: 'Marks', href: '/student/marks', icon: 'Award' },\n  { name: 'Reports', href: '/student/reports', icon: 'FileText' },\n  { name: 'Profile', href: '/student/profile', icon: 'User' },\n];\n\n/**\n * Get the default dashboard URL for a user role\n */\nexport function getRoleDashboardUrl(role: string): string {\n  switch (role) {\n    case 'ADMIN':\n      return '/admin'\n    case 'TEACHER':\n      return '/teacher'\n    case 'STUDENT':\n      return '/student'\n    default:\n      return '/'\n  }\n}\n\n/**\n * Get navigation items for a user role\n */\nexport function getRoleNavigation(role: string) {\n  switch (role) {\n    case 'ADMIN':\n      return adminNavigation\n    case 'TEACHER':\n      return teacherNavigation\n    case 'STUDENT':\n      return studentNavigation\n    default:\n      return []\n  }\n}"], "names": [], "mappings": "AAAA,4DAA4D;;;;;;;;;;;;;AAErD,MAAM,kBAAkB;IAC7B;QAAE,MAAM;QAAa,MAAM;QAAU,MAAM;IAAY;IACvD;QAAE,MAAM;QAAY,MAAM;QAAmB,MAAM;IAAQ;IAC3D;QAAE,MAAM;QAAY,MAAM;QAAmB,MAAM;IAAgB;IACnE;QAAE,MAAM;QAAsB,MAAM;QAAkB,MAAM;IAAW;IACvE;QAAE,MAAM;QAAY,MAAM;QAAmB,MAAM;IAAW;IAC9D;QAAE,MAAM;QAAiB,MAAM;QAAgB,MAAM;IAAW;IAChE;QAAE,MAAM;QAAc,MAAM;QAAqB,MAAM;IAAgB;IACvE;QAAE,MAAM;QAAS,MAAM;QAAgB,MAAM;IAAQ;IACrD;QAAE,MAAM;QAAW,MAAM;QAAkB,MAAM;IAAW;IAC5D;QAAE,MAAM;QAAY,MAAM;QAAmB,MAAM;IAAW;CAC/D;AAEM,MAAM,oBAAoB;IAC/B;QAAE,MAAM;QAAa,MAAM;QAAY,MAAM;IAAY;IACzD;QAAE,MAAM;QAAc,MAAM;QAAoB,MAAM;IAAW;IACjE;QAAE,MAAM;QAAc,MAAM;QAAuB,MAAM;IAAgB;IACzE;QAAE,MAAM;QAAS,MAAM;QAAkB,MAAM;IAAQ;IACvD;QAAE,MAAM;QAAY,MAAM;QAAqB,MAAM;IAAQ;IAC7D;QAAE,MAAM;QAAW,MAAM;QAAoB,MAAM;IAAW;IAC9D;QAAE,MAAM;QAAW,MAAM;QAAoB,MAAM;IAAO;CAC3D;AAEM,MAAM,oBAAoB;IAC/B;QAAE,MAAM;QAAa,MAAM;QAAY,MAAM;IAAY;IACzD;QAAE,MAAM;QAAc,MAAM;QAAoB,MAAM;IAAW;IACjE;QAAE,MAAM;QAAc,MAAM;QAAuB,MAAM;IAAgB;IACzE;QAAE,MAAM;QAAS,MAAM;QAAkB,MAAM;IAAQ;IACvD;QAAE,MAAM;QAAW,MAAM;QAAoB,MAAM;IAAW;IAC9D;QAAE,MAAM;QAAW,MAAM;QAAoB,MAAM;IAAO;CAC3D;AAKM,SAAS,oBAAoB,IAAY;IAC9C,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF;AAKO,SAAS,kBAAkB,IAAY;IAC5C,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO,EAAE;IACb;AACF", "debugId": null}}, {"offset": {"line": 1390, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/app/%28dash%29/admin/reports/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Label } from '@/components/ui/label'\nimport DashboardLayout from '@/components/layout/dashboard-layout'\nimport { getGradeBadgeColor } from '@/lib/grading'\nimport {\n  Calendar,\n  FileText,\n  Download,\n  BarChart3,\n  Users,\n  Award,\n  TrendingUp,\n  Printer,\n  Eye,\n  Loader2,\n  AlertCircle,\n  Plus\n} from 'lucide-react'\n\ninterface ReportCard {\n  id: string\n  studentId: string\n  termId: string\n  jsonSnapshot: any\n  pdfPath?: string\n  generatedAt: string\n  student: {\n    admissionNo: string\n    rollNumber: string\n    user: {\n      firstName: string\n      lastName: string\n    }\n    currentClass: {\n      name: string\n    }\n    currentSection: {\n      name: string\n    }\n  }\n  term: {\n    name: string\n    academicYear: string\n  }\n}\n\ninterface Term {\n  id: string\n  name: string\n  academicYear: string\n}\n\ninterface Class {\n  id: string\n  name: string\n}\n\nimport { adminNavigation } from '@/lib/navigation';\n\nexport default function ReportsPage() {\n  const [reportCards, setReportCards] = useState<ReportCard[]>([])\n  const [terms, setTerms] = useState<Term[]>([])\n  const [classes, setClasses] = useState<Class[]>([])\n  const [selectedTerm, setSelectedTerm] = useState('all')\n  const [selectedClass, setSelectedClass] = useState('all')\n  const [selectedStatus, setSelectedStatus] = useState('all')\n  const [loading, setLoading] = useState(true)\n  const [generating, setGenerating] = useState(false)\n  const [error, setError] = useState<string | null>(null)\n\n  useEffect(() => {\n    fetchReportCards()\n    fetchTermsAndClasses()\n  }, [selectedTerm, selectedClass, selectedStatus])\n\n  const fetchReportCards = async () => {\n    try {\n      setLoading(true)\n      const params = new URLSearchParams()\n      if (selectedTerm !== 'all') params.append('termId', selectedTerm)\n      if (selectedClass !== 'all') params.append('classId', selectedClass)\n      if (selectedStatus !== 'all') params.append('status', selectedStatus)\n\n      const response = await fetch(`/api/admin/reports?${params}`)\n      if (response.ok) {\n        const data = await response.json()\n        setReportCards(data)\n      } else {\n        setError('Failed to fetch report cards')\n      }\n    } catch (error) {\n      console.error('Error fetching report cards:', error)\n      setError('Error fetching report cards')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const fetchTermsAndClasses = async () => {\n    try {\n      const [termsResponse, classesResponse] = await Promise.all([\n        fetch('/api/admin/exams?type=terms'),\n        fetch('/api/admin/classes')\n      ])\n\n      if (termsResponse.ok) {\n        const termsData = await termsResponse.json()\n        setTerms(termsData)\n      }\n\n      if (classesResponse.ok) {\n        const classesData = await classesResponse.json()\n        setClasses(classesData.classes || [])\n      }\n    } catch (error) {\n      console.error('Error fetching terms and classes:', error)\n    }\n  }\n\n  const handleGenerateReports = async () => {\n    if (selectedTerm === 'all' || selectedClass === 'all') {\n      setError('Please select both term and class to generate reports')\n      return\n    }\n\n    try {\n      setGenerating(true)\n      setError(null)\n\n      const response = await fetch('/api/admin/reports', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          termId: selectedTerm,\n          classId: selectedClass\n        })\n      })\n\n      if (response.ok) {\n        await fetchReportCards() // Refresh the list\n        setError(null)\n      } else {\n        const errorData = await response.json()\n        setError(errorData.error || 'Failed to generate reports')\n      }\n    } catch (error) {\n      console.error('Error generating reports:', error)\n      setError('Error generating reports')\n    } finally {\n      setGenerating(false)\n    }\n  }\n\n  const handleDownloadPDF = async (reportCardId: string, studentName: string) => {\n    try {\n      const response = await fetch('/api/reports/generate', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          type: 'report-card',\n          reportCardId: reportCardId\n        })\n      })\n\n      if (response.ok) {\n        const blob = await response.blob()\n        const url = window.URL.createObjectURL(blob)\n        const a = document.createElement('a')\n        a.href = url\n        a.download = `report-card-${studentName.replace(/\\s+/g, '-')}.pdf`\n        document.body.appendChild(a)\n        a.click()\n        window.URL.revokeObjectURL(url)\n        document.body.removeChild(a)\n      } else {\n        setError('Failed to download PDF')\n      }\n    } catch (error) {\n      console.error('Error downloading PDF:', error)\n      setError('Error downloading PDF')\n    }\n  }\n\n  const handleDownloadClassReport = async () => {\n    if (selectedTerm === 'all' || selectedClass === 'all') {\n      setError('Please select both term and class to download class report')\n      return\n    }\n\n    try {\n      const response = await fetch('/api/reports/generate', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          type: 'class-report',\n          termId: selectedTerm,\n          classId: selectedClass\n        })\n      })\n\n      if (response.ok) {\n        const blob = await response.blob()\n        const url = window.URL.createObjectURL(blob)\n        const a = document.createElement('a')\n        a.href = url\n        a.download = `class-report-${selectedTerm}-${selectedClass}.pdf`\n        document.body.appendChild(a)\n        a.click()\n        window.URL.revokeObjectURL(url)\n        document.body.removeChild(a)\n      } else {\n        setError('Failed to download class report')\n      }\n    } catch (error) {\n      console.error('Error downloading class report:', error)\n      setError('Error downloading class report')\n    }\n  }\n\n  // Calculate statistics\n  const stats = {\n    total: reportCards.length,\n    generated: reportCards.filter(r => r.pdfPath).length,\n    pending: reportCards.filter(r => !r.pdfPath).length,\n    averagePercentage: reportCards.length > 0\n      ? Math.round(reportCards.reduce((sum, r) => sum + ((r.jsonSnapshot as any)?.summary?.percentage || 0), 0) / reportCards.length)\n      : 0\n  }\n  const getStatusColor = (hasPdf: boolean) => {\n    return hasPdf\n      ? 'bg-green-100 text-green-800'\n      : 'bg-yellow-100 text-yellow-800'\n  }\n\n  const getStatusText = (hasPdf: boolean) => {\n    return hasPdf ? 'Generated' : 'Pending'\n  }\n\n\n\n\n\n  return (\n    <DashboardLayout title=\"Reports Management\" navigation={adminNavigation}>\n      <div className=\"space-y-6\">\n        {/* Header */}\n        <div className=\"flex flex-col space-y-4 sm:flex-row sm:justify-between sm:items-center sm:space-y-0\">\n          <div>\n            <h1 className=\"text-xl sm:text-2xl font-bold text-gray-900\">Reports Management</h1>\n            <p className=\"text-sm sm:text-base text-gray-600\">Generate and manage student report cards</p>\n          </div>\n          <div className=\"flex flex-col space-y-2 sm:flex-row sm:space-y-0 sm:space-x-2\">\n            <Button\n              variant=\"outline\"\n              className=\"w-full sm:w-auto\"\n              onClick={handleDownloadClassReport}\n              disabled={selectedTerm === 'all' || selectedClass === 'all'}\n            >\n              <BarChart3 className=\"w-4 h-4 mr-2\" />\n              <span className=\"sm:hidden\">Class Report</span>\n              <span className=\"hidden sm:inline\">Download Class Report</span>\n            </Button>\n          </div>\n        </div>\n\n        {/* Error Alert */}\n        {error && (\n          <div className=\"bg-red-50 border border-red-200 rounded-md p-4\">\n            <div className=\"flex\">\n              <AlertCircle className=\"h-5 w-5 text-red-400\" />\n              <div className=\"ml-3\">\n                <p className=\"text-sm text-red-800\">{error}</p>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Stats Cards */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n          <Card>\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n              <CardTitle className=\"text-sm font-medium\">Total Reports</CardTitle>\n              <FileText className=\"h-4 w-4 text-muted-foreground\" />\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-2xl font-bold\">{stats.total}</div>\n            </CardContent>\n          </Card>\n          <Card>\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n              <CardTitle className=\"text-sm font-medium\">Generated PDFs</CardTitle>\n              <Award className=\"h-4 w-4 text-green-600\" />\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-2xl font-bold text-green-600\">{stats.generated}</div>\n            </CardContent>\n          </Card>\n          <Card>\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n              <CardTitle className=\"text-sm font-medium\">Pending PDFs</CardTitle>\n              <Award className=\"h-4 w-4 text-orange-600\" />\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-2xl font-bold text-orange-600\">{stats.pending}</div>\n            </CardContent>\n          </Card>\n          <Card>\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n              <CardTitle className=\"text-sm font-medium\">Class Average</CardTitle>\n              <TrendingUp className=\"h-4 w-4 text-blue-600\" />\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-2xl font-bold text-blue-600\">{stats.averagePercentage}%</div>\n            </CardContent>\n          </Card>\n        </div>\n\n        {/* Report Generation */}\n        <Card>\n          <CardHeader>\n            <CardTitle className=\"flex items-center\">\n              <FileText className=\"w-5 h-5 mr-2\" />\n              Generate Report Cards\n            </CardTitle>\n            <CardDescription>\n              Create report cards for all students in a class\n            </CardDescription>\n          </CardHeader>\n          <CardContent>\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n              <div>\n                <Label htmlFor=\"term-select\">Select Term</Label>\n                <select\n                  id=\"term-select\"\n                  value={selectedTerm}\n                  onChange={(e) => setSelectedTerm(e.target.value)}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                >\n                  <option value=\"all\">Choose a term</option>\n                  {terms.map(term => (\n                    <option key={term.id} value={term.id}>{term.name} ({term.academicYear})</option>\n                  ))}\n                </select>\n              </div>\n              <div>\n                <Label htmlFor=\"class-select\">Select Class</Label>\n                <select\n                  id=\"class-select\"\n                  value={selectedClass}\n                  onChange={(e) => setSelectedClass(e.target.value)}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                >\n                  <option value=\"all\">Choose a class</option>\n                  {classes.map(cls => (\n                    <option key={cls.id} value={cls.id}>{cls.name}</option>\n                  ))}\n                </select>\n              </div>\n              <div className=\"flex items-end\">\n                <Button\n                  className=\"w-full\"\n                  onClick={handleGenerateReports}\n                  disabled={generating || selectedTerm === 'all' || selectedClass === 'all'}\n                >\n                  {generating ? (\n                    <>\n                      <Loader2 className=\"w-4 h-4 mr-2 animate-spin\" />\n                      Generating...\n                    </>\n                  ) : (\n                    <>\n                      <Plus className=\"w-4 h-4 mr-2\" />\n                      Generate Reports\n                    </>\n                  )}\n                </Button>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n\n        {/* Filters */}\n        <Card>\n          <CardHeader>\n            <CardTitle>Filters</CardTitle>\n          </CardHeader>\n          <CardContent>\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n              <div>\n                <Label htmlFor=\"term\">Term</Label>\n                <select\n                  id=\"term\"\n                  value={selectedTerm}\n                  onChange={(e) => setSelectedTerm(e.target.value)}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                >\n                  <option value=\"all\">All Terms</option>\n                  {terms.map(term => (\n                    <option key={term.id} value={term.id}>{term.name} ({term.academicYear})</option>\n                  ))}\n                </select>\n              </div>\n              <div>\n                <Label htmlFor=\"class\">Class</Label>\n                <select\n                  id=\"class\"\n                  value={selectedClass}\n                  onChange={(e) => setSelectedClass(e.target.value)}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                >\n                  <option value=\"all\">All Classes</option>\n                  {classes.map(cls => (\n                    <option key={cls.id} value={cls.id}>{cls.name}</option>\n                  ))}\n                </select>\n              </div>\n              <div>\n                <Label htmlFor=\"status\">Status</Label>\n                <select\n                  id=\"status\"\n                  value={selectedStatus}\n                  onChange={(e) => setSelectedStatus(e.target.value)}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                >\n                  <option value=\"all\">All Status</option>\n                  <option value=\"generated\">Generated</option>\n                  <option value=\"pending\">Pending</option>\n                </select>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n\n        {/* Report Cards Table */}\n        <Card>\n          <CardHeader>\n            <CardTitle>Report Cards</CardTitle>\n            <CardDescription>\n              Generated report cards for students\n            </CardDescription>\n          </CardHeader>\n          <CardContent>\n            {loading ? (\n              <div className=\"text-center py-8\">\n                <Loader2 className=\"h-8 w-8 animate-spin mx-auto\" />\n                <p className=\"mt-2 text-gray-600\">Loading report cards...</p>\n              </div>\n            ) : reportCards.length === 0 ? (\n              <div className=\"text-center py-8\">\n                <FileText className=\"h-12 w-12 text-gray-400 mx-auto mb-4\" />\n                <p className=\"text-gray-600\">No report cards found</p>\n                <p className=\"text-sm text-gray-500 mt-2\">Generate reports for a class to see them here</p>\n              </div>\n            ) : (\n              <>\n                {/* Desktop Table */}\n                <div className=\"hidden lg:block overflow-x-auto\">\n                  <table className=\"min-w-full divide-y divide-gray-200\">\n                    <thead className=\"bg-gray-50\">\n                      <tr>\n                        <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                          Student\n                        </th>\n                        <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                          Class\n                        </th>\n                        <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                          Term\n                        </th>\n                        <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                          Percentage\n                        </th>\n                        <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                          Grade\n                        </th>\n                        <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                          Status\n                        </th>\n                        <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                          Actions\n                        </th>\n                      </tr>\n                    </thead>\n                    <tbody className=\"bg-white divide-y divide-gray-200\">\n                      {reportCards.map((card) => {\n                        const summary = (card.jsonSnapshot as any)?.summary || {}\n                        const studentName = `${card.student.user.firstName} ${card.student.user.lastName}`\n\n                        return (\n                          <tr key={card.id}>\n                            <td className=\"px-6 py-4 whitespace-nowrap\">\n                              <div className=\"flex items-center\">\n                                <div className=\"flex-shrink-0 h-10 w-10\">\n                                  <div className=\"h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center\">\n                                    <span className=\"text-sm font-medium text-gray-700\">\n                                      {card.student.user.firstName[0]}{card.student.user.lastName[0]}\n                                    </span>\n                                  </div>\n                                </div>\n                                <div className=\"ml-4\">\n                                  <div className=\"text-sm font-medium text-gray-900\">{studentName}</div>\n                                  <div className=\"text-sm text-gray-500\">{card.student.admissionNo}</div>\n                                </div>\n                              </div>\n                            </td>\n                            <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                              {card.student.currentClass.name} - {card.student.currentSection.name}\n                            </td>\n                            <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                              {card.term.name}\n                            </td>\n                            <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                              {summary.percentage ? `${summary.percentage.toFixed(1)}%` : 'N/A'}\n                            </td>\n                            <td className=\"px-6 py-4 whitespace-nowrap\">\n                              <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getGradeBadgeColor(summary.grade || 'F')}`}>\n                                {summary.grade || 'N/A'}\n                              </span>\n                            </td>\n                            <td className=\"px-6 py-4 whitespace-nowrap\">\n                              <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(!!card.pdfPath)}`}>\n                                {getStatusText(!!card.pdfPath)}\n                              </span>\n                            </td>\n                            <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium\">\n                                    <div className=\"flex space-x-2\">\n                                <Button\n                                  variant=\"outline\"\n                                  size=\"sm\"\n                                  onClick={() => handleDownloadPDF(card.id, studentName)}\n                                >\n                                  <Download className=\"w-4 h-4\" />\n                                </Button>\n                              </div>\n                            </td>\n                          </tr>\n                        )\n                      })}\n                    </tbody>\n                  </table>\n                </div>\n\n                {/* Mobile Cards */}\n                <div className=\"lg:hidden space-y-4\">\n                  {reportCards.map((card) => {\n                    const summary = (card.jsonSnapshot as any)?.summary || {}\n                    const studentName = `${card.student.user.firstName} ${card.student.user.lastName}`\n\n                    return (\n                      <Card key={card.id} className=\"p-4\">\n                        <div className=\"flex flex-col space-y-3\">\n                          <div className=\"flex items-start justify-between\">\n                            <div className=\"flex items-center space-x-3\">\n                              <div className=\"h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center flex-shrink-0\">\n                                <span className=\"text-sm font-medium text-gray-700\">\n                                  {card.student.user.firstName[0]}{card.student.user.lastName[0]}\n                                </span>\n                              </div>\n                              <div className=\"min-w-0 flex-1\">\n                                <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100 truncate\">\n                                  {studentName}\n                                </h3>\n                                <p className=\"text-sm text-gray-500 dark:text-gray-400\">\n                                  {card.student.admissionNo} • {card.student.currentClass.name} - {card.student.currentSection.name}\n                                </p>\n                              </div>\n                            </div>\n                            <div className=\"flex items-center space-x-2\">\n                              <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(!!card.pdfPath)}`}>\n                                {getStatusText(!!card.pdfPath)}\n                              </span>\n                            </div>\n                          </div>\n\n                          <div className=\"grid grid-cols-2 gap-4 text-sm\">\n                            <div>\n                              <span className=\"font-medium text-gray-700 dark:text-gray-300\">Term:</span>\n                              <p className=\"text-gray-600 dark:text-gray-400\">{card.term.name}</p>\n                            </div>\n                            <div>\n                              <span className=\"font-medium text-gray-700 dark:text-gray-300\">Percentage:</span>\n                              <p className=\"text-gray-600 dark:text-gray-400\">{summary.percentage ? `${summary.percentage.toFixed(1)}%` : 'N/A'}</p>\n                            </div>\n                            <div>\n                              <span className=\"font-medium text-gray-700 dark:text-gray-300\">Grade:</span>\n                              <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getGradeBadgeColor(summary.grade || 'F')}`}>\n                                {summary.grade || 'N/A'}\n                              </span>\n                            </div>\n                            <div>\n                              <span className=\"font-medium text-gray-700 dark:text-gray-300\">Generated:</span>\n                              <p className=\"text-gray-600 dark:text-gray-400\">{new Date(card.generatedAt).toLocaleDateString()}</p>\n                            </div>\n                          </div>\n\n                          <div className=\"flex space-x-2 pt-2 border-t border-gray-200 dark:border-gray-700\">\n                            <Button\n                              variant=\"outline\"\n                              size=\"sm\"\n                              className=\"flex-1\"\n                              onClick={() => handleDownloadPDF(card.id, studentName)}\n                            >\n                              <Download className=\"w-4 h-4 mr-1\" />\n                              Download\n                            </Button>\n                          </div>\n                        </div>\n                      </Card>\n                    )\n                  })}\n                </div>\n              </>\n            )}\n          </CardContent>\n        </Card>\n      </div>\n    </DashboardLayout>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAqDA;AA9DA;;;;;;;;;;AAgEe,SAAS;IACtB,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,kWAAQ,EAAe,EAAE;IAC/D,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,kWAAQ,EAAS,EAAE;IAC7C,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,kWAAQ,EAAU,EAAE;IAClD,MAAM,CAAC,cAAc,gBAAgB,GAAG,IAAA,kWAAQ,EAAC;IACjD,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,kWAAQ,EAAC;IACnD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,IAAA,kWAAQ,EAAC;IACrD,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,kWAAQ,EAAC;IACvC,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,kWAAQ,EAAC;IAC7C,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,kWAAQ,EAAgB;IAElD,IAAA,mWAAS,EAAC;QACR;QACA;IACF,GAAG;QAAC;QAAc;QAAe;KAAe;IAEhD,MAAM,mBAAmB;QACvB,IAAI;YACF,WAAW;YACX,MAAM,SAAS,IAAI;YACnB,IAAI,iBAAiB,OAAO,OAAO,MAAM,CAAC,UAAU;YACpD,IAAI,kBAAkB,OAAO,OAAO,MAAM,CAAC,WAAW;YACtD,IAAI,mBAAmB,OAAO,OAAO,MAAM,CAAC,UAAU;YAEtD,MAAM,WAAW,MAAM,MAAM,CAAC,mBAAmB,EAAE,QAAQ;YAC3D,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,eAAe;YACjB,OAAO;gBACL,SAAS;YACX;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,SAAS;QACX,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,uBAAuB;QAC3B,IAAI;YACF,MAAM,CAAC,eAAe,gBAAgB,GAAG,MAAM,QAAQ,GAAG,CAAC;gBACzD,MAAM;gBACN,MAAM;aACP;YAED,IAAI,cAAc,EAAE,EAAE;gBACpB,MAAM,YAAY,MAAM,cAAc,IAAI;gBAC1C,SAAS;YACX;YAEA,IAAI,gBAAgB,EAAE,EAAE;gBACtB,MAAM,cAAc,MAAM,gBAAgB,IAAI;gBAC9C,WAAW,YAAY,OAAO,IAAI,EAAE;YACtC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qCAAqC;QACrD;IACF;IAEA,MAAM,wBAAwB;QAC5B,IAAI,iBAAiB,SAAS,kBAAkB,OAAO;YACrD,SAAS;YACT;QACF;QAEA,IAAI;YACF,cAAc;YACd,SAAS;YAET,MAAM,WAAW,MAAM,MAAM,sBAAsB;gBACjD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,QAAQ;oBACR,SAAS;gBACX;YACF;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,oBAAmB,mBAAmB;gBAC5C,SAAS;YACX,OAAO;gBACL,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,SAAS,UAAU,KAAK,IAAI;YAC9B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,SAAS;QACX,SAAU;YACR,cAAc;QAChB;IACF;IAEA,MAAM,oBAAoB,OAAO,cAAsB;QACrD,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,yBAAyB;gBACpD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,MAAM;oBACN,cAAc;gBAChB;YACF;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,MAAM,MAAM,OAAO,GAAG,CAAC,eAAe,CAAC;gBACvC,MAAM,IAAI,SAAS,aAAa,CAAC;gBACjC,EAAE,IAAI,GAAG;gBACT,EAAE,QAAQ,GAAG,CAAC,YAAY,EAAE,YAAY,OAAO,CAAC,QAAQ,KAAK,IAAI,CAAC;gBAClE,SAAS,IAAI,CAAC,WAAW,CAAC;gBAC1B,EAAE,KAAK;gBACP,OAAO,GAAG,CAAC,eAAe,CAAC;gBAC3B,SAAS,IAAI,CAAC,WAAW,CAAC;YAC5B,OAAO;gBACL,SAAS;YACX;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,SAAS;QACX;IACF;IAEA,MAAM,4BAA4B;QAChC,IAAI,iBAAiB,SAAS,kBAAkB,OAAO;YACrD,SAAS;YACT;QACF;QAEA,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,yBAAyB;gBACpD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,MAAM;oBACN,QAAQ;oBACR,SAAS;gBACX;YACF;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,MAAM,MAAM,OAAO,GAAG,CAAC,eAAe,CAAC;gBACvC,MAAM,IAAI,SAAS,aAAa,CAAC;gBACjC,EAAE,IAAI,GAAG;gBACT,EAAE,QAAQ,GAAG,CAAC,aAAa,EAAE,aAAa,CAAC,EAAE,cAAc,IAAI,CAAC;gBAChE,SAAS,IAAI,CAAC,WAAW,CAAC;gBAC1B,EAAE,KAAK;gBACP,OAAO,GAAG,CAAC,eAAe,CAAC;gBAC3B,SAAS,IAAI,CAAC,WAAW,CAAC;YAC5B,OAAO;gBACL,SAAS;YACX;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;YACjD,SAAS;QACX;IACF;IAEA,uBAAuB;IACvB,MAAM,QAAQ;QACZ,OAAO,YAAY,MAAM;QACzB,WAAW,YAAY,MAAM,CAAC,CAAA,IAAK,EAAE,OAAO,EAAE,MAAM;QACpD,SAAS,YAAY,MAAM,CAAC,CAAA,IAAK,CAAC,EAAE,OAAO,EAAE,MAAM;QACnD,mBAAmB,YAAY,MAAM,GAAG,IACpC,KAAK,KAAK,CAAC,YAAY,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,CAAC,AAAC,EAAE,YAAY,EAAU,SAAS,cAAc,CAAC,GAAG,KAAK,YAAY,MAAM,IAC5H;IACN;IACA,MAAM,iBAAiB,CAAC;QACtB,OAAO,SACH,gCACA;IACN;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAO,SAAS,cAAc;IAChC;IAMA,qBACE,+XAAC,gMAAe;QAAC,OAAM;QAAqB,YAAY,6KAAe;kBACrE,cAAA,+XAAC;YAAI,WAAU;;8BAEb,+XAAC;oBAAI,WAAU;;sCACb,+XAAC;;8CACC,+XAAC;oCAAG,WAAU;8CAA8C;;;;;;8CAC5D,+XAAC;oCAAE,WAAU;8CAAqC;;;;;;;;;;;;sCAEpD,+XAAC;4BAAI,WAAU;sCACb,cAAA,+XAAC,8KAAM;gCACL,SAAQ;gCACR,WAAU;gCACV,SAAS;gCACT,UAAU,iBAAiB,SAAS,kBAAkB;;kDAEtD,+XAAC,qVAAS;wCAAC,WAAU;;;;;;kDACrB,+XAAC;wCAAK,WAAU;kDAAY;;;;;;kDAC5B,+XAAC;wCAAK,WAAU;kDAAmB;;;;;;;;;;;;;;;;;;;;;;;gBAMxC,uBACC,+XAAC;oBAAI,WAAU;8BACb,cAAA,+XAAC;wBAAI,WAAU;;0CACb,+XAAC,yVAAW;gCAAC,WAAU;;;;;;0CACvB,+XAAC;gCAAI,WAAU;0CACb,cAAA,+XAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;;;;;;;;;;;8BAO7C,+XAAC;oBAAI,WAAU;;sCACb,+XAAC,0KAAI;;8CACH,+XAAC,gLAAU;oCAAC,WAAU;;sDACpB,+XAAC,+KAAS;4CAAC,WAAU;sDAAsB;;;;;;sDAC3C,+XAAC,gVAAQ;4CAAC,WAAU;;;;;;;;;;;;8CAEtB,+XAAC,iLAAW;8CACV,cAAA,+XAAC;wCAAI,WAAU;kDAAsB,MAAM,KAAK;;;;;;;;;;;;;;;;;sCAGpD,+XAAC,0KAAI;;8CACH,+XAAC,gLAAU;oCAAC,WAAU;;sDACpB,+XAAC,+KAAS;4CAAC,WAAU;sDAAsB;;;;;;sDAC3C,+XAAC,mUAAK;4CAAC,WAAU;;;;;;;;;;;;8CAEnB,+XAAC,iLAAW;8CACV,cAAA,+XAAC;wCAAI,WAAU;kDAAqC,MAAM,SAAS;;;;;;;;;;;;;;;;;sCAGvE,+XAAC,0KAAI;;8CACH,+XAAC,gLAAU;oCAAC,WAAU;;sDACpB,+XAAC,+KAAS;4CAAC,WAAU;sDAAsB;;;;;;sDAC3C,+XAAC,mUAAK;4CAAC,WAAU;;;;;;;;;;;;8CAEnB,+XAAC,iLAAW;8CACV,cAAA,+XAAC;wCAAI,WAAU;kDAAsC,MAAM,OAAO;;;;;;;;;;;;;;;;;sCAGtE,+XAAC,0KAAI;;8CACH,+XAAC,gLAAU;oCAAC,WAAU;;sDACpB,+XAAC,+KAAS;4CAAC,WAAU;sDAAsB;;;;;;sDAC3C,+XAAC,sVAAU;4CAAC,WAAU;;;;;;;;;;;;8CAExB,+XAAC,iLAAW;8CACV,cAAA,+XAAC;wCAAI,WAAU;;4CAAoC,MAAM,iBAAiB;4CAAC;;;;;;;;;;;;;;;;;;;;;;;;8BAMjF,+XAAC,0KAAI;;sCACH,+XAAC,gLAAU;;8CACT,+XAAC,+KAAS;oCAAC,WAAU;;sDACnB,+XAAC,gVAAQ;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAGvC,+XAAC,qLAAe;8CAAC;;;;;;;;;;;;sCAInB,+XAAC,iLAAW;sCACV,cAAA,+XAAC;gCAAI,WAAU;;kDACb,+XAAC;;0DACC,+XAAC,4KAAK;gDAAC,SAAQ;0DAAc;;;;;;0DAC7B,+XAAC;gDACC,IAAG;gDACH,OAAO;gDACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;gDAC/C,WAAU;;kEAEV,+XAAC;wDAAO,OAAM;kEAAM;;;;;;oDACnB,MAAM,GAAG,CAAC,CAAA,qBACT,+XAAC;4DAAqB,OAAO,KAAK,EAAE;;gEAAG,KAAK,IAAI;gEAAC;gEAAG,KAAK,YAAY;gEAAC;;2DAAzD,KAAK,EAAE;;;;;;;;;;;;;;;;;kDAI1B,+XAAC;;0DACC,+XAAC,4KAAK;gDAAC,SAAQ;0DAAe;;;;;;0DAC9B,+XAAC;gDACC,IAAG;gDACH,OAAO;gDACP,UAAU,CAAC,IAAM,iBAAiB,EAAE,MAAM,CAAC,KAAK;gDAChD,WAAU;;kEAEV,+XAAC;wDAAO,OAAM;kEAAM;;;;;;oDACnB,QAAQ,GAAG,CAAC,CAAA,oBACX,+XAAC;4DAAoB,OAAO,IAAI,EAAE;sEAAG,IAAI,IAAI;2DAAhC,IAAI,EAAE;;;;;;;;;;;;;;;;;kDAIzB,+XAAC;wCAAI,WAAU;kDACb,cAAA,+XAAC,8KAAM;4CACL,WAAU;4CACV,SAAS;4CACT,UAAU,cAAc,iBAAiB,SAAS,kBAAkB;sDAEnE,2BACC;;kEACE,+XAAC,kVAAO;wDAAC,WAAU;;;;;;oDAA8B;;6EAInD;;kEACE,+XAAC,gUAAI;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAW/C,+XAAC,0KAAI;;sCACH,+XAAC,gLAAU;sCACT,cAAA,+XAAC,+KAAS;0CAAC;;;;;;;;;;;sCAEb,+XAAC,iLAAW;sCACV,cAAA,+XAAC;gCAAI,WAAU;;kDACb,+XAAC;;0DACC,+XAAC,4KAAK;gDAAC,SAAQ;0DAAO;;;;;;0DACtB,+XAAC;gDACC,IAAG;gDACH,OAAO;gDACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;gDAC/C,WAAU;;kEAEV,+XAAC;wDAAO,OAAM;kEAAM;;;;;;oDACnB,MAAM,GAAG,CAAC,CAAA,qBACT,+XAAC;4DAAqB,OAAO,KAAK,EAAE;;gEAAG,KAAK,IAAI;gEAAC;gEAAG,KAAK,YAAY;gEAAC;;2DAAzD,KAAK,EAAE;;;;;;;;;;;;;;;;;kDAI1B,+XAAC;;0DACC,+XAAC,4KAAK;gDAAC,SAAQ;0DAAQ;;;;;;0DACvB,+XAAC;gDACC,IAAG;gDACH,OAAO;gDACP,UAAU,CAAC,IAAM,iBAAiB,EAAE,MAAM,CAAC,KAAK;gDAChD,WAAU;;kEAEV,+XAAC;wDAAO,OAAM;kEAAM;;;;;;oDACnB,QAAQ,GAAG,CAAC,CAAA,oBACX,+XAAC;4DAAoB,OAAO,IAAI,EAAE;sEAAG,IAAI,IAAI;2DAAhC,IAAI,EAAE;;;;;;;;;;;;;;;;;kDAIzB,+XAAC;;0DACC,+XAAC,4KAAK;gDAAC,SAAQ;0DAAS;;;;;;0DACxB,+XAAC;gDACC,IAAG;gDACH,OAAO;gDACP,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;gDACjD,WAAU;;kEAEV,+XAAC;wDAAO,OAAM;kEAAM;;;;;;kEACpB,+XAAC;wDAAO,OAAM;kEAAY;;;;;;kEAC1B,+XAAC;wDAAO,OAAM;kEAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQlC,+XAAC,0KAAI;;sCACH,+XAAC,gLAAU;;8CACT,+XAAC,+KAAS;8CAAC;;;;;;8CACX,+XAAC,qLAAe;8CAAC;;;;;;;;;;;;sCAInB,+XAAC,iLAAW;sCACT,wBACC,+XAAC;gCAAI,WAAU;;kDACb,+XAAC,kVAAO;wCAAC,WAAU;;;;;;kDACnB,+XAAC;wCAAE,WAAU;kDAAqB;;;;;;;;;;;uCAElC,YAAY,MAAM,KAAK,kBACzB,+XAAC;gCAAI,WAAU;;kDACb,+XAAC,gVAAQ;wCAAC,WAAU;;;;;;kDACpB,+XAAC;wCAAE,WAAU;kDAAgB;;;;;;kDAC7B,+XAAC;wCAAE,WAAU;kDAA6B;;;;;;;;;;;qDAG5C;;kDAEE,+XAAC;wCAAI,WAAU;kDACb,cAAA,+XAAC;4CAAM,WAAU;;8DACf,+XAAC;oDAAM,WAAU;8DACf,cAAA,+XAAC;;0EACC,+XAAC;gEAAG,WAAU;0EAAiF;;;;;;0EAG/F,+XAAC;gEAAG,WAAU;0EAAiF;;;;;;0EAG/F,+XAAC;gEAAG,WAAU;0EAAiF;;;;;;0EAG/F,+XAAC;gEAAG,WAAU;0EAAiF;;;;;;0EAG/F,+XAAC;gEAAG,WAAU;0EAAiF;;;;;;0EAG/F,+XAAC;gEAAG,WAAU;0EAAiF;;;;;;0EAG/F,+XAAC;gEAAG,WAAU;0EAAiF;;;;;;;;;;;;;;;;;8DAKnG,+XAAC;oDAAM,WAAU;8DACd,YAAY,GAAG,CAAC,CAAC;wDAChB,MAAM,UAAU,AAAC,KAAK,YAAY,EAAU,WAAW,CAAC;wDACxD,MAAM,cAAc,GAAG,KAAK,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,KAAK,OAAO,CAAC,IAAI,CAAC,QAAQ,EAAE;wDAElF,qBACE,+XAAC;;8EACC,+XAAC;oEAAG,WAAU;8EACZ,cAAA,+XAAC;wEAAI,WAAU;;0FACb,+XAAC;gFAAI,WAAU;0FACb,cAAA,+XAAC;oFAAI,WAAU;8FACb,cAAA,+XAAC;wFAAK,WAAU;;4FACb,KAAK,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE;4FAAE,KAAK,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE;;;;;;;;;;;;;;;;;0FAIpE,+XAAC;gFAAI,WAAU;;kGACb,+XAAC;wFAAI,WAAU;kGAAqC;;;;;;kGACpD,+XAAC;wFAAI,WAAU;kGAAyB,KAAK,OAAO,CAAC,WAAW;;;;;;;;;;;;;;;;;;;;;;;8EAItE,+XAAC;oEAAG,WAAU;;wEACX,KAAK,OAAO,CAAC,YAAY,CAAC,IAAI;wEAAC;wEAAI,KAAK,OAAO,CAAC,cAAc,CAAC,IAAI;;;;;;;8EAEtE,+XAAC;oEAAG,WAAU;8EACX,KAAK,IAAI,CAAC,IAAI;;;;;;8EAEjB,+XAAC;oEAAG,WAAU;8EACX,QAAQ,UAAU,GAAG,GAAG,QAAQ,UAAU,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG;;;;;;8EAE9D,+XAAC;oEAAG,WAAU;8EACZ,cAAA,+XAAC;wEAAK,WAAW,CAAC,yDAAyD,EAAE,IAAA,6KAAkB,EAAC,QAAQ,KAAK,IAAI,MAAM;kFACpH,QAAQ,KAAK,IAAI;;;;;;;;;;;8EAGtB,+XAAC;oEAAG,WAAU;8EACZ,cAAA,+XAAC;wEAAK,WAAW,CAAC,yDAAyD,EAAE,eAAe,CAAC,CAAC,KAAK,OAAO,GAAG;kFAC1G,cAAc,CAAC,CAAC,KAAK,OAAO;;;;;;;;;;;8EAGjC,+XAAC;oEAAG,WAAU;8EACN,cAAA,+XAAC;wEAAI,WAAU;kFACnB,cAAA,+XAAC,8KAAM;4EACL,SAAQ;4EACR,MAAK;4EACL,SAAS,IAAM,kBAAkB,KAAK,EAAE,EAAE;sFAE1C,cAAA,+XAAC,4UAAQ;gFAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;2DA1CnB,KAAK,EAAE;;;;;oDAgDpB;;;;;;;;;;;;;;;;;kDAMN,+XAAC;wCAAI,WAAU;kDACZ,YAAY,GAAG,CAAC,CAAC;4CAChB,MAAM,UAAU,AAAC,KAAK,YAAY,EAAU,WAAW,CAAC;4CACxD,MAAM,cAAc,GAAG,KAAK,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,KAAK,OAAO,CAAC,IAAI,CAAC,QAAQ,EAAE;4CAElF,qBACE,+XAAC,0KAAI;gDAAe,WAAU;0DAC5B,cAAA,+XAAC;oDAAI,WAAU;;sEACb,+XAAC;4DAAI,WAAU;;8EACb,+XAAC;oEAAI,WAAU;;sFACb,+XAAC;4EAAI,WAAU;sFACb,cAAA,+XAAC;gFAAK,WAAU;;oFACb,KAAK,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE;oFAAE,KAAK,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE;;;;;;;;;;;;sFAGlE,+XAAC;4EAAI,WAAU;;8FACb,+XAAC;oFAAG,WAAU;8FACX;;;;;;8FAEH,+XAAC;oFAAE,WAAU;;wFACV,KAAK,OAAO,CAAC,WAAW;wFAAC;wFAAI,KAAK,OAAO,CAAC,YAAY,CAAC,IAAI;wFAAC;wFAAI,KAAK,OAAO,CAAC,cAAc,CAAC,IAAI;;;;;;;;;;;;;;;;;;;8EAIvG,+XAAC;oEAAI,WAAU;8EACb,cAAA,+XAAC;wEAAK,WAAW,CAAC,yDAAyD,EAAE,eAAe,CAAC,CAAC,KAAK,OAAO,GAAG;kFAC1G,cAAc,CAAC,CAAC,KAAK,OAAO;;;;;;;;;;;;;;;;;sEAKnC,+XAAC;4DAAI,WAAU;;8EACb,+XAAC;;sFACC,+XAAC;4EAAK,WAAU;sFAA+C;;;;;;sFAC/D,+XAAC;4EAAE,WAAU;sFAAoC,KAAK,IAAI,CAAC,IAAI;;;;;;;;;;;;8EAEjE,+XAAC;;sFACC,+XAAC;4EAAK,WAAU;sFAA+C;;;;;;sFAC/D,+XAAC;4EAAE,WAAU;sFAAoC,QAAQ,UAAU,GAAG,GAAG,QAAQ,UAAU,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG;;;;;;;;;;;;8EAE9G,+XAAC;;sFACC,+XAAC;4EAAK,WAAU;sFAA+C;;;;;;sFAC/D,+XAAC;4EAAK,WAAW,CAAC,yDAAyD,EAAE,IAAA,6KAAkB,EAAC,QAAQ,KAAK,IAAI,MAAM;sFACpH,QAAQ,KAAK,IAAI;;;;;;;;;;;;8EAGtB,+XAAC;;sFACC,+XAAC;4EAAK,WAAU;sFAA+C;;;;;;sFAC/D,+XAAC;4EAAE,WAAU;sFAAoC,IAAI,KAAK,KAAK,WAAW,EAAE,kBAAkB;;;;;;;;;;;;;;;;;;sEAIlG,+XAAC;4DAAI,WAAU;sEACb,cAAA,+XAAC,8KAAM;gEACL,SAAQ;gEACR,MAAK;gEACL,WAAU;gEACV,SAAS,IAAM,kBAAkB,KAAK,EAAE,EAAE;;kFAE1C,+XAAC,4UAAQ;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;;;;;;;;;;;;+CArDlC,KAAK,EAAE;;;;;wCA4DtB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASlB", "debugId": null}}]}