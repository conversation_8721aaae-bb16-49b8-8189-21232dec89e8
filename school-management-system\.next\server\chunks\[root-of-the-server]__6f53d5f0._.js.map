{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 121, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/lib/db.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\r\n\r\nconst globalForPrisma = globalThis as unknown as {\r\n  prisma: PrismaClient | undefined\r\n}\r\n\r\nexport const prisma = globalForPrisma.prisma ?? new PrismaClient()\r\n\r\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\r\n"], "names": [], "mappings": ";;;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SAAS,gBAAgB,MAAM,IAAI,IAAI,6IAAY;AAEhE,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 134, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/lib/auth.ts"], "sourcesContent": ["import { NextAuthOptions } from 'next-auth'\r\nimport Credential<PERSON><PERSON>rovider from 'next-auth/providers/credentials'\r\nimport bcrypt from 'bcryptjs'\r\nimport { prisma } from './db'\r\n\r\nexport const authOptions: NextAuthOptions = {\r\n  providers: [\r\n    CredentialsProvider({\r\n      name: 'credentials',\r\n      credentials: {\r\n        email: { label: 'Email', type: 'email' },\r\n        password: { label: 'Password', type: 'password' }\r\n      },\r\n      async authorize(credentials) {\r\n        if (!credentials?.email || !credentials?.password) {\r\n          return null\r\n        }\r\n\r\n        try {\r\n          const user = await prisma.user.findUnique({\r\n            where: {\r\n              email: credentials.email\r\n            }\r\n          })\r\n\r\n          if (!user || !user.hashedPassword) {\r\n            return null\r\n          }\r\n\r\n          const isCorrectPassword = await bcrypt.compare(\r\n            credentials.password,\r\n            user.hashedPassword\r\n          )\r\n\r\n          if (!isCorrectPassword) {\r\n            return null\r\n          }\r\n\r\n          return {\r\n            id: user.id,\r\n            email: user.email,\r\n            name: `${user.firstName} ${user.lastName}`,\r\n            role: user.role,\r\n            firstName: user.firstName,\r\n            lastName: user.lastName\r\n          }\r\n        } catch (error) {\r\n          console.error('Auth error:', error)\r\n          return null\r\n        }\r\n      }\r\n    })\r\n  ],\r\n  session: {\r\n    strategy: 'jwt',\r\n    maxAge: 24 * 60 * 60, // 24 hours\r\n  },\r\n  callbacks: {\r\n    async jwt({ token, user }) {\r\n      if (user) {\r\n        token.role = user.role\r\n        token.firstName = user.firstName\r\n        token.lastName = user.lastName\r\n      }\r\n      return token\r\n    },\r\n    async session({ session, token }) {\r\n      if (token) {\r\n        session.user.id = token.sub!\r\n        session.user.role = token.role as string\r\n        session.user.firstName = token.firstName as string\r\n        session.user.lastName = token.lastName as string\r\n      }\r\n      return session\r\n    }\r\n  },\r\n  pages: {\r\n    signIn: '/login',\r\n    error: '/login'\r\n  },\r\n  secret: process.env.NEXTAUTH_SECRET\r\n}\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;;;;AAEO,MAAM,cAA+B;IAC1C,WAAW;QACT,IAAA,mTAAmB,EAAC;YAClB,MAAM;YACN,aAAa;gBACX,OAAO;oBAAE,OAAO;oBAAS,MAAM;gBAAQ;gBACvC,UAAU;oBAAE,OAAO;oBAAY,MAAM;gBAAW;YAClD;YACA,MAAM,WAAU,WAAW;gBACzB,IAAI,CAAC,aAAa,SAAS,CAAC,aAAa,UAAU;oBACjD,OAAO;gBACT;gBAEA,IAAI;oBACF,MAAM,OAAO,MAAM,8JAAM,CAAC,IAAI,CAAC,UAAU,CAAC;wBACxC,OAAO;4BACL,OAAO,YAAY,KAAK;wBAC1B;oBACF;oBAEA,IAAI,CAAC,QAAQ,CAAC,KAAK,cAAc,EAAE;wBACjC,OAAO;oBACT;oBAEA,MAAM,oBAAoB,MAAM,qOAAM,CAAC,OAAO,CAC5C,YAAY,QAAQ,EACpB,KAAK,cAAc;oBAGrB,IAAI,CAAC,mBAAmB;wBACtB,OAAO;oBACT;oBAEA,OAAO;wBACL,IAAI,KAAK,EAAE;wBACX,OAAO,KAAK,KAAK;wBACjB,MAAM,GAAG,KAAK,SAAS,CAAC,CAAC,EAAE,KAAK,QAAQ,EAAE;wBAC1C,MAAM,KAAK,IAAI;wBACf,WAAW,KAAK,SAAS;wBACzB,UAAU,KAAK,QAAQ;oBACzB;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,eAAe;oBAC7B,OAAO;gBACT;YACF;QACF;KACD;IACD,SAAS;QACP,UAAU;QACV,QAAQ,KAAK,KAAK;IACpB;IACA,WAAW;QACT,MAAM,KAAI,EAAE,KAAK,EAAE,IAAI,EAAE;YACvB,IAAI,MAAM;gBACR,MAAM,IAAI,GAAG,KAAK,IAAI;gBACtB,MAAM,SAAS,GAAG,KAAK,SAAS;gBAChC,MAAM,QAAQ,GAAG,KAAK,QAAQ;YAChC;YACA,OAAO;QACT;QACA,MAAM,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAE;YAC9B,IAAI,OAAO;gBACT,QAAQ,IAAI,CAAC,EAAE,GAAG,MAAM,GAAG;gBAC3B,QAAQ,IAAI,CAAC,IAAI,GAAG,MAAM,IAAI;gBAC9B,QAAQ,IAAI,CAAC,SAAS,GAAG,MAAM,SAAS;gBACxC,QAAQ,IAAI,CAAC,QAAQ,GAAG,MAAM,QAAQ;YACxC;YACA,OAAO;QACT;IACF;IACA,OAAO;QACL,QAAQ;QACR,OAAO;IACT;IACA,QAAQ,QAAQ,GAAG,CAAC,eAAe;AACrC", "debugId": null}}, {"offset": {"line": 223, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/app/api/admin/exams/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\r\nimport { getServerSession } from 'next-auth'\r\nimport { authOptions } from '@/lib/auth'\r\nimport { prisma } from '@/lib/db'\r\n\r\nexport async function GET(request: NextRequest) {\r\n  try {\r\n    const session = await getServerSession(authOptions)\r\n    \r\n    if (!session || session.user.role !== 'ADMIN') {\r\n      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })\r\n    }\r\n\r\n    const { searchParams } = new URL(request.url)\r\n    const type = searchParams.get('type') // 'terms' or 'exams'\r\n\r\n    if (type === 'terms') {\r\n      const terms = await prisma.term.findMany({\r\n        orderBy: { createdAt: 'desc' }\r\n      })\r\n      return NextResponse.json(terms)\r\n    } else if (type === 'exams') {\r\n      const exams = await prisma.exam.findMany({\r\n        include: {\r\n          term: true,\r\n          subject: true\r\n        },\r\n        orderBy: { date: 'desc' }\r\n      })\r\n      return NextResponse.json(exams)\r\n    }\r\n\r\n    return NextResponse.json({ error: 'Invalid type parameter' }, { status: 400 })\r\n  } catch (error) {\r\n    console.error('Error fetching exams/terms:', error)\r\n    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })\r\n  }\r\n}\r\n\r\nexport async function POST(request: NextRequest) {\r\n  try {\r\n    const session = await getServerSession(authOptions)\r\n    \r\n    if (!session || session.user.role !== 'ADMIN') {\r\n      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })\r\n    }\r\n\r\n    const body = await request.json()\r\n    const { type, data } = body\r\n\r\n    if (type === 'term') {\r\n      const term = await prisma.term.create({\r\n        data: {\r\n          name: data.name,\r\n          startDate: new Date(data.startDate),\r\n          endDate: new Date(data.endDate),\r\n          academicYear: data.academicYear\r\n        }\r\n      })\r\n      return NextResponse.json(term)\r\n    } else if (type === 'exam') {\r\n      const exam = await prisma.exam.create({\r\n        data: {\r\n          name: data.name,\r\n          termId: data.termId,\r\n          subjectId: data.subjectId,\r\n          maxMarks: data.maxMarks,\r\n          weightagePercent: data.weightagePercent,\r\n          date: new Date(data.date)\r\n        }\r\n      })\r\n      return NextResponse.json(exam)\r\n    }\r\n\r\n    return NextResponse.json({ error: 'Invalid type parameter' }, { status: 400 })\r\n  } catch (error) {\r\n    console.error('Error creating exam/term:', error)\r\n    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AACA;AACA;;;;;AAEO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,UAAU,MAAM,IAAA,ySAAgB,EAAC,qKAAW;QAElD,IAAI,CAAC,WAAW,QAAQ,IAAI,CAAC,IAAI,KAAK,SAAS;YAC7C,OAAO,iSAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAe,GAAG;gBAAE,QAAQ;YAAI;QACpE;QAEA,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,OAAO,aAAa,GAAG,CAAC,QAAQ,qBAAqB;;QAE3D,IAAI,SAAS,SAAS;YACpB,MAAM,QAAQ,MAAM,8JAAM,CAAC,IAAI,CAAC,QAAQ,CAAC;gBACvC,SAAS;oBAAE,WAAW;gBAAO;YAC/B;YACA,OAAO,iSAAY,CAAC,IAAI,CAAC;QAC3B,OAAO,IAAI,SAAS,SAAS;YAC3B,MAAM,QAAQ,MAAM,8JAAM,CAAC,IAAI,CAAC,QAAQ,CAAC;gBACvC,SAAS;oBACP,MAAM;oBACN,SAAS;gBACX;gBACA,SAAS;oBAAE,MAAM;gBAAO;YAC1B;YACA,OAAO,iSAAY,CAAC,IAAI,CAAC;QAC3B;QAEA,OAAO,iSAAY,CAAC,IAAI,CAAC;YAAE,OAAO;QAAyB,GAAG;YAAE,QAAQ;QAAI;IAC9E,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,OAAO,iSAAY,CAAC,IAAI,CAAC;YAAE,OAAO;QAAwB,GAAG;YAAE,QAAQ;QAAI;IAC7E;AACF;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,UAAU,MAAM,IAAA,ySAAgB,EAAC,qKAAW;QAElD,IAAI,CAAC,WAAW,QAAQ,IAAI,CAAC,IAAI,KAAK,SAAS;YAC7C,OAAO,iSAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAe,GAAG;gBAAE,QAAQ;YAAI;QACpE;QAEA,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG;QAEvB,IAAI,SAAS,QAAQ;YACnB,MAAM,OAAO,MAAM,8JAAM,CAAC,IAAI,CAAC,MAAM,CAAC;gBACpC,MAAM;oBACJ,MAAM,KAAK,IAAI;oBACf,WAAW,IAAI,KAAK,KAAK,SAAS;oBAClC,SAAS,IAAI,KAAK,KAAK,OAAO;oBAC9B,cAAc,KAAK,YAAY;gBACjC;YACF;YACA,OAAO,iSAAY,CAAC,IAAI,CAAC;QAC3B,OAAO,IAAI,SAAS,QAAQ;YAC1B,MAAM,OAAO,MAAM,8JAAM,CAAC,IAAI,CAAC,MAAM,CAAC;gBACpC,MAAM;oBACJ,MAAM,KAAK,IAAI;oBACf,QAAQ,KAAK,MAAM;oBACnB,WAAW,KAAK,SAAS;oBACzB,UAAU,KAAK,QAAQ;oBACvB,kBAAkB,KAAK,gBAAgB;oBACvC,MAAM,IAAI,KAAK,KAAK,IAAI;gBAC1B;YACF;YACA,OAAO,iSAAY,CAAC,IAAI,CAAC;QAC3B;QAEA,OAAO,iSAAY,CAAC,IAAI,CAAC;YAAE,OAAO;QAAyB,GAAG;YAAE,QAAQ;QAAI;IAC9E,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,OAAO,iSAAY,CAAC,IAAI,CAAC;YAAE,OAAO;QAAwB,GAAG;YAAE,QAAQ;QAAI;IAC7E;AACF", "debugId": null}}]}