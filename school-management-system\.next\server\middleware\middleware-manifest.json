{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/school-management-system_8c1ea602._.js", "server/edge/chunks/[root-of-the-server]__433d4839._.js", "server/edge/chunks/turbopack-school-management-system_edge-wrapper_db311ba0.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/admin/:path*{(\\\\.json)}?", "originalSource": "/admin/:path*"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/teacher/:path*{(\\\\.json)}?", "originalSource": "/teacher/:path*"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/student/:path*{(\\\\.json)}?", "originalSource": "/student/:path*"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/api/admin/:path*{(\\\\.json)}?", "originalSource": "/api/admin/:path*"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/api/teacher/:path*{(\\\\.json)}?", "originalSource": "/api/teacher/:path*"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/api/student/:path*{(\\\\.json)}?", "originalSource": "/api/student/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "BKXM2I+ssGQTqtUOWvedzQSNT5dkJLMwm4lU1Iki8iU=", "__NEXT_PREVIEW_MODE_ID": "b0cae12025e202e12bb1d5877bad6f62", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "ec378562086b56e1121f2129dc86e9b705755efe960c83eebf2880a629c2be14", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "752b98cf1d483bf37ba4c7641d13b3cdfe8e477ef90e0c1876b6f1dbf6997b23"}}}, "instrumentation": null, "functions": {}}