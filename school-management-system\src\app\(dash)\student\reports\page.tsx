'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import DashboardLayout from '@/components/layout/dashboard-layout'
import { studentNavigation } from '@/lib/navigation'
import {
  Calendar,
  FileText,
  Download,
  Award,
  TrendingUp,
  Eye,
  Printer,
  Loader2,
  AlertCircle
} from 'lucide-react'

interface ReportCard {
  id: string
  studentId: string
  termId: string
  jsonSnapshot: any
  pdfPath?: string
  generatedAt: string
  term: {
    name: string
    academicYear: string
  }
}

export default function StudentReportsPage() {
  const { data: session } = useSession()
  const [reportCards, setReportCards] = useState<ReportCard[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    fetchReportCards()
  }, [])

  const fetchReportCards = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/student/reports')
      if (response.ok) {
        const data = await response.json()
        setReportCards(data)
      } else {
        setError('Failed to fetch report cards')
      }
    } catch (error) {
      console.error('Error fetching report cards:', error)
      setError('Error fetching report cards')
    } finally {
      setLoading(false)
    }
  }

  const handleDownloadPDF = async (reportCardId: string, termName: string) => {
    try {
      const response = await fetch('/api/reports/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          type: 'report-card',
          reportCardId: reportCardId
        })
      })

      if (response.ok) {
        const blob = await response.blob()
        const url = window.URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = `report-card-${termName.replace(/\s+/g, '-')}.pdf`
        document.body.appendChild(a)
        a.click()
        window.URL.revokeObjectURL(url)
        document.body.removeChild(a)
      } else {
        setError('Failed to download PDF')
      }
    } catch (error) {
      console.error('Error downloading PDF:', error)
      setError('Error downloading PDF')
    }
  }

  const getStatusColor = (hasPdf: boolean) => {
    return hasPdf
      ? 'bg-green-100 text-green-800'
      : 'bg-yellow-100 text-yellow-800'
  }

  const getStatusText = (hasPdf: boolean) => {
    return hasPdf ? 'Available' : 'Pending'
  }

  const getGradeColor = (grade: string) => {
    switch (grade) {
      case 'A+':
        return 'bg-green-100 text-green-800'
      case 'A':
        return 'bg-green-100 text-green-800'
      case 'B+':
        return 'bg-blue-100 text-blue-800'
      case 'B':
        return 'bg-blue-100 text-blue-800'
      case 'C+':
        return 'bg-yellow-100 text-yellow-800'
      case 'C':
        return 'bg-yellow-100 text-yellow-800'
      case 'D':
        return 'bg-orange-100 text-orange-800'
      case 'F':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  // Calculate statistics from real data
  const reportStats = {
    total: reportCards.length,
    available: reportCards.filter(r => r.pdfPath).length,
    pending: reportCards.filter(r => !r.pdfPath).length,
    average: reportCards.length > 0
      ? Math.round(reportCards.reduce((sum, card) => {
          const summary = (card.jsonSnapshot as any)?.summary || {}
          return sum + (summary.percentage || 0)
        }, 0) / reportCards.length)
      : 0
  }

  return (
    <DashboardLayout title="My Report Cards" navigation={studentNavigation}>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">My Report Cards</h1>
            <p className="text-gray-600">View your academic performance reports</p>
          </div>
        </div>

        {/* Error Alert */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-md p-4">
            <div className="flex">
              <AlertCircle className="h-5 w-5 text-red-400" />
              <div className="ml-3">
                <p className="text-sm text-red-800">{error}</p>
              </div>
            </div>
          </div>
        )}

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Reports</CardTitle>
              <FileText className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{reportStats.total}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Average Score</CardTitle>
              <TrendingUp className="h-4 w-4 text-blue-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-600">{reportStats.average}%</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Available</CardTitle>
              <Award className="h-4 w-4 text-green-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">{reportStats.available}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Pending</CardTitle>
              <FileText className="h-4 w-4 text-orange-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-orange-600">{reportStats.pending}</div>
            </CardContent>
          </Card>
        </div>

        {/* Report Cards */}
        <Card>
          <CardHeader>
            <CardTitle>My Report Cards</CardTitle>
            <CardDescription>
              Your academic performance reports for each term
            </CardDescription>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="text-center py-8">
                <Loader2 className="h-8 w-8 animate-spin mx-auto" />
                <p className="mt-2 text-gray-600">Loading report cards...</p>
              </div>
            ) : reportCards.length === 0 ? (
              <div className="text-center py-8">
                <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-600">No report cards available</p>
                <p className="text-sm text-gray-500 mt-2">Report cards will appear here once generated by your teachers</p>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {reportCards.map((report) => {
                  const summary = (report.jsonSnapshot as any)?.summary || {}

                  return (
                    <Card key={report.id} className="hover:shadow-lg transition-shadow">
                      <CardHeader>
                        <div className="flex justify-between items-start">
                          <div>
                            <CardTitle className="flex items-center">
                              <FileText className="w-5 h-5 mr-2 text-blue-600" />
                              {report.term.name}
                            </CardTitle>
                            <CardDescription>{report.term.academicYear}</CardDescription>
                          </div>
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(!!report.pdfPath)}`}>
                            {getStatusText(!!report.pdfPath)}
                          </span>
                        </div>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-4">
                          <div className="grid grid-cols-2 gap-4">
                            <div>
                              <p className="text-sm text-gray-600">Total Marks</p>
                              <p className="text-lg font-semibold">{summary.totalMarks || 'N/A'}</p>
                            </div>
                            <div>
                              <p className="text-sm text-gray-600">Obtained</p>
                              <p className="text-lg font-semibold">{summary.obtainedMarks || 'N/A'}</p>
                            </div>
                          </div>

                          <div className="grid grid-cols-2 gap-4">
                            <div>
                              <p className="text-sm text-gray-600">Percentage</p>
                              <p className="text-lg font-semibold text-blue-600">
                                {summary.percentage ? `${summary.percentage.toFixed(1)}%` : 'N/A'}
                              </p>
                            </div>
                            <div>
                              <p className="text-sm text-gray-600">Grade</p>
                              <span className={`inline-flex px-2 py-1 text-sm font-semibold rounded-full ${getGradeColor(summary.grade || 'F')}`}>
                                {summary.grade || 'N/A'}
                              </span>
                    </div>
                          </div>

                          <div>
                            <p className="text-sm text-gray-600">Class Rank</p>
                            <p className="text-lg font-semibold text-purple-600">
                              #{summary.rank || 'N/A'}/{summary.totalStudents || 'N/A'}
                            </p>
                          </div>

                          <div className="pt-4 border-t">
                            <p className="text-xs text-gray-500">
                              Generated: {new Date(report.generatedAt).toLocaleDateString()}
                            </p>
                          </div>

                          <div className="flex space-x-2 pt-4">
                            {report.pdfPath ? (
                              <Button
                                variant="outline"
                                size="sm"
                                className="flex-1"
                                onClick={() => handleDownloadPDF(report.id, report.term.name)}
                              >
                                <Download className="w-4 h-4 mr-1" />
                                Download PDF
                              </Button>
                            ) : (
                              <Button variant="outline" size="sm" className="flex-1" disabled>
                                <FileText className="w-4 h-4 mr-1" />
                                PDF Pending
                              </Button>
                            )}
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  )
                })}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  )
}
