module.exports=[18622,(e,t,r)=>{t.exports=e.x("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js"))},56704,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/work-async-storage.external.js",()=>require("next/dist/server/app-render/work-async-storage.external.js"))},32319,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/work-unit-async-storage.external.js",()=>require("next/dist/server/app-render/work-unit-async-storage.external.js"))},20635,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/action-async-storage.external.js",()=>require("next/dist/server/app-render/action-async-storage.external.js"))},24725,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/after-task-async-storage.external.js",()=>require("next/dist/server/app-render/after-task-async-storage.external.js"))},24361,(e,t,r)=>{t.exports=e.x("util",()=>require("util"))},29173,(e,t,r)=>{t.exports=e.x("@prisma/client",()=>require("@prisma/client"))},70406,(e,t,r)=>{t.exports=e.x("next/dist/compiled/@opentelemetry/api",()=>require("next/dist/compiled/@opentelemetry/api"))},93695,(e,t,r)=>{t.exports=e.x("next/dist/shared/lib/no-fallback-error.external.js",()=>require("next/dist/shared/lib/no-fallback-error.external.js"))},61724,(e,t,r)=>{t.exports=e.x("next/dist/compiled/next-server/app-route-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-route-turbo.runtime.prod.js"))},54799,(e,t,r)=>{t.exports=e.x("crypto",()=>require("crypto"))},92509,(e,t,r)=>{t.exports=e.x("url",()=>require("url"))},21517,(e,t,r)=>{t.exports=e.x("http",()=>require("http"))},49719,(e,t,r)=>{t.exports=e.x("assert",()=>require("assert"))},45706,(e,t,r)=>{t.exports=e.x("querystring",()=>require("querystring"))},874,(e,t,r)=>{t.exports=e.x("buffer",()=>require("buffer"))},6461,(e,t,r)=>{t.exports=e.x("zlib",()=>require("zlib"))},24836,(e,t,r)=>{t.exports=e.x("https",()=>require("https"))},27699,(e,t,r)=>{t.exports=e.x("events",()=>require("events"))},12756,e=>{e.v(JSON.parse('{"name":"openid-client","version":"5.7.1","description":"OpenID Connect Relying Party (RP, Client) implementation for Node.js runtime, supports passportjs","keywords":["auth","authentication","basic","certified","client","connect","dynamic","electron","hybrid","identity","implicit","oauth","oauth2","oidc","openid","passport","relying party","strategy"],"homepage":"https://github.com/panva/openid-client","repository":"panva/openid-client","funding":{"url":"https://github.com/sponsors/panva"},"license":"MIT","author":"Filip Skokan <<EMAIL>>","exports":{"types":"./types/index.d.ts","import":"./lib/index.mjs","require":"./lib/index.js"},"main":"./lib/index.js","types":"./types/index.d.ts","files":["lib","types/index.d.ts"],"scripts":{"format":"npx prettier --loglevel silent --write ./lib ./test ./certification ./types","test":"mocha test/**/*.test.js"},"dependencies":{"jose":"^4.15.9","lru-cache":"^6.0.0","object-hash":"^2.2.0","oidc-token-hash":"^5.0.3"},"devDependencies":{"@types/node":"^16.18.106","@types/passport":"^1.0.16","base64url":"^3.0.1","chai":"^4.5.0","mocha":"^10.7.3","nock":"^13.5.5","prettier":"^2.8.8","readable-mock-req":"^0.2.2","sinon":"^9.2.4","timekeeper":"^2.3.1"},"standard-version":{"scripts":{"postchangelog":"sed -i \'\' -e \'s/### \\\\[/## [/g\' CHANGELOG.md"},"types":[{"type":"feat","section":"Features"},{"type":"fix","section":"Fixes"},{"type":"chore","hidden":true},{"type":"docs","hidden":true},{"type":"style","hidden":true},{"type":"refactor","section":"Refactor","hidden":false},{"type":"perf","section":"Performance","hidden":false},{"type":"test","hidden":true}]}}'))},12276,e=>{"use strict";e.s(["hasPermission",()=>r]);let t={ADMIN:["users:read","users:write","users:delete","students:read","students:write","students:delete","teachers:read","teachers:write","teachers:delete","classes:read","classes:write","classes:delete","subjects:read","subjects:write","subjects:delete","attendance:read","attendance:write","marks:read","marks:write","reports:read","reports:write","settings:read","settings:write","audit:read"],TEACHER:["students:read","attendance:read","attendance:write","marks:read","marks:write","reports:read"],STUDENT:["attendance:read","marks:read","reports:read"]};function r(e,r){return t[e]?.includes(r)??!1}},90619,(e,t,r)=>{},15919,e=>{"use strict";e.s(["handler",()=>I,"patchFetch",()=>S,"routeModule",()=>T,"serverHooks",()=>P,"workAsyncStorage",()=>k,"workUnitAsyncStorage",()=>O],15919);var t=e.i(6137),r=e.i(11365),a=e.i(9638),s=e.i(15243),n=e.i(66378),i=e.i(92101),o=e.i(50012),l=e.i(62885),d=e.i(31409),u=e.i(78448),p=e.i(28015),c=e.i(72721),h=e.i(75714),m=e.i(12634),x=e.i(93695);e.i(74732);var f=e.i(66662);e.s(["GET",()=>b,"POST",()=>C,"PUT",()=>q],20456);var g=e.i(2835),y=e.i(58356),w=e.i(43382),R=e.i(31279),v=e.i(12276),E=e.i(47504),j=e.i(24638);let N=E.z.object({firstName:E.z.string().min(1,"First name is required"),lastName:E.z.string().min(1,"Last name is required"),email:E.z.string().email("Invalid email address"),phone:E.z.string().optional(),dateOfBirth:E.z.string().optional(),gender:E.z.enum(["MALE","FEMALE","OTHER"]).optional(),address:E.z.string().optional(),qualification:E.z.string().optional(),experience:E.z.number().min(0).optional(),joiningDate:E.z.string().optional(),salary:E.z.number().min(0).optional(),isActive:E.z.boolean().default(!0)});async function b(e){try{let{searchParams:t}=new URL(e.url),r=parseInt(t.get("page")||"1"),a=parseInt(t.get("limit")||"10"),s=t.get("search")||"";t.get("isActive");let n=(await R.prisma.teacher.findMany({include:{user:!0,attendances:!0,marks:!0},orderBy:{createdAt:"desc"}})).map(e=>({id:e.id,userId:e.userId,employeeCode:e.employeeCode,qualification:e.qualification,phoneAlt:e.phoneAlt,joinedOn:e.joinedOn,createdAt:e.createdAt,updatedAt:e.updatedAt,firstName:e.user.firstName,lastName:e.user.lastName,email:e.user.email,phone:e.user.phone,role:e.user.role,isActive:!0,gender:null,experience:null,classes:[],subjects:[],user:e.user,attendances:e.attendances,marks:e.marks})),i=n;s&&(i=n.filter(e=>e.firstName?.toLowerCase().includes(s.toLowerCase())||e.lastName?.toLowerCase().includes(s.toLowerCase())||e.email?.toLowerCase().includes(s.toLowerCase())));let o=i.length,l=Math.ceil(o/a),d=(r-1)*a,u=i.slice(d,d+a);return g.NextResponse.json({teachers:u,pagination:{page:r,limit:a,total:o,totalPages:l}})}catch(e){return console.error("Error fetching teachers:",e),g.NextResponse.json({error:"Failed to fetch teachers"},{status:500})}}async function C(e){try{let t=await e.json(),r=N.parse(t);if(await R.prisma.user.findUnique({where:{email:r.email}}))return g.NextResponse.json({error:"Email already exists"},{status:400});let a=await R.prisma.teacher.count(),s=`T${String(a+1).padStart(3,"0")}`,n=await R.prisma.teacher.create({data:{employeeCode:s,dateOfBirth:r.dateOfBirth?new Date(r.dateOfBirth):null,gender:r.gender||null,address:r.address||null,qualification:r.qualification||null,experience:r.experience||null,joinedOn:r.joiningDate?new Date(r.joiningDate):new Date,salary:r.salary||null,isActive:r.isActive??!0,user:{create:{email:r.email,hashedPassword:await j.default.hash("Teacher@12345",12),role:"TEACHER",firstName:r.firstName,lastName:r.lastName,phone:r.phone||null}}},include:{user:{select:{id:!0,email:!0,role:!0,firstName:!0,lastName:!0}}}});return g.NextResponse.json({message:"Teacher created successfully",teacher:n,credentials:{email:r.email,password:"Teacher@12345"}},{status:201})}catch(e){if(e instanceof E.z.ZodError)return g.NextResponse.json({error:"Validation error",details:e.errors},{status:400});return console.error("Error creating teacher:",e),g.NextResponse.json({error:"Failed to create teacher"},{status:500})}}async function q(e){try{let t=await (0,y.getServerSession)(w.authOptions);if(!t?.user||!(0,v.hasPermission)(t.user.role,"teachers:write"))return g.NextResponse.json({error:"Unauthorized"},{status:401});let{id:r,...a}=await e.json();if(!r)return g.NextResponse.json({error:"Teacher ID is required"},{status:400});let s=N.partial().parse(a);if(!await R.prisma.teacher.findUnique({where:{id:parseInt(r)}}))return g.NextResponse.json({error:"Teacher not found"},{status:404});let n=await R.prisma.teacher.update({where:{id:parseInt(r)},data:s,include:{user:{select:{id:!0,email:!0,role:!0}},attendances:!0,marks:!0}});return g.NextResponse.json({message:"Teacher updated successfully",teacher:n})}catch(e){if(e instanceof E.z.ZodError)return g.NextResponse.json({error:"Validation error",details:e.errors},{status:400});return console.error("Error updating teacher:",e),g.NextResponse.json({error:"Failed to update teacher"},{status:500})}}var A=e.i(20456);let T=new t.AppRouteRouteModule({definition:{kind:r.RouteKind.APP_ROUTE,page:"/api/admin/teachers/route",pathname:"/api/admin/teachers",filename:"route",bundlePath:""},distDir:".next",relativeProjectDir:"",resolvedPagePath:"[project]/school-management-system/src/app/api/admin/teachers/route.ts",nextConfigOutput:"",userland:A}),{workAsyncStorage:k,workUnitAsyncStorage:O,serverHooks:P}=T;function S(){return(0,a.patchFetch)({workAsyncStorage:k,workUnitAsyncStorage:O})}async function I(e,t,a){var g;let y="/api/admin/teachers/route";y=y.replace(/\/index$/,"")||"/";let w=await T.prepare(e,t,{srcPage:y,multiZoneDraftMode:!1});if(!w)return t.statusCode=400,t.end("Bad Request"),null==a.waitUntil||a.waitUntil.call(a,Promise.resolve()),null;let{buildId:R,params:v,nextConfig:E,isDraftMode:j,prerenderManifest:N,routerServerContext:b,isOnDemandRevalidate:C,revalidateOnlyGenerated:q,resolvedPathname:A}=w,k=(0,i.normalizeAppPath)(y),O=!!(N.dynamicRoutes[k]||N.routes[A]);if(O&&!j){let e=!!N.routes[A],t=N.dynamicRoutes[k];if(t&&!1===t.fallback&&!e)throw new x.NoFallbackError}let P=null;!O||T.isDev||j||(P="/index"===(P=A)?"/":P);let S=!0===T.isDev||!O,I=O&&!S,D=e.method||"GET",U=(0,n.getTracer)(),H=U.getActiveScopeSpan(),M={params:v,prerenderManifest:N,renderOpts:{experimental:{cacheComponents:!!E.experimental.cacheComponents,authInterrupts:!!E.experimental.authInterrupts},supportsDynamicResponse:S,incrementalCache:(0,s.getRequestMeta)(e,"incrementalCache"),cacheLifeProfiles:null==(g=E.experimental)?void 0:g.cacheLife,isRevalidate:I,waitUntil:a.waitUntil,onClose:e=>{t.on("close",e)},onAfterTaskError:void 0,onInstrumentationRequestError:(t,r,a)=>T.onRequestError(e,t,a,b)},sharedContext:{buildId:R}},_=new o.NodeNextRequest(e),z=new o.NodeNextResponse(t),F=l.NextRequestAdapter.fromNodeNextRequest(_,(0,l.signalFromNodeResponse)(t));try{let i=async r=>T.handle(F,M).finally(()=>{if(!r)return;r.setAttributes({"http.status_code":t.statusCode,"next.rsc":!1});let a=U.getRootSpanAttributes();if(!a)return;if(a.get("next.span_type")!==d.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${a.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let s=a.get("next.route");if(s){let e=`${D} ${s}`;r.setAttributes({"next.route":s,"http.route":s,"next.span_name":e}),r.updateName(e)}else r.updateName(`${D} ${e.url}`)}),o=async n=>{var o,l;let d=async({previousCacheEntry:r})=>{try{if(!(0,s.getRequestMeta)(e,"minimalMode")&&C&&q&&!r)return t.statusCode=404,t.setHeader("x-nextjs-cache","REVALIDATED"),t.end("This page could not be found"),null;let o=await i(n);e.fetchMetrics=M.renderOpts.fetchMetrics;let l=M.renderOpts.pendingWaitUntil;l&&a.waitUntil&&(a.waitUntil(l),l=void 0);let d=M.renderOpts.collectedTags;if(!O)return await (0,p.sendResponse)(_,z,o,M.renderOpts.pendingWaitUntil),null;{let e=await o.blob(),t=(0,c.toNodeOutgoingHttpHeaders)(o.headers);d&&(t[m.NEXT_CACHE_TAGS_HEADER]=d),!t["content-type"]&&e.type&&(t["content-type"]=e.type);let r=void 0!==M.renderOpts.collectedRevalidate&&!(M.renderOpts.collectedRevalidate>=m.INFINITE_CACHE)&&M.renderOpts.collectedRevalidate,a=void 0===M.renderOpts.collectedExpire||M.renderOpts.collectedExpire>=m.INFINITE_CACHE?void 0:M.renderOpts.collectedExpire;return{value:{kind:f.CachedRouteKind.APP_ROUTE,status:o.status,body:Buffer.from(await e.arrayBuffer()),headers:t},cacheControl:{revalidate:r,expire:a}}}}catch(t){throw(null==r?void 0:r.isStale)&&await T.onRequestError(e,t,{routerKind:"App Router",routePath:y,routeType:"route",revalidateReason:(0,u.getRevalidateReason)({isRevalidate:I,isOnDemandRevalidate:C})},b),t}},x=await T.handleResponse({req:e,nextConfig:E,cacheKey:P,routeKind:r.RouteKind.APP_ROUTE,isFallback:!1,prerenderManifest:N,isRoutePPREnabled:!1,isOnDemandRevalidate:C,revalidateOnlyGenerated:q,responseGenerator:d,waitUntil:a.waitUntil});if(!O)return null;if((null==x||null==(o=x.value)?void 0:o.kind)!==f.CachedRouteKind.APP_ROUTE)throw Object.defineProperty(Error(`Invariant: app-route received invalid cache entry ${null==x||null==(l=x.value)?void 0:l.kind}`),"__NEXT_ERROR_CODE",{value:"E701",enumerable:!1,configurable:!0});(0,s.getRequestMeta)(e,"minimalMode")||t.setHeader("x-nextjs-cache",C?"REVALIDATED":x.isMiss?"MISS":x.isStale?"STALE":"HIT"),j&&t.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");let g=(0,c.fromNodeOutgoingHttpHeaders)(x.value.headers);return(0,s.getRequestMeta)(e,"minimalMode")&&O||g.delete(m.NEXT_CACHE_TAGS_HEADER),!x.cacheControl||t.getHeader("Cache-Control")||g.get("Cache-Control")||g.set("Cache-Control",(0,h.getCacheControlHeader)(x.cacheControl)),await (0,p.sendResponse)(_,z,new Response(x.value.body,{headers:g,status:x.value.status||200})),null};H?await o(H):await U.withPropagatedContext(e.headers,()=>U.trace(d.BaseServerSpan.handleRequest,{spanName:`${D} ${e.url}`,kind:n.SpanKind.SERVER,attributes:{"http.method":D,"http.target":e.url}},o))}catch(t){if(H||t instanceof x.NoFallbackError||await T.onRequestError(e,t,{routerKind:"App Router",routePath:k,routeType:"route",revalidateReason:(0,u.getRevalidateReason)({isRevalidate:I,isOnDemandRevalidate:C})}),O)throw t;return await (0,p.sendResponse)(_,z,new Response(null,{status:500})),null}}}];

//# sourceMappingURL=%5Broot-of-the-server%5D__14810377._.js.map