module.exports=[18622,(e,t,r)=>{t.exports=e.x("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js"))},56704,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/work-async-storage.external.js",()=>require("next/dist/server/app-render/work-async-storage.external.js"))},32319,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/work-unit-async-storage.external.js",()=>require("next/dist/server/app-render/work-unit-async-storage.external.js"))},20635,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/action-async-storage.external.js",()=>require("next/dist/server/app-render/action-async-storage.external.js"))},24725,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/after-task-async-storage.external.js",()=>require("next/dist/server/app-render/after-task-async-storage.external.js"))},29173,(e,t,r)=>{t.exports=e.x("@prisma/client",()=>require("@prisma/client"))},70406,(e,t,r)=>{t.exports=e.x("next/dist/compiled/@opentelemetry/api",()=>require("next/dist/compiled/@opentelemetry/api"))},93695,(e,t,r)=>{t.exports=e.x("next/dist/shared/lib/no-fallback-error.external.js",()=>require("next/dist/shared/lib/no-fallback-error.external.js"))},61724,(e,t,r)=>{t.exports=e.x("next/dist/compiled/next-server/app-route-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-route-turbo.runtime.prod.js"))},90065,(e,t,r)=>{},81878,e=>{"use strict";e.s(["handler",()=>P,"patchFetch",()=>q,"routeModule",()=>j,"serverHooks",()=>A,"workAsyncStorage",()=>I,"workUnitAsyncStorage",()=>b],81878);var t=e.i(6137),r=e.i(11365),n=e.i(9638),a=e.i(15243),s=e.i(66378),i=e.i(92101),o=e.i(50012),d=e.i(62885),l=e.i(31409),u=e.i(78448),p=e.i(28015),c=e.i(72721),m=e.i(75714),x=e.i(12634),h=e.i(93695);e.i(74732);var f=e.i(66662);e.s(["DELETE",()=>N,"GET",()=>E,"PUT",()=>y],58651);var g=e.i(2835),R=e.i(31279),w=e.i(47504);let v=w.z.object({firstName:w.z.string().min(1,"First name is required").optional(),lastName:w.z.string().min(1,"Last name is required").optional(),email:w.z.string().email("Invalid email address").optional(),dateOfBirth:w.z.string().regex(/^\d{4}-\d{2}-\d{2}$/,"Date must be in YYYY-MM-DD format").optional(),gender:w.z.enum(["MALE","FEMALE","OTHER"]).optional(),phoneNumber:w.z.string().optional(),address:w.z.string().optional(),emergencyContact:w.z.string().optional(),emergencyPhone:w.z.string().optional(),admissionDate:w.z.string().regex(/^\d{4}-\d{2}-\d{2}$/,"Date must be in YYYY-MM-DD format").optional(),classId:w.z.string().optional(),parentName:w.z.string().optional(),parentPhone:w.z.string().optional(),parentEmail:w.z.string().email("Invalid parent email").optional()});async function E(e,{params:t}){try{let{id:e}=await t,r=await R.prisma.student.findUnique({where:{id:parseInt(e)},include:{user:!0,currentClass:!0,currentSection:!0}});if(!r)return g.NextResponse.json({error:"Student not found"},{status:404});return g.NextResponse.json({student:r})}catch(e){return console.error("Error fetching student:",e),g.NextResponse.json({error:"Internal server error"},{status:500})}}async function y(e,{params:t}){try{let{id:r}=await t,n=await e.json(),a=v.parse(n),s=await R.prisma.student.findUnique({where:{id:parseInt(r)},include:{user:!0}});if(!s)return g.NextResponse.json({error:"Student not found"},{status:404});if(a.email&&a.email!==s.user?.email&&await R.prisma.user.findUnique({where:{email:a.email}}))return g.NextResponse.json({error:"A user with this email already exists"},{status:400});let i=null,o=null;if(a.classId){if([i,o]=a.classId.split("-"),!await R.prisma.class.findUnique({where:{id:parseInt(i)}}))return g.NextResponse.json({error:"Class not found"},{status:400});if(o&&!await R.prisma.section.findUnique({where:{id:parseInt(o)}}))return g.NextResponse.json({error:"Section not found"},{status:400})}let d={};a.firstName&&(d.firstName=a.firstName),a.lastName&&(d.lastName=a.lastName),a.email&&(d.email=a.email),a.phoneNumber&&(d.phone=a.phoneNumber);let l={};a.dateOfBirth&&(l.dateOfBirth=new Date(a.dateOfBirth)),a.gender&&(l.gender=a.gender),a.address&&(l.address=a.address),a.emergencyContact&&(l.guardianName=a.emergencyContact),a.emergencyPhone&&(l.guardianPhone=a.emergencyPhone),i&&(l.currentClassId=parseInt(i)),o&&(l.currentSectionId=parseInt(o)),await R.prisma.user.update({where:{id:s.userId},data:d});let u=await R.prisma.student.update({where:{id:parseInt(r)},data:l,include:{user:!0,currentClass:!0,currentSection:!0}});return g.NextResponse.json({message:"Student updated successfully",student:u})}catch(e){if(e instanceof w.z.ZodError)return g.NextResponse.json({error:"Validation error",details:e.format()},{status:400});return console.error("Error updating student:",e),g.NextResponse.json({error:"Internal server error"},{status:500})}}async function N(e,{params:t}){try{let{id:e}=await t,r=await R.prisma.student.findUnique({where:{id:parseInt(e)},include:{user:!0}});if(!r)return g.NextResponse.json({error:"Student not found"},{status:404});return await R.prisma.student.delete({where:{id:parseInt(e)}}),await R.prisma.user.delete({where:{id:r.userId}}),g.NextResponse.json({message:"Student deleted successfully"})}catch(e){return console.error("Error deleting student:",e),g.NextResponse.json({error:"Internal server error"},{status:500})}}var C=e.i(58651);let j=new t.AppRouteRouteModule({definition:{kind:r.RouteKind.APP_ROUTE,page:"/api/admin/students/[id]/route",pathname:"/api/admin/students/[id]",filename:"route",bundlePath:""},distDir:".next",relativeProjectDir:"",resolvedPagePath:"[project]/school-management-system/src/app/api/admin/students/[id]/route.ts",nextConfigOutput:"",userland:C}),{workAsyncStorage:I,workUnitAsyncStorage:b,serverHooks:A}=j;function q(){return(0,n.patchFetch)({workAsyncStorage:I,workUnitAsyncStorage:b})}async function P(e,t,n){var g;let R="/api/admin/students/[id]/route";R=R.replace(/\/index$/,"")||"/";let w=await j.prepare(e,t,{srcPage:R,multiZoneDraftMode:!1});if(!w)return t.statusCode=400,t.end("Bad Request"),null==n.waitUntil||n.waitUntil.call(n,Promise.resolve()),null;let{buildId:v,params:E,nextConfig:y,isDraftMode:N,prerenderManifest:C,routerServerContext:I,isOnDemandRevalidate:b,revalidateOnlyGenerated:A,resolvedPathname:q}=w,P=(0,i.normalizeAppPath)(R),S=!!(C.dynamicRoutes[P]||C.routes[q]);if(S&&!N){let e=!!C.routes[q],t=C.dynamicRoutes[P];if(t&&!1===t.fallback&&!e)throw new h.NoFallbackError}let O=null;!S||j.isDev||N||(O="/index"===(O=q)?"/":O);let T=!0===j.isDev||!S,U=S&&!T,k=e.method||"GET",D=(0,s.getTracer)(),M=D.getActiveScopeSpan(),_={params:E,prerenderManifest:C,renderOpts:{experimental:{cacheComponents:!!y.experimental.cacheComponents,authInterrupts:!!y.experimental.authInterrupts},supportsDynamicResponse:T,incrementalCache:(0,a.getRequestMeta)(e,"incrementalCache"),cacheLifeProfiles:null==(g=y.experimental)?void 0:g.cacheLife,isRevalidate:U,waitUntil:n.waitUntil,onClose:e=>{t.on("close",e)},onAfterTaskError:void 0,onInstrumentationRequestError:(t,r,n)=>j.onRequestError(e,t,n,I)},sharedContext:{buildId:v}},H=new o.NodeNextRequest(e),z=new o.NodeNextResponse(t),$=d.NextRequestAdapter.fromNodeNextRequest(H,(0,d.signalFromNodeResponse)(t));try{let i=async r=>j.handle($,_).finally(()=>{if(!r)return;r.setAttributes({"http.status_code":t.statusCode,"next.rsc":!1});let n=D.getRootSpanAttributes();if(!n)return;if(n.get("next.span_type")!==l.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${n.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let a=n.get("next.route");if(a){let e=`${k} ${a}`;r.setAttributes({"next.route":a,"http.route":a,"next.span_name":e}),r.updateName(e)}else r.updateName(`${k} ${e.url}`)}),o=async s=>{var o,d;let l=async({previousCacheEntry:r})=>{try{if(!(0,a.getRequestMeta)(e,"minimalMode")&&b&&A&&!r)return t.statusCode=404,t.setHeader("x-nextjs-cache","REVALIDATED"),t.end("This page could not be found"),null;let o=await i(s);e.fetchMetrics=_.renderOpts.fetchMetrics;let d=_.renderOpts.pendingWaitUntil;d&&n.waitUntil&&(n.waitUntil(d),d=void 0);let l=_.renderOpts.collectedTags;if(!S)return await (0,p.sendResponse)(H,z,o,_.renderOpts.pendingWaitUntil),null;{let e=await o.blob(),t=(0,c.toNodeOutgoingHttpHeaders)(o.headers);l&&(t[x.NEXT_CACHE_TAGS_HEADER]=l),!t["content-type"]&&e.type&&(t["content-type"]=e.type);let r=void 0!==_.renderOpts.collectedRevalidate&&!(_.renderOpts.collectedRevalidate>=x.INFINITE_CACHE)&&_.renderOpts.collectedRevalidate,n=void 0===_.renderOpts.collectedExpire||_.renderOpts.collectedExpire>=x.INFINITE_CACHE?void 0:_.renderOpts.collectedExpire;return{value:{kind:f.CachedRouteKind.APP_ROUTE,status:o.status,body:Buffer.from(await e.arrayBuffer()),headers:t},cacheControl:{revalidate:r,expire:n}}}}catch(t){throw(null==r?void 0:r.isStale)&&await j.onRequestError(e,t,{routerKind:"App Router",routePath:R,routeType:"route",revalidateReason:(0,u.getRevalidateReason)({isRevalidate:U,isOnDemandRevalidate:b})},I),t}},h=await j.handleResponse({req:e,nextConfig:y,cacheKey:O,routeKind:r.RouteKind.APP_ROUTE,isFallback:!1,prerenderManifest:C,isRoutePPREnabled:!1,isOnDemandRevalidate:b,revalidateOnlyGenerated:A,responseGenerator:l,waitUntil:n.waitUntil});if(!S)return null;if((null==h||null==(o=h.value)?void 0:o.kind)!==f.CachedRouteKind.APP_ROUTE)throw Object.defineProperty(Error(`Invariant: app-route received invalid cache entry ${null==h||null==(d=h.value)?void 0:d.kind}`),"__NEXT_ERROR_CODE",{value:"E701",enumerable:!1,configurable:!0});(0,a.getRequestMeta)(e,"minimalMode")||t.setHeader("x-nextjs-cache",b?"REVALIDATED":h.isMiss?"MISS":h.isStale?"STALE":"HIT"),N&&t.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");let g=(0,c.fromNodeOutgoingHttpHeaders)(h.value.headers);return(0,a.getRequestMeta)(e,"minimalMode")&&S||g.delete(x.NEXT_CACHE_TAGS_HEADER),!h.cacheControl||t.getHeader("Cache-Control")||g.get("Cache-Control")||g.set("Cache-Control",(0,m.getCacheControlHeader)(h.cacheControl)),await (0,p.sendResponse)(H,z,new Response(h.value.body,{headers:g,status:h.value.status||200})),null};M?await o(M):await D.withPropagatedContext(e.headers,()=>D.trace(l.BaseServerSpan.handleRequest,{spanName:`${k} ${e.url}`,kind:s.SpanKind.SERVER,attributes:{"http.method":k,"http.target":e.url}},o))}catch(t){if(M||t instanceof h.NoFallbackError||await j.onRequestError(e,t,{routerKind:"App Router",routePath:P,routeType:"route",revalidateReason:(0,u.getRevalidateReason)({isRevalidate:U,isOnDemandRevalidate:b})}),S)throw t;return await (0,p.sendResponse)(H,z,new Response(null,{status:500})),null}}}];

//# sourceMappingURL=%5Broot-of-the-server%5D__6400aa05._.js.map