{"version": 3, "sources": ["turbopack:///[turbopack]/shared/runtime-utils.ts", "turbopack:///[turbopack]/browser/runtime/base/runtime-base.ts", "turbopack:///[turbopack]/browser/runtime/base/build-base.ts", "turbopack:///[turbopack]/shared-node/base-externals-utils.ts", "turbopack:///[turbopack]/browser/runtime/edge/runtime-backend-edge.ts"], "sourcesContent": ["/**\r\n * This file contains runtime types and functions that are shared between all\r\n * TurboPack ECMAScript runtimes.\r\n *\r\n * It will be prepended to the runtime code of each runtime.\r\n */\r\n\r\n/* eslint-disable @typescript-eslint/no-unused-vars */\r\n\r\n/// <reference path=\"./runtime-types.d.ts\" />\r\n\r\ntype EsmNamespaceObject = Record<string, any>\r\n\r\n// @ts-ignore Defined in `dev-base.ts`\r\ndeclare function getOrInstantiateModuleFromParent<M>(\r\n  id: ModuleId,\r\n  sourceModule: M\r\n): M\r\n\r\nconst REEXPORTED_OBJECTS = new WeakMap<Module, ReexportedObjects>()\r\n\r\n/**\r\n * Constructs the `__turbopack_context__` object for a module.\r\n */\r\nfunction Context(\r\n  this: TurbopackBaseContext<Module>,\r\n  module: Module,\r\n  exports: Exports\r\n) {\r\n  this.m = module\r\n  // We need to store this here instead of accessing it from the module object to:\r\n  // 1. Make it available to factories directly, since we rewrite `this` to\r\n  //    `__turbopack_context__.e` in CJS modules.\r\n  // 2. Support async modules which rewrite `module.exports` to a promise, so we\r\n  //    can still access the original exports object from functions like\r\n  //    `esmExport`\r\n  // Ideally we could find a new approach for async modules and drop this property altogether.\r\n  this.e = exports\r\n}\r\nconst contextPrototype = Context.prototype as TurbopackBaseContext<Module>\r\n\r\ntype ModuleContextMap = Record<ModuleId, ModuleContextEntry>\r\n\r\ninterface ModuleContextEntry {\r\n  id: () => ModuleId\r\n  module: () => any\r\n}\r\n\r\ninterface ModuleContext {\r\n  // require call\r\n  (moduleId: ModuleId): Exports | EsmNamespaceObject\r\n\r\n  // async import call\r\n  import(moduleId: ModuleId): Promise<Exports | EsmNamespaceObject>\r\n\r\n  keys(): ModuleId[]\r\n\r\n  resolve(moduleId: ModuleId): ModuleId\r\n}\r\n\r\ntype GetOrInstantiateModuleFromParent<M extends Module> = (\r\n  moduleId: M['id'],\r\n  parentModule: M\r\n) => M\r\n\r\ndeclare function getOrInstantiateRuntimeModule(\r\n  chunkPath: ChunkPath,\r\n  moduleId: ModuleId\r\n): Module\r\n\r\nconst hasOwnProperty = Object.prototype.hasOwnProperty\r\nconst toStringTag = typeof Symbol !== 'undefined' && Symbol.toStringTag\r\n\r\nfunction defineProp(\r\n  obj: any,\r\n  name: PropertyKey,\r\n  options: PropertyDescriptor & ThisType<any>\r\n) {\r\n  if (!hasOwnProperty.call(obj, name)) Object.defineProperty(obj, name, options)\r\n}\r\n\r\nfunction getOverwrittenModule(\r\n  moduleCache: ModuleCache<Module>,\r\n  id: ModuleId\r\n): Module {\r\n  let module = moduleCache[id]\r\n  if (!module) {\r\n    // This is invoked when a module is merged into another module, thus it wasn't invoked via\r\n    // instantiateModule and the cache entry wasn't created yet.\r\n    module = createModuleObject(id)\r\n    moduleCache[id] = module\r\n  }\r\n  return module\r\n}\r\n\r\n/**\r\n * Creates the module object. Only done here to ensure all module objects have the same shape.\r\n */\r\nfunction createModuleObject(id: ModuleId): Module {\r\n  return {\r\n    exports: {},\r\n    error: undefined,\r\n    id,\r\n    namespaceObject: undefined,\r\n  }\r\n}\r\n\r\n/**\r\n * Adds the getters to the exports object.\r\n */\r\nfunction esm(\r\n  exports: Exports,\r\n  getters: Array<string | (() => unknown) | ((v: unknown) => void)>\r\n) {\r\n  defineProp(exports, '__esModule', { value: true })\r\n  if (toStringTag) defineProp(exports, toStringTag, { value: 'Module' })\r\n  let i = 0\r\n  while (i < getters.length) {\r\n    const propName = getters[i++] as string\r\n    // TODO(luke.sandberg): we could support raw values here, but would need a discriminator beyond 'not a function'\r\n    const getter = getters[i++] as () => unknown\r\n    if (typeof getters[i] === 'function') {\r\n      // a setter\r\n      defineProp(exports, propName, {\r\n        get: getter,\r\n        set: getters[i++] as (v: unknown) => void,\r\n        enumerable: true,\r\n      })\r\n    } else {\r\n      defineProp(exports, propName, { get: getter, enumerable: true })\r\n    }\r\n  }\r\n  Object.seal(exports)\r\n}\r\n\r\n/**\r\n * Makes the module an ESM with exports\r\n */\r\nfunction esmExport(\r\n  this: TurbopackBaseContext<Module>,\r\n  getters: Array<string | (() => unknown) | ((v: unknown) => void)>,\r\n  id: ModuleId | undefined\r\n) {\r\n  let module: Module\r\n  let exports: Module['exports']\r\n  if (id != null) {\r\n    module = getOverwrittenModule(this.c, id)\r\n    exports = module.exports\r\n  } else {\r\n    module = this.m\r\n    exports = this.e\r\n  }\r\n  module.namespaceObject = exports\r\n  esm(exports, getters)\r\n}\r\ncontextPrototype.s = esmExport\r\n\r\ntype ReexportedObjects = Record<PropertyKey, unknown>[]\r\nfunction ensureDynamicExports(\r\n  module: Module,\r\n  exports: Exports\r\n): ReexportedObjects {\r\n  let reexportedObjects: ReexportedObjects | undefined =\r\n    REEXPORTED_OBJECTS.get(module)\r\n\r\n  if (!reexportedObjects) {\r\n    REEXPORTED_OBJECTS.set(module, (reexportedObjects = []))\r\n    module.exports = module.namespaceObject = new Proxy(exports, {\r\n      get(target, prop) {\r\n        if (\r\n          hasOwnProperty.call(target, prop) ||\r\n          prop === 'default' ||\r\n          prop === '__esModule'\r\n        ) {\r\n          return Reflect.get(target, prop)\r\n        }\r\n        for (const obj of reexportedObjects!) {\r\n          const value = Reflect.get(obj, prop)\r\n          if (value !== undefined) return value\r\n        }\r\n        return undefined\r\n      },\r\n      ownKeys(target) {\r\n        const keys = Reflect.ownKeys(target)\r\n        for (const obj of reexportedObjects!) {\r\n          for (const key of Reflect.ownKeys(obj)) {\r\n            if (key !== 'default' && !keys.includes(key)) keys.push(key)\r\n          }\r\n        }\r\n        return keys\r\n      },\r\n    })\r\n  }\r\n  return reexportedObjects\r\n}\r\n\r\n/**\r\n * Dynamically exports properties from an object\r\n */\r\nfunction dynamicExport(\r\n  this: TurbopackBaseContext<Module>,\r\n  object: Record<string, any>,\r\n  id: ModuleId | undefined\r\n) {\r\n  let module: Module\r\n  let exports: Module['exports']\r\n  if (id != null) {\r\n    module = getOverwrittenModule(this.c, id)\r\n    exports = module.exports\r\n  } else {\r\n    module = this.m\r\n    exports = this.e\r\n  }\r\n  const reexportedObjects = ensureDynamicExports(module, exports)\r\n\r\n  if (typeof object === 'object' && object !== null) {\r\n    reexportedObjects.push(object)\r\n  }\r\n}\r\ncontextPrototype.j = dynamicExport\r\n\r\nfunction exportValue(\r\n  this: TurbopackBaseContext<Module>,\r\n  value: any,\r\n  id: ModuleId | undefined\r\n) {\r\n  let module: Module\r\n  if (id != null) {\r\n    module = getOverwrittenModule(this.c, id)\r\n  } else {\r\n    module = this.m\r\n  }\r\n  module.exports = value\r\n}\r\ncontextPrototype.v = exportValue\r\n\r\nfunction exportNamespace(\r\n  this: TurbopackBaseContext<Module>,\r\n  namespace: any,\r\n  id: ModuleId | undefined\r\n) {\r\n  let module: Module\r\n  if (id != null) {\r\n    module = getOverwrittenModule(this.c, id)\r\n  } else {\r\n    module = this.m\r\n  }\r\n  module.exports = module.namespaceObject = namespace\r\n}\r\ncontextPrototype.n = exportNamespace\r\n\r\nfunction createGetter(obj: Record<string | symbol, any>, key: string | symbol) {\r\n  return () => obj[key]\r\n}\r\n\r\n/**\r\n * @returns prototype of the object\r\n */\r\nconst getProto: (obj: any) => any = Object.getPrototypeOf\r\n  ? (obj) => Object.getPrototypeOf(obj)\r\n  : (obj) => obj.__proto__\r\n\r\n/** Prototypes that are not expanded for exports */\r\nconst LEAF_PROTOTYPES = [null, getProto({}), getProto([]), getProto(getProto)]\r\n\r\n/**\r\n * @param raw\r\n * @param ns\r\n * @param allowExportDefault\r\n *   * `false`: will have the raw module as default export\r\n *   * `true`: will have the default property as default export\r\n */\r\nfunction interopEsm(\r\n  raw: Exports,\r\n  ns: EsmNamespaceObject,\r\n  allowExportDefault?: boolean\r\n) {\r\n  const getters: Array<string | (() => unknown) | ((v: unknown) => void)> = []\r\n  // The index of the `default` export if any\r\n  let defaultLocation = -1\r\n  for (\r\n    let current = raw;\r\n    (typeof current === 'object' || typeof current === 'function') &&\r\n    !LEAF_PROTOTYPES.includes(current);\r\n    current = getProto(current)\r\n  ) {\r\n    for (const key of Object.getOwnPropertyNames(current)) {\r\n      getters.push(key, createGetter(raw, key))\r\n      if (defaultLocation === -1 && key === 'default') {\r\n        defaultLocation = getters.length - 1\r\n      }\r\n    }\r\n  }\r\n\r\n  // this is not really correct\r\n  // we should set the `default` getter if the imported module is a `.cjs file`\r\n  if (!(allowExportDefault && defaultLocation >= 0)) {\r\n    // Replace the binding with one for the namespace itself in order to preserve iteration order.\r\n    if (defaultLocation >= 0) {\r\n      getters[defaultLocation] = () => raw\r\n    } else {\r\n      getters.push('default', () => raw)\r\n    }\r\n  }\r\n\r\n  esm(ns, getters)\r\n  return ns\r\n}\r\n\r\nfunction createNS(raw: Module['exports']): EsmNamespaceObject {\r\n  if (typeof raw === 'function') {\r\n    return function (this: any, ...args: any[]) {\r\n      return raw.apply(this, args)\r\n    }\r\n  } else {\r\n    return Object.create(null)\r\n  }\r\n}\r\n\r\nfunction esmImport(\r\n  this: TurbopackBaseContext<Module>,\r\n  id: ModuleId\r\n): Exclude<Module['namespaceObject'], undefined> {\r\n  const module = getOrInstantiateModuleFromParent(id, this.m)\r\n\r\n  // any ES module has to have `module.namespaceObject` defined.\r\n  if (module.namespaceObject) return module.namespaceObject\r\n\r\n  // only ESM can be an async module, so we don't need to worry about exports being a promise here.\r\n  const raw = module.exports\r\n  return (module.namespaceObject = interopEsm(\r\n    raw,\r\n    createNS(raw),\r\n    raw && (raw as any).__esModule\r\n  ))\r\n}\r\ncontextPrototype.i = esmImport\r\n\r\nfunction asyncLoader(\r\n  this: TurbopackBaseContext<Module>,\r\n  moduleId: ModuleId\r\n): Promise<Exports> {\r\n  const loader = this.r(moduleId) as (\r\n    importFunction: EsmImport\r\n  ) => Promise<Exports>\r\n  return loader(this.i.bind(this))\r\n}\r\ncontextPrototype.A = asyncLoader\r\n\r\n// Add a simple runtime require so that environments without one can still pass\r\n// `typeof require` CommonJS checks so that exports are correctly registered.\r\nconst runtimeRequire =\r\n  // @ts-ignore\r\n  typeof require === 'function'\r\n    ? // @ts-ignore\r\n      require\r\n    : function require() {\r\n        throw new Error('Unexpected use of runtime require')\r\n      }\r\ncontextPrototype.t = runtimeRequire\r\n\r\nfunction commonJsRequire(\r\n  this: TurbopackBaseContext<Module>,\r\n  id: ModuleId\r\n): Exports {\r\n  return getOrInstantiateModuleFromParent(id, this.m).exports\r\n}\r\ncontextPrototype.r = commonJsRequire\r\n\r\n/**\r\n * `require.context` and require/import expression runtime.\r\n */\r\nfunction moduleContext(map: ModuleContextMap): ModuleContext {\r\n  function moduleContext(id: ModuleId): Exports {\r\n    if (hasOwnProperty.call(map, id)) {\r\n      return map[id].module()\r\n    }\r\n\r\n    const e = new Error(`Cannot find module '${id}'`)\r\n    ;(e as any).code = 'MODULE_NOT_FOUND'\r\n    throw e\r\n  }\r\n\r\n  moduleContext.keys = (): ModuleId[] => {\r\n    return Object.keys(map)\r\n  }\r\n\r\n  moduleContext.resolve = (id: ModuleId): ModuleId => {\r\n    if (hasOwnProperty.call(map, id)) {\r\n      return map[id].id()\r\n    }\r\n\r\n    const e = new Error(`Cannot find module '${id}'`)\r\n    ;(e as any).code = 'MODULE_NOT_FOUND'\r\n    throw e\r\n  }\r\n\r\n  moduleContext.import = async (id: ModuleId) => {\r\n    return await (moduleContext(id) as Promise<Exports>)\r\n  }\r\n\r\n  return moduleContext\r\n}\r\ncontextPrototype.f = moduleContext\r\n\r\n/**\r\n * Returns the path of a chunk defined by its data.\r\n */\r\nfunction getChunkPath(chunkData: ChunkData): ChunkPath {\r\n  return typeof chunkData === 'string' ? chunkData : chunkData.path\r\n}\r\n\r\nfunction isPromise<T = any>(maybePromise: any): maybePromise is Promise<T> {\r\n  return (\r\n    maybePromise != null &&\r\n    typeof maybePromise === 'object' &&\r\n    'then' in maybePromise &&\r\n    typeof maybePromise.then === 'function'\r\n  )\r\n}\r\n\r\nfunction isAsyncModuleExt<T extends {}>(obj: T): obj is AsyncModuleExt & T {\r\n  return turbopackQueues in obj\r\n}\r\n\r\nfunction createPromise<T>() {\r\n  let resolve: (value: T | PromiseLike<T>) => void\r\n  let reject: (reason?: any) => void\r\n\r\n  const promise = new Promise<T>((res, rej) => {\r\n    reject = rej\r\n    resolve = res\r\n  })\r\n\r\n  return {\r\n    promise,\r\n    resolve: resolve!,\r\n    reject: reject!,\r\n  }\r\n}\r\n\r\n// Load the CompressedmoduleFactories of a chunk into the `moduleFactories` Map.\r\n// The CompressedModuleFactories format is\r\n// - 1 or more module ids\r\n// - a module factory function\r\n// So walking this is a little complex but the flat structure is also fast to\r\n// traverse, we can use `typeof` operators to distinguish the two cases.\r\nfunction installCompressedModuleFactories(\r\n  chunkModules: CompressedModuleFactories,\r\n  offset: number,\r\n  moduleFactories: ModuleFactories,\r\n  newModuleId?: (id: ModuleId) => void\r\n) {\r\n  let i = offset\r\n  while (i < chunkModules.length) {\r\n    let moduleId = chunkModules[i] as ModuleId\r\n    let end = i + 1\r\n    // Find our factory function\r\n    while (\r\n      end < chunkModules.length &&\r\n      typeof chunkModules[end] !== 'function'\r\n    ) {\r\n      end++\r\n    }\r\n    if (end === chunkModules.length) {\r\n      throw new Error('malformed chunk format, expected a factory function')\r\n    }\r\n    // Each chunk item has a 'primary id' and optional additional ids. If the primary id is already\r\n    // present we know all the additional ids are also present, so we don't need to check.\r\n    if (!moduleFactories.has(moduleId)) {\r\n      const moduleFactoryFn = chunkModules[end] as Function\r\n      applyModuleFactoryName(moduleFactoryFn)\r\n      newModuleId?.(moduleId)\r\n      for (; i < end; i++) {\r\n        moduleId = chunkModules[i] as ModuleId\r\n        moduleFactories.set(moduleId, moduleFactoryFn)\r\n      }\r\n    }\r\n    i = end + 1 // end is pointing at the last factory advance to the next id or the end of the array.\r\n  }\r\n}\r\n\r\n// everything below is adapted from webpack\r\n// https://github.com/webpack/webpack/blob/6be4065ade1e252c1d8dcba4af0f43e32af1bdc1/lib/runtime/AsyncModuleRuntimeModule.js#L13\r\n\r\nconst turbopackQueues = Symbol('turbopack queues')\r\nconst turbopackExports = Symbol('turbopack exports')\r\nconst turbopackError = Symbol('turbopack error')\r\n\r\nconst enum QueueStatus {\r\n  Unknown = -1,\r\n  Unresolved = 0,\r\n  Resolved = 1,\r\n}\r\n\r\ntype AsyncQueueFn = (() => void) & { queueCount: number }\r\ntype AsyncQueue = AsyncQueueFn[] & {\r\n  status: QueueStatus\r\n}\r\n\r\nfunction resolveQueue(queue?: AsyncQueue) {\r\n  if (queue && queue.status !== QueueStatus.Resolved) {\r\n    queue.status = QueueStatus.Resolved\r\n    queue.forEach((fn) => fn.queueCount--)\r\n    queue.forEach((fn) => (fn.queueCount-- ? fn.queueCount++ : fn()))\r\n  }\r\n}\r\n\r\ntype Dep = Exports | AsyncModulePromise | Promise<Exports>\r\n\r\ntype AsyncModuleExt = {\r\n  [turbopackQueues]: (fn: (queue: AsyncQueue) => void) => void\r\n  [turbopackExports]: Exports\r\n  [turbopackError]?: any\r\n}\r\n\r\ntype AsyncModulePromise<T = Exports> = Promise<T> & AsyncModuleExt\r\n\r\nfunction wrapDeps(deps: Dep[]): AsyncModuleExt[] {\r\n  return deps.map((dep): AsyncModuleExt => {\r\n    if (dep !== null && typeof dep === 'object') {\r\n      if (isAsyncModuleExt(dep)) return dep\r\n      if (isPromise(dep)) {\r\n        const queue: AsyncQueue = Object.assign([], {\r\n          status: QueueStatus.Unresolved,\r\n        })\r\n\r\n        const obj: AsyncModuleExt = {\r\n          [turbopackExports]: {},\r\n          [turbopackQueues]: (fn: (queue: AsyncQueue) => void) => fn(queue),\r\n        }\r\n\r\n        dep.then(\r\n          (res) => {\r\n            obj[turbopackExports] = res\r\n            resolveQueue(queue)\r\n          },\r\n          (err) => {\r\n            obj[turbopackError] = err\r\n            resolveQueue(queue)\r\n          }\r\n        )\r\n\r\n        return obj\r\n      }\r\n    }\r\n\r\n    return {\r\n      [turbopackExports]: dep,\r\n      [turbopackQueues]: () => {},\r\n    }\r\n  })\r\n}\r\n\r\nfunction asyncModule(\r\n  this: TurbopackBaseContext<Module>,\r\n  body: (\r\n    handleAsyncDependencies: (\r\n      deps: Dep[]\r\n    ) => Exports[] | Promise<() => Exports[]>,\r\n    asyncResult: (err?: any) => void\r\n  ) => void,\r\n  hasAwait: boolean\r\n) {\r\n  const module = this.m\r\n  const queue: AsyncQueue | undefined = hasAwait\r\n    ? Object.assign([], { status: QueueStatus.Unknown })\r\n    : undefined\r\n\r\n  const depQueues: Set<AsyncQueue> = new Set()\r\n\r\n  const { resolve, reject, promise: rawPromise } = createPromise<Exports>()\r\n\r\n  const promise: AsyncModulePromise = Object.assign(rawPromise, {\r\n    [turbopackExports]: module.exports,\r\n    [turbopackQueues]: (fn) => {\r\n      queue && fn(queue)\r\n      depQueues.forEach(fn)\r\n      promise['catch'](() => {})\r\n    },\r\n  } satisfies AsyncModuleExt)\r\n\r\n  const attributes: PropertyDescriptor = {\r\n    get(): any {\r\n      return promise\r\n    },\r\n    set(v: any) {\r\n      // Calling `esmExport` leads to this.\r\n      if (v !== promise) {\r\n        promise[turbopackExports] = v\r\n      }\r\n    },\r\n  }\r\n\r\n  Object.defineProperty(module, 'exports', attributes)\r\n  Object.defineProperty(module, 'namespaceObject', attributes)\r\n\r\n  function handleAsyncDependencies(deps: Dep[]) {\r\n    const currentDeps = wrapDeps(deps)\r\n\r\n    const getResult = () =>\r\n      currentDeps.map((d) => {\r\n        if (d[turbopackError]) throw d[turbopackError]\r\n        return d[turbopackExports]\r\n      })\r\n\r\n    const { promise, resolve } = createPromise<() => Exports[]>()\r\n\r\n    const fn: AsyncQueueFn = Object.assign(() => resolve(getResult), {\r\n      queueCount: 0,\r\n    })\r\n\r\n    function fnQueue(q: AsyncQueue) {\r\n      if (q !== queue && !depQueues.has(q)) {\r\n        depQueues.add(q)\r\n        if (q && q.status === QueueStatus.Unresolved) {\r\n          fn.queueCount++\r\n          q.push(fn)\r\n        }\r\n      }\r\n    }\r\n\r\n    currentDeps.map((dep) => dep[turbopackQueues](fnQueue))\r\n\r\n    return fn.queueCount ? promise : getResult()\r\n  }\r\n\r\n  function asyncResult(err?: any) {\r\n    if (err) {\r\n      reject((promise[turbopackError] = err))\r\n    } else {\r\n      resolve(promise[turbopackExports])\r\n    }\r\n\r\n    resolveQueue(queue)\r\n  }\r\n\r\n  body(handleAsyncDependencies, asyncResult)\r\n\r\n  if (queue && queue.status === QueueStatus.Unknown) {\r\n    queue.status = QueueStatus.Unresolved\r\n  }\r\n}\r\ncontextPrototype.a = asyncModule\r\n\r\n/**\r\n * A pseudo \"fake\" URL object to resolve to its relative path.\r\n *\r\n * When UrlRewriteBehavior is set to relative, calls to the `new URL()` will construct url without base using this\r\n * runtime function to generate context-agnostic urls between different rendering context, i.e ssr / client to avoid\r\n * hydration mismatch.\r\n *\r\n * This is based on webpack's existing implementation:\r\n * https://github.com/webpack/webpack/blob/87660921808566ef3b8796f8df61bd79fc026108/lib/runtime/RelativeUrlRuntimeModule.js\r\n */\r\nconst relativeURL = function relativeURL(this: any, inputUrl: string) {\r\n  const realUrl = new URL(inputUrl, 'x:/')\r\n  const values: Record<string, any> = {}\r\n  for (const key in realUrl) values[key] = (realUrl as any)[key]\r\n  values.href = inputUrl\r\n  values.pathname = inputUrl.replace(/[?#].*/, '')\r\n  values.origin = values.protocol = ''\r\n  values.toString = values.toJSON = (..._args: Array<any>) => inputUrl\r\n  for (const key in values)\r\n    Object.defineProperty(this, key, {\r\n      enumerable: true,\r\n      configurable: true,\r\n      value: values[key],\r\n    })\r\n}\r\nrelativeURL.prototype = URL.prototype\r\ncontextPrototype.U = relativeURL\r\n\r\n/**\r\n * Utility function to ensure all variants of an enum are handled.\r\n */\r\nfunction invariant(never: never, computeMessage: (arg: any) => string): never {\r\n  throw new Error(`Invariant: ${computeMessage(never)}`)\r\n}\r\n\r\n/**\r\n * A stub function to make `require` available but non-functional in ESM.\r\n */\r\nfunction requireStub(_moduleId: ModuleId): never {\r\n  throw new Error('dynamic usage of require is not supported')\r\n}\r\ncontextPrototype.z = requireStub\r\n\r\n// Make `globalThis` available to the module in a way that cannot be shadowed by a local variable.\r\ncontextPrototype.g = globalThis\r\n\r\ntype ContextConstructor<M> = {\r\n  new (module: Module, exports: Exports): TurbopackBaseContext<M>\r\n}\r\n\r\nfunction applyModuleFactoryName(factory: Function) {\r\n  // Give the module factory a nice name to improve stack traces.\r\n  Object.defineProperty(factory, 'name', {\r\n    value: '__TURBOPACK__module__evaluation__',\r\n  })\r\n}\r\n", "/**\r\n * This file contains runtime types and functions that are shared between all\r\n * Turbopack *development* ECMAScript runtimes.\r\n *\r\n * It will be appended to the runtime code of each runtime right after the\r\n * shared runtime utils.\r\n */\r\n\r\n/* eslint-disable @typescript-eslint/no-unused-vars */\r\n\r\n/// <reference path=\"../base/globals.d.ts\" />\r\n/// <reference path=\"../../../shared/runtime-utils.ts\" />\r\n\r\n// Used in WebWorkers to tell the runtime about the chunk base path\r\ndeclare var TURBOPACK_WORKER_LOCATION: string\r\n// Used in WebWorkers to tell the runtime about the current chunk url since it can't be detected via document.currentScript\r\n// Note it's stored in reversed order to use push and pop\r\ndeclare var TURBOPACK_NEXT_CHUNK_URLS: ChunkUrl[] | undefined\r\n\r\n// Injected by rust code\r\ndeclare var CHUNK_BASE_PATH: string\r\ndeclare var CHUNK_SUFFIX_PATH: string\r\n\r\ninterface TurbopackBrowserBaseContext<M> extends TurbopackBaseContext<M> {\r\n  R: ResolvePathFromModule\r\n}\r\n\r\nconst browserContextPrototype =\r\n  Context.prototype as TurbopackBrowserBaseContext<unknown>\r\n\r\n// Provided by build or dev base\r\ndeclare function instantiateModule(\r\n  id: ModuleId,\r\n  sourceType: SourceType,\r\n  sourceData: SourceData\r\n): Module\r\n\r\ntype RuntimeParams = {\r\n  otherChunks: ChunkData[]\r\n  runtimeModuleIds: ModuleId[]\r\n}\r\n\r\ntype ChunkRegistration = [\r\n  chunkPath: ChunkScript,\r\n  ...([RuntimeParams] | CompressedModuleFactories),\r\n]\r\n\r\ntype ChunkList = {\r\n  script: ChunkListScript\r\n  chunks: ChunkData[]\r\n  source: 'entry' | 'dynamic'\r\n}\r\n\r\nenum SourceType {\r\n  /**\r\n   * The module was instantiated because it was included in an evaluated chunk's\r\n   * runtime.\r\n   * SourceData is a ChunkPath.\r\n   */\r\n  Runtime = 0,\r\n  /**\r\n   * The module was instantiated because a parent module imported it.\r\n   * SourceData is a ModuleId.\r\n   */\r\n  Parent = 1,\r\n  /**\r\n   * The module was instantiated because it was included in a chunk's hot module\r\n   * update.\r\n   * SourceData is an array of ModuleIds or undefined.\r\n   */\r\n  Update = 2,\r\n}\r\n\r\ntype SourceData = ChunkPath | ModuleId | ModuleId[] | undefined\r\ninterface RuntimeBackend {\r\n  registerChunk: (chunkPath: ChunkPath, params?: RuntimeParams) => void\r\n  /**\r\n   * Returns the same Promise for the same chunk URL.\r\n   */\r\n  loadChunkCached: (sourceType: SourceType, chunkUrl: ChunkUrl) => Promise<void>\r\n  loadWebAssembly: (\r\n    sourceType: SourceType,\r\n    sourceData: SourceData,\r\n    wasmChunkPath: ChunkPath,\r\n    edgeModule: () => WebAssembly.Module,\r\n    importsObj: WebAssembly.Imports\r\n  ) => Promise<Exports>\r\n  loadWebAssemblyModule: (\r\n    sourceType: SourceType,\r\n    sourceData: SourceData,\r\n    wasmChunkPath: ChunkPath,\r\n    edgeModule: () => WebAssembly.Module\r\n  ) => Promise<WebAssembly.Module>\r\n}\r\n\r\ninterface DevRuntimeBackend {\r\n  reloadChunk?: (chunkUrl: ChunkUrl) => Promise<void>\r\n  unloadChunk?: (chunkUrl: ChunkUrl) => void\r\n  restart: () => void\r\n}\r\n\r\nconst moduleFactories: ModuleFactories = new Map()\r\ncontextPrototype.M = moduleFactories\r\n\r\nconst availableModules: Map<ModuleId, Promise<any> | true> = new Map()\r\n\r\nconst availableModuleChunks: Map<ChunkPath, Promise<any> | true> = new Map()\r\n\r\nfunction factoryNotAvailable(\r\n  moduleId: ModuleId,\r\n  sourceType: SourceType,\r\n  sourceData: SourceData\r\n): never {\r\n  let instantiationReason\r\n  switch (sourceType) {\r\n    case SourceType.Runtime:\r\n      instantiationReason = `as a runtime entry of chunk ${sourceData}`\r\n      break\r\n    case SourceType.Parent:\r\n      instantiationReason = `because it was required from module ${sourceData}`\r\n      break\r\n    case SourceType.Update:\r\n      instantiationReason = 'because of an HMR update'\r\n      break\r\n    default:\r\n      invariant(\r\n        sourceType,\r\n        (sourceType) => `Unknown source type: ${sourceType}`\r\n      )\r\n  }\r\n  throw new Error(\r\n    `Module ${moduleId} was instantiated ${instantiationReason}, but the module factory is not available. It might have been deleted in an HMR update.`\r\n  )\r\n}\r\n\r\nfunction loadChunk(\r\n  this: TurbopackBrowserBaseContext<Module>,\r\n  chunkData: ChunkData\r\n): Promise<void> {\r\n  return loadChunkInternal(SourceType.Parent, this.m.id, chunkData)\r\n}\r\nbrowserContextPrototype.l = loadChunk\r\n\r\nfunction loadInitialChunk(chunkPath: ChunkPath, chunkData: ChunkData) {\r\n  return loadChunkInternal(SourceType.Runtime, chunkPath, chunkData)\r\n}\r\n\r\nasync function loadChunkInternal(\r\n  sourceType: SourceType,\r\n  sourceData: SourceData,\r\n  chunkData: ChunkData\r\n): Promise<void> {\r\n  if (typeof chunkData === 'string') {\r\n    return loadChunkPath(sourceType, sourceData, chunkData)\r\n  }\r\n\r\n  const includedList = chunkData.included || []\r\n  const modulesPromises = includedList.map((included) => {\r\n    if (moduleFactories.has(included)) return true\r\n    return availableModules.get(included)\r\n  })\r\n  if (modulesPromises.length > 0 && modulesPromises.every((p) => p)) {\r\n    // When all included items are already loaded or loading, we can skip loading ourselves\r\n    await Promise.all(modulesPromises)\r\n    return\r\n  }\r\n\r\n  const includedModuleChunksList = chunkData.moduleChunks || []\r\n  const moduleChunksPromises = includedModuleChunksList\r\n    .map((included) => {\r\n      // TODO(alexkirsz) Do we need this check?\r\n      // if (moduleFactories[included]) return true;\r\n      return availableModuleChunks.get(included)\r\n    })\r\n    .filter((p) => p)\r\n\r\n  let promise: Promise<unknown>\r\n  if (moduleChunksPromises.length > 0) {\r\n    // Some module chunks are already loaded or loading.\r\n\r\n    if (moduleChunksPromises.length === includedModuleChunksList.length) {\r\n      // When all included module chunks are already loaded or loading, we can skip loading ourselves\r\n      await Promise.all(moduleChunksPromises)\r\n      return\r\n    }\r\n\r\n    const moduleChunksToLoad: Set<ChunkPath> = new Set()\r\n    for (const moduleChunk of includedModuleChunksList) {\r\n      if (!availableModuleChunks.has(moduleChunk)) {\r\n        moduleChunksToLoad.add(moduleChunk)\r\n      }\r\n    }\r\n\r\n    for (const moduleChunkToLoad of moduleChunksToLoad) {\r\n      const promise = loadChunkPath(sourceType, sourceData, moduleChunkToLoad)\r\n\r\n      availableModuleChunks.set(moduleChunkToLoad, promise)\r\n\r\n      moduleChunksPromises.push(promise)\r\n    }\r\n\r\n    promise = Promise.all(moduleChunksPromises)\r\n  } else {\r\n    promise = loadChunkPath(sourceType, sourceData, chunkData.path)\r\n\r\n    // Mark all included module chunks as loading if they are not already loaded or loading.\r\n    for (const includedModuleChunk of includedModuleChunksList) {\r\n      if (!availableModuleChunks.has(includedModuleChunk)) {\r\n        availableModuleChunks.set(includedModuleChunk, promise)\r\n      }\r\n    }\r\n  }\r\n\r\n  for (const included of includedList) {\r\n    if (!availableModules.has(included)) {\r\n      // It might be better to race old and new promises, but it's rare that the new promise will be faster than a request started earlier.\r\n      // In production it's even more rare, because the chunk optimization tries to deduplicate modules anyway.\r\n      availableModules.set(included, promise)\r\n    }\r\n  }\r\n\r\n  await promise\r\n}\r\n\r\nconst loadedChunk = Promise.resolve(undefined)\r\nconst instrumentedBackendLoadChunks = new WeakMap<\r\n  Promise<any>,\r\n  Promise<any> | typeof loadedChunk\r\n>()\r\n// Do not make this async. React relies on referential equality of the returned Promise.\r\nfunction loadChunkByUrl(\r\n  this: TurbopackBrowserBaseContext<Module>,\r\n  chunkUrl: ChunkUrl\r\n) {\r\n  return loadChunkByUrlInternal(SourceType.Parent, this.m.id, chunkUrl)\r\n}\r\nbrowserContextPrototype.L = loadChunkByUrl\r\n\r\n// Do not make this async. React relies on referential equality of the returned Promise.\r\nfunction loadChunkByUrlInternal(\r\n  sourceType: SourceType,\r\n  sourceData: SourceData,\r\n  chunkUrl: ChunkUrl\r\n): Promise<any> {\r\n  const thenable = BACKEND.loadChunkCached(sourceType, chunkUrl)\r\n  let entry = instrumentedBackendLoadChunks.get(thenable)\r\n  if (entry === undefined) {\r\n    const resolve = instrumentedBackendLoadChunks.set.bind(\r\n      instrumentedBackendLoadChunks,\r\n      thenable,\r\n      loadedChunk\r\n    )\r\n    entry = thenable.then(resolve).catch((error) => {\r\n      let loadReason: string\r\n      switch (sourceType) {\r\n        case SourceType.Runtime:\r\n          loadReason = `as a runtime dependency of chunk ${sourceData}`\r\n          break\r\n        case SourceType.Parent:\r\n          loadReason = `from module ${sourceData}`\r\n          break\r\n        case SourceType.Update:\r\n          loadReason = 'from an HMR update'\r\n          break\r\n        default:\r\n          invariant(\r\n            sourceType,\r\n            (sourceType) => `Unknown source type: ${sourceType}`\r\n          )\r\n      }\r\n      throw new Error(\r\n        `Failed to load chunk ${chunkUrl} ${loadReason}${\r\n          error ? `: ${error}` : ''\r\n        }`,\r\n        error\r\n          ? {\r\n              cause: error,\r\n            }\r\n          : undefined\r\n      )\r\n    })\r\n    instrumentedBackendLoadChunks.set(thenable, entry)\r\n  }\r\n\r\n  return entry\r\n}\r\n\r\n// Do not make this async. React relies on referential equality of the returned Promise.\r\nfunction loadChunkPath(\r\n  sourceType: SourceType,\r\n  sourceData: SourceData,\r\n  chunkPath: ChunkPath\r\n): Promise<void> {\r\n  const url = getChunkRelativeUrl(chunkPath)\r\n  return loadChunkByUrlInternal(sourceType, sourceData, url)\r\n}\r\n\r\n/**\r\n * Returns an absolute url to an asset.\r\n */\r\nfunction resolvePathFromModule(\r\n  this: TurbopackBaseContext<Module>,\r\n  moduleId: string\r\n): string {\r\n  const exported = this.r(moduleId)\r\n  return exported?.default ?? exported\r\n}\r\nbrowserContextPrototype.R = resolvePathFromModule\r\n\r\n/**\r\n * no-op for browser\r\n * @param modulePath\r\n */\r\nfunction resolveAbsolutePath(modulePath?: string): string {\r\n  return `/ROOT/${modulePath ?? ''}`\r\n}\r\nbrowserContextPrototype.P = resolveAbsolutePath\r\n\r\n/**\r\n * Returns a blob URL for the worker.\r\n * @param chunks list of chunks to load\r\n */\r\nfunction getWorkerBlobURL(chunks: ChunkPath[]): string {\r\n  // It is important to reverse the array so when bootstrapping we can infer what chunk is being\r\n  // evaluated by poping urls off of this array.  See `getPathFromScript`\r\n  let bootstrap = `self.TURBOPACK_WORKER_LOCATION = ${JSON.stringify(location.origin)};\r\nself.TURBOPACK_NEXT_CHUNK_URLS = ${JSON.stringify(chunks.reverse().map(getChunkRelativeUrl), null, 2)};\r\nimportScripts(...self.TURBOPACK_NEXT_CHUNK_URLS.map(c => self.TURBOPACK_WORKER_LOCATION + c).reverse());`\r\n  let blob = new Blob([bootstrap], { type: 'text/javascript' })\r\n  return URL.createObjectURL(blob)\r\n}\r\nbrowserContextPrototype.b = getWorkerBlobURL\r\n\r\n/**\r\n * Instantiates a runtime module.\r\n */\r\nfunction instantiateRuntimeModule(\r\n  moduleId: ModuleId,\r\n  chunkPath: ChunkPath\r\n): Module {\r\n  return instantiateModule(moduleId, SourceType.Runtime, chunkPath)\r\n}\r\n/**\r\n * Returns the URL relative to the origin where a chunk can be fetched from.\r\n */\r\nfunction getChunkRelativeUrl(chunkPath: ChunkPath | ChunkListPath): ChunkUrl {\r\n  return `${CHUNK_BASE_PATH}${chunkPath\r\n    .split('/')\r\n    .map((p) => encodeURIComponent(p))\r\n    .join('/')}${CHUNK_SUFFIX_PATH}` as ChunkUrl\r\n}\r\n\r\n/**\r\n * Return the ChunkPath from a ChunkScript.\r\n */\r\nfunction getPathFromScript(chunkScript: ChunkPath | ChunkScript): ChunkPath\r\nfunction getPathFromScript(\r\n  chunkScript: ChunkListPath | ChunkListScript\r\n): ChunkListPath\r\nfunction getPathFromScript(\r\n  chunkScript: ChunkPath | ChunkListPath | ChunkScript | ChunkListScript\r\n): ChunkPath | ChunkListPath {\r\n  if (typeof chunkScript === 'string') {\r\n    return chunkScript as ChunkPath | ChunkListPath\r\n  }\r\n  const chunkUrl =\r\n    typeof TURBOPACK_NEXT_CHUNK_URLS !== 'undefined'\r\n      ? TURBOPACK_NEXT_CHUNK_URLS.pop()!\r\n      : chunkScript.getAttribute('src')!\r\n  const src = decodeURIComponent(chunkUrl.replace(/[?#].*$/, ''))\r\n  const path = src.startsWith(CHUNK_BASE_PATH)\r\n    ? src.slice(CHUNK_BASE_PATH.length)\r\n    : src\r\n  return path as ChunkPath | ChunkListPath\r\n}\r\n\r\nconst regexJsUrl = /\\.js(?:\\?[^#]*)?(?:#.*)?$/\r\n/**\r\n * Checks if a given path/URL ends with .js, optionally followed by ?query or #fragment.\r\n */\r\nfunction isJs(chunkUrlOrPath: ChunkUrl | ChunkPath): boolean {\r\n  return regexJsUrl.test(chunkUrlOrPath)\r\n}\r\n\r\nconst regexCssUrl = /\\.css(?:\\?[^#]*)?(?:#.*)?$/\r\n/**\r\n * Checks if a given path/URL ends with .css, optionally followed by ?query or #fragment.\r\n */\r\nfunction isCss(chunkUrl: ChunkUrl): boolean {\r\n  return regexCssUrl.test(chunkUrl)\r\n}\r\n\r\nfunction loadWebAssembly(\r\n  this: TurbopackBaseContext<Module>,\r\n  chunkPath: ChunkPath,\r\n  edgeModule: () => WebAssembly.Module,\r\n  importsObj: WebAssembly.Imports\r\n): Promise<Exports> {\r\n  return BACKEND.loadWebAssembly(\r\n    SourceType.Parent,\r\n    this.m.id,\r\n    chunkPath,\r\n    edgeModule,\r\n    importsObj\r\n  )\r\n}\r\ncontextPrototype.w = loadWebAssembly\r\n\r\nfunction loadWebAssemblyModule(\r\n  this: TurbopackBaseContext<Module>,\r\n  chunkPath: ChunkPath,\r\n  edgeModule: () => WebAssembly.Module\r\n): Promise<WebAssembly.Module> {\r\n  return BACKEND.loadWebAssemblyModule(\r\n    SourceType.Parent,\r\n    this.m.id,\r\n    chunkPath,\r\n    edgeModule\r\n  )\r\n}\r\ncontextPrototype.u = loadWebAssemblyModule\r\n", "/// <reference path=\"./runtime-base.ts\" />\r\n/// <reference path=\"./dummy.ts\" />\r\n\r\nconst moduleCache: ModuleCache<Module> = {}\r\ncontextPrototype.c = moduleCache\r\n\r\n/**\r\n * Gets or instantiates a runtime module.\r\n */\r\n// @ts-ignore\r\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\r\nfunction getOrInstantiateRuntimeModule(\r\n  chunkPath: ChunkPath,\r\n  moduleId: ModuleId\r\n): Module {\r\n  const module = moduleCache[moduleId]\r\n  if (module) {\r\n    if (module.error) {\r\n      throw module.error\r\n    }\r\n    return module\r\n  }\r\n\r\n  return instantiateModule(moduleId, SourceType.Runtime, chunkPath)\r\n}\r\n\r\n/**\r\n * Retrieves a module from the cache, or instantiate it if it is not cached.\r\n */\r\n// Used by the backend\r\n// @ts-ignore\r\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\r\nconst getOrInstantiateModuleFromParent: GetOrInstantiateModuleFromParent<\r\n  Module\r\n> = (id, sourceModule) => {\r\n  const module = moduleCache[id]\r\n\r\n  if (module) {\r\n    if (module.error) {\r\n      throw module.error\r\n    }\r\n    return module\r\n  }\r\n\r\n  return instantiateModule(id, SourceType.Parent, sourceModule.id)\r\n}\r\n\r\nfunction instantiateModule(\r\n  id: ModuleId,\r\n  sourceType: SourceType,\r\n  sourceData: SourceData\r\n): Module {\r\n  const moduleFactory = moduleFactories.get(id)\r\n  if (typeof moduleFactory !== 'function') {\r\n    // This can happen if modules incorrectly handle HMR disposes/updates,\r\n    // e.g. when they keep a `setTimeout` around which still executes old code\r\n    // and contains e.g. a `require(\"something\")` call.\r\n    factoryNotAvailable(id, sourceType, sourceData)\r\n  }\r\n\r\n  const module: Module = createModuleObject(id)\r\n  const exports = module.exports\r\n\r\n  moduleCache[id] = module\r\n\r\n  // NOTE(alexkirsz) This can fail when the module encounters a runtime error.\r\n  const context = new (Context as any as ContextConstructor<Module>)(\r\n    module,\r\n    exports\r\n  )\r\n  try {\r\n    moduleFactory(context, module, exports)\r\n  } catch (error) {\r\n    module.error = error as any\r\n    throw error\r\n  }\r\n\r\n  if (module.namespaceObject && module.exports !== module.namespaceObject) {\r\n    // in case of a circular dependency: cjs1 -> esm2 -> cjs1\r\n    interopEsm(module.exports, module.namespaceObject)\r\n  }\r\n\r\n  return module\r\n}\r\n\r\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\r\nfunction registerChunk(registration: ChunkRegistration) {\r\n  const chunkPath = getPathFromScript(registration[0])\r\n  let runtimeParams: RuntimeParams | undefined\r\n  // When bootstrapping we are passed a single runtimeParams object so we can distinguish purely based on length\r\n  if (registration.length === 2) {\r\n    runtimeParams = registration[1] as RuntimeParams\r\n  } else {\r\n    runtimeParams = undefined\r\n    installCompressedModuleFactories(\r\n      registration as CompressedModuleFactories,\r\n      /* offset= */ 1,\r\n      moduleFactories\r\n    )\r\n  }\r\n\r\n  return BACKEND.registerChunk(chunkPath, runtimeParams)\r\n}\r\n", "/* eslint-disable @typescript-eslint/no-unused-vars */\r\n\r\n/// <reference path=\"../shared/runtime-utils.ts\" />\r\n\r\n/// A 'base' utilities to support runtime can have externals.\r\n/// Currently this is for node.js / edge runtime both.\r\n/// If a fn requires node.js specific behavior, it should be placed in `node-external-utils` instead.\r\n\r\nasync function externalImport(id: DependencySpecifier) {\r\n  let raw\r\n  try {\r\n    raw = await import(id)\r\n  } catch (err) {\r\n    // TODO(alexkirsz) This can happen when a client-side module tries to load\r\n    // an external module we don't provide a shim for (e.g. querystring, url).\r\n    // For now, we fail semi-silently, but in the future this should be a\r\n    // compilation error.\r\n    throw new Error(`Failed to load external module ${id}: ${err}`)\r\n  }\r\n\r\n  if (raw && raw.__esModule && raw.default && 'default' in raw.default) {\r\n    return interopEsm(raw.default, createNS(raw), true)\r\n  }\r\n\r\n  return raw\r\n}\r\ncontextPrototype.y = externalImport\r\n\r\nfunction externalRequire(\r\n  id: ModuleId,\r\n  thunk: () => any,\r\n  esm: boolean = false\r\n): Exports | EsmNamespaceObject {\r\n  let raw\r\n  try {\r\n    raw = thunk()\r\n  } catch (err) {\r\n    // TODO(alexkirsz) This can happen when a client-side module tries to load\r\n    // an external module we don't provide a shim for (e.g. querystring, url).\r\n    // For now, we fail semi-silently, but in the future this should be a\r\n    // compilation error.\r\n    throw new Error(`Failed to load external module ${id}: ${err}`)\r\n  }\r\n\r\n  if (!esm || raw.__esModule) {\r\n    return raw\r\n  }\r\n\r\n  return interopEsm(raw, createNS(raw), true)\r\n}\r\n\r\nexternalRequire.resolve = (\r\n  id: string,\r\n  options?: {\r\n    paths?: string[]\r\n  }\r\n) => {\r\n  return require.resolve(id, options)\r\n}\r\ncontextPrototype.x = externalRequire\r\n", "/**\r\n * This file contains the runtime code specific to the Turbopack development\r\n * ECMAScript \"None\" runtime (e.g. for Edge).\r\n *\r\n * It will be appended to the base development runtime code.\r\n */\r\n\r\n/* eslint-disable @typescript-eslint/no-unused-vars */\r\n\r\n/// <reference path=\"../base/runtime-base.ts\" />\r\n/// <reference path=\"../../../shared/require-type.d.ts\" />\r\n/// <reference path=\"../../../shared-node/base-externals-utils.ts\" />\r\n\r\ntype ChunkRunner = {\r\n  requiredChunks: Set<ChunkPath>\r\n  chunkPath: ChunkPath\r\n  runtimeModuleIds: ModuleId[]\r\n}\r\n\r\nlet BACKEND: RuntimeBackend\r\n;(() => {\r\n  BACKEND = {\r\n    // The \"none\" runtime expects all chunks within the same chunk group to be\r\n    // registered before any of them are instantiated.\r\n    // Furthermore, modules must be instantiated synchronously, hence we don't\r\n    // use promises here.\r\n    registerChunk(chunkPath, params) {\r\n      registeredChunks.add(chunkPath)\r\n      instantiateDependentChunks(chunkPath)\r\n\r\n      if (params == null) {\r\n        return\r\n      }\r\n\r\n      if (params.otherChunks.length === 0) {\r\n        // The current chunk does not depend on any other chunks, it can be\r\n        // instantiated immediately.\r\n        instantiateRuntimeModules(params.runtimeModuleIds, chunkPath)\r\n      } else {\r\n        // The current chunk depends on other chunks, so we need to wait for\r\n        // those chunks to be registered before instantiating the runtime\r\n        // modules.\r\n        registerChunkRunner(\r\n          chunkPath,\r\n          params.otherChunks.filter((chunk) =>\r\n            // The none runtime can only handle JS chunks, so we only wait for these\r\n            isJs(getChunkPath(chunk))\r\n          ),\r\n          params.runtimeModuleIds\r\n        )\r\n      }\r\n    },\r\n\r\n    loadChunkCached(_sourceType: SourceType, _chunkUrl: ChunkUrl) {\r\n      throw new Error('chunk loading is not supported')\r\n    },\r\n\r\n    async loadWebAssembly(\r\n      _sourceType: SourceType,\r\n      _sourceData: SourceData,\r\n      chunkPath: ChunkPath,\r\n      edgeModule: () => WebAssembly.Module,\r\n      imports: WebAssembly.Imports\r\n    ): Promise<Exports> {\r\n      const module = await loadEdgeWasm(chunkPath, edgeModule)\r\n\r\n      return await WebAssembly.instantiate(module, imports)\r\n    },\r\n\r\n    async loadWebAssemblyModule(\r\n      _sourceType: SourceType,\r\n      _sourceData: SourceData,\r\n      chunkPath: ChunkPath,\r\n      edgeModule: () => WebAssembly.Module\r\n    ): Promise<WebAssembly.Module> {\r\n      return loadEdgeWasm(chunkPath, edgeModule)\r\n    },\r\n  }\r\n\r\n  const registeredChunks: Set<ChunkPath> = new Set()\r\n  const runners: Map<ChunkPath, Set<ChunkRunner>> = new Map()\r\n\r\n  /**\r\n   * Registers a chunk runner that will be instantiated once all of the\r\n   * dependencies of the chunk have been registered.\r\n   */\r\n  function registerChunkRunner(\r\n    chunkPath: ChunkPath,\r\n    otherChunks: ChunkData[],\r\n    runtimeModuleIds: ModuleId[]\r\n  ) {\r\n    const requiredChunks: Set<ChunkPath> = new Set()\r\n    const runner = {\r\n      runtimeModuleIds,\r\n      chunkPath,\r\n      requiredChunks,\r\n    }\r\n\r\n    for (const otherChunkData of otherChunks) {\r\n      const otherChunkPath = getChunkPath(otherChunkData)\r\n      if (registeredChunks.has(otherChunkPath)) {\r\n        continue\r\n      }\r\n\r\n      requiredChunks.add(otherChunkPath)\r\n      let runnersForChunk = runners.get(otherChunkPath)\r\n      if (runnersForChunk == null) {\r\n        runnersForChunk = new Set()\r\n        runners.set(otherChunkPath, runnersForChunk)\r\n      }\r\n      runnersForChunk.add(runner)\r\n    }\r\n    // When all chunks are already registered, we can instantiate the runtime module\r\n    if (runner.requiredChunks.size === 0) {\r\n      instantiateRuntimeModules(runner.runtimeModuleIds, runner.chunkPath)\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Instantiates any chunk runners that were waiting for the given chunk to be\r\n   * registered.\r\n   */\r\n  function instantiateDependentChunks(chunkPath: ChunkPath) {\r\n    // Run any chunk runners that were waiting for this chunk to be\r\n    // registered.\r\n    const runnersForChunk = runners.get(chunkPath)\r\n    if (runnersForChunk != null) {\r\n      for (const runner of runnersForChunk) {\r\n        runner.requiredChunks.delete(chunkPath)\r\n\r\n        if (runner.requiredChunks.size === 0) {\r\n          instantiateRuntimeModules(runner.runtimeModuleIds, runner.chunkPath)\r\n        }\r\n      }\r\n      runners.delete(chunkPath)\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Instantiates the runtime modules for the given chunk.\r\n   */\r\n  function instantiateRuntimeModules(\r\n    runtimeModuleIds: ModuleId[],\r\n    chunkPath: ChunkPath\r\n  ) {\r\n    for (const moduleId of runtimeModuleIds) {\r\n      getOrInstantiateRuntimeModule(chunkPath, moduleId)\r\n    }\r\n  }\r\n\r\n  async function loadEdgeWasm(\r\n    chunkPath: ChunkPath,\r\n    edgeModule: () => WebAssembly.Module\r\n  ): Promise<WebAssembly.Module> {\r\n    let module\r\n    try {\r\n      module = edgeModule()\r\n    } catch (_e) {}\r\n\r\n    if (!module) {\r\n      throw new Error(\r\n        `dynamically loading WebAssembly is not supported in this runtime as global was not injected for chunk '${chunkPath}'`\r\n      )\r\n    }\r\n\r\n    return module\r\n  }\r\n})()\r\n"], "names": [], "mappings": "0RImBI,iDJAJ,IAAM,EAAqB,IAAI,QAK/B,SAAS,EAEP,CAAc,CACd,CAAgB,EAEhB,IAAI,CAAC,CAAC,CAAG,EAQT,IAAI,CAAC,CAAC,CAAG,CACX,CACA,IAAM,EAAmB,EAAQ,SAAS,CA+BpC,EAAiB,OAAO,SAAS,CAAC,cAAc,CAChD,EAAgC,aAAlB,OAAO,QAA0B,OAAO,WAAW,CAEvE,SAAS,EACP,CAAQ,CACR,CAAiB,CACjB,CAA2C,EAEvC,AAAC,EAAe,IAAI,CAAC,EAAK,IAAO,OAAO,cAAc,CAAC,EAAK,EAAM,EACxE,CAEA,SAAS,EACP,CAAgC,CAChC,CAAY,EAEZ,IAAI,EAAS,CAAW,CAAC,EAAG,CAO5B,OANK,IAGH,EAAS,EAHE,AAGiB,GAC5B,CAAW,CAAC,EAAG,CAAG,GAEb,CACT,CAKA,SAAS,EAAmB,CAAY,EACtC,MAAO,CACL,QAAS,CAAC,EACV,WAAO,KACP,EACA,qBAAiB,CACnB,CACF,CAKA,SAAS,EACP,CAAgB,CAChB,CAAiE,EAEjE,EAAW,EAAS,aAAc,CAAE,OAAO,CAAK,GAC5C,GAAa,EAAW,EAAS,EAAa,CAAE,MAAO,QAAS,GACpE,IAAI,EAAI,EACR,KAAO,EAAI,EAAQ,MAAM,EAAE,CACzB,IAAM,EAAW,CAAO,CAAC,IAAI,CAEvB,EAAS,CAAO,CAAC,IAAI,CACD,YAAtB,AAAkC,OAA3B,CAAO,CAAC,EAAE,CAEnB,EAAW,EAAS,EAAU,CAC5B,IAAK,EACL,IAAK,CAAO,CAAC,IAAI,CACjB,YAAY,CACd,GAEA,EAAW,EAAS,EAAU,CAAE,IAAK,EAAQ,YAAY,CAAK,EAElE,CACA,OAAO,IAAI,CAAC,EACd,CAsBA,EAAiB,CAAC,CAjBlB,EAiBqB,OAfnB,AAFO,CAE0D,CACjE,CAAwB,EAExB,IAAI,EACA,CACM,MAAM,CAAZ,EAEF,EAAU,CADV,EAAS,EAAqB,IAAI,CAAC,CAAC,CAAE,EAAA,EACrB,OAAO,EAExB,EAAS,IAAI,CAAC,CAAC,CACf,EAAU,IAAI,CAAC,CAAC,EAElB,EAAO,eAAe,CAAG,EACzB,EAAI,EAAS,EACf,EAiEA,EAAiB,CAAC,CApBlB,EAoBqB,OApBZ,AAEP,CAA2B,CAC3B,CAAwB,MA3CxB,EACA,IADc,EA6CV,CA5CY,CA6CZ,GACM,MAAM,CAAZ,EAEF,EAAU,AADV,GAAS,EAAqB,IAAI,CAAC,CAAC,CAAE,EAAA,EACrB,OAAO,EAExB,EAAS,IAAI,CAAC,CAAC,CACf,EAAU,IAAI,CAAC,CAAC,EAElB,IAAM,KAAyC,IAAQ,GAnDnD,EACF,EAAmB,GAAG,CAkDE,AAlDD,MAGvB,EAAmB,GAAG,CAAC,EAAS,EAAoB,EAAE,EACtD,EAAO,OAAO,CAAG,EAAO,eAAe,CAAG,IAAI,MAAM,EAAS,CAC3D,IAAI,CAAM,CAAE,CAAI,EACd,GACE,EAAe,IAAI,CAAC,EAAQ,IACnB,YAAT,GACS,cACT,CADA,EAEA,OAAO,QAAQ,GAAG,CAAC,EAAQ,GAE7B,IAAK,IAAM,KAAO,EAAoB,CACpC,IAAM,EAAQ,QAAQ,GAAG,CAAC,EAAK,GAC/B,QAAc,IAAV,EAAqB,OAAO,CAClC,CAEF,EACA,QAAQ,CAAM,EACZ,IAAM,EAAO,QAAQ,OAAO,CAAC,GAC7B,IAAK,IAAM,KAAO,EAChB,IAAK,IAAM,KAAO,GADkB,KACV,OAAO,CAAC,GACpB,EAD0B,UAClC,CAAqB,EAAC,EAAK,QAAQ,CAAC,IAAM,EAAK,IAAI,CAAC,GAG5D,OAAO,CACT,CACF,IAEK,GAsBe,UAAlB,OAAO,GAAkC,MAAM,CAAjB,GAChC,EAAkB,IAAI,CAAC,EAE3B,EAgBA,EAAiB,CAAC,CAblB,EAaqB,OAbZ,AAEP,CAAU,CACV,CAAwB,EAQxB,CALU,MAAN,AAAY,EACL,EAAqB,IAAI,CAAC,CAAC,CAAE,GAE7B,IAAI,CAAC,CAAC,EAEV,OAAO,CAAG,CACnB,EAgBA,EAAiB,CAAC,CAblB,EAaqB,OAbZ,AAEP,CAAc,CACd,CAAwB,EAExB,IAAI,CAMJ,EAJE,EADQ,MAAN,AAAY,EACL,EAAqB,IAAI,CAAC,CAAC,CAAE,GAE7B,IAAI,CAAC,CAAC,EAEV,OAAO,CAAG,EAAO,eAAe,CAAG,CAC5C,EAUA,IAAM,EAA8B,OAAO,cAAc,CACrD,AAAC,GAAQ,OAAO,cAAc,CAAC,GAC/B,AAAC,GAAQ,EAAI,SAAS,CAGpB,EAAkB,CAAC,KAAM,EAAS,CAAC,GAAI,EAAS,EAAE,EAAG,EAAS,GAAU,CAS9E,SAAS,EACP,CAAY,CACZ,CAAsB,CACtB,CAA4B,EAE5B,IAAM,EAAoE,EAAE,CAExE,EAAkB,CAAC,EACvB,IACE,IAAI,EAAU,EACd,CAAoB,UAAnB,OAAO,GAA2C,YAAnB,OAAO,CAAY,CAAU,EAC7D,CAAC,EAAgB,QAAQ,CAAC,GAC1B,EAAU,EAAS,GAEnB,IAAK,EADL,EACW,KAAO,OAAO,mBAAmB,CAAC,GAC3C,EAAQ,IAD6C,AACzC,CAAC,EApCnB,AAoCwB,SApCf,AAAa,CAAiC,CAAE,CAAoB,EAC3E,MAAO,IAAM,CAAG,CAAC,EACnB,AADuB,EAmCc,EAAK,IACZ,CAAC,IAArB,GAAkC,WAAW,CAAnB,IAC5B,EAAkB,EAAQ,MAAM,EAAG,EAiBzC,OAVM,GAAsB,GAAmB,CAAC,GAE1C,AAF6C,GAE1B,EACrB,CADwB,AACjB,CAAC,EAAgB,CAAG,IAAM,EAEjC,EAAQ,IAAI,CAAC,UAAW,IAAM,IAIlC,EAAI,EAAI,GACD,CACT,CAEA,SAAS,EAAS,CAAsB,QACtC,AAAmB,YAAf,AAA2B,OAApB,EACF,SAAqB,GAAG,CAAW,EACxC,OAAO,EAAI,KAAK,CAAC,IAAI,CAAE,EACzB,EAEO,OAAO,MAAM,CAAC,KAEzB,CA2FA,SAAS,EAAa,CAAoB,EACxC,MAA4B,UAArB,OAAO,EAAyB,EAAY,EAAU,IAAI,AACnE,CAeA,SAAS,IACP,IAAI,EACA,EAOJ,MAAO,CACL,QANc,IAAI,QAAW,CAAC,EAAK,KACnC,EAAS,EACT,EAAU,CACZ,GAIE,QAAS,EACT,OAAQ,CACV,CACF,CAvGA,EAAiB,CAAC,CAjBlB,EAiBqB,OAjBZ,AAEP,CAAY,EAEZ,IAAM,EAAS,EAAiC,EAAI,IAAI,CAAC,CAAC,EAG1D,GAAI,EAAO,eAAe,CAAE,OAAO,EAAO,eAAe,CAGzD,IAAM,EAAM,EAAO,OAAO,CAC1B,OAAQ,EAAO,eAAe,CAAG,EAC/B,EACA,EAAS,GACT,GAAQ,EAAY,UAAU,CAElC,EAYA,EAAiB,CAAC,CATlB,EASqB,OATZ,AAEP,CAAkB,EAKlB,OAAO,AAHQ,IAAI,CAAC,CAAC,CAAC,GAGR,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,EAChC,EAaA,EAAiB,CAAC,CANG,EAMA,UANnB,OAAO,QAEH,QACA,SAAS,EACP,MAAM,AAAI,MAAM,oCAClB,EASN,EAAiB,CAAC,CANlB,EAMqB,OANZ,AAEP,CAAY,EAEZ,OAAO,EAAiC,EAAI,IAAI,CAAC,CAAC,EAAE,OAAO,AAC7D,EAqCA,EAAiB,CAAC,CA/BlB,EA+BqB,OA/BE,AAAd,CAAmC,EAC1C,SAAS,EAAc,CAAY,EACjC,GAAI,EAAe,IAAI,CAAC,EAAK,GAC3B,EADgC,KACzB,CAAG,CAAC,EAAG,CAAC,MAAM,GAGvB,IAAM,EAAQ,AAAJ,MAAU,CAAC,oBAAoB,EAAE,EAAG,CAAC,CAAC,CAEhD,OADE,EAAU,IAAI,CAAG,mBACb,CACR,CAoBA,OAlBA,EAAc,IAAI,CAAG,IACZ,OAAO,IAAI,CAAC,GAGrB,EAAc,OAAO,CAAG,AAAC,IACvB,GAAI,EAAe,IAAI,CAAC,EAAK,GAC3B,EADgC,KACzB,CAAG,CAAC,EAAG,CAAC,EAAE,GAGnB,IAAM,EAAI,AAAI,MAAM,CAAC,oBAAoB,EAAE,EAAG,CAAC,CAAC,CAEhD,OADE,EAAU,IAAI,CAAG,mBACb,CACR,EAEA,EAAc,MAAM,CAAG,MAAO,GACrB,MAAO,EAAc,GAGvB,CACT,EAmFA,IAAM,EAAkB,OAAO,oBACzB,EAAmB,OAAO,qBAC1B,EAAiB,OAAO,mBAa9B,SAAS,EAAa,CAAkB,EAClC,GAAqB,GAA2B,CAAvC,EAAM,MAAM,GACvB,EAAM,MAAM,CAAA,EACZ,EAAM,OAAO,CAAE,AAAD,GAAQ,EAAG,UAAU,IACnC,EAAM,OAAO,CAAC,AAAC,GAAQ,EAAG,UAAU,GAAK,EAAG,UAAU,GAAK,KAE/D,CAyIA,EAAiB,CAAC,CAzFlB,EAyFqB,OAzFZ,AAEP,CAKS,CACT,CAAiB,EAEjB,IAAM,EAAS,IAAI,CAAC,CAAC,CACf,EAAgC,EAClC,OAAO,MAAM,CAAC,EAAE,CAAE,CAAE,MAAM,CAAA,EAAsB,QAChD,EAEE,EAA6B,IAAI,IAEjC,SAAE,CAAO,QAAE,CAAM,CAAE,QAAS,CAAU,CAAE,CAAG,IAE3C,EAA8B,OAAO,MAAM,CAAC,EAAY,CAC5D,CAAC,EAAiB,CAAE,EAAO,OAAO,CAClC,CAAC,EAAgB,CAAE,AAAC,IAClB,GAAS,EAAG,GACZ,EAAU,OAAO,CAAC,GAClB,EAAQ,KAAD,AAAS,CAAC,KAAO,EAC1B,CACF,GAEM,EAAiC,KACrC,IACS,EAET,IAAI,CAAM,EAEJ,IAAM,IACR,CAAO,CAAC,EAAiB,CADR,CACW,CAEhC,CACF,EAEA,OAAO,cAAc,CAAC,EAAQ,UAAW,GACzC,OAAO,cAAc,CAAC,EAAQ,kBAAmB,GA0CjD,EAxCA,GAwCK,MAxC4B,AAAxB,CAAmC,EAC1C,IAAM,EA/ED,AA+EwB,EA/EnB,GAAG,CAAC,AAAC,IAsHa,AArH5B,EA8EoB,CA9ER,OAAR,GAA+B,UAAf,OAAO,EAAkB,CAC3C,GAnGG,CAmGC,IAAiB,EAAM,OAAO,EAClC,GA5GF,AAQwB,AARR,CA4GV,QA3GkB,UAAxB,OAAO,GACP,UAAU,EACmB,YAA7B,OAyGgB,AAzGT,EAAa,IAAI,CAyGF,CAClB,IAAM,EAAoB,OAAO,MAAM,CAAC,EAAE,CAAE,CAC1C,MAAM,CAAA,CACR,GAEM,EAAsB,CAC1B,CAAC,EAAiB,CAAE,CAAC,EACrB,CAAC,EAAgB,CAAE,AAAC,GAAoC,EAAG,EAC7D,EAaA,OAXA,EAAI,IAAI,CACN,AAAC,IACC,CAAG,CAAC,EAAiB,CAAG,EACxB,EAAa,EACf,EACA,AAAC,IACC,CAAG,CAAC,EAAe,CAAG,EACtB,EAAa,EACf,GAGK,CACT,CACF,CAEA,MAAO,CACL,CAAC,EAAiB,CAAE,EACpB,CAAC,EAAgB,CAAE,KAAO,CAC5B,CACF,GAiDQ,EAAY,IAChB,EAAY,GAAG,CAAC,AAAC,IACf,GAAI,CAAC,CAAC,EAAe,CAAE,MAAM,CAAC,CAAC,EAAe,CAC9C,OAAO,CAAC,CAAC,EAAiB,AAC5B,GAEI,SAAE,CAAO,SAAE,CAAO,CAAE,CAAG,IAEvB,EAAmB,OAAO,MAAM,CAAC,IAAM,EAAQ,GAAY,CAC/D,WAAY,CACd,GAEA,SAAS,EAAQ,CAAa,EACxB,IAAM,GAAS,CAAC,EAAU,GAAG,CAAC,IAAI,CACpC,EAAU,GAAG,CAAC,GACV,GAAa,GAA6B,CAArC,EAAE,MAAM,GACf,EAAG,UAAU,GACb,EAAE,IAAI,CAAC,IAGb,CAIA,OAFA,EAAY,GAAG,CAAC,AAAC,GAAQ,CAAG,CAAC,EAAgB,CAAC,IAEvC,EAAG,UAAU,CAAG,EAAU,GACnC,EAEA,SAAS,AAAY,CAAS,EACxB,EACF,EAAQ,CAAO,AADR,CACS,EAAe,CAAG,GAElC,EAAQ,CAAO,CAAC,EAAiB,EAGnC,EAAa,EACf,GAII,GAAqB,IAA0B,CAAtC,EAAM,MAAM,GACvB,EAAM,MAAM,CAAA,CAAA,CAEhB,EAaA,IAAM,EAAc,SAAS,AAAuB,CAAgB,EAClE,IAAM,EAAU,IAAI,IAAI,EAAU,OAC5B,EAA8B,CAAC,EACrC,IAAK,IAAM,KAAO,EAAS,CAAM,CAAC,EAAI,CAAI,CAAe,CAAC,EAAI,CAK9D,IAAK,IAAM,KAJX,EAAO,IAAI,CAAG,EACd,EAAO,QAAQ,CAAG,EAAS,OAAO,CAAC,SAAU,IAC7C,EAAO,MAAM,CAAG,EAAO,QAAQ,CAAG,GAClC,EAAO,QAAQ,CAAG,EAAO,MAAM,CAAG,CAAC,GAAG,IAAsB,EAC1C,EAChB,OAAO,cAAc,CAAC,IAAI,CAAE,EAAK,CAC/B,YAAY,EACZ,cAAc,EACd,MAAO,CAAM,CAAC,EAAI,AACpB,EACJ,EAOA,SAAS,EAAU,CAAY,CAAE,CAAoC,EACnE,MAAM,AAAI,MAAM,CAAC,WAAW,EAAE,EAAe,GAAA,CAAQ,CACvD,CARA,EAAY,SAAS,CAAG,IAAI,SAAS,CACrC,EAAiB,CAAC,CAAG,EAerB,EAAiB,CAAC,CAHlB,EAGqB,OAHA,AAAZ,CAA+B,EACtC,MAAM,AAAI,MAAM,4CAClB,EAIA,EAAiB,CAAC,CAAG,WCtpBrB,IAAM,EACJ,EAAQ,SAAS,CAyBnB,IAAK,EAAA,SAAA,CAAA,SAKF,CAAA,CAAA,EAAA,OAAA,CAAA,EAAA,CAAA,UAKA,CAAA,CAAA,EAAA,MAAA,CAAA,EAAA,CAAA,SAMA,CAAA,CAAA,EAAA,MAAA,CAAA,EAAA,CAAA,SAhBE,GAAA,GAAA,CAAA,GAgDL,IAAM,EAAmC,IAAI,IAC7C,EAAiB,CAAC,CAAG,EAErB,IAAM,EAAuD,IAAI,IAE3D,EAA6D,IAAI,IAyCvE,eAAe,EACb,CAAsB,CACtB,CAAsB,CACtB,CAAoB,MA0BhB,EAxBJ,GAAyB,UAArB,AAA+B,OAAxB,EACT,OAAO,EAAc,EAAY,IAAY,IAG/C,IAAM,EAAe,EAAU,QAAQ,EAAI,EAAE,CACvC,EAAkB,EAAa,GAAG,CAAC,AAAC,GACxC,EAAI,EAAgB,GAAG,CAAC,IACjB,EAAiB,GAAG,CAAC,CADO,GAGrC,GAAI,CAHwC,CAGxB,MAAM,CAAG,GAAK,EAAgB,KAAK,CAAC,AAAC,GAAM,GAAI,YAEjE,MAAM,QAAQ,GAAG,CAAC,GAIpB,IAAM,EAA2B,EAAU,YAAY,EAAI,EAAE,CACvD,EAAuB,EAC1B,GAAG,CAAC,AAAC,GAGG,EAAsB,GAAG,CAAC,IAElC,MAAM,CAAC,AAAC,GAAM,GAGjB,GAAI,EAAqB,MAAM,CAAG,EAAG,CAGnC,GAAI,EAAqB,MAAM,GAAK,EAAyB,MAAM,CAAE,YAEnE,MAAM,QAAQ,GAAG,CAAC,GAIpB,IAAM,EAAqC,IAAI,IAC/C,IAAK,IAAM,KAAe,EACpB,AAAC,EAAsB,GAAG,CAAC,IAC7B,EAAmB,GAAG,CAAC,GAI3B,CAL+C,GADK,AAM/C,IAAM,KAAqB,EAAoB,CAClD,IAAM,IAAwB,EAAY,IAA1B,AAAsC,IAEtD,EAAsB,GAAG,CAAC,EAAmB,GAE7C,EAAqB,IAAI,CAAC,EAC5B,CAEA,EAAU,QAAQ,GAAG,CAAC,EACxB,MAIE,CAJK,GAIA,IAAM,KAHX,EA2FK,EA3FmB,EAAY,EA0F1B,EA1FA,AAAsC,EAAU,IAAI,GAG5B,GAC5B,AAAC,EAAsB,CAuFD,EAvFI,CAAC,AAsFH,IArF1B,EAAsB,GAAG,AAsFW,CAtFV,EAAqB,EAEnD,CAGF,CAP8D,GAOzD,EAiFiD,CAvFG,CAM9C,KAAY,EACjB,AAAC,EAAiB,GAAG,CAAC,IAGxB,CAJiC,CAIhB,GAAG,CAAC,CAHc,CAGJ,EAInC,OAAM,CACR,CAjFA,EAAwB,CAAC,CANzB,EAM4B,OAJ1B,AAFO,CAEa,EAEpB,OAAO,EAAA,EAAqC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAE,EACzD,EAoFA,IAAM,EAAc,QAAQ,OAAO,MAAC,GAC9B,EAAgC,IAAI,QAc1C,SAAS,EACP,CAAsB,CACtB,CAAsB,CACtB,CAAkB,EAElB,IAAM,EAAW,EAAQ,eAAe,CAAC,EAAY,GACjD,EAAQ,EAA8B,GAAG,CAAC,GAC9C,QAAc,IAAV,EAAqB,CACvB,IAAM,EAAU,EAA8B,GAAG,CAAC,IAAI,CACpD,EACA,EACA,GAEF,EAAQ,EAAS,IAAI,CAAC,GAAS,KAAK,CAAC,AAAC,IACpC,IAAI,EACJ,OAAQ,GACN,KAAA,EACE,EAAa,CAAC,iCAAiC,EAAE,EAAA,CAAY,CAC7D,KACF,MAAA,EACE,EAAa,CAAC,YAAY,EAAE,EAAA,CAAY,CACxC,KACF,MAAA,EACE,EAAa,qBACb,KACF,SACE,EACE,EACA,AAAC,GAAe,CAAC,qBAAqB,EAAE,EAAA,CAAY,CAE1D,CACA,MAAM,AAAI,MACR,CAAC,qBAAqB,EAAE,EAAS,CAAC,EAAE,EAAA,EAClC,EAAQ,CAAC,EAAE,EAAE,EAAA,CAAO,CAAG,GAAA,CACvB,CACF,EACI,CACE,MAAO,CACT,OACA,EAER,GACA,EAA8B,GAAG,CAAC,EAAU,EAC9C,CAEA,OAAO,CACT,CA4DA,SAAS,EAAoB,CAAoC,EAC/D,MAAO,GAAG,AAAkB,EACzB,KAAK,CAAC,KACN,GAAG,CAAC,AAAC,GAAM,mBAAmB,IAC9B,IAAI,CAAC,MACV,AADoC,CAjHpC,AAiHiB,EAjHO,CAAC,CANzB,EAM4B,OAJ1B,AAFO,CAEW,EAElB,OAAO,EAAA,EAA0C,IAAI,CAAC,CAAC,CAAC,EAAE,CAAE,EAC9D,EAwEA,EAAwB,CAAC,CAPzB,EAO4B,OAPnB,AAEP,CAAgB,EAEhB,IAAM,EAAW,IAAI,CAAC,CAAC,CAAC,GACxB,OAAO,GAAU,SAAW,CAC9B,EAUA,EAAwB,CAAC,CAHzB,EAG4B,OAHnB,AAAoB,CAAmB,EAC9C,MAAO,CAAC,MAAM,EAAE,GAAc,GAAA,CAAI,AACpC,EAgBA,EAAwB,CAAC,CATzB,EAS4B,OATnB,AAAiB,CAAmB,EAM3C,IAAI,EAAO,IAAI,KAAK,CAHJ,CAAC,iCAAiC,EAAE,KAAK,SAAS,CAAC,SAAS,MAAM,EAAE;iCACrD,EAAE,KAAK,SAAS,CAAC,EAAO,OAAO,GAAG,GAAG,CAAC,GAAsB,KAAM,GAAG;wGACE,CAAC,CACxE,CAAE,CAAE,KAAM,iBAAkB,GAC3D,OAAO,IAAI,eAAe,CAAC,EAC7B,EA8CA,IAAM,EAAa,4BA8BnB,EAAiB,CAAC,CAdlB,EAcqB,OAdZ,AAEP,CAAoB,CACpB,CAAoC,CACpC,CAA+B,EAE/B,OAAO,EAAQ,eAAe,CAAA,EAE5B,IAAI,CAAC,CAAC,CAAC,EAAE,CACT,EACA,EACA,EAEJ,EAeA,EAAiB,CAAC,CAZlB,EAYqB,OAZZ,AAEP,CAAoB,CACpB,CAAoC,EAEpC,OAAO,EAAQ,qBAAqB,CAAA,EAElC,IAAI,CAAC,CAAC,CAAC,EAAE,CACT,EACA,EAEJ,EChaA,IAAM,EAAmC,CAAC,EAC1C,EAAiB,CAAC,CAAG,EA4BrB,IAAM,EAEF,CAAC,EAAI,KACP,IAAM,EAAS,CAAW,CAAC,EAAG,CAE9B,GAAI,EAAQ,CACV,GAAI,EAAO,KAAK,CACd,CADgB,KACV,EAAO,KAAK,CAEpB,OAAO,CACT,CAEA,OAAO,EAAkB,EAAI,EAAW,MAAM,CAAE,EAAa,EAAE,CACjE,EAEA,SAAS,EACP,CAAY,CACZ,CAAsB,CACtB,CAAsB,EAEtB,IAAM,EAAgB,EAAgB,GAAG,CAAC,GACb,YAAzB,AAAqC,OAA9B,GDuDb,ACnDI,SDmDK,AACP,CAAkB,CAClB,CAAsB,CACtB,CAAsB,EAEtB,IAAI,EACJ,OAAQ,GACN,KAAA,EACE,EAAsB,CAAC,4BAA4B,EAAE,EAAA,CAAY,CACjE,KACF,MAAA,EACE,EAAsB,CAAC,oCAAoC,EAAE,EAAA,CAAY,CACzE,KACF,MAAA,EACE,EAAsB,2BACtB,KACF,SACE,EACE,EACA,AAAC,GAAe,CAAC,qBAAqB,EAAE,EAAA,CAAY,CAE1D,CACA,MAAM,AAAI,MACR,CAAC,OAAO,EAAE,EAAS,kBAAkB,EAAE,EAAoB,uFAAuF,CAAC,CAEvJ,EC5EwB,EAAI,EAAY,GAGtC,IAAM,EAAiB,EAAmB,GACpC,EAAU,EAAO,OAAO,CAE9B,CAAW,CAAC,EAAG,CAAG,EAGlB,IAAM,EAAU,IAAK,EACnB,EACA,GAEF,GAAI,CACF,EAAc,EAAS,EAAQ,EACjC,CAAE,MAAO,EAAO,CAEd,MADA,EAAO,KAAK,CAAG,EACT,CACR,CAOA,OALI,EAAO,eAAe,EAAI,EAAO,OAAO,GAAK,EAAO,eAAe,EAAE,AAEvE,EAAW,EAAO,OAAO,CAAE,EAAO,eAAe,EAG5C,CACT,CAGA,SAAS,EAAc,CAA+B,EACpD,IACI,EADE,EAAY,ADgRpB,SAAS,AACP,CAAsE,EAEtE,GAA2B,UAAvB,AAAiC,OAA1B,EACT,OAAO,EAMT,IAAM,EAAM,mBAAmB,CAHQ,aAArC,OAAO,0BACH,0BAA0B,GAAG,GAC7B,EAAY,YAAY,CAAC,MAAA,EACS,OAAO,CAAC,UAAW,KAI3D,OAHa,AAGN,EAHU,UAAU,CAAC,IACxB,EAAI,KAAK,CAAC,GACV,CAEN,EC/RsC,CAAY,CAAC,EAAE,EAcnD,ID8Q8B,GCzRF,GAAG,ADyRK,CCzRhC,EAAa,MAAM,CACrB,EAAgB,CAAY,CAAC,EAAE,EAE/B,OAAgB,GFkWpB,AEjWI,SFkWF,AADO,CACgC,CACvC,CAAc,CACd,CAAgC,CAChC,CAAoC,EAEpC,IAAI,IAAI,AACR,KAAO,EAAI,EAAa,MAAM,EAAE,CAC9B,IAAI,EAAW,CAAY,CAAC,EAAE,CAC1B,EAAM,EAAI,EAEd,KACE,EAAM,EAAa,MAAM,EACI,WAC7B,CADA,OAAO,CAAY,CAAC,EAAI,EAExB,IAEF,GAAI,IAAQ,EAAa,MAAM,CAC7B,CAD+B,KACzB,AAAI,MAAM,uDAIlB,GAAI,CAAC,EAAgB,GAAG,CAAC,GAAW,CAClC,IAAM,EAAkB,CAAY,CAAC,EAAI,CAGzC,IAgOJ,OAAO,cAAc,CAAC,AAlOK,EAkOI,OAAQ,CACrC,MAAO,mCACT,GAlOW,EAAI,EAAK,IACd,AADmB,EACR,CAAY,CAAC,EAAE,CAC1B,EAAgB,GAAG,CAAC,EAAU,EAElC,CACA,EAAI,EAAM,CACZ,CACF,CAFgB,CE/XV,EACc,EACd,IAIG,EAAQ,IALX,SAKwB,CAAC,CALd,CAKyB,EAC1C,CC1EA,SAAS,EACP,CAAY,CACZ,CAAgB,CAChB,GAAe,CAAK,EAEpB,IAAI,EACJ,GAAI,CACF,EAAM,GACR,CAAE,MAAO,EAAK,CAKZ,MAAM,AAAI,IHqbwF,EGrblF,CAAC,+BAA+B,EAAE,EAAG,EAAE,EAAE,EAAA,CAAK,CAChE,OAEA,AAAI,CAAC,GAAO,EAAI,UAAU,CACjB,CADmB,CAIrB,EAAW,EAAK,EAAS,IAAM,EACxC,CAvBA,EAAiB,CAAC,CAlBlB,EAkBqB,aAlBN,AAAe,CAAuB,EACnD,IAAI,EACJ,GAAI,CACF,EAAM,MAAM,MAAM,CAAC,EACrB,CAAE,MAAO,EAAK,CAKZ,MAAU,AAAJ,MAAU,CAAC,+BAA+B,EAAE,EAAG,EAAE,EAAE,EAAA,CAAK,CAChE,QAEA,AAAI,GAAO,EAAI,UAAU,EAAI,EAAI,OAAO,EAAI,YAAa,EAAI,OAAO,CAC3D,CAD6D,CAClD,EAAI,OAAO,CAAE,EAAS,IAAM,GAGzC,CACT,EA0BA,EAAgB,OAAO,CAAG,CACxB,EACA,IAIO,QAAQ,OAAO,CAAC,EAAI,GAE7B,EAAiB,CAAC,CAAG,ECvCpB,AAAC,MACA,EAAU,CAKR,cAAc,CAAS,CAAE,CAAM,EAC7B,EAAiB,GAAG,CAAC,GA+FzB,AA9FI,SA8FK,AAA2B,CAAoB,EAGtD,IAAM,EAAkB,EAAQ,GAAG,CAAC,GACpC,GAAuB,MAAnB,EAAyB,CAC3B,IAAK,IAAM,KAAU,EACnB,EAAO,YAD6B,EACf,CAAC,MAAM,CAAC,GAEM,GAAG,CAAlC,EAAO,cAAc,CAAC,IAAI,EAC5B,EAA0B,EAAO,gBAAgB,CAAE,EAAO,SAAS,EAGvE,EAAQ,MAAM,CAAC,EACjB,CACF,EA5G+B,GAEb,MAAV,AAAgB,IAIc,GAAG,CAAjC,EAAO,WAAW,CAAC,MAAM,CAG3B,EAA0B,EAAO,gBAAgB,CAAE,GAKnD,AA4CN,SAAS,AACP,CAAoB,CACpB,CAAwB,CACxB,CAA4B,EAE5B,IAAM,EAAiC,IAAI,IACrC,EAAS,kBACb,YACA,iBACA,CACF,EAEA,IAAK,IAAM,KAAkB,EAAa,CACxC,IAAM,EAAiB,EAAa,GACpC,GAAI,EAAiB,GAAG,CAAC,GACvB,SAGF,EAAe,GAAG,AAJwB,CAIvB,GACnB,IAAI,EAAkB,EAAQ,GAAG,CAAC,EACX,MAAM,CAAzB,IACF,EAAkB,IAAI,IACtB,EAAQ,GAAG,CAAC,EAAgB,IAE9B,EAAgB,GAAG,CAAC,EACtB,CAEmC,GAAG,CAAlC,EAAO,cAAc,CAAC,IAAI,EAC5B,EAA0B,EAAO,gBAAgB,CAAE,EAAO,SAAS,CAEvE,EAzEQ,EACA,EAAO,WAAW,CAAC,MAAM,CAAC,AAAC,QACzB,SH+UE,EG9UG,EAAa,GH+UrB,EAAW,IAAI,CAAC,AADyB,KG5UxC,EAAO,gBAAgB,EAG7B,EAEA,gBAAgB,CAAuB,CAAE,CAAmB,EAC1D,CAT8E,KASxE,AAAI,MAAM,iCAClB,EAEA,MAAM,gBACJ,CAAuB,CACvB,CAAuB,CACvB,CAAoB,CACpB,CAAoC,CACpC,CAA4B,EAE5B,IAAM,EAAS,MAAM,EAAa,EAAW,GAE7C,OAAO,MAAM,YAAY,WAAW,CAAC,EAAQ,EAC/C,EAEM,sBAAN,MACE,EACA,EACA,EACA,IAEO,CALgB,CAKH,CAJG,AACH,CAGW,EAEnC,AAJwC,EAMxC,IAAM,EAAmC,IAAI,IACvC,EAA4C,IAAI,IA6DtD,SAAS,EACP,CAA4B,CAC5B,CAAoB,EAEpB,IAAK,IAAM,KAAY,GACrB,AFvIN,SAAS,AACP,CAAoB,CACpB,CAAkB,EAElB,AEkI2C,IFlIrC,EAAS,CAAW,CAAC,EAAS,CACpC,GAAI,EAAQ,CACV,GAAI,EAAO,KAAK,CACd,CADgB,KACV,EAAO,KAAK,CAEpB,MACF,CADS,AAGF,EAAkB,EAAU,EAAW,OAAO,CAAE,EACzD,EE0HoC,EAAW,EAE7C,CAEA,eAAe,EACb,CAAoB,CACpB,CAAoC,EAEpC,IAAI,EACJ,GAAI,CACF,EAAS,GACX,CAAE,MAAO,EAAI,CAAC,CAEd,GAAI,CAAC,EACH,MADW,AACL,AAAI,MACR,CAAC,uGAAuG,EAAE,EAAU,CAAC,CAAC,EAI1H,OAAO,CACT,EACF,CAAC", "ignoreList": [0, 1, 2, 3, 4]}