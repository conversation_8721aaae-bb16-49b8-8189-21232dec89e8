module.exports=[18622,(e,t,r)=>{t.exports=e.x("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js"))},56704,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/work-async-storage.external.js",()=>require("next/dist/server/app-render/work-async-storage.external.js"))},32319,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/work-unit-async-storage.external.js",()=>require("next/dist/server/app-render/work-unit-async-storage.external.js"))},20635,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/action-async-storage.external.js",()=>require("next/dist/server/app-render/action-async-storage.external.js"))},24725,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/after-task-async-storage.external.js",()=>require("next/dist/server/app-render/after-task-async-storage.external.js"))},24361,(e,t,r)=>{t.exports=e.x("util",()=>require("util"))},29173,(e,t,r)=>{t.exports=e.x("@prisma/client",()=>require("@prisma/client"))},70406,(e,t,r)=>{t.exports=e.x("next/dist/compiled/@opentelemetry/api",()=>require("next/dist/compiled/@opentelemetry/api"))},93695,(e,t,r)=>{t.exports=e.x("next/dist/shared/lib/no-fallback-error.external.js",()=>require("next/dist/shared/lib/no-fallback-error.external.js"))},61724,(e,t,r)=>{t.exports=e.x("next/dist/compiled/next-server/app-route-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-route-turbo.runtime.prod.js"))},54799,(e,t,r)=>{t.exports=e.x("crypto",()=>require("crypto"))},92509,(e,t,r)=>{t.exports=e.x("url",()=>require("url"))},21517,(e,t,r)=>{t.exports=e.x("http",()=>require("http"))},49719,(e,t,r)=>{t.exports=e.x("assert",()=>require("assert"))},45706,(e,t,r)=>{t.exports=e.x("querystring",()=>require("querystring"))},874,(e,t,r)=>{t.exports=e.x("buffer",()=>require("buffer"))},6461,(e,t,r)=>{t.exports=e.x("zlib",()=>require("zlib"))},24836,(e,t,r)=>{t.exports=e.x("https",()=>require("https"))},27699,(e,t,r)=>{t.exports=e.x("events",()=>require("events"))},12756,e=>{e.v(JSON.parse('{"name":"openid-client","version":"5.7.1","description":"OpenID Connect Relying Party (RP, Client) implementation for Node.js runtime, supports passportjs","keywords":["auth","authentication","basic","certified","client","connect","dynamic","electron","hybrid","identity","implicit","oauth","oauth2","oidc","openid","passport","relying party","strategy"],"homepage":"https://github.com/panva/openid-client","repository":"panva/openid-client","funding":{"url":"https://github.com/sponsors/panva"},"license":"MIT","author":"Filip Skokan <<EMAIL>>","exports":{"types":"./types/index.d.ts","import":"./lib/index.mjs","require":"./lib/index.js"},"main":"./lib/index.js","types":"./types/index.d.ts","files":["lib","types/index.d.ts"],"scripts":{"format":"npx prettier --loglevel silent --write ./lib ./test ./certification ./types","test":"mocha test/**/*.test.js"},"dependencies":{"jose":"^4.15.9","lru-cache":"^6.0.0","object-hash":"^2.2.0","oidc-token-hash":"^5.0.3"},"devDependencies":{"@types/node":"^16.18.106","@types/passport":"^1.0.16","base64url":"^3.0.1","chai":"^4.5.0","mocha":"^10.7.3","nock":"^13.5.5","prettier":"^2.8.8","readable-mock-req":"^0.2.2","sinon":"^9.2.4","timekeeper":"^2.3.1"},"standard-version":{"scripts":{"postchangelog":"sed -i \'\' -e \'s/### \\\\[/## [/g\' CHANGELOG.md"},"types":[{"type":"feat","section":"Features"},{"type":"fix","section":"Fixes"},{"type":"chore","hidden":true},{"type":"docs","hidden":true},{"type":"style","hidden":true},{"type":"refactor","section":"Refactor","hidden":false},{"type":"perf","section":"Performance","hidden":false},{"type":"test","hidden":true}]}}'))},12260,(e,t,r)=>{},82550,e=>{"use strict";e.s(["handler",()=>I,"patchFetch",()=>k,"routeModule",()=>C,"serverHooks",()=>O,"workAsyncStorage",()=>S,"workUnitAsyncStorage",()=>A],82550);var t=e.i(6137),r=e.i(11365),s=e.i(9638),n=e.i(15243),a=e.i(66378),i=e.i(92101),o=e.i(50012),u=e.i(62885),l=e.i(31409),d=e.i(78448),p=e.i(28015),c=e.i(72721),x=e.i(75714),h=e.i(12634),m=e.i(93695);e.i(74732);var f=e.i(66662);e.s(["GET",()=>w,"POST",()=>E,"PUT",()=>N],51661);var y=e.i(2835),R=e.i(58356),g=e.i(43382),v=e.i(31279),j=e.i(47504);let b=j.z.object({name:j.z.string().min(1,"Subject name is required"),code:j.z.string().min(1,"Subject code is required"),description:j.z.string().optional(),classId:j.z.string().min(1,"Class ID is required")});async function w(e){try{let t=await (0,R.getServerSession)(g.authOptions);if(!t?.user||"ADMIN"!==t.user.role)return y.NextResponse.json({error:"Unauthorized"},{status:401});let{searchParams:r}=new URL(e.url),s=parseInt(r.get("page")||"1"),n=parseInt(r.get("limit")||"10"),a=r.get("search")||"",i=(s-1)*n,o={};a&&(o.OR=[{name:{contains:a,mode:"insensitive"}},{code:{contains:a,mode:"insensitive"}},{description:{contains:a,mode:"insensitive"}}]);let[u,l]=await Promise.all([v.prisma.subject.findMany({where:o,skip:i,take:n,orderBy:{name:"asc"},include:{class:{select:{id:!0,name:!0}},_count:{select:{exams:!0}}}}),v.prisma.subject.count({where:o})]),d=Math.ceil(l/n);return y.NextResponse.json({subjects:u,pagination:{page:s,limit:n,total:l,totalPages:d}})}catch(e){return console.error("Error fetching subjects:",e),y.NextResponse.json({error:"Failed to fetch subjects"},{status:500})}}async function E(e){try{let t=await (0,R.getServerSession)(g.authOptions);if(!t?.user||"ADMIN"!==t.user.role)return y.NextResponse.json({error:"Unauthorized"},{status:401});let r=await e.json(),s=b.parse(r);if(await v.prisma.subject.findFirst({where:{OR:[{name:s.name},{code:s.code}]}}))return y.NextResponse.json({error:"Subject with this name or code already exists"},{status:400});if(!await v.prisma.class.findUnique({where:{id:s.classId}}))return y.NextResponse.json({error:"Class not found"},{status:400});let n=await v.prisma.subject.create({data:s,include:{class:{select:{id:!0,name:!0}}}});return y.NextResponse.json({message:"Subject created successfully",subject:n},{status:201})}catch(e){if(e instanceof j.z.ZodError)return y.NextResponse.json({error:"Validation error",details:e.errors},{status:400});return console.error("Error creating subject:",e),y.NextResponse.json({error:"Failed to create subject"},{status:500})}}async function N(e){try{let t=await (0,R.getServerSession)(g.authOptions);if(!t?.user||"ADMIN"!==t.user.role)return y.NextResponse.json({error:"Unauthorized"},{status:401});let{id:r,...s}=await e.json();if(!r)return y.NextResponse.json({error:"Subject ID is required"},{status:400});let n=b.partial().parse(s);if(!await v.prisma.subject.findUnique({where:{id:parseInt(r)}}))return y.NextResponse.json({error:"Subject not found"},{status:404});if(n.classId&&!await v.prisma.class.findUnique({where:{id:n.classId}}))return y.NextResponse.json({error:"Class not found"},{status:400});let a=await v.prisma.subject.update({where:{id:parseInt(r)},data:n,include:{class:{select:{id:!0,name:!0}}}});return y.NextResponse.json({message:"Subject updated successfully",subject:a})}catch(e){if(e instanceof j.z.ZodError)return y.NextResponse.json({error:"Validation error",details:e.errors},{status:400});return console.error("Error updating subject:",e),y.NextResponse.json({error:"Failed to update subject"},{status:500})}}var q=e.i(51661);let C=new t.AppRouteRouteModule({definition:{kind:r.RouteKind.APP_ROUTE,page:"/api/admin/subjects/route",pathname:"/api/admin/subjects",filename:"route",bundlePath:""},distDir:".next",relativeProjectDir:"",resolvedPagePath:"[project]/school-management-system/src/app/api/admin/subjects/route.ts",nextConfigOutput:"",userland:q}),{workAsyncStorage:S,workUnitAsyncStorage:A,serverHooks:O}=C;function k(){return(0,s.patchFetch)({workAsyncStorage:S,workUnitAsyncStorage:A})}async function I(e,t,s){var y;let R="/api/admin/subjects/route";R=R.replace(/\/index$/,"")||"/";let g=await C.prepare(e,t,{srcPage:R,multiZoneDraftMode:!1});if(!g)return t.statusCode=400,t.end("Bad Request"),null==s.waitUntil||s.waitUntil.call(s,Promise.resolve()),null;let{buildId:v,params:j,nextConfig:b,isDraftMode:w,prerenderManifest:E,routerServerContext:N,isOnDemandRevalidate:q,revalidateOnlyGenerated:S,resolvedPathname:A}=g,O=(0,i.normalizeAppPath)(R),k=!!(E.dynamicRoutes[O]||E.routes[A]);if(k&&!w){let e=!!E.routes[A],t=E.dynamicRoutes[O];if(t&&!1===t.fallback&&!e)throw new m.NoFallbackError}let I=null;!k||C.isDev||w||(I="/index"===(I=A)?"/":I);let P=!0===C.isDev||!k,T=k&&!P,U=e.method||"GET",_=(0,a.getTracer)(),M=_.getActiveScopeSpan(),D={params:j,prerenderManifest:E,renderOpts:{experimental:{cacheComponents:!!b.experimental.cacheComponents,authInterrupts:!!b.experimental.authInterrupts},supportsDynamicResponse:P,incrementalCache:(0,n.getRequestMeta)(e,"incrementalCache"),cacheLifeProfiles:null==(y=b.experimental)?void 0:y.cacheLife,isRevalidate:T,waitUntil:s.waitUntil,onClose:e=>{t.on("close",e)},onAfterTaskError:void 0,onInstrumentationRequestError:(t,r,s)=>C.onRequestError(e,t,s,N)},sharedContext:{buildId:v}},H=new o.NodeNextRequest(e),F=new o.NodeNextResponse(t),z=u.NextRequestAdapter.fromNodeNextRequest(H,(0,u.signalFromNodeResponse)(t));try{let i=async r=>C.handle(z,D).finally(()=>{if(!r)return;r.setAttributes({"http.status_code":t.statusCode,"next.rsc":!1});let s=_.getRootSpanAttributes();if(!s)return;if(s.get("next.span_type")!==l.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${s.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let n=s.get("next.route");if(n){let e=`${U} ${n}`;r.setAttributes({"next.route":n,"http.route":n,"next.span_name":e}),r.updateName(e)}else r.updateName(`${U} ${e.url}`)}),o=async a=>{var o,u;let l=async({previousCacheEntry:r})=>{try{if(!(0,n.getRequestMeta)(e,"minimalMode")&&q&&S&&!r)return t.statusCode=404,t.setHeader("x-nextjs-cache","REVALIDATED"),t.end("This page could not be found"),null;let o=await i(a);e.fetchMetrics=D.renderOpts.fetchMetrics;let u=D.renderOpts.pendingWaitUntil;u&&s.waitUntil&&(s.waitUntil(u),u=void 0);let l=D.renderOpts.collectedTags;if(!k)return await (0,p.sendResponse)(H,F,o,D.renderOpts.pendingWaitUntil),null;{let e=await o.blob(),t=(0,c.toNodeOutgoingHttpHeaders)(o.headers);l&&(t[h.NEXT_CACHE_TAGS_HEADER]=l),!t["content-type"]&&e.type&&(t["content-type"]=e.type);let r=void 0!==D.renderOpts.collectedRevalidate&&!(D.renderOpts.collectedRevalidate>=h.INFINITE_CACHE)&&D.renderOpts.collectedRevalidate,s=void 0===D.renderOpts.collectedExpire||D.renderOpts.collectedExpire>=h.INFINITE_CACHE?void 0:D.renderOpts.collectedExpire;return{value:{kind:f.CachedRouteKind.APP_ROUTE,status:o.status,body:Buffer.from(await e.arrayBuffer()),headers:t},cacheControl:{revalidate:r,expire:s}}}}catch(t){throw(null==r?void 0:r.isStale)&&await C.onRequestError(e,t,{routerKind:"App Router",routePath:R,routeType:"route",revalidateReason:(0,d.getRevalidateReason)({isRevalidate:T,isOnDemandRevalidate:q})},N),t}},m=await C.handleResponse({req:e,nextConfig:b,cacheKey:I,routeKind:r.RouteKind.APP_ROUTE,isFallback:!1,prerenderManifest:E,isRoutePPREnabled:!1,isOnDemandRevalidate:q,revalidateOnlyGenerated:S,responseGenerator:l,waitUntil:s.waitUntil});if(!k)return null;if((null==m||null==(o=m.value)?void 0:o.kind)!==f.CachedRouteKind.APP_ROUTE)throw Object.defineProperty(Error(`Invariant: app-route received invalid cache entry ${null==m||null==(u=m.value)?void 0:u.kind}`),"__NEXT_ERROR_CODE",{value:"E701",enumerable:!1,configurable:!0});(0,n.getRequestMeta)(e,"minimalMode")||t.setHeader("x-nextjs-cache",q?"REVALIDATED":m.isMiss?"MISS":m.isStale?"STALE":"HIT"),w&&t.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");let y=(0,c.fromNodeOutgoingHttpHeaders)(m.value.headers);return(0,n.getRequestMeta)(e,"minimalMode")&&k||y.delete(h.NEXT_CACHE_TAGS_HEADER),!m.cacheControl||t.getHeader("Cache-Control")||y.get("Cache-Control")||y.set("Cache-Control",(0,x.getCacheControlHeader)(m.cacheControl)),await (0,p.sendResponse)(H,F,new Response(m.value.body,{headers:y,status:m.value.status||200})),null};M?await o(M):await _.withPropagatedContext(e.headers,()=>_.trace(l.BaseServerSpan.handleRequest,{spanName:`${U} ${e.url}`,kind:a.SpanKind.SERVER,attributes:{"http.method":U,"http.target":e.url}},o))}catch(t){if(M||t instanceof m.NoFallbackError||await C.onRequestError(e,t,{routerKind:"App Router",routePath:O,routeType:"route",revalidateReason:(0,d.getRevalidateReason)({isRevalidate:T,isOnDemandRevalidate:q})}),k)throw t;return await (0,p.sendResponse)(H,F,new Response(null,{status:500})),null}}}];

//# sourceMappingURL=%5Broot-of-the-server%5D__714a2e38._.js.map