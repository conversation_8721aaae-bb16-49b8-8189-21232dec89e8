{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\r\nimport { twMerge } from \"tailwind-merge\"\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs))\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,SAAS;IAAG,IAAA,IAAA,OAAA,UAAA,QAAA,AAAG,SAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAG,OAAH,QAAA,SAAA,CAAA,KAAuB;;IACxC,OAAO,IAAA,yQAAO,EAAC,IAAA,mOAAI,EAAC;AACtB", "debugId": null}}, {"offset": {"line": 25, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Card = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\r\n      \"rounded-lg border border-gray-200 dark:border-gray-800 bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 shadow-sm\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nCard.displayName = \"Card\"\r\n\r\nconst CardHeader = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardHeader.displayName = \"CardHeader\"\r\n\r\nconst CardTitle = React.forwardRef<\r\n  HTMLParagraphElement,\r\n  React.HTMLAttributes<HTMLHeadingElement>\r\n>(({ className, ...props }, ref) => (\r\n  <h3\r\n    ref={ref}\r\n    className={cn(\r\n      \"text-2xl font-semibold leading-none tracking-tight\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nCardTitle.displayName = \"CardTitle\"\r\n\r\nconst CardDescription = React.forwardRef<\r\n  HTMLParagraphElement,\r\n  React.HTMLAttributes<HTMLParagraphElement>\r\n>(({ className, ...props }, ref) => (\r\n  <p\r\n    ref={ref}\r\n    className={cn(\"text-sm text-gray-600 dark:text-gray-400\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardDescription.displayName = \"CardDescription\"\r\n\r\nconst CardContent = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\r\n))\r\nCardContent.displayName = \"CardContent\"\r\n\r\nconst CardFooter = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"flex items-center p-6 pt-0\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardFooter.displayName = \"CardFooter\"\r\n\r\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,4TAAgB,MAG3B,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,8UAAC;QACC,KAAK;QACL,WAAW,IAAA,8JAAE,EACX,+HACA;QAED,GAAG,KAAK;;;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,4TAAgB,OAGjC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,8UAAC;QACC,KAAK;QACL,WAAW,IAAA,8JAAE,EAAC,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,4TAAgB,OAGhC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,8UAAC;QACC,KAAK;QACL,WAAW,IAAA,8JAAE,EACX,sDACA;QAED,GAAG,KAAK;;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,4TAAgB,OAGtC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,8UAAC;QACC,KAAK;QACL,WAAW,IAAA,8JAAE,EAAC,4CAA4C;QACzD,GAAG,KAAK;;;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,4TAAgB,OAGlC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,8UAAC;QAAI,KAAK;QAAK,WAAW,IAAA,8JAAE,EAAC,YAAY;QAAa,GAAG,KAAK;;;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,4TAAgB,QAGjC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,8UAAC;QACC,KAAK;QACL,WAAW,IAAA,8JAAE,EAAC,8BAA8B;QAC3C,GAAG,KAAK;;;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 150, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst buttonVariants = cva(\r\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: \"bg-blue-600 text-white hover:bg-blue-700 dark:bg-blue-600 dark:hover:bg-blue-700\",\r\n        destructive:\r\n          \"bg-red-600 text-white hover:bg-red-700 dark:bg-red-600 dark:hover:bg-red-700\",\r\n        outline:\r\n          \"border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-900 hover:bg-gray-50 dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-gray-100\",\r\n        secondary:\r\n          \"bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-gray-100 hover:bg-gray-200 dark:hover:bg-gray-700\",\r\n        ghost: \"hover:bg-gray-100 dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-gray-100\",\r\n        link: \"text-blue-600 dark:text-blue-400 underline-offset-4 hover:underline\",\r\n      },\r\n      size: {\r\n        default: \"h-10 px-4 py-2\",\r\n        sm: \"h-9 rounded-md px-3\",\r\n        lg: \"h-11 rounded-md px-8\",\r\n        icon: \"h-10 w-10\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n      size: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nexport interface ButtonProps\r\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\r\n    VariantProps<typeof buttonVariants> {\r\n  asChild?: boolean\r\n}\r\n\r\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\r\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\r\n    const Comp = asChild ? Slot : \"button\"\r\n    return (\r\n      <Comp\r\n        className={cn(buttonVariants({ variant, size, className }))}\r\n        ref={ref}\r\n        {...props}\r\n      />\r\n    )\r\n  }\r\n)\r\nButton.displayName = \"Button\"\r\n\r\nexport { Button, buttonVariants }\r\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;;;;;;AAEA,MAAM,iBAAiB,IAAA,uRAAG,EACxB,uQACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,4TAAgB,MAC7B,QAA0D;QAAzD,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO;IACtD,MAAM,OAAO,UAAU,kVAAI,GAAG;IAC9B,qBACE,8UAAC;QACC,WAAW,IAAA,8JAAE,EAAC,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 218, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/components/ui/dropdown-menu.tsx"], "sourcesContent": ["'use client'\n\nimport * as React from 'react'\nimport * as DropdownMenuPrimitive from '@radix-ui/react-dropdown-menu'\nimport { Check, ChevronRight, Circle } from 'lucide-react'\nimport { cn } from '@/lib/utils'\n\nconst DropdownMenu = DropdownMenuPrimitive.Root\n\nconst DropdownMenuTrigger = DropdownMenuPrimitive.Trigger\n\nconst DropdownMenuGroup = DropdownMenuPrimitive.Group\n\nconst DropdownMenuPortal = DropdownMenuPrimitive.Portal\n\nconst DropdownMenuSub = DropdownMenuPrimitive.Sub\n\nconst DropdownMenuRadioGroup = DropdownMenuPrimitive.RadioGroup\n\nconst DropdownMenuSubTrigger = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.SubTrigger>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.SubTrigger> & {\n    inset?: boolean\n  }\n>(({ className, inset, children, ...props }, ref) => (\n  <DropdownMenuPrimitive.SubTrigger\n    ref={ref}\n    className={cn(\n      'flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent',\n      inset && 'pl-8',\n      className\n    )}\n    {...props}\n  >\n    {children}\n    <ChevronRight className=\"ml-auto h-4 w-4\" />\n  </DropdownMenuPrimitive.SubTrigger>\n))\nDropdownMenuSubTrigger.displayName = DropdownMenuPrimitive.SubTrigger.displayName\n\nconst DropdownMenuSubContent = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.SubContent>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.SubContent>\n>(({ className, ...props }, ref) => (\n  <DropdownMenuPrimitive.SubContent\n    ref={ref}\n    className={cn(\n      'z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2',\n      className\n    )}\n    {...props}\n  />\n))\nDropdownMenuSubContent.displayName = DropdownMenuPrimitive.SubContent.displayName\n\nconst DropdownMenuContent = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Content>\n>(({ className, sideOffset = 4, ...props }, ref) => (\n  <DropdownMenuPrimitive.Portal>\n    <DropdownMenuPrimitive.Content\n      ref={ref}\n      sideOffset={sideOffset}\n      className={cn(\n        'z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2',\n        className\n      )}\n      {...props}\n    />\n  </DropdownMenuPrimitive.Portal>\n))\nDropdownMenuContent.displayName = DropdownMenuPrimitive.Content.displayName\n\nconst DropdownMenuItem = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Item> & {\n    inset?: boolean\n  }\n>(({ className, inset, ...props }, ref) => (\n  <DropdownMenuPrimitive.Item\n    ref={ref}\n    className={cn(\n      'relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50',\n      inset && 'pl-8',\n      className\n    )}\n    {...props}\n  />\n))\nDropdownMenuItem.displayName = DropdownMenuPrimitive.Item.displayName\n\nconst DropdownMenuCheckboxItem = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.CheckboxItem>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.CheckboxItem>\n>(({ className, children, checked, ...props }, ref) => (\n  <DropdownMenuPrimitive.CheckboxItem\n    ref={ref}\n    className={cn(\n      'relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50',\n      className\n    )}\n    checked={checked}\n    {...props}\n  >\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\n      <DropdownMenuPrimitive.ItemIndicator>\n        <Check className=\"h-4 w-4\" />\n      </DropdownMenuPrimitive.ItemIndicator>\n    </span>\n    {children}\n  </DropdownMenuPrimitive.CheckboxItem>\n))\nDropdownMenuCheckboxItem.displayName = DropdownMenuPrimitive.CheckboxItem.displayName\n\nconst DropdownMenuRadioItem = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.RadioItem>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.RadioItem>\n>(({ className, children, ...props }, ref) => (\n  <DropdownMenuPrimitive.RadioItem\n    ref={ref}\n    className={cn(\n      'relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50',\n      className\n    )}\n    {...props}\n  >\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\n      <DropdownMenuPrimitive.ItemIndicator>\n        <Circle className=\"h-2 w-2 fill-current\" />\n      </DropdownMenuPrimitive.ItemIndicator>\n    </span>\n    {children}\n  </DropdownMenuPrimitive.RadioItem>\n))\nDropdownMenuRadioItem.displayName = DropdownMenuPrimitive.RadioItem.displayName\n\nconst DropdownMenuLabel = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Label>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Label> & {\n    inset?: boolean\n  }\n>(({ className, inset, ...props }, ref) => (\n  <DropdownMenuPrimitive.Label\n    ref={ref}\n    className={cn(\n      'px-2 py-1.5 text-sm font-semibold',\n      inset && 'pl-8',\n      className\n    )}\n    {...props}\n  />\n))\nDropdownMenuLabel.displayName = DropdownMenuPrimitive.Label.displayName\n\nconst DropdownMenuSeparator = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Separator>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Separator>\n>(({ className, ...props }, ref) => (\n  <DropdownMenuPrimitive.Separator\n    ref={ref}\n    className={cn('-mx-1 my-1 h-px bg-muted', className)}\n    {...props}\n  />\n))\nDropdownMenuSeparator.displayName = DropdownMenuPrimitive.Separator.displayName\n\nconst DropdownMenuShortcut = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLSpanElement>) => {\n  return (\n    <span\n      className={cn('ml-auto text-xs tracking-widest opacity-60', className)}\n      {...props}\n    />\n  )\n}\nDropdownMenuShortcut.displayName = 'DropdownMenuShortcut'\n\nexport {\n  DropdownMenu,\n  DropdownMenuTrigger,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuCheckboxItem,\n  DropdownMenuRadioItem,\n  DropdownMenuLabel,\n  DropdownMenuSeparator,\n  DropdownMenuShortcut,\n  DropdownMenuGroup,\n  DropdownMenuPortal,\n  DropdownMenuSub,\n  DropdownMenuSubContent,\n  DropdownMenuSubTrigger,\n  DropdownMenuRadioGroup,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA;AACA;AACA;AAAA;AAAA;AACA;AALA;;;;;;AAOA,MAAM,eAAe,gUAA0B;AAE/C,MAAM,sBAAsB,mUAA6B;AAEzD,MAAM,oBAAoB,iUAA2B;AAErD,MAAM,qBAAqB,kUAA4B;AAEvD,MAAM,kBAAkB,+TAAyB;AAEjD,MAAM,yBAAyB,sUAAgC;AAE/D,MAAM,uCAAyB,4TAAgB,MAK7C,QAA2C;QAA1C,EAAE,SAAS,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,OAAO;yBACzC,8UAAC,sUAAgC;QAC/B,KAAK;QACL,WAAW,IAAA,8JAAE,EACX,wIACA,SAAS,QACT;QAED,GAAG,KAAK;;YAER;0BACD,8UAAC,+VAAY;gBAAC,WAAU;;;;;;;;;;;;;;AAG5B,uBAAuB,WAAW,GAAG,sUAAgC,CAAC,WAAW;AAEjF,MAAM,uCAAyB,4TAAgB,OAG7C,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,8UAAC,sUAAgC;QAC/B,KAAK;QACL,WAAW,IAAA,8JAAE,EACX,ybACA;QAED,GAAG,KAAK;;;;;;;;AAGb,uBAAuB,WAAW,GAAG,sUAAgC,CAAC,WAAW;AAEjF,MAAM,oCAAsB,4TAAgB,OAG1C,QAA0C;QAAzC,EAAE,SAAS,EAAE,aAAa,CAAC,EAAE,GAAG,OAAO;yBACxC,8UAAC,kUAA4B;kBAC3B,cAAA,8UAAC,mUAA6B;YAC5B,KAAK;YACL,YAAY;YACZ,WAAW,IAAA,8JAAE,EACX,ybACA;YAED,GAAG,KAAK;;;;;;;;;;;;;AAIf,oBAAoB,WAAW,GAAG,mUAA6B,CAAC,WAAW;AAE3E,MAAM,iCAAmB,4TAAgB,OAKvC,QAAiC;QAAhC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO;yBAC/B,8UAAC,gUAA0B;QACzB,KAAK;QACL,WAAW,IAAA,8JAAE,EACX,mOACA,SAAS,QACT;QAED,GAAG,KAAK;;;;;;;;AAGb,iBAAiB,WAAW,GAAG,gUAA0B,CAAC,WAAW;AAErE,MAAM,yCAA2B,4TAAgB,OAG/C,QAA6C;QAA5C,EAAE,SAAS,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,OAAO;yBAC3C,8UAAC,wUAAkC;QACjC,KAAK;QACL,WAAW,IAAA,8JAAE,EACX,wOACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,8UAAC;gBAAK,WAAU;0BACd,cAAA,8UAAC,yUAAmC;8BAClC,cAAA,8UAAC,sUAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGpB;;;;;;;;;AAGL,yBAAyB,WAAW,GAAG,wUAAkC,CAAC,WAAW;AAErF,MAAM,sCAAwB,4TAAgB,QAG5C,QAAoC;QAAnC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO;yBAClC,8UAAC,qUAA+B;QAC9B,KAAK;QACL,WAAW,IAAA,8JAAE,EACX,wOACA;QAED,GAAG,KAAK;;0BAET,8UAAC;gBAAK,WAAU;0BACd,cAAA,8UAAC,yUAAmC;8BAClC,cAAA,8UAAC,yUAAM;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGrB;;;;;;;;;AAGL,sBAAsB,WAAW,GAAG,qUAA+B,CAAC,WAAW;AAE/E,MAAM,kCAAoB,4TAAgB,QAKxC,QAAiC;QAAhC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO;yBAC/B,8UAAC,iUAA2B;QAC1B,KAAK;QACL,WAAW,IAAA,8JAAE,EACX,qCACA,SAAS,QACT;QAED,GAAG,KAAK;;;;;;;;AAGb,kBAAkB,WAAW,GAAG,iUAA2B,CAAC,WAAW;AAEvE,MAAM,sCAAwB,4TAAgB,QAG5C,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,8UAAC,qUAA+B;QAC9B,KAAK;QACL,WAAW,IAAA,8JAAE,EAAC,4BAA4B;QACzC,GAAG,KAAK;;;;;;;;AAGb,sBAAsB,WAAW,GAAG,qUAA+B,CAAC,WAAW;AAE/E,MAAM,uBAAuB;QAAC,EAC5B,SAAS,EACT,GAAG,OACmC;IACtC,qBACE,8UAAC;QACC,WAAW,IAAA,8JAAE,EAAC,8CAA8C;QAC3D,GAAG,KAAK;;;;;;AAGf;OAVM;AAWN,qBAAqB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 484, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/components/ui/theme-toggle.tsx"], "sourcesContent": ["'use client'\n\nimport { <PERSON>, Sun, Monitor } from 'lucide-react'\nimport { Button } from './button'\nimport { useTheme } from '@/components/providers/theme-provider'\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuTrigger,\n} from './dropdown-menu'\n\nexport function ThemeToggle() {\n  const { theme, actualTheme, mounted, setTheme } = useTheme()\n\n  const toggleTheme = () => {\n    if (!mounted) return\n    const next = actualTheme === 'light' ? 'dark' : 'light'\n    setTheme(next)\n  }\n\n  return (\n    <DropdownMenu>\n      <DropdownMenuTrigger asChild>\n        <Button\n          variant=\"ghost\"\n          size=\"icon\"\n          className=\"h-9 w-9\"\n          onClick={toggleTheme}\n          title={`Switch to ${actualTheme === 'light' ? 'dark' : 'light'} mode`}\n        >\n          <Sun className=\"h-4 w-4 rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0\" />\n          <Moon className=\"absolute h-4 w-4 rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100\" />\n          <span className=\"sr-only\">Toggle theme</span>\n        </Button>\n      </DropdownMenuTrigger>\n      <DropdownMenuContent align=\"end\">\n        <DropdownMenuItem onClick={() => setTheme('light')}>\n          <Sun className=\"mr-2 h-4 w-4\" />\n          <span>Light</span>\n        </DropdownMenuItem>\n        <DropdownMenuItem onClick={() => setTheme('dark')}>\n          <Moon className=\"mr-2 h-4 w-4\" />\n          <span>Dark</span>\n        </DropdownMenuItem>\n        <DropdownMenuItem onClick={() => setTheme('system')}>\n          <Monitor className=\"mr-2 h-4 w-4\" />\n          <span>System</span>\n        </DropdownMenuItem>\n      </DropdownMenuContent>\n    </DropdownMenu>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAAA;AAAA;AACA;AACA;AACA;;;AALA;;;;;AAYO,SAAS;;IACd,MAAM,EAAE,KAAK,EAAE,WAAW,EAAE,OAAO,EAAE,QAAQ,EAAE,GAAG,IAAA,qMAAQ;IAE1D,MAAM,cAAc;QAClB,IAAI,CAAC,SAAS;QACd,MAAM,OAAO,gBAAgB,UAAU,SAAS;QAChD,SAAS;IACX;IAEA,qBACE,8UAAC,iMAAY;;0BACX,8UAAC,wMAAmB;gBAAC,OAAO;0BAC1B,cAAA,8UAAC,iLAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,WAAU;oBACV,SAAS;oBACT,OAAO,AAAC,aAAuD,OAA3C,gBAAgB,UAAU,SAAS,SAAQ;;sCAE/D,8UAAC,gUAAG;4BAAC,WAAU;;;;;;sCACf,8UAAC,mUAAI;4BAAC,WAAU;;;;;;sCAChB,8UAAC;4BAAK,WAAU;sCAAU;;;;;;;;;;;;;;;;;0BAG9B,8UAAC,wMAAmB;gBAAC,OAAM;;kCACzB,8UAAC,qMAAgB;wBAAC,SAAS,IAAM,SAAS;;0CACxC,8UAAC,gUAAG;gCAAC,WAAU;;;;;;0CACf,8UAAC;0CAAK;;;;;;;;;;;;kCAER,8UAAC,qMAAgB;wBAAC,SAAS,IAAM,SAAS;;0CACxC,8UAAC,mUAAI;gCAAC,WAAU;;;;;;0CAChB,8UAAC;0CAAK;;;;;;;;;;;;kCAER,8UAAC,qMAAgB;wBAAC,SAAS,IAAM,SAAS;;0CACxC,8UAAC,4UAAO;gCAAC,WAAU;;;;;;0CACnB,8UAAC;0CAAK;;;;;;;;;;;;;;;;;;;;;;;;AAKhB;GAxCgB;;QACoC,qMAAQ;;;KAD5C", "debugId": null}}, {"offset": {"line": 654, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/components/layout/dashboard-layout.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { useSession, signOut } from 'next-auth/react'\nimport { useRouter } from 'next/navigation'\nimport { Button } from '@/components/ui/button'\n\nimport { ThemeToggle } from '@/components/ui/theme-toggle'\nimport {\n  Menu,\n  X,\n  User,\n  LogOut,\n  Settings,\n  Bell,\n  Search,\n  School,\n  Plus,\n  Upload,\n  Download,\n  Users,\n  BookOpen,\n  GraduationCap,\n  FileText,\n  BarChart3,\n  Calendar,\n  Home,\n  Edit,\n  ClipboardList,\n  Award\n} from 'lucide-react'\n\ninterface DashboardLayoutProps {\n  children: React.ReactNode\n  title: string\n  navigation: {\n    name: string\n    href: string\n    icon: string\n  }[]\n}\n\n// Icon mapping object\nconst iconMap: Record<string, React.ComponentType<{ className?: string }>> = {\n  Plus,\n  Upload,\n  Download,\n  Users,\n  BookOpen,\n  GraduationCap,\n  FileText,\n  BarChart3,\n  Calendar,\n  Home,\n  Settings,\n  Bell,\n  User,\n  Edit,\n  ClipboardList,\n  Award\n}\n\nexport default function DashboardLayout({ children, title, navigation }: DashboardLayoutProps) {\n  const { data: session } = useSession()\n  const router = useRouter()\n  const [sidebarOpen, setSidebarOpen] = useState(false)\n\n  const handleSignOut = async () => {\n    await signOut({ callbackUrl: '/' })\n  }\n\n  const getIcon = (iconName: string) => {\n    const IconComponent = iconMap[iconName]\n    return IconComponent || Home // fallback to Home icon if not found\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 dark:bg-gray-950\">\n      {/* Mobile sidebar */}\n      <div className={`fixed inset-0 z-50 lg:hidden ${sidebarOpen ? 'block' : 'hidden'}`}>\n        <div className=\"fixed inset-0 bg-gray-600 bg-opacity-75\" onClick={() => setSidebarOpen(false)} />\n        <div className=\"fixed inset-y-0 left-0 flex w-64 flex-col bg-white dark:bg-gray-900\">\n          <div className=\"flex h-16 items-center justify-between px-4\">\n            <div className=\"flex items-center\">\n              <School className=\"h-8 w-8 text-blue-600\" />\n              <span className=\"ml-2 text-lg font-semibold\">SMS</span>\n            </div>\n            <Button\n              variant=\"ghost\"\n              size=\"sm\"\n              onClick={() => setSidebarOpen(false)}\n            >\n              <X className=\"h-5 w-5\" />\n            </Button>\n          </div>\n          <nav className=\"flex-1 space-y-1 px-2 py-4\">\n            {navigation.map((item) => {\n              const IconComponent = getIcon(item.icon)\n              return (\n                <Button\n                  key={`mobile-${item.name}`}\n                  variant=\"ghost\"\n                  className=\"w-full justify-start\"\n                  onClick={() => {\n                    router.push(item.href)\n                    setSidebarOpen(false)\n                  }}\n                >\n                  <IconComponent className=\"mr-3 h-5 w-5\" />\n                  {item.name}\n                </Button>\n              )\n            })}\n          </nav>\n        </div>\n      </div>\n\n      {/* Desktop sidebar */}\n      <div className=\"hidden lg:fixed lg:inset-y-0 lg:flex lg:w-64 lg:flex-col\">\n        <div className=\"flex flex-col flex-grow bg-white dark:bg-gray-900 border-r border-gray-200 dark:border-gray-800\">\n          <div className=\"flex h-16 items-center px-4\">\n            <School className=\"h-8 w-8 text-blue-600\" />\n            <span className=\"ml-2 text-lg font-semibold hidden xl:inline\">School Management System</span>\n            <span className=\"ml-2 text-lg font-semibold xl:hidden\">SMS</span>\n          </div>\n          <nav className=\"flex-1 space-y-1 px-2 py-4\">\n            {navigation.map((item) => {\n              const IconComponent = getIcon(item.icon)\n              return (\n                <Button\n                  key={`desktop-${item.name}`}\n                  variant=\"ghost\"\n                  className=\"w-full justify-start\"\n                  onClick={() => router.push(item.href)}\n                >\n                  <IconComponent className=\"mr-3 h-5 w-5\" />\n                  {item.name}\n                </Button>\n              )\n            })}\n          </nav>\n        </div>\n      </div>\n\n      {/* Main content */}\n      <div className=\"lg:pl-64\">\n        {/* Top bar */}\n        <div className=\"sticky top-0 z-40 flex h-16 shrink-0 items-center gap-x-4 border-b border-gray-200 dark:border-gray-800 bg-white dark:bg-gray-900 px-4 shadow-sm sm:gap-x-6 sm:px-6 lg:px-8\">\n          <Button\n            variant=\"ghost\"\n            size=\"sm\"\n            className=\"lg:hidden\"\n            onClick={() => setSidebarOpen(true)}\n          >\n            <Menu className=\"h-5 w-5\" />\n          </Button>\n\n          <div className=\"flex flex-1 gap-x-4 self-stretch lg:gap-x-6\">\n            <div className=\"relative flex flex-1\">\n              <div className=\"pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3\">\n                <Search className=\"h-5 w-5 text-gray-400\" />\n              </div>\n              <input\n                type=\"text\"\n                placeholder=\"Search...\"\n                className=\"block h-full w-full border-0 py-0 pl-10 pr-0 text-gray-900 dark:text-gray-100 placeholder:text-gray-400 dark:placeholder:text-gray-500 focus:ring-0 sm:text-sm bg-transparent\"\n              />\n            </div>\n          </div>\n\n          <div className=\"flex items-center gap-x-4 lg:gap-x-6\">\n            <ThemeToggle />\n\n            <Button variant=\"ghost\" size=\"sm\">\n              <Bell className=\"h-5 w-5\" />\n            </Button>\n\n            <div className=\"relative\">\n              <div className=\"flex items-center gap-x-3\">\n                <div className=\"text-sm hidden sm:block\">\n                  <p className=\"font-medium text-gray-900 dark:text-gray-100\">\n                    {session?.user?.firstName} {session?.user?.lastName}\n                  </p>\n                  <p className=\"text-gray-500 dark:text-gray-400 capitalize\">\n                    {session?.user?.role?.toLowerCase()}\n                  </p>\n                </div>\n                <div className=\"flex items-center gap-x-2\">\n                  <Button\n                    variant=\"ghost\"\n                    size=\"sm\"\n                    onClick={handleSignOut}\n                    className=\"flex items-center gap-2\"\n                  >\n                    <LogOut className=\"h-4 w-4\" />\n                    <span className=\"hidden sm:inline\">Sign Out</span>\n                  </Button>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Page content */}\n        <main className=\"py-6\">\n          <div className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\n            <div className=\"mb-6\">\n              <h1 className=\"text-2xl font-bold text-gray-900 dark:text-gray-100\">{title}</h1>\n            </div>\n            {children}\n          </div>\n        </main>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AARA;;;;;;;AA0CA,sBAAsB;AACtB,MAAM,UAAuE;IAC3E,MAAA,mUAAI;IACJ,QAAA,yUAAM;IACN,UAAA,+UAAQ;IACR,OAAA,sUAAK;IACL,UAAA,mVAAQ;IACR,eAAA,kWAAa;IACb,UAAA,mVAAQ;IACR,WAAA,wVAAS;IACT,UAAA,+UAAQ;IACR,MAAA,oUAAI;IACJ,UAAA,+UAAQ;IACR,MAAA,mUAAI;IACJ,MAAA,mUAAI;IACJ,MAAA,4UAAI;IACJ,eAAA,kWAAa;IACb,OAAA,sUAAK;AACP;AAEe,SAAS,gBAAgB,KAAqD;QAArD,EAAE,QAAQ,EAAE,KAAK,EAAE,UAAU,EAAwB,GAArD;QAuHnB,eAA2B,gBAG3B,oBAAA;;IAzHnB,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,IAAA,6SAAU;IACpC,MAAM,SAAS,IAAA,mSAAS;IACxB,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,0TAAQ,EAAC;IAE/C,MAAM,gBAAgB;QACpB,MAAM,IAAA,0SAAO,EAAC;YAAE,aAAa;QAAI;IACnC;IAEA,MAAM,UAAU,CAAC;QACf,MAAM,gBAAgB,OAAO,CAAC,SAAS;QACvC,OAAO,iBAAiB,oUAAI,CAAC,qCAAqC;;IACpE;IAEA,qBACE,8UAAC;QAAI,WAAU;;0BAEb,8UAAC;gBAAI,WAAW,AAAC,gCAAgE,OAAjC,cAAc,UAAU;;kCACtE,8UAAC;wBAAI,WAAU;wBAA0C,SAAS,IAAM,eAAe;;;;;;kCACvF,8UAAC;wBAAI,WAAU;;0CACb,8UAAC;gCAAI,WAAU;;kDACb,8UAAC;wCAAI,WAAU;;0DACb,8UAAC,yUAAM;gDAAC,WAAU;;;;;;0DAClB,8UAAC;gDAAK,WAAU;0DAA6B;;;;;;;;;;;;kDAE/C,8UAAC,iLAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,eAAe;kDAE9B,cAAA,8UAAC,0TAAC;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAGjB,8UAAC;gCAAI,WAAU;0CACZ,WAAW,GAAG,CAAC,CAAC;oCACf,MAAM,gBAAgB,QAAQ,KAAK,IAAI;oCACvC,qBACE,8UAAC,iLAAM;wCAEL,SAAQ;wCACR,WAAU;wCACV,SAAS;4CACP,OAAO,IAAI,CAAC,KAAK,IAAI;4CACrB,eAAe;wCACjB;;0DAEA,8UAAC;gDAAc,WAAU;;;;;;4CACxB,KAAK,IAAI;;uCATL,AAAC,UAAmB,OAAV,KAAK,IAAI;;;;;gCAY9B;;;;;;;;;;;;;;;;;;0BAMN,8UAAC;gBAAI,WAAU;0BACb,cAAA,8UAAC;oBAAI,WAAU;;sCACb,8UAAC;4BAAI,WAAU;;8CACb,8UAAC,yUAAM;oCAAC,WAAU;;;;;;8CAClB,8UAAC;oCAAK,WAAU;8CAA8C;;;;;;8CAC9D,8UAAC;oCAAK,WAAU;8CAAuC;;;;;;;;;;;;sCAEzD,8UAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAC;gCACf,MAAM,gBAAgB,QAAQ,KAAK,IAAI;gCACvC,qBACE,8UAAC,iLAAM;oCAEL,SAAQ;oCACR,WAAU;oCACV,SAAS,IAAM,OAAO,IAAI,CAAC,KAAK,IAAI;;sDAEpC,8UAAC;4CAAc,WAAU;;;;;;wCACxB,KAAK,IAAI;;mCANL,AAAC,WAAoB,OAAV,KAAK,IAAI;;;;;4BAS/B;;;;;;;;;;;;;;;;;0BAMN,8UAAC;gBAAI,WAAU;;kCAEb,8UAAC;wBAAI,WAAU;;0CACb,8UAAC,iLAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,eAAe;0CAE9B,cAAA,8UAAC,mUAAI;oCAAC,WAAU;;;;;;;;;;;0CAGlB,8UAAC;gCAAI,WAAU;0CACb,cAAA,8UAAC;oCAAI,WAAU;;sDACb,8UAAC;4CAAI,WAAU;sDACb,cAAA,8UAAC,yUAAM;gDAAC,WAAU;;;;;;;;;;;sDAEpB,8UAAC;4CACC,MAAK;4CACL,aAAY;4CACZ,WAAU;;;;;;;;;;;;;;;;;0CAKhB,8UAAC;gCAAI,WAAU;;kDACb,8UAAC,+LAAW;;;;;kDAEZ,8UAAC,iLAAM;wCAAC,SAAQ;wCAAQ,MAAK;kDAC3B,cAAA,8UAAC,mUAAI;4CAAC,WAAU;;;;;;;;;;;kDAGlB,8UAAC;wCAAI,WAAU;kDACb,cAAA,8UAAC;4CAAI,WAAU;;8DACb,8UAAC;oDAAI,WAAU;;sEACb,8UAAC;4DAAE,WAAU;;gEACV,oBAAA,+BAAA,gBAAA,QAAS,IAAI,cAAb,oCAAA,cAAe,SAAS;gEAAC;gEAAE,oBAAA,+BAAA,iBAAA,QAAS,IAAI,cAAb,qCAAA,eAAe,QAAQ;;;;;;;sEAErD,8UAAC;4DAAE,WAAU;sEACV,oBAAA,+BAAA,iBAAA,QAAS,IAAI,cAAb,sCAAA,qBAAA,eAAe,IAAI,cAAnB,yCAAA,mBAAqB,WAAW;;;;;;;;;;;;8DAGrC,8UAAC;oDAAI,WAAU;8DACb,cAAA,8UAAC,iLAAM;wDACL,SAAQ;wDACR,MAAK;wDACL,SAAS;wDACT,WAAU;;0EAEV,8UAAC,6UAAM;gEAAC,WAAU;;;;;;0EAClB,8UAAC;gEAAK,WAAU;0EAAmB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAS/C,8UAAC;wBAAK,WAAU;kCACd,cAAA,8UAAC;4BAAI,WAAU;;8CACb,8UAAC;oCAAI,WAAU;8CACb,cAAA,8UAAC;wCAAG,WAAU;kDAAuD;;;;;;;;;;;gCAEtE;;;;;;;;;;;;;;;;;;;;;;;;AAMb;GAzJwB;;QACI,6SAAU;QACrB,mSAAS;;;KAFF", "debugId": null}}, {"offset": {"line": 1154, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/lib/navigation.ts"], "sourcesContent": ["// Shared navigation configurations for different user roles\n\nexport const adminNavigation = [\n  { name: 'Dashboard', href: '/admin', icon: 'BarChart3' },\n  { name: 'Students', href: '/admin/students', icon: 'Users' },\n  { name: 'Teachers', href: '/admin/teachers', icon: 'GraduationCap' },\n  { name: 'Classes & Sections', href: '/admin/classes', icon: 'BookOpen' },\n  { name: 'Subjects', href: '/admin/subjects', icon: 'FileText' },\n  { name: 'Terms & Exams', href: '/admin/exams', icon: 'Calendar' },\n  { name: 'Attendance', href: '/admin/attendance', icon: 'ClipboardList' },\n  { name: 'Marks', href: '/admin/marks', icon: 'Award' },\n  { name: 'Reports', href: '/admin/reports', icon: 'Download' },\n  { name: 'Settings', href: '/admin/settings', icon: 'Settings' },\n];\n\nexport const teacherNavigation = [\n  { name: 'Dashboard', href: '/teacher', icon: 'BarChart3' },\n  { name: 'My Classes', href: '/teacher/classes', icon: 'BookOpen' },\n  { name: 'Attendance', href: '/teacher/attendance', icon: 'ClipboardList' },\n  { name: 'Marks', href: '/teacher/marks', icon: 'Award' },\n  { name: 'Students', href: '/teacher/students', icon: 'Users' },\n  { name: 'Reports', href: '/teacher/reports', icon: 'FileText' },\n  { name: 'Profile', href: '/teacher/profile', icon: 'User' },\n];\n\nexport const studentNavigation = [\n  { name: 'Dashboard', href: '/student', icon: 'BarChart3' },\n  { name: 'My Classes', href: '/student/classes', icon: 'BookOpen' },\n  { name: 'Attendance', href: '/student/attendance', icon: 'ClipboardList' },\n  { name: 'Marks', href: '/student/marks', icon: 'Award' },\n  { name: 'Reports', href: '/student/reports', icon: 'FileText' },\n  { name: 'Profile', href: '/student/profile', icon: 'User' },\n];\n\n/**\n * Get the default dashboard URL for a user role\n */\nexport function getRoleDashboardUrl(role: string): string {\n  switch (role) {\n    case 'ADMIN':\n      return '/admin'\n    case 'TEACHER':\n      return '/teacher'\n    case 'STUDENT':\n      return '/student'\n    default:\n      return '/'\n  }\n}\n\n/**\n * Get navigation items for a user role\n */\nexport function getRoleNavigation(role: string) {\n  switch (role) {\n    case 'ADMIN':\n      return adminNavigation\n    case 'TEACHER':\n      return teacherNavigation\n    case 'STUDENT':\n      return studentNavigation\n    default:\n      return []\n  }\n}"], "names": [], "mappings": "AAAA,4DAA4D;;;;;;;;;;;;;AAErD,MAAM,kBAAkB;IAC7B;QAAE,MAAM;QAAa,MAAM;QAAU,MAAM;IAAY;IACvD;QAAE,MAAM;QAAY,MAAM;QAAmB,MAAM;IAAQ;IAC3D;QAAE,MAAM;QAAY,MAAM;QAAmB,MAAM;IAAgB;IACnE;QAAE,MAAM;QAAsB,MAAM;QAAkB,MAAM;IAAW;IACvE;QAAE,MAAM;QAAY,MAAM;QAAmB,MAAM;IAAW;IAC9D;QAAE,MAAM;QAAiB,MAAM;QAAgB,MAAM;IAAW;IAChE;QAAE,MAAM;QAAc,MAAM;QAAqB,MAAM;IAAgB;IACvE;QAAE,MAAM;QAAS,MAAM;QAAgB,MAAM;IAAQ;IACrD;QAAE,MAAM;QAAW,MAAM;QAAkB,MAAM;IAAW;IAC5D;QAAE,MAAM;QAAY,MAAM;QAAmB,MAAM;IAAW;CAC/D;AAEM,MAAM,oBAAoB;IAC/B;QAAE,MAAM;QAAa,MAAM;QAAY,MAAM;IAAY;IACzD;QAAE,MAAM;QAAc,MAAM;QAAoB,MAAM;IAAW;IACjE;QAAE,MAAM;QAAc,MAAM;QAAuB,MAAM;IAAgB;IACzE;QAAE,MAAM;QAAS,MAAM;QAAkB,MAAM;IAAQ;IACvD;QAAE,MAAM;QAAY,MAAM;QAAqB,MAAM;IAAQ;IAC7D;QAAE,MAAM;QAAW,MAAM;QAAoB,MAAM;IAAW;IAC9D;QAAE,MAAM;QAAW,MAAM;QAAoB,MAAM;IAAO;CAC3D;AAEM,MAAM,oBAAoB;IAC/B;QAAE,MAAM;QAAa,MAAM;QAAY,MAAM;IAAY;IACzD;QAAE,MAAM;QAAc,MAAM;QAAoB,MAAM;IAAW;IACjE;QAAE,MAAM;QAAc,MAAM;QAAuB,MAAM;IAAgB;IACzE;QAAE,MAAM;QAAS,MAAM;QAAkB,MAAM;IAAQ;IACvD;QAAE,MAAM;QAAW,MAAM;QAAoB,MAAM;IAAW;IAC9D;QAAE,MAAM;QAAW,MAAM;QAAoB,MAAM;IAAO;CAC3D;AAKM,SAAS,oBAAoB,IAAY;IAC9C,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF;AAKO,SAAS,kBAAkB,IAAY;IAC5C,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO,EAAE;IACb;AACF", "debugId": null}}, {"offset": {"line": 1319, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/app/%28dash%29/admin/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useSession } from 'next-auth/react'\nimport { useRouter } from 'next/navigation'\nimport { useEffect, useState } from 'react'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport DashboardLayout from '@/components/layout/dashboard-layout'\nimport { adminNavigation } from '@/lib/navigation'\nimport {\n  Users,\n  GraduationCap,\n  BookOpen,\n  FileText,\n  Calendar,\n  BarChart3,\n  Settings,\n  UserPlus,\n  ClipboardList,\n  Award,\n  Loader2\n} from 'lucide-react'\n\ninterface DashboardStats {\n  totalStudents: number\n  totalTeachers: number\n  totalClasses: number\n  totalSubjects: number\n  activeStudents: number\n  activeTeachers: number\n  attendanceRate: number\n  averageMarks: number\n  totalMarksRecords: number\n  totalAttendanceRecords: number\n}\n\nexport default function AdminDashboard() {\n  const { data: session } = useSession()\n  const router = useRouter()\n  const [stats, setStats] = useState<DashboardStats | null>(null)\n  const [loading, setLoading] = useState(true)\n  const [error, setError] = useState<string | null>(null)\n\n  useEffect(() => {\n    const fetchStats = async () => {\n      try {\n        const response = await fetch('/api/admin/dashboard/stats')\n        if (!response.ok) {\n          throw new Error('Failed to fetch dashboard statistics')\n        }\n        const data = await response.json()\n        setStats(data)\n      } catch (err) {\n        setError(err instanceof Error ? err.message : 'An error occurred')\n        console.error('Error fetching dashboard stats:', err)\n      } finally {\n        setLoading(false)\n      }\n    }\n\n    if (session?.user?.role === 'ADMIN') {\n      fetchStats()\n    }\n  }, [session])\n\n  const quickActions = [\n    {\n      title: 'Add New Student',\n      description: 'Register a new student',\n      icon: UserPlus,\n      href: '/admin/students/new',\n      color: 'bg-blue-500'\n    },\n    {\n      title: 'Add New Teacher',\n      description: 'Register a new teacher',\n      icon: GraduationCap,\n      href: '/admin/teachers/new',\n      color: 'bg-indigo-500'\n    },\n    {\n      title: 'Generate Reports',\n      description: 'Create term reports',\n      icon: FileText,\n      href: '/admin/reports',\n      color: 'bg-green-500'\n    },\n    {\n      title: 'View Attendance',\n      description: 'Check daily attendance',\n      icon: ClipboardList,\n      href: '/admin/attendance',\n      color: 'bg-purple-500'\n    },\n    {\n      title: 'Manage Marks',\n      description: 'Enter and review marks',\n      icon: Award,\n      href: '/admin/marks',\n      color: 'bg-orange-500'\n    }\n  ]\n\n  return (\n    <DashboardLayout title=\"Admin Dashboard\" navigation={adminNavigation}>\n      <div className=\"space-y-6\">\n        {/* Welcome Section */}\n        <Card>\n          <CardContent className=\"p-6\">\n            <h2 className=\"text-xl font-semibold text-gray-900 dark:text-gray-100 mb-2\">\n              Welcome back, {session?.user?.firstName || 'Admin'}!\n            </h2>\n            <p className=\"text-gray-600 dark:text-gray-400\">\n              Here&apos;s an overview of your school management system.\n            </p>\n          </CardContent>\n        </Card>\n\n        {/* Loading State */}\n        {loading && (\n          <div className=\"flex items-center justify-center py-8\">\n            <Loader2 className=\"h-8 w-8 animate-spin\" />\n            <span className=\"ml-2\">Loading dashboard statistics...</span>\n          </div>\n        )}\n\n        {/* Error State */}\n        {error && (\n          <div className=\"bg-red-50 border border-red-200 rounded-lg p-4\">\n            <p className=\"text-red-800\">Error loading dashboard: {error}</p>\n          </div>\n        )}\n\n        {/* Stats Cards */}\n        {stats && (\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n            <Card key=\"total-students\">\n              <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n                <CardTitle className=\"text-sm font-medium\">Total Students</CardTitle>\n                <Users className=\"h-4 w-4 text-muted-foreground\" />\n              </CardHeader>\n              <CardContent>\n                <div className=\"text-2xl font-bold\">{stats.totalStudents}</div>\n                <p className=\"text-xs text-muted-foreground\">\n                  {stats.activeStudents} active in last 30 days\n                </p>\n              </CardContent>\n            </Card>\n\n          <Card key=\"total-teachers\">\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n              <CardTitle className=\"text-sm font-medium\">Total Teachers</CardTitle>\n              <GraduationCap className=\"h-4 w-4 text-muted-foreground\" />\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-2xl font-bold\">{stats.totalTeachers}</div>\n              <p className=\"text-xs text-muted-foreground\">\n                {stats.activeTeachers} active in last 30 days\n              </p>\n            </CardContent>\n          </Card>\n\n          <Card key=\"total-classes\">\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n              <CardTitle className=\"text-sm font-medium\">Total Classes</CardTitle>\n              <BookOpen className=\"h-4 w-4 text-muted-foreground\" />\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-2xl font-bold\">{stats.totalClasses}</div>\n              <p className=\"text-xs text-muted-foreground\">\n                Across all grades\n              </p>\n            </CardContent>\n          </Card>\n\n          <Card key=\"attendance-rate\">\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n              <CardTitle className=\"text-sm font-medium\">Attendance Rate</CardTitle>\n              <BarChart3 className=\"h-4 w-4 text-muted-foreground\" />\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-2xl font-bold\">{stats.attendanceRate}%</div>\n              <p className=\"text-xs text-muted-foreground\">\n                {stats.totalAttendanceRecords} records (last 30 days)\n              </p>\n            </CardContent>\n          </Card>\n\n          <Card key=\"average-marks\">\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n              <CardTitle className=\"text-sm font-medium\">Average Marks</CardTitle>\n              <Award className=\"h-4 w-4 text-muted-foreground\" />\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-2xl font-bold\">{stats.averageMarks}%</div>\n              <p className=\"text-xs text-muted-foreground\">\n                {stats.totalMarksRecords} marks recorded\n              </p>\n            </CardContent>\n          </Card>\n\n          <Card key=\"total-subjects\">\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n              <CardTitle className=\"text-sm font-medium\">Total Subjects</CardTitle>\n              <FileText className=\"h-4 w-4 text-muted-foreground\" />\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-2xl font-bold\">{stats.totalSubjects}</div>\n              <p className=\"text-xs text-muted-foreground\">\n                Active curriculum\n              </p>\n            </CardContent>\n          </Card>\n        </div>\n        )}\n\n        {/* Quick Actions */}\n        <Card>\n          <CardContent className=\"p-6\">\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4\">Quick Actions</h3>\n            <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-4\">\n              {quickActions.map((action) => (\n                <Card \n                  key={action.title} \n                  className=\"cursor-pointer hover:shadow-md transition-shadow\"\n                  onClick={() => router.push(action.href)}\n                >\n                  <CardContent className=\"p-4\">\n                    <div className=\"flex flex-col items-center text-center space-y-3 sm:flex-row sm:items-center sm:text-left sm:space-y-0 sm:space-x-3\">\n                      <div className={`p-2 rounded-lg ${action.color} flex-shrink-0`}>\n                        <action.icon className=\"h-5 w-5 text-white\" />\n                      </div>\n                      <div className=\"min-w-0 flex-1\">\n                        <h4 className=\"font-medium text-sm text-gray-900 dark:text-gray-100 truncate\">{action.title}</h4>\n                        <p className=\"text-xs text-gray-500 dark:text-gray-400 truncate\">{action.description}</p>\n                      </div>\n                    </div>\n                  </CardContent>\n                </Card>\n              ))}\n            </div>\n          </CardContent>\n        </Card>\n\n        {/* Recent Activity */}\n        <Card>\n          <CardContent className=\"p-6\">\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4\">Recent Activity</h3>\n            <div className=\"space-y-3\">\n              <div className=\"flex items-center space-x-3 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg\">\n                <div className=\"w-2 h-2 bg-green-500 rounded-full\"></div>\n                <div className=\"flex-1\">\n                  <p className=\"text-sm font-medium text-gray-900 dark:text-gray-100\">New student registered</p>\n                  <p className=\"text-xs text-gray-500 dark:text-gray-400\">John Doe - Grade 8A</p>\n                </div>\n                <span className=\"text-xs text-gray-500 dark:text-gray-400\">2 hours ago</span>\n              </div>\n              <div className=\"flex items-center space-x-3 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg\">\n                <div className=\"w-2 h-2 bg-blue-500 rounded-full\"></div>\n                <div className=\"flex-1\">\n                  <p className=\"text-sm font-medium text-gray-900 dark:text-gray-100\">Attendance marked</p>\n                  <p className=\"text-xs text-gray-500 dark:text-gray-400\">Grade 8A - 95% present</p>\n                </div>\n                <span className=\"text-xs text-gray-500 dark:text-gray-400\">4 hours ago</span>\n              </div>\n              <div className=\"flex items-center space-x-3 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg\">\n                <div className=\"w-2 h-2 bg-purple-500 rounded-full\"></div>\n                <div className=\"flex-1\">\n                  <p className=\"text-sm font-medium text-gray-900 dark:text-gray-100\">Marks uploaded</p>\n                  <p className=\"text-xs text-gray-500 dark:text-gray-400\">Mathematics - Unit Test 1</p>\n                </div>\n                <span className=\"text-xs text-gray-500 dark:text-gray-400\">1 day ago</span>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n      </div>\n    </DashboardLayout>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AARA;;;;;;;;AAmCe,SAAS;QA0EK;;IAzE3B,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,IAAA,6SAAU;IACpC,MAAM,SAAS,IAAA,mSAAS;IACxB,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,0TAAQ,EAAwB;IAC1D,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,0TAAQ,EAAC;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,0TAAQ,EAAgB;IAElD,IAAA,2TAAS;oCAAC;gBAiBJ;YAhBJ,MAAM;uDAAa;oBACjB,IAAI;wBACF,MAAM,WAAW,MAAM,MAAM;wBAC7B,IAAI,CAAC,SAAS,EAAE,EAAE;4BAChB,MAAM,IAAI,MAAM;wBAClB;wBACA,MAAM,OAAO,MAAM,SAAS,IAAI;wBAChC,SAAS;oBACX,EAAE,OAAO,KAAK;wBACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;wBAC9C,QAAQ,KAAK,CAAC,mCAAmC;oBACnD,SAAU;wBACR,WAAW;oBACb;gBACF;;YAEA,IAAI,CAAA,oBAAA,+BAAA,gBAAA,QAAS,IAAI,cAAb,oCAAA,cAAe,IAAI,MAAK,SAAS;gBACnC;YACF;QACF;mCAAG;QAAC;KAAQ;IAEZ,MAAM,eAAe;QACnB;YACE,OAAO;YACP,aAAa;YACb,MAAM,mVAAQ;YACd,MAAM;YACN,OAAO;QACT;QACA;YACE,OAAO;YACP,aAAa;YACb,MAAM,kWAAa;YACnB,MAAM;YACN,OAAO;QACT;QACA;YACE,OAAO;YACP,aAAa;YACb,MAAM,mVAAQ;YACd,MAAM;YACN,OAAO;QACT;QACA;YACE,OAAO;YACP,aAAa;YACb,MAAM,kWAAa;YACnB,MAAM;YACN,OAAO;QACT;QACA;YACE,OAAO;YACP,aAAa;YACb,MAAM,sUAAK;YACX,MAAM;YACN,OAAO;QACT;KACD;IAED,qBACE,8UAAC,mMAAe;QAAC,OAAM;QAAkB,YAAY,gLAAe;kBAClE,cAAA,8UAAC;YAAI,WAAU;;8BAEb,8UAAC,6KAAI;8BACH,cAAA,8UAAC,oLAAW;wBAAC,WAAU;;0CACrB,8UAAC;gCAAG,WAAU;;oCAA8D;oCAC3D,CAAA,oBAAA,+BAAA,gBAAA,QAAS,IAAI,cAAb,oCAAA,cAAe,SAAS,KAAI;oCAAQ;;;;;;;0CAErD,8UAAC;gCAAE,WAAU;0CAAmC;;;;;;;;;;;;;;;;;gBAOnD,yBACC,8UAAC;oBAAI,WAAU;;sCACb,8UAAC,qVAAO;4BAAC,WAAU;;;;;;sCACnB,8UAAC;4BAAK,WAAU;sCAAO;;;;;;;;;;;;gBAK1B,uBACC,8UAAC;oBAAI,WAAU;8BACb,cAAA,8UAAC;wBAAE,WAAU;;4BAAe;4BAA0B;;;;;;;;;;;;gBAKzD,uBACC,8UAAC;oBAAI,WAAU;;sCACb,8UAAC,6KAAI;;8CACH,8UAAC,mLAAU;oCAAC,WAAU;;sDACpB,8UAAC,kLAAS;4CAAC,WAAU;sDAAsB;;;;;;sDAC3C,8UAAC,sUAAK;4CAAC,WAAU;;;;;;;;;;;;8CAEnB,8UAAC,oLAAW;;sDACV,8UAAC;4CAAI,WAAU;sDAAsB,MAAM,aAAa;;;;;;sDACxD,8UAAC;4CAAE,WAAU;;gDACV,MAAM,cAAc;gDAAC;;;;;;;;;;;;;;2BARlB;;;;;sCAaZ,8UAAC,6KAAI;;8CACH,8UAAC,mLAAU;oCAAC,WAAU;;sDACpB,8UAAC,kLAAS;4CAAC,WAAU;sDAAsB;;;;;;sDAC3C,8UAAC,kWAAa;4CAAC,WAAU;;;;;;;;;;;;8CAE3B,8UAAC,oLAAW;;sDACV,8UAAC;4CAAI,WAAU;sDAAsB,MAAM,aAAa;;;;;;sDACxD,8UAAC;4CAAE,WAAU;;gDACV,MAAM,cAAc;gDAAC;;;;;;;;;;;;;;2BARlB;;;;;sCAaV,8UAAC,6KAAI;;8CACH,8UAAC,mLAAU;oCAAC,WAAU;;sDACpB,8UAAC,kLAAS;4CAAC,WAAU;sDAAsB;;;;;;sDAC3C,8UAAC,mVAAQ;4CAAC,WAAU;;;;;;;;;;;;8CAEtB,8UAAC,oLAAW;;sDACV,8UAAC;4CAAI,WAAU;sDAAsB,MAAM,YAAY;;;;;;sDACvD,8UAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;;;2BAPvC;;;;;sCAaV,8UAAC,6KAAI;;8CACH,8UAAC,mLAAU;oCAAC,WAAU;;sDACpB,8UAAC,kLAAS;4CAAC,WAAU;sDAAsB;;;;;;sDAC3C,8UAAC,wVAAS;4CAAC,WAAU;;;;;;;;;;;;8CAEvB,8UAAC,oLAAW;;sDACV,8UAAC;4CAAI,WAAU;;gDAAsB,MAAM,cAAc;gDAAC;;;;;;;sDAC1D,8UAAC;4CAAE,WAAU;;gDACV,MAAM,sBAAsB;gDAAC;;;;;;;;;;;;;;2BAR1B;;;;;sCAaV,8UAAC,6KAAI;;8CACH,8UAAC,mLAAU;oCAAC,WAAU;;sDACpB,8UAAC,kLAAS;4CAAC,WAAU;sDAAsB;;;;;;sDAC3C,8UAAC,sUAAK;4CAAC,WAAU;;;;;;;;;;;;8CAEnB,8UAAC,oLAAW;;sDACV,8UAAC;4CAAI,WAAU;;gDAAsB,MAAM,YAAY;gDAAC;;;;;;;sDACxD,8UAAC;4CAAE,WAAU;;gDACV,MAAM,iBAAiB;gDAAC;;;;;;;;;;;;;;2BARrB;;;;;sCAaV,8UAAC,6KAAI;;8CACH,8UAAC,mLAAU;oCAAC,WAAU;;sDACpB,8UAAC,kLAAS;4CAAC,WAAU;sDAAsB;;;;;;sDAC3C,8UAAC,mVAAQ;4CAAC,WAAU;;;;;;;;;;;;8CAEtB,8UAAC,oLAAW;;sDACV,8UAAC;4CAAI,WAAU;sDAAsB,MAAM,aAAa;;;;;;sDACxD,8UAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;;;2BAPvC;;;;;;;;;;;8BAgBZ,8UAAC,6KAAI;8BACH,cAAA,8UAAC,oLAAW;wBAAC,WAAU;;0CACrB,8UAAC;gCAAG,WAAU;0CAA8D;;;;;;0CAC5E,8UAAC;gCAAI,WAAU;0CACZ,aAAa,GAAG,CAAC,CAAC,uBACjB,8UAAC,6KAAI;wCAEH,WAAU;wCACV,SAAS,IAAM,OAAO,IAAI,CAAC,OAAO,IAAI;kDAEtC,cAAA,8UAAC,oLAAW;4CAAC,WAAU;sDACrB,cAAA,8UAAC;gDAAI,WAAU;;kEACb,8UAAC;wDAAI,WAAW,AAAC,kBAA8B,OAAb,OAAO,KAAK,EAAC;kEAC7C,cAAA,8UAAC,OAAO,IAAI;4DAAC,WAAU;;;;;;;;;;;kEAEzB,8UAAC;wDAAI,WAAU;;0EACb,8UAAC;gEAAG,WAAU;0EAAiE,OAAO,KAAK;;;;;;0EAC3F,8UAAC;gEAAE,WAAU;0EAAqD,OAAO,WAAW;;;;;;;;;;;;;;;;;;;;;;;uCAXrF,OAAO,KAAK;;;;;;;;;;;;;;;;;;;;;8BAsB3B,8UAAC,6KAAI;8BACH,cAAA,8UAAC,oLAAW;wBAAC,WAAU;;0CACrB,8UAAC;gCAAG,WAAU;0CAA8D;;;;;;0CAC5E,8UAAC;gCAAI,WAAU;;kDACb,8UAAC;wCAAI,WAAU;;0DACb,8UAAC;gDAAI,WAAU;;;;;;0DACf,8UAAC;gDAAI,WAAU;;kEACb,8UAAC;wDAAE,WAAU;kEAAuD;;;;;;kEACpE,8UAAC;wDAAE,WAAU;kEAA2C;;;;;;;;;;;;0DAE1D,8UAAC;gDAAK,WAAU;0DAA2C;;;;;;;;;;;;kDAE7D,8UAAC;wCAAI,WAAU;;0DACb,8UAAC;gDAAI,WAAU;;;;;;0DACf,8UAAC;gDAAI,WAAU;;kEACb,8UAAC;wDAAE,WAAU;kEAAuD;;;;;;kEACpE,8UAAC;wDAAE,WAAU;kEAA2C;;;;;;;;;;;;0DAE1D,8UAAC;gDAAK,WAAU;0DAA2C;;;;;;;;;;;;kDAE7D,8UAAC;wCAAI,WAAU;;0DACb,8UAAC;gDAAI,WAAU;;;;;;0DACf,8UAAC;gDAAI,WAAU;;kEACb,8UAAC;wDAAE,WAAU;kEAAuD;;;;;;kEACpE,8UAAC;wDAAE,WAAU;kEAA2C;;;;;;;;;;;;0DAE1D,8UAAC;gDAAK,WAAU;0DAA2C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ3E;GAnPwB;;QACI,6SAAU;QACrB,mSAAS;;;KAFF", "debugId": null}}]}