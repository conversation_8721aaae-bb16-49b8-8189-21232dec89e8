module.exports=[36870,a=>{"use strict";a.s(["Primitive",()=>f,"dispatchDiscreteCustomEvent",()=>g]);var b=a.i(54159),c=a.i(81453),d=a.i(54472),e=a.i(41825),f=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((a,c)=>{let f=(0,d.createSlot)(`Primitive.${c}`),g=b.forwardRef((a,b)=>{let{asChild:d,...g}=a;return(0,e.jsx)(d?f:c,{...g,ref:b})});return g.displayName=`Primitive.${c}`,{...a,[c]:g}},{});function g(a,b){a&&c.flushSync(()=>a.dispatchEvent(b))}},56704,(a,b,c)=>{b.exports=a.x("next/dist/server/app-render/work-async-storage.external.js",()=>require("next/dist/server/app-render/work-async-storage.external.js"))},32319,(a,b,c)=>{b.exports=a.x("next/dist/server/app-render/work-unit-async-storage.external.js",()=>require("next/dist/server/app-render/work-unit-async-storage.external.js"))},20635,(a,b,c)=>{b.exports=a.x("next/dist/server/app-render/action-async-storage.external.js",()=>require("next/dist/server/app-render/action-async-storage.external.js"))},76449,(a,b,c)=>{"use strict";b.exports=a.r(59230).vendored.contexts.AppRouterContext},72108,(a,b,c)=>{"use strict";b.exports=a.r(59230).vendored.contexts.HooksClientContext},2580,(a,b,c)=>{"use strict";b.exports=a.r(59230).vendored.contexts.ServerInsertedHtml},81453,(a,b,c)=>{"use strict";b.exports=a.r(59230).vendored["react-ssr"].ReactDOM},4082,a=>{"use strict";a.s(["Card",()=>e,"CardContent",()=>i,"CardDescription",()=>h,"CardHeader",()=>f,"CardTitle",()=>g]);var b=a.i(41825),c=a.i(54159),d=a.i(18688);let e=c.forwardRef(({className:a,...c},e)=>(0,b.jsx)("div",{ref:e,className:(0,d.cn)("rounded-lg border border-gray-200 dark:border-gray-800 bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 shadow-sm",a),...c}));e.displayName="Card";let f=c.forwardRef(({className:a,...c},e)=>(0,b.jsx)("div",{ref:e,className:(0,d.cn)("flex flex-col space-y-1.5 p-6",a),...c}));f.displayName="CardHeader";let g=c.forwardRef(({className:a,...c},e)=>(0,b.jsx)("h3",{ref:e,className:(0,d.cn)("text-2xl font-semibold leading-none tracking-tight",a),...c}));g.displayName="CardTitle";let h=c.forwardRef(({className:a,...c},e)=>(0,b.jsx)("p",{ref:e,className:(0,d.cn)("text-sm text-gray-600 dark:text-gray-400",a),...c}));h.displayName="CardDescription";let i=c.forwardRef(({className:a,...c},e)=>(0,b.jsx)("div",{ref:e,className:(0,d.cn)("p-6 pt-0",a),...c}));i.displayName="CardContent",c.forwardRef(({className:a,...c},e)=>(0,b.jsx)("div",{ref:e,className:(0,d.cn)("flex items-center p-6 pt-0",a),...c})).displayName="CardFooter"},2331,98752,54472,a=>{"use strict";a.s(["Button",()=>n],2331);var b=a.i(41825),c=a.i(54159);function d(a,b){if("function"==typeof a)return a(b);null!=a&&(a.current=b)}function e(...a){return b=>{let c=!1,e=a.map(a=>{let e=d(a,b);return c||"function"!=typeof e||(c=!0),e});if(c)return()=>{for(let b=0;b<e.length;b++){let c=e[b];"function"==typeof c?c():d(a[b],null)}}}}function f(...a){return c.useCallback(e(...a),a)}function g(a){let d=function(a){let b=c.forwardRef((a,b)=>{let{children:d,...f}=a;if(c.isValidElement(d)){var g;let a,h,i=(g=d,(h=(a=Object.getOwnPropertyDescriptor(g.props,"ref")?.get)&&"isReactWarning"in a&&a.isReactWarning)?g.ref:(h=(a=Object.getOwnPropertyDescriptor(g,"ref")?.get)&&"isReactWarning"in a&&a.isReactWarning)?g.props.ref:g.props.ref||g.ref),j=function(a,b){let c={...b};for(let d in b){let e=a[d],f=b[d];/^on[A-Z]/.test(d)?e&&f?c[d]=(...a)=>{let b=f(...a);return e(...a),b}:e&&(c[d]=e):"style"===d?c[d]={...e,...f}:"className"===d&&(c[d]=[e,f].filter(Boolean).join(" "))}return{...a,...c}}(f,d.props);return d.type!==c.Fragment&&(j.ref=b?e(b,i):i),c.cloneElement(d,j)}return c.Children.count(d)>1?c.Children.only(null):null});return b.displayName=`${a}.SlotClone`,b}(a),f=c.forwardRef((a,e)=>{let{children:f,...g}=a,h=c.Children.toArray(f),i=h.find(j);if(i){let a=i.props.children,f=h.map(b=>b!==i?b:c.Children.count(a)>1?c.Children.only(null):c.isValidElement(a)?a.props.children:null);return(0,b.jsx)(d,{...g,ref:e,children:c.isValidElement(a)?c.cloneElement(a,void 0,f):null})}return(0,b.jsx)(d,{...g,ref:e,children:f})});return f.displayName=`${a}.Slot`,f}a.s(["Slot",()=>h,"createSlot",()=>g],54472),a.s(["composeRefs",()=>e,"useComposedRefs",()=>f],98752);var h=g("Slot"),i=Symbol("radix.slottable");function j(a){return c.isValidElement(a)&&"function"==typeof a.type&&"__radixId"in a.type&&a.type.__radixId===i}var k=a.i(24311),l=a.i(18688);let m=(0,k.cva)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-blue-600 text-white hover:bg-blue-700 dark:bg-blue-600 dark:hover:bg-blue-700",destructive:"bg-red-600 text-white hover:bg-red-700 dark:bg-red-600 dark:hover:bg-red-700",outline:"border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-900 hover:bg-gray-50 dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-gray-100",secondary:"bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-gray-100 hover:bg-gray-200 dark:hover:bg-gray-700",ghost:"hover:bg-gray-100 dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-gray-100",link:"text-blue-600 dark:text-blue-400 underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),n=c.forwardRef(({className:a,variant:c,size:d,asChild:e=!1,...f},g)=>(0,b.jsx)(e?h:"button",{className:(0,l.cn)(m({variant:c,size:d,className:a})),ref:g,...f}));n.displayName="Button"},32639,a=>{"use strict";a.s(["default",()=>g],32639);var b=a.i(54159);let c=a=>{let b=a.replace(/^([A-Z])|[\s-_]+(\w)/g,(a,b,c)=>c?c.toUpperCase():b.toLowerCase());return b.charAt(0).toUpperCase()+b.slice(1)},d=(...a)=>a.filter((a,b,c)=>!!a&&""!==a.trim()&&c.indexOf(a)===b).join(" ").trim();var e={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let f=(0,b.forwardRef)(({color:a="currentColor",size:c=24,strokeWidth:f=2,absoluteStrokeWidth:g,className:h="",children:i,iconNode:j,...k},l)=>(0,b.createElement)("svg",{ref:l,...e,width:c,height:c,stroke:a,strokeWidth:g?24*Number(f)/Number(c):f,className:d("lucide",h),...!i&&!(a=>{for(let b in a)if(b.startsWith("aria-")||"role"===b||"title"===b)return!0})(k)&&{"aria-hidden":"true"},...k},[...j.map(([a,c])=>(0,b.createElement)(a,c)),...Array.isArray(i)?i:[i]])),g=(a,e)=>{let g=(0,b.forwardRef)(({className:g,...h},i)=>(0,b.createElement)(f,{ref:i,iconNode:e,className:d(`lucide-${c(a).replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()}`,`lucide-${a}`,g),...h}));return g.displayName=c(a),g}},59844,a=>{"use strict";a.s(["User",()=>b],59844);let b=(0,a.i(32639).default)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},95788,a=>{"use strict";a.s(["adminNavigation",()=>b,"getRoleDashboardUrl",()=>e,"studentNavigation",()=>d,"teacherNavigation",()=>c]);let b=[{name:"Dashboard",href:"/admin",icon:"BarChart3"},{name:"Students",href:"/admin/students",icon:"Users"},{name:"Teachers",href:"/admin/teachers",icon:"GraduationCap"},{name:"Classes & Sections",href:"/admin/classes",icon:"BookOpen"},{name:"Subjects",href:"/admin/subjects",icon:"FileText"},{name:"Terms & Exams",href:"/admin/exams",icon:"Calendar"},{name:"Attendance",href:"/admin/attendance",icon:"ClipboardList"},{name:"Marks",href:"/admin/marks",icon:"Award"},{name:"Reports",href:"/admin/reports",icon:"Download"},{name:"Settings",href:"/admin/settings",icon:"Settings"}],c=[{name:"Dashboard",href:"/teacher",icon:"BarChart3"},{name:"My Classes",href:"/teacher/classes",icon:"BookOpen"},{name:"Attendance",href:"/teacher/attendance",icon:"ClipboardList"},{name:"Marks",href:"/teacher/marks",icon:"Award"},{name:"Students",href:"/teacher/students",icon:"Users"},{name:"Reports",href:"/teacher/reports",icon:"FileText"},{name:"Profile",href:"/teacher/profile",icon:"User"}],d=[{name:"Dashboard",href:"/student",icon:"BarChart3"},{name:"My Classes",href:"/student/classes",icon:"BookOpen"},{name:"Attendance",href:"/student/attendance",icon:"ClipboardList"},{name:"Marks",href:"/student/marks",icon:"Award"},{name:"Reports",href:"/student/reports",icon:"FileText"},{name:"Profile",href:"/student/profile",icon:"User"}];function e(a){switch(a){case"ADMIN":return"/admin";case"TEACHER":return"/teacher";case"STUDENT":return"/student";default:return"/"}}},11090,a=>{"use strict";a.s(["Label",()=>i],11090);var b=a.i(41825),c=a.i(54159),d=a.i(36870),e=c.forwardRef((a,c)=>(0,b.jsx)(d.Primitive.label,{...a,ref:c,onMouseDown:b=>{b.target.closest("button, input, select, textarea")||(a.onMouseDown?.(b),!b.defaultPrevented&&b.detail>1&&b.preventDefault())}}));e.displayName="Label";var f=a.i(24311),g=a.i(18688);let h=(0,f.cva)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),i=c.forwardRef(({className:a,...c},d)=>(0,b.jsx)(e,{ref:d,className:(0,g.cn)(h(),a),...c}));i.displayName=e.displayName},5492,a=>{"use strict";a.s(["Input",()=>e]);var b=a.i(41825),c=a.i(54159),d=a.i(18688);let e=c.forwardRef(({className:a,type:c,...e},f)=>(0,b.jsx)("input",{type:c,className:(0,d.cn)("flex h-10 w-full rounded-md border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-900 px-3 py-2 text-sm text-gray-900 dark:text-gray-100 file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-500 dark:placeholder:text-gray-400 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",a),ref:f,...e}));e.displayName="Input"},75422,a=>{"use strict";a.s(["Alert",()=>g,"AlertDescription",()=>h]);var b=a.i(41825),c=a.i(54159),d=a.i(24311),e=a.i(18688);let f=(0,d.cva)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-gray-900 dark:[&>svg]:text-gray-100",{variants:{variant:{default:"bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 border-gray-200 dark:border-gray-800",destructive:"border-red-200 dark:border-red-800 bg-red-50 dark:bg-red-950 text-red-800 dark:text-red-200 [&>svg]:text-red-600 dark:[&>svg]:text-red-400"}},defaultVariants:{variant:"default"}}),g=c.forwardRef(({className:a,variant:c,...d},g)=>(0,b.jsx)("div",{ref:g,role:"alert",className:(0,e.cn)(f({variant:c}),a),...d}));g.displayName="Alert",c.forwardRef(({className:a,...c},d)=>(0,b.jsx)("h5",{ref:d,className:(0,e.cn)("mb-1 font-medium leading-none tracking-tight",a),...c})).displayName="AlertTitle";let h=c.forwardRef(({className:a,...c},d)=>(0,b.jsx)("div",{ref:d,className:(0,e.cn)("text-sm [&_p]:leading-relaxed",a),...c}));h.displayName="AlertDescription"},43626,a=>{"use strict";a.s(["Loader2",()=>b],43626);let b=(0,a.i(32639).default)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},73211,a=>{"use strict";a.s(["Eye",()=>b],73211);let b=(0,a.i(32639).default)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},76510,a=>{"use strict";a.s(["default",()=>u],76510);var b=a.i(41825),c=a.i(54159),d=a.i(2331),e=a.i(5492),f=a.i(11090),g=a.i(4082),h=a.i(75422),i=a.i(43626),j=a.i(73211),k=a.i(32639);let l=(0,k.default)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]]);var m=a.i(59844);let n=(0,k.default)("lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]]);var o=a.i(25384),p=a.i(95788);async function q(a){try{if(!a.email||!a.password)return{success:!1,error:"Email and password are required"};if(!a.email.includes("@"))return{success:!1,error:"Please enter a valid email address"};if(a.password.length<6)return{success:!1,error:"Password must be at least 6 characters long"};let b=await (0,o.signIn)("credentials",{email:a.email,password:a.password,redirect:!1});if(b?.error)return{success:!1,error:"Invalid email or password. Please try again."};let c=await (0,o.getSession)(),d=c?.user?.role?(0,p.getRoleDashboardUrl)(c.user.role):"/";return{success:!0,redirectUrl:d}}catch(a){return console.error("Login error:",a),{success:!1,error:"An unexpected error occurred. Please try again."}}}async function r(a="/login"){try{await (0,o.signOut)({redirect:!0,callbackUrl:a})}catch(b){console.error("Logout error:",b),window.location.href=a}}let s={admin:{email:"<EMAIL>",password:"Admin@12345",role:"ADMIN"},teacher:{email:"<EMAIL>",password:"Teacher@12345",role:"TEACHER"},student:{email:"<EMAIL>",password:"Student@12345",role:"STUDENT"}};var t=a.i(52963);function u(){let[a,k]=(0,c.useState)(!1),{formData:o,validationErrors:p,isLoading:u,error:v,updateField:w,handleSubmit:x,fillDemoCredentials:y}=function(){let{login:a,...b}=function(){let a=(0,t.useRouter)(),[b,d]=(0,c.useState)({isLoading:!1,error:null,success:!1}),e=(0,c.useCallback)(async b=>{d(a=>({...a,isLoading:!0,error:null,success:!1}));try{let c=await q(b);return c.success?(d(a=>({...a,isLoading:!1,success:!0})),c.redirectUrl&&a.push(c.redirectUrl)):d(a=>({...a,isLoading:!1,error:c.error||"Login failed"})),c}catch(b){let a=b instanceof Error?b.message:"An unexpected error occurred";return d(b=>({...b,isLoading:!1,error:a})),{success:!1,error:a}}},[a]),f=(0,c.useCallback)(async(a="/login")=>{d(a=>({...a,isLoading:!0,error:null}));try{await r(a),d(a=>({...a,isLoading:!1,success:!0}))}catch(b){let a=b instanceof Error?b.message:"Logout failed";d(b=>({...b,isLoading:!1,error:a}))}},[]),g=(0,c.useCallback)(()=>{d(a=>({...a,error:null}))},[]),h=(0,c.useCallback)(()=>{d(a=>({...a,success:!1}))},[]),i=(0,c.useCallback)(()=>{d({isLoading:!1,error:null,success:!1})},[]);return{...b,login:e,logout:f,clearError:g,clearSuccess:h,reset:i}}(),[d,e]=(0,c.useState)({email:"",password:""}),[f,g]=(0,c.useState)({}),h=(0,c.useCallback)((a,c)=>{e(b=>({...b,[a]:c})),f[a]&&g(b=>({...b,[a]:void 0})),b.error&&b.clearError()},[f,b]),i=(0,c.useCallback)(()=>{let a={};return d.email.trim()?d.email.includes("@")||(a.email="Please enter a valid email address"):a.email="Email is required",d.password.trim()?d.password.length<6&&(a.password="Password must be at least 6 characters long"):a.password="Password is required",g(a),0===Object.keys(a).length},[d]),j=(0,c.useCallback)(async b=>(b&&b.preventDefault(),i())?await a(d):{success:!1,error:"Please fix the validation errors"},[d,i,a]),k=(0,c.useCallback)(()=>{e({email:"",password:""}),g({}),b.reset()},[b]),l=(0,c.useCallback)(a=>{e({admin:{email:"<EMAIL>",password:"Admin@12345"},teacher:{email:"<EMAIL>",password:"Teacher@12345"},student:{email:"<EMAIL>",password:"Student@12345"}}[a]),g({})},[]);return{...b,formData:d,validationErrors:f,updateField:h,validateForm:i,handleSubmit:j,resetForm:k,fillDemoCredentials:l}}();return(0,b.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-950 p-4",children:(0,b.jsxs)(g.Card,{className:"w-full max-w-md mx-auto",children:[(0,b.jsxs)(g.CardHeader,{className:"text-center px-4 sm:px-6",children:[(0,b.jsx)(g.CardTitle,{className:"text-xl sm:text-2xl font-bold leading-tight",children:"School Management System"}),(0,b.jsx)(g.CardDescription,{className:"text-sm sm:text-base mt-2",children:"Sign in to your account"})]}),(0,b.jsxs)(g.CardContent,{className:"px-4 sm:px-6",children:[(0,b.jsxs)("form",{onSubmit:x,className:"space-y-4",children:[v&&(0,b.jsx)(h.Alert,{variant:"destructive",children:(0,b.jsx)(h.AlertDescription,{children:v})}),(0,b.jsxs)("div",{className:"space-y-2",children:[(0,b.jsx)(f.Label,{htmlFor:"email",children:"Email Address"}),(0,b.jsxs)("div",{className:"relative",children:[(0,b.jsx)(m.User,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,b.jsx)(e.Input,{id:"email",type:"email",value:o.email,onChange:a=>w("email",a.target.value),placeholder:"Enter your email address",className:`pl-10 min-h-[44px] text-base ${p.email?"border-red-500":""}`,required:!0,disabled:u})]}),p.email&&(0,b.jsx)("p",{className:"text-sm text-red-600 dark:text-red-400",children:p.email})]}),(0,b.jsxs)("div",{className:"space-y-2",children:[(0,b.jsx)(f.Label,{htmlFor:"password",children:"Password"}),(0,b.jsxs)("div",{className:"relative",children:[(0,b.jsx)(n,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,b.jsx)(e.Input,{id:"password",type:a?"text":"password",value:o.password,onChange:a=>w("password",a.target.value),placeholder:"Enter your password",className:`pl-10 pr-10 min-h-[44px] text-base ${p.password?"border-red-500":""}`,required:!0,disabled:u}),(0,b.jsx)("button",{type:"button",onClick:()=>k(!a),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300",disabled:u,children:a?(0,b.jsx)(l,{className:"h-4 w-4"}):(0,b.jsx)(j.Eye,{className:"h-4 w-4"})})]}),p.password&&(0,b.jsx)("p",{className:"text-sm text-red-600 dark:text-red-400",children:p.password})]}),(0,b.jsx)(d.Button,{type:"submit",className:"w-full min-h-[44px] text-base",disabled:u,children:u?(0,b.jsxs)(b.Fragment,{children:[(0,b.jsx)(i.Loader2,{className:"w-4 h-4 mr-2 animate-spin"}),"Signing in..."]}):"Sign In"})]}),(0,b.jsxs)("div",{className:"mt-6 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg",children:[(0,b.jsx)("h3",{className:"font-semibold mb-3 text-center text-gray-900 dark:text-gray-100 text-sm sm:text-base",children:"Demo Credentials"}),(0,b.jsxs)("div",{className:"space-y-3",children:[(0,b.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-3 gap-2",children:[(0,b.jsx)(d.Button,{type:"button",variant:"outline",size:"sm",onClick:()=>y("admin"),disabled:u,className:"text-xs",children:"Fill Admin"}),(0,b.jsx)(d.Button,{type:"button",variant:"outline",size:"sm",onClick:()=>y("teacher"),disabled:u,className:"text-xs",children:"Fill Teacher"}),(0,b.jsx)(d.Button,{type:"button",variant:"outline",size:"sm",onClick:()=>y("student"),disabled:u,className:"text-xs",children:"Fill Student"})]}),(0,b.jsx)("div",{className:"space-y-1 text-xs text-gray-600 dark:text-gray-400",children:(0,b.jsxs)("div",{className:"text-center",children:[(0,b.jsxs)("p",{children:[(0,b.jsx)("strong",{children:"Admin:"})," ",s.admin.email]}),(0,b.jsxs)("p",{children:[(0,b.jsx)("strong",{children:"Teacher:"})," ",s.teacher.email]}),(0,b.jsxs)("p",{children:[(0,b.jsx)("strong",{children:"Student:"})," ",s.student.email]})]})})]})]})]})]})})}}];

//# sourceMappingURL=%5Broot-of-the-server%5D__4f00ae07._.js.map