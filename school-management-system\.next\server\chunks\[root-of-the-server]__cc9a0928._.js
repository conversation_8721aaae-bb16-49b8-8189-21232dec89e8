module.exports=[18622,(e,t,r)=>{t.exports=e.x("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js"))},56704,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/work-async-storage.external.js",()=>require("next/dist/server/app-render/work-async-storage.external.js"))},32319,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/work-unit-async-storage.external.js",()=>require("next/dist/server/app-render/work-unit-async-storage.external.js"))},20635,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/action-async-storage.external.js",()=>require("next/dist/server/app-render/action-async-storage.external.js"))},24725,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/after-task-async-storage.external.js",()=>require("next/dist/server/app-render/after-task-async-storage.external.js"))},24361,(e,t,r)=>{t.exports=e.x("util",()=>require("util"))},29173,(e,t,r)=>{t.exports=e.x("@prisma/client",()=>require("@prisma/client"))},70406,(e,t,r)=>{t.exports=e.x("next/dist/compiled/@opentelemetry/api",()=>require("next/dist/compiled/@opentelemetry/api"))},93695,(e,t,r)=>{t.exports=e.x("next/dist/shared/lib/no-fallback-error.external.js",()=>require("next/dist/shared/lib/no-fallback-error.external.js"))},61724,(e,t,r)=>{t.exports=e.x("next/dist/compiled/next-server/app-route-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-route-turbo.runtime.prod.js"))},54799,(e,t,r)=>{t.exports=e.x("crypto",()=>require("crypto"))},92509,(e,t,r)=>{t.exports=e.x("url",()=>require("url"))},21517,(e,t,r)=>{t.exports=e.x("http",()=>require("http"))},49719,(e,t,r)=>{t.exports=e.x("assert",()=>require("assert"))},45706,(e,t,r)=>{t.exports=e.x("querystring",()=>require("querystring"))},874,(e,t,r)=>{t.exports=e.x("buffer",()=>require("buffer"))},6461,(e,t,r)=>{t.exports=e.x("zlib",()=>require("zlib"))},24836,(e,t,r)=>{t.exports=e.x("https",()=>require("https"))},27699,(e,t,r)=>{t.exports=e.x("events",()=>require("events"))},12756,e=>{e.v(JSON.parse('{"name":"openid-client","version":"5.7.1","description":"OpenID Connect Relying Party (RP, Client) implementation for Node.js runtime, supports passportjs","keywords":["auth","authentication","basic","certified","client","connect","dynamic","electron","hybrid","identity","implicit","oauth","oauth2","oidc","openid","passport","relying party","strategy"],"homepage":"https://github.com/panva/openid-client","repository":"panva/openid-client","funding":{"url":"https://github.com/sponsors/panva"},"license":"MIT","author":"Filip Skokan <<EMAIL>>","exports":{"types":"./types/index.d.ts","import":"./lib/index.mjs","require":"./lib/index.js"},"main":"./lib/index.js","types":"./types/index.d.ts","files":["lib","types/index.d.ts"],"scripts":{"format":"npx prettier --loglevel silent --write ./lib ./test ./certification ./types","test":"mocha test/**/*.test.js"},"dependencies":{"jose":"^4.15.9","lru-cache":"^6.0.0","object-hash":"^2.2.0","oidc-token-hash":"^5.0.3"},"devDependencies":{"@types/node":"^16.18.106","@types/passport":"^1.0.16","base64url":"^3.0.1","chai":"^4.5.0","mocha":"^10.7.3","nock":"^13.5.5","prettier":"^2.8.8","readable-mock-req":"^0.2.2","sinon":"^9.2.4","timekeeper":"^2.3.1"},"standard-version":{"scripts":{"postchangelog":"sed -i \'\' -e \'s/### \\\\[/## [/g\' CHANGELOG.md"},"types":[{"type":"feat","section":"Features"},{"type":"fix","section":"Fixes"},{"type":"chore","hidden":true},{"type":"docs","hidden":true},{"type":"style","hidden":true},{"type":"refactor","section":"Refactor","hidden":false},{"type":"perf","section":"Performance","hidden":false},{"type":"test","hidden":true}]}}'))},65467,(e,t,r)=>{},58608,e=>{"use strict";e.s(["handler",()=>N,"patchFetch",()=>q,"routeModule",()=>E,"serverHooks",()=>C,"workAsyncStorage",()=>k,"workUnitAsyncStorage",()=>j],58608);var t=e.i(6137),r=e.i(11365),a=e.i(9638),n=e.i(15243),s=e.i(66378),i=e.i(92101),o=e.i(50012),d=e.i(62885),l=e.i(31409),u=e.i(78448),p=e.i(28015),c=e.i(72721),x=e.i(75714),h=e.i(12634),m=e.i(93695);e.i(74732);var g=e.i(66662);e.s(["GET",()=>b],65239);var y=e.i(2835),v=e.i(58356),R=e.i(43382),f=e.i(31279);async function b(e){try{let e=await (0,v.getServerSession)(R.authOptions);if(!e||"STUDENT"!==e.user.role)return y.NextResponse.json({error:"Unauthorized"},{status:401});let t=await f.prisma.student.findUnique({where:{userId:e.user.id},include:{currentClass:{include:{subjects:!0}},currentSection:!0}});if(!t)return y.NextResponse.json({error:"Student not found"},{status:404});let[r,a,n]=await Promise.all([f.prisma.attendance.aggregate({where:{studentId:t.id,date:{gte:new Date(Date.now()-2592e6)}},_count:{id:!0}}).then(async e=>{let r=await f.prisma.attendance.count({where:{studentId:t.id,date:{gte:new Date(Date.now()-2592e6)},status:"PRESENT"}});return{total:e._count.id,present:r,rate:e._count.id>0?r/e._count.id*100:0}}),f.prisma.mark.aggregate({where:{studentId:t.id},_avg:{obtainedMarks:!0},_count:{id:!0}}),f.prisma.exam.findMany({where:{subject:{classId:t.currentClassId},date:{gte:new Date}},include:{subject:!0},orderBy:{date:"asc"},take:5})]),s=await f.prisma.mark.findMany({where:{studentId:t.id},include:{exam:{include:{subject:!0}}},orderBy:{createdAt:"desc"},take:10}),i={currentClass:t.currentClass?.name||"Not assigned",currentSection:t.currentSection?.name||"Not assigned",rollNumber:t.rollNumber||"Not assigned",attendanceRate:Math.round(10*r.rate)/10,averageMarks:a._avg.obtainedMarks?Math.round(10*a._avg.obtainedMarks)/10:0,totalSubjects:t.currentClass?.subjects.length||0,upcomingExams:n.length,totalMarksRecords:a._count.id,totalAttendanceRecords:r.total,recentMarks:s.map(e=>({id:e.id,subject:e.exam.subject.name,examName:e.exam.name,obtainedMarks:e.obtainedMarks,maxMarks:e.exam.maxMarks,percentage:Math.round(e.obtainedMarks/e.exam.maxMarks*100),date:e.exam.date})),upcomingExamsList:n.map(e=>({id:e.id,name:e.name,subject:e.subject.name,date:e.date,maxMarks:e.maxMarks}))};return y.NextResponse.json(i)}catch(e){return console.error("Error fetching student dashboard stats:",e),y.NextResponse.json({error:"Internal server error"},{status:500})}}var w=e.i(65239);let E=new t.AppRouteRouteModule({definition:{kind:r.RouteKind.APP_ROUTE,page:"/api/student/dashboard/stats/route",pathname:"/api/student/dashboard/stats",filename:"route",bundlePath:""},distDir:".next",relativeProjectDir:"",resolvedPagePath:"[project]/school-management-system/src/app/api/student/dashboard/stats/route.ts",nextConfigOutput:"",userland:w}),{workAsyncStorage:k,workUnitAsyncStorage:j,serverHooks:C}=E;function q(){return(0,a.patchFetch)({workAsyncStorage:k,workUnitAsyncStorage:j})}async function N(e,t,a){var y;let v="/api/student/dashboard/stats/route";v=v.replace(/\/index$/,"")||"/";let R=await E.prepare(e,t,{srcPage:v,multiZoneDraftMode:!1});if(!R)return t.statusCode=400,t.end("Bad Request"),null==a.waitUntil||a.waitUntil.call(a,Promise.resolve()),null;let{buildId:f,params:b,nextConfig:w,isDraftMode:k,prerenderManifest:j,routerServerContext:C,isOnDemandRevalidate:q,revalidateOnlyGenerated:N,resolvedPathname:M}=R,A=(0,i.normalizeAppPath)(v),S=!!(j.dynamicRoutes[A]||j.routes[M]);if(S&&!k){let e=!!j.routes[M],t=j.dynamicRoutes[A];if(t&&!1===t.fallback&&!e)throw new m.NoFallbackError}let _=null;!S||E.isDev||k||(_="/index"===(_=M)?"/":_);let P=!0===E.isDev||!S,T=S&&!P,O=e.method||"GET",I=(0,s.getTracer)(),D=I.getActiveScopeSpan(),U={params:b,prerenderManifest:j,renderOpts:{experimental:{cacheComponents:!!w.experimental.cacheComponents,authInterrupts:!!w.experimental.authInterrupts},supportsDynamicResponse:P,incrementalCache:(0,n.getRequestMeta)(e,"incrementalCache"),cacheLifeProfiles:null==(y=w.experimental)?void 0:y.cacheLife,isRevalidate:T,waitUntil:a.waitUntil,onClose:e=>{t.on("close",e)},onAfterTaskError:void 0,onInstrumentationRequestError:(t,r,a)=>E.onRequestError(e,t,a,C)},sharedContext:{buildId:f}},H=new o.NodeNextRequest(e),F=new o.NodeNextResponse(t),$=d.NextRequestAdapter.fromNodeNextRequest(H,(0,d.signalFromNodeResponse)(t));try{let i=async r=>E.handle($,U).finally(()=>{if(!r)return;r.setAttributes({"http.status_code":t.statusCode,"next.rsc":!1});let a=I.getRootSpanAttributes();if(!a)return;if(a.get("next.span_type")!==l.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${a.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let n=a.get("next.route");if(n){let e=`${O} ${n}`;r.setAttributes({"next.route":n,"http.route":n,"next.span_name":e}),r.updateName(e)}else r.updateName(`${O} ${e.url}`)}),o=async s=>{var o,d;let l=async({previousCacheEntry:r})=>{try{if(!(0,n.getRequestMeta)(e,"minimalMode")&&q&&N&&!r)return t.statusCode=404,t.setHeader("x-nextjs-cache","REVALIDATED"),t.end("This page could not be found"),null;let o=await i(s);e.fetchMetrics=U.renderOpts.fetchMetrics;let d=U.renderOpts.pendingWaitUntil;d&&a.waitUntil&&(a.waitUntil(d),d=void 0);let l=U.renderOpts.collectedTags;if(!S)return await (0,p.sendResponse)(H,F,o,U.renderOpts.pendingWaitUntil),null;{let e=await o.blob(),t=(0,c.toNodeOutgoingHttpHeaders)(o.headers);l&&(t[h.NEXT_CACHE_TAGS_HEADER]=l),!t["content-type"]&&e.type&&(t["content-type"]=e.type);let r=void 0!==U.renderOpts.collectedRevalidate&&!(U.renderOpts.collectedRevalidate>=h.INFINITE_CACHE)&&U.renderOpts.collectedRevalidate,a=void 0===U.renderOpts.collectedExpire||U.renderOpts.collectedExpire>=h.INFINITE_CACHE?void 0:U.renderOpts.collectedExpire;return{value:{kind:g.CachedRouteKind.APP_ROUTE,status:o.status,body:Buffer.from(await e.arrayBuffer()),headers:t},cacheControl:{revalidate:r,expire:a}}}}catch(t){throw(null==r?void 0:r.isStale)&&await E.onRequestError(e,t,{routerKind:"App Router",routePath:v,routeType:"route",revalidateReason:(0,u.getRevalidateReason)({isRevalidate:T,isOnDemandRevalidate:q})},C),t}},m=await E.handleResponse({req:e,nextConfig:w,cacheKey:_,routeKind:r.RouteKind.APP_ROUTE,isFallback:!1,prerenderManifest:j,isRoutePPREnabled:!1,isOnDemandRevalidate:q,revalidateOnlyGenerated:N,responseGenerator:l,waitUntil:a.waitUntil});if(!S)return null;if((null==m||null==(o=m.value)?void 0:o.kind)!==g.CachedRouteKind.APP_ROUTE)throw Object.defineProperty(Error(`Invariant: app-route received invalid cache entry ${null==m||null==(d=m.value)?void 0:d.kind}`),"__NEXT_ERROR_CODE",{value:"E701",enumerable:!1,configurable:!0});(0,n.getRequestMeta)(e,"minimalMode")||t.setHeader("x-nextjs-cache",q?"REVALIDATED":m.isMiss?"MISS":m.isStale?"STALE":"HIT"),k&&t.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");let y=(0,c.fromNodeOutgoingHttpHeaders)(m.value.headers);return(0,n.getRequestMeta)(e,"minimalMode")&&S||y.delete(h.NEXT_CACHE_TAGS_HEADER),!m.cacheControl||t.getHeader("Cache-Control")||y.get("Cache-Control")||y.set("Cache-Control",(0,x.getCacheControlHeader)(m.cacheControl)),await (0,p.sendResponse)(H,F,new Response(m.value.body,{headers:y,status:m.value.status||200})),null};D?await o(D):await I.withPropagatedContext(e.headers,()=>I.trace(l.BaseServerSpan.handleRequest,{spanName:`${O} ${e.url}`,kind:s.SpanKind.SERVER,attributes:{"http.method":O,"http.target":e.url}},o))}catch(t){if(D||t instanceof m.NoFallbackError||await E.onRequestError(e,t,{routerKind:"App Router",routePath:A,routeType:"route",revalidateReason:(0,u.getRevalidateReason)({isRevalidate:T,isOnDemandRevalidate:q})}),S)throw t;return await (0,p.sendResponse)(H,F,new Response(null,{status:500})),null}}}];

//# sourceMappingURL=%5Broot-of-the-server%5D__cc9a0928._.js.map