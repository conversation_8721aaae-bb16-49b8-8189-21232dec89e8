'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import DashboardLayout from '@/components/layout/dashboard-layout'
import { getGradeBadgeColor } from '@/lib/grading'
import { 
  Calendar, 
  Award, 
  BookOpen, 
  Users, 
  TrendingUp,
  FileText,
  Download,
  Filter,
  Plus,
  CheckCircle,
  XCircle
} from 'lucide-react'

interface MarkRecord {
  id: string
  studentName: string
  admissionNo: string
  examName: string
  subjectName: string
  className: string
  sectionName: string
  obtainedMarks: number
  maxMarks: number
  percentage: number
  grade: string
  gradedBy: string
  date: string
}

import { adminNavigation } from '@/lib/navigation';

export default function MarksPage() {
  const [markRecords, setMarkRecords] = useState<MarkRecord[]>([])
  const [selectedExam, setSelectedExam] = useState('all')
  const [selectedSubject, setSelectedSubject] = useState('all')
  const [selectedClass, setSelectedClass] = useState('all')

  // Mock data - in real app, this would come from API
  useEffect(() => {
    setMarkRecords([
      {
        id: '1',
        studentName: 'John Doe',
        admissionNo: 'STU001',
        examName: 'Unit Test 1',
        subjectName: 'Mathematics',
        className: 'Grade 8',
        sectionName: 'A',
        obtainedMarks: 45,
        maxMarks: 50,
        percentage: 90,
        grade: 'A+',
        gradedBy: 'Mrs. Smith',
        date: '2024-10-15'
      },
      {
        id: '2',
        studentName: 'Jane Smith',
        admissionNo: 'STU002',
        examName: 'Unit Test 1',
        subjectName: 'Mathematics',
        className: 'Grade 8',
        sectionName: 'A',
        obtainedMarks: 38,
        maxMarks: 50,
        percentage: 76,
        grade: 'B+',
        gradedBy: 'Mrs. Smith',
        date: '2024-10-15'
      },
      {
        id: '3',
        studentName: 'Mike Johnson',
        admissionNo: 'STU003',
        examName: 'Mid Term Exam',
        subjectName: 'English',
        className: 'Grade 8',
        sectionName: 'A',
        obtainedMarks: 85,
        maxMarks: 100,
        percentage: 85,
        grade: 'A',
        gradedBy: 'Mr. Brown',
        date: '2024-11-20'
      }
    ])
  }, [])



  const marksStats = {
    total: markRecords.length,
    average: markRecords.length > 0 
      ? Math.round(markRecords.reduce((sum, record) => sum + record.percentage, 0) / markRecords.length)
      : 0,
    highest: Math.max(...markRecords.map(r => r.percentage)),
    lowest: Math.min(...markRecords.map(r => r.percentage)),
    passed: markRecords.filter(r => r.percentage >= 40).length,
    failed: markRecords.filter(r => r.percentage < 40).length
  }

  return (
    <DashboardLayout title="Marks Management" navigation={adminNavigation}>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Marks Management</h1>
            <p className="text-gray-600">Monitor and manage student examination marks</p>
          </div>
          <div className="flex space-x-2">
            <Button variant="outline">
              <Download className="w-4 h-4 mr-2" />
              Export Marks
            </Button>
            <Button>
              <Plus className="w-4 h-4 mr-2" />
              Add Marks
            </Button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-6">
          <Card key="total-marks-records">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Records</CardTitle>
              <FileText className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{marksStats.total}</div>
            </CardContent>
          </Card>
          <Card key="average-marks-score">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Average Score</CardTitle>
              <TrendingUp className="h-4 w-4 text-blue-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-600">{marksStats.average}%</div>
            </CardContent>
          </Card>
          <Card key="highest-marks-score">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Highest Score</CardTitle>
              <Award className="h-4 w-4 text-green-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">{marksStats.highest}%</div>
            </CardContent>
          </Card>
          <Card key="lowest-marks-score">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Lowest Score</CardTitle>
              <TrendingUp className="h-4 w-4 text-red-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-600">{marksStats.lowest}%</div>
            </CardContent>
          </Card>
          <Card key="passed-students">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Passed</CardTitle>
              <CheckCircle className="h-4 w-4 text-green-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">{marksStats.passed}</div>
            </CardContent>
          </Card>
          <Card key="failed-students">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Failed</CardTitle>
              <XCircle className="h-4 w-4 text-red-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-600">{marksStats.failed}</div>
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Filter className="w-5 h-5 mr-2" />
              Filters
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <Label htmlFor="exam">Exam</Label>
                <select
                  id="exam"
                  value={selectedExam}
                  onChange={(e) => setSelectedExam(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="all">All Exams</option>
                  <option value="unit1">Unit Test 1</option>
                  <option value="midterm">Mid Term Exam</option>
                  <option value="final">Final Exam</option>
                </select>
              </div>
              <div>
                <Label htmlFor="subject">Subject</Label>
                <select
                  id="subject"
                  value={selectedSubject}
                  onChange={(e) => setSelectedSubject(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="all">All Subjects</option>
                  <option value="math">Mathematics</option>
                  <option value="english">English</option>
                  <option value="science">Science</option>
                </select>
              </div>
              <div>
                <Label htmlFor="class">Class</Label>
                <select
                  id="class"
                  value={selectedClass}
                  onChange={(e) => setSelectedClass(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="all">All Classes</option>
                  <option value="grade8">Grade 8</option>
                  <option value="grade9">Grade 9</option>
                  <option value="grade10">Grade 10</option>
                </select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Marks Table */}
        <Card>
          <CardHeader>
            <CardTitle>Marks Records</CardTitle>
            <CardDescription>
              Examination marks and grades for all students
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Student
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Exam
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Subject
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Class
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Marks
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Percentage
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Grade
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Graded By
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {markRecords.map((record) => (
                    <tr key={record.id}>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="flex-shrink-0 h-10 w-10">
                            <div className="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center">
                              <span className="text-sm font-medium text-gray-700">
                                {record.studentName.split(' ').map(n => n[0]).join('')}
                              </span>
                            </div>
                          </div>
                          <div className="ml-4">
                            <div className="text-sm font-medium text-gray-900">{record.studentName}</div>
                            <div className="text-sm text-gray-500">{record.admissionNo}</div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {record.examName}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {record.subjectName}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {record.className} - {record.sectionName}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {record.obtainedMarks}/{record.maxMarks}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {record.percentage}%
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getGradeBadgeColor(record.grade)}`}>
                          {record.grade}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {record.gradedBy}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex space-x-2">
                          <Button variant="outline" size="sm">
                            Edit
                          </Button>
                          <Button variant="outline" size="sm">
                            View
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  )
}
