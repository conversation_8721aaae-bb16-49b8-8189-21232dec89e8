module.exports=[18622,(e,t,r)=>{t.exports=e.x("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js"))},56704,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/work-async-storage.external.js",()=>require("next/dist/server/app-render/work-async-storage.external.js"))},32319,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/work-unit-async-storage.external.js",()=>require("next/dist/server/app-render/work-unit-async-storage.external.js"))},20635,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/action-async-storage.external.js",()=>require("next/dist/server/app-render/action-async-storage.external.js"))},24725,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/after-task-async-storage.external.js",()=>require("next/dist/server/app-render/after-task-async-storage.external.js"))},24361,(e,t,r)=>{t.exports=e.x("util",()=>require("util"))},29173,(e,t,r)=>{t.exports=e.x("@prisma/client",()=>require("@prisma/client"))},70406,(e,t,r)=>{t.exports=e.x("next/dist/compiled/@opentelemetry/api",()=>require("next/dist/compiled/@opentelemetry/api"))},93695,(e,t,r)=>{t.exports=e.x("next/dist/shared/lib/no-fallback-error.external.js",()=>require("next/dist/shared/lib/no-fallback-error.external.js"))},61724,(e,t,r)=>{t.exports=e.x("next/dist/compiled/next-server/app-route-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-route-turbo.runtime.prod.js"))},54799,(e,t,r)=>{t.exports=e.x("crypto",()=>require("crypto"))},92509,(e,t,r)=>{t.exports=e.x("url",()=>require("url"))},21517,(e,t,r)=>{t.exports=e.x("http",()=>require("http"))},49719,(e,t,r)=>{t.exports=e.x("assert",()=>require("assert"))},45706,(e,t,r)=>{t.exports=e.x("querystring",()=>require("querystring"))},874,(e,t,r)=>{t.exports=e.x("buffer",()=>require("buffer"))},6461,(e,t,r)=>{t.exports=e.x("zlib",()=>require("zlib"))},24836,(e,t,r)=>{t.exports=e.x("https",()=>require("https"))},27699,(e,t,r)=>{t.exports=e.x("events",()=>require("events"))},12756,e=>{e.v(JSON.parse('{"name":"openid-client","version":"5.7.1","description":"OpenID Connect Relying Party (RP, Client) implementation for Node.js runtime, supports passportjs","keywords":["auth","authentication","basic","certified","client","connect","dynamic","electron","hybrid","identity","implicit","oauth","oauth2","oidc","openid","passport","relying party","strategy"],"homepage":"https://github.com/panva/openid-client","repository":"panva/openid-client","funding":{"url":"https://github.com/sponsors/panva"},"license":"MIT","author":"Filip Skokan <<EMAIL>>","exports":{"types":"./types/index.d.ts","import":"./lib/index.mjs","require":"./lib/index.js"},"main":"./lib/index.js","types":"./types/index.d.ts","files":["lib","types/index.d.ts"],"scripts":{"format":"npx prettier --loglevel silent --write ./lib ./test ./certification ./types","test":"mocha test/**/*.test.js"},"dependencies":{"jose":"^4.15.9","lru-cache":"^6.0.0","object-hash":"^2.2.0","oidc-token-hash":"^5.0.3"},"devDependencies":{"@types/node":"^16.18.106","@types/passport":"^1.0.16","base64url":"^3.0.1","chai":"^4.5.0","mocha":"^10.7.3","nock":"^13.5.5","prettier":"^2.8.8","readable-mock-req":"^0.2.2","sinon":"^9.2.4","timekeeper":"^2.3.1"},"standard-version":{"scripts":{"postchangelog":"sed -i \'\' -e \'s/### \\\\[/## [/g\' CHANGELOG.md"},"types":[{"type":"feat","section":"Features"},{"type":"fix","section":"Fixes"},{"type":"chore","hidden":true},{"type":"docs","hidden":true},{"type":"style","hidden":true},{"type":"refactor","section":"Refactor","hidden":false},{"type":"perf","section":"Performance","hidden":false},{"type":"test","hidden":true}]}}'))},97376,(e,t,r)=>{},90818,e=>{"use strict";e.s(["handler",()=>A,"patchFetch",()=>N,"routeModule",()=>E,"serverHooks",()=>q,"workAsyncStorage",()=>j,"workUnitAsyncStorage",()=>k],90818);var t=e.i(6137),r=e.i(11365),a=e.i(9638),n=e.i(15243),s=e.i(66378),i=e.i(92101),o=e.i(50012),d=e.i(62885),l=e.i(31409),p=e.i(78448),u=e.i(28015),c=e.i(72721),x=e.i(75714),m=e.i(12634),h=e.i(93695);e.i(74732);var y=e.i(66662);e.s(["GET",()=>w,"POST",()=>b],94989);var f=e.i(2835),v=e.i(58356),g=e.i(43382),R=e.i(31279);async function w(e){try{let t=await (0,v.getServerSession)(g.authOptions);if(!t||"ADMIN"!==t.user.role)return f.NextResponse.json({error:"Unauthorized"},{status:401});let{searchParams:r}=new URL(e.url),a=r.get("termId"),n=r.get("classId"),s=(r.get("status"),{});a&&"all"!==a&&(s.termId=a),n&&"all"!==n&&(s.student={currentClassId:n});let i=await R.prisma.reportCard.findMany({where:s,include:{student:{include:{user:!0,currentClass:!0,currentSection:!0}},term:!0},orderBy:{generatedAt:"desc"}});return f.NextResponse.json(i)}catch(e){return console.error("Error fetching report cards:",e),f.NextResponse.json({error:"Internal server error"},{status:500})}}async function b(e){try{let r=await (0,v.getServerSession)(g.authOptions);if(!r||"ADMIN"!==r.user.role)return f.NextResponse.json({error:"Unauthorized"},{status:401});let{termId:a,classId:n}=await e.json(),s=await R.prisma.student.findMany({where:{currentClassId:n},include:{user:!0,currentClass:!0,currentSection:!0}}),i=[];for(let e of s){var t;let r=await R.prisma.mark.findMany({where:{studentId:e.id,exam:{termId:a}},include:{exam:{include:{subject:!0}}}}),n=0,s=0;r.forEach(e=>{n+=e.obtainedMarks,s+=e.exam.maxMarks});let o=s>0?n/s*100:0,d={studentId:e.id,termId:a,jsonSnapshot:{student:{name:`${e.user.firstName} ${e.user.lastName}`,admissionNo:e.admissionNo,class:e.currentClass?.name,section:e.currentSection?.name},marks:r.map(e=>({subject:e.exam.subject.name,exam:e.exam.name,obtainedMarks:e.obtainedMarks,maxMarks:e.exam.maxMarks,percentage:e.obtainedMarks/e.exam.maxMarks*100})),totalMarks:s,obtainedMarks:n,percentage:o,grade:(t=o)>=90?"A+":t>=80?"A":t>=70?"B+":t>=60?"B":t>=50?"C+":t>=40?"C":t>=30?"D":"F"}},l=await R.prisma.reportCard.findUnique({where:{studentId_termId:{studentId:e.id,termId:a}}});if(l){let e=await R.prisma.reportCard.update({where:{id:l.id},data:{jsonSnapshot:d.jsonSnapshot}});i.push(e)}else{let e=await R.prisma.reportCard.create({data:d});i.push(e)}}return f.NextResponse.json(i)}catch(e){return console.error("Error generating report cards:",e),f.NextResponse.json({error:"Internal server error"},{status:500})}}var C=e.i(94989);let E=new t.AppRouteRouteModule({definition:{kind:r.RouteKind.APP_ROUTE,page:"/api/admin/reports/route",pathname:"/api/admin/reports",filename:"route",bundlePath:""},distDir:".next",relativeProjectDir:"",resolvedPagePath:"[project]/school-management-system/src/app/api/admin/reports/route.ts",nextConfigOutput:"",userland:C}),{workAsyncStorage:j,workUnitAsyncStorage:k,serverHooks:q}=E;function N(){return(0,a.patchFetch)({workAsyncStorage:j,workUnitAsyncStorage:k})}async function A(e,t,a){var f;let v="/api/admin/reports/route";v=v.replace(/\/index$/,"")||"/";let g=await E.prepare(e,t,{srcPage:v,multiZoneDraftMode:!1});if(!g)return t.statusCode=400,t.end("Bad Request"),null==a.waitUntil||a.waitUntil.call(a,Promise.resolve()),null;let{buildId:R,params:w,nextConfig:b,isDraftMode:C,prerenderManifest:j,routerServerContext:k,isOnDemandRevalidate:q,revalidateOnlyGenerated:N,resolvedPathname:A}=g,I=(0,i.normalizeAppPath)(v),S=!!(j.dynamicRoutes[I]||j.routes[A]);if(S&&!C){let e=!!j.routes[A],t=j.dynamicRoutes[I];if(t&&!1===t.fallback&&!e)throw new h.NoFallbackError}let M=null;!S||E.isDev||C||(M="/index"===(M=A)?"/":M);let O=!0===E.isDev||!S,P=S&&!O,T=e.method||"GET",_=(0,s.getTracer)(),U=_.getActiveScopeSpan(),H={params:w,prerenderManifest:j,renderOpts:{experimental:{cacheComponents:!!b.experimental.cacheComponents,authInterrupts:!!b.experimental.authInterrupts},supportsDynamicResponse:O,incrementalCache:(0,n.getRequestMeta)(e,"incrementalCache"),cacheLifeProfiles:null==(f=b.experimental)?void 0:f.cacheLife,isRevalidate:P,waitUntil:a.waitUntil,onClose:e=>{t.on("close",e)},onAfterTaskError:void 0,onInstrumentationRequestError:(t,r,a)=>E.onRequestError(e,t,a,k)},sharedContext:{buildId:R}},D=new o.NodeNextRequest(e),F=new o.NodeNextResponse(t),$=d.NextRequestAdapter.fromNodeNextRequest(D,(0,d.signalFromNodeResponse)(t));try{let i=async r=>E.handle($,H).finally(()=>{if(!r)return;r.setAttributes({"http.status_code":t.statusCode,"next.rsc":!1});let a=_.getRootSpanAttributes();if(!a)return;if(a.get("next.span_type")!==l.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${a.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let n=a.get("next.route");if(n){let e=`${T} ${n}`;r.setAttributes({"next.route":n,"http.route":n,"next.span_name":e}),r.updateName(e)}else r.updateName(`${T} ${e.url}`)}),o=async s=>{var o,d;let l=async({previousCacheEntry:r})=>{try{if(!(0,n.getRequestMeta)(e,"minimalMode")&&q&&N&&!r)return t.statusCode=404,t.setHeader("x-nextjs-cache","REVALIDATED"),t.end("This page could not be found"),null;let o=await i(s);e.fetchMetrics=H.renderOpts.fetchMetrics;let d=H.renderOpts.pendingWaitUntil;d&&a.waitUntil&&(a.waitUntil(d),d=void 0);let l=H.renderOpts.collectedTags;if(!S)return await (0,u.sendResponse)(D,F,o,H.renderOpts.pendingWaitUntil),null;{let e=await o.blob(),t=(0,c.toNodeOutgoingHttpHeaders)(o.headers);l&&(t[m.NEXT_CACHE_TAGS_HEADER]=l),!t["content-type"]&&e.type&&(t["content-type"]=e.type);let r=void 0!==H.renderOpts.collectedRevalidate&&!(H.renderOpts.collectedRevalidate>=m.INFINITE_CACHE)&&H.renderOpts.collectedRevalidate,a=void 0===H.renderOpts.collectedExpire||H.renderOpts.collectedExpire>=m.INFINITE_CACHE?void 0:H.renderOpts.collectedExpire;return{value:{kind:y.CachedRouteKind.APP_ROUTE,status:o.status,body:Buffer.from(await e.arrayBuffer()),headers:t},cacheControl:{revalidate:r,expire:a}}}}catch(t){throw(null==r?void 0:r.isStale)&&await E.onRequestError(e,t,{routerKind:"App Router",routePath:v,routeType:"route",revalidateReason:(0,p.getRevalidateReason)({isRevalidate:P,isOnDemandRevalidate:q})},k),t}},h=await E.handleResponse({req:e,nextConfig:b,cacheKey:M,routeKind:r.RouteKind.APP_ROUTE,isFallback:!1,prerenderManifest:j,isRoutePPREnabled:!1,isOnDemandRevalidate:q,revalidateOnlyGenerated:N,responseGenerator:l,waitUntil:a.waitUntil});if(!S)return null;if((null==h||null==(o=h.value)?void 0:o.kind)!==y.CachedRouteKind.APP_ROUTE)throw Object.defineProperty(Error(`Invariant: app-route received invalid cache entry ${null==h||null==(d=h.value)?void 0:d.kind}`),"__NEXT_ERROR_CODE",{value:"E701",enumerable:!1,configurable:!0});(0,n.getRequestMeta)(e,"minimalMode")||t.setHeader("x-nextjs-cache",q?"REVALIDATED":h.isMiss?"MISS":h.isStale?"STALE":"HIT"),C&&t.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");let f=(0,c.fromNodeOutgoingHttpHeaders)(h.value.headers);return(0,n.getRequestMeta)(e,"minimalMode")&&S||f.delete(m.NEXT_CACHE_TAGS_HEADER),!h.cacheControl||t.getHeader("Cache-Control")||f.get("Cache-Control")||f.set("Cache-Control",(0,x.getCacheControlHeader)(h.cacheControl)),await (0,u.sendResponse)(D,F,new Response(h.value.body,{headers:f,status:h.value.status||200})),null};U?await o(U):await _.withPropagatedContext(e.headers,()=>_.trace(l.BaseServerSpan.handleRequest,{spanName:`${T} ${e.url}`,kind:s.SpanKind.SERVER,attributes:{"http.method":T,"http.target":e.url}},o))}catch(t){if(U||t instanceof h.NoFallbackError||await E.onRequestError(e,t,{routerKind:"App Router",routePath:I,routeType:"route",revalidateReason:(0,p.getRevalidateReason)({isRevalidate:P,isOnDemandRevalidate:q})}),S)throw t;return await (0,u.sendResponse)(D,F,new Response(null,{status:500})),null}}}];

//# sourceMappingURL=%5Broot-of-the-server%5D__8d04d530._.js.map