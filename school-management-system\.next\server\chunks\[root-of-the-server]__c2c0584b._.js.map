{"version": 3, "sources": ["turbopack:///[project]/school-management-system/src/lib/rbac.ts"], "sourcesContent": ["import { UserRole } from '@prisma/client'\r\n\r\n// Define permission types\r\nexport type Permission = \r\n  | 'users:read' | 'users:write' | 'users:delete'\r\n  | 'students:read' | 'students:write' | 'students:delete'\r\n  | 'teachers:read' | 'teachers:write' | 'teachers:delete'\r\n  | 'classes:read' | 'classes:write' | 'classes:delete'\r\n  | 'subjects:read' | 'subjects:write' | 'subjects:delete'\r\n  | 'attendance:read' | 'attendance:write'\r\n  | 'marks:read' | 'marks:write'\r\n  | 'reports:read' | 'reports:write'\r\n  | 'settings:read' | 'settings:write'\r\n  | 'audit:read'\r\n\r\n// Define role permissions\r\nconst rolePermissions: Record<UserRole, Permission[]> = {\r\n  ADMIN: [\r\n    'users:read', 'users:write', 'users:delete',\r\n    'students:read', 'students:write', 'students:delete',\r\n    'teachers:read', 'teachers:write', 'teachers:delete',\r\n    'classes:read', 'classes:write', 'classes:delete',\r\n    'subjects:read', 'subjects:write', 'subjects:delete',\r\n    'attendance:read', 'attendance:write',\r\n    'marks:read', 'marks:write',\r\n    'reports:read', 'reports:write',\r\n    'settings:read', 'settings:write',\r\n    'audit:read'\r\n  ],\r\n  TEACHER: [\r\n    'students:read',\r\n    'attendance:read', 'attendance:write',\r\n    'marks:read', 'marks:write',\r\n    'reports:read'\r\n  ],\r\n  STUDENT: [\r\n    'attendance:read',\r\n    'marks:read',\r\n    'reports:read'\r\n  ]\r\n}\r\n\r\n/**\r\n * Check if a user has a specific permission\r\n */\r\nexport function hasPermission(userRole: UserRole | string, permission: Permission): boolean {\r\n  const role = userRole as UserRole;\r\n  return rolePermissions[role]?.includes(permission) ?? false\r\n}\r\n\r\n/**\r\n * Check if a user can access a specific resource\r\n */\r\nexport function canAccess(userRole: UserRole | string, resource: string, action: 'read' | 'write' | 'delete'): boolean {\r\n  const permission = `${resource}:${action}` as Permission\r\n  return hasPermission(userRole, permission)\r\n}\r\n\r\n/**\r\n * Get all permissions for a role\r\n */\r\nexport function getRolePermissions(role: UserRole | string): Permission[] {\r\n  const userRole = role as UserRole;\r\n  return rolePermissions[userRole] ?? []\r\n}\r\n\r\n/**\r\n * Check if user can access student data (teachers can only see their assigned students)\r\n */\r\nexport function canAccessStudentData(userRole: UserRole | string, teacherId?: string, studentClassId?: string): boolean {\r\n  const role = userRole as UserRole;\r\n  if (role === 'ADMIN') return true\r\n  if (role === 'STUDENT') return false // Students can't access other students' data\r\n  \r\n  // For teachers, we'll need additional logic to check if they're assigned to the student's class\r\n  // This will be implemented when we add teacher-class assignments\r\n  return role === 'TEACHER'\r\n}\r\n\r\n/**\r\n * Check if user can access class data (teachers can only see their assigned classes)\r\n */\r\nexport function canAccessClassData(userRole: UserRole | string, teacherId?: string, classId?: string): boolean {\r\n  const role = userRole as UserRole;\r\n  if (role === 'ADMIN') return true\r\n  \r\n  // For teachers, we'll need additional logic to check if they're assigned to the class\r\n  // This will be implemented when we add teacher-class assignments\r\n  return role === 'TEACHER'\r\n}\r\n"], "names": [], "mappings": "4pHAgBA,IAAM,EAAkD,CACtD,MAAO,CACL,aAAc,cAAe,eAC7B,gBAAiB,iBAAkB,kBACnC,gBAAiB,iBAAkB,kBACnC,eAAgB,gBAAiB,iBACjC,gBAAiB,iBAAkB,kBACnC,kBAAmB,mBACnB,aAAc,cACd,eAAgB,gBAChB,gBAAiB,iBACjB,aACD,CACD,QAAS,CACP,gBACA,kBAAmB,mBACnB,aAAc,cACd,eACD,CACD,QAAS,CACP,kBACA,aACA,eAEJ,AADG,EAMI,SAAS,EAAc,CAA2B,CAAE,CAAsB,EAE/E,OAAO,CAAe,CADT,AACU,EAAK,EAAE,SAAS,KAAe,CACxD"}