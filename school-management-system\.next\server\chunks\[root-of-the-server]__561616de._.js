module.exports=[18622,(e,t,r)=>{t.exports=e.x("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js"))},56704,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/work-async-storage.external.js",()=>require("next/dist/server/app-render/work-async-storage.external.js"))},32319,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/work-unit-async-storage.external.js",()=>require("next/dist/server/app-render/work-unit-async-storage.external.js"))},20635,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/action-async-storage.external.js",()=>require("next/dist/server/app-render/action-async-storage.external.js"))},24725,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/after-task-async-storage.external.js",()=>require("next/dist/server/app-render/after-task-async-storage.external.js"))},24361,(e,t,r)=>{t.exports=e.x("util",()=>require("util"))},29173,(e,t,r)=>{t.exports=e.x("@prisma/client",()=>require("@prisma/client"))},70406,(e,t,r)=>{t.exports=e.x("next/dist/compiled/@opentelemetry/api",()=>require("next/dist/compiled/@opentelemetry/api"))},93695,(e,t,r)=>{t.exports=e.x("next/dist/shared/lib/no-fallback-error.external.js",()=>require("next/dist/shared/lib/no-fallback-error.external.js"))},61724,(e,t,r)=>{t.exports=e.x("next/dist/compiled/next-server/app-route-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-route-turbo.runtime.prod.js"))},54799,(e,t,r)=>{t.exports=e.x("crypto",()=>require("crypto"))},92509,(e,t,r)=>{t.exports=e.x("url",()=>require("url"))},21517,(e,t,r)=>{t.exports=e.x("http",()=>require("http"))},49719,(e,t,r)=>{t.exports=e.x("assert",()=>require("assert"))},45706,(e,t,r)=>{t.exports=e.x("querystring",()=>require("querystring"))},874,(e,t,r)=>{t.exports=e.x("buffer",()=>require("buffer"))},6461,(e,t,r)=>{t.exports=e.x("zlib",()=>require("zlib"))},24836,(e,t,r)=>{t.exports=e.x("https",()=>require("https"))},27699,(e,t,r)=>{t.exports=e.x("events",()=>require("events"))},12756,e=>{e.v(JSON.parse('{"name":"openid-client","version":"5.7.1","description":"OpenID Connect Relying Party (RP, Client) implementation for Node.js runtime, supports passportjs","keywords":["auth","authentication","basic","certified","client","connect","dynamic","electron","hybrid","identity","implicit","oauth","oauth2","oidc","openid","passport","relying party","strategy"],"homepage":"https://github.com/panva/openid-client","repository":"panva/openid-client","funding":{"url":"https://github.com/sponsors/panva"},"license":"MIT","author":"Filip Skokan <<EMAIL>>","exports":{"types":"./types/index.d.ts","import":"./lib/index.mjs","require":"./lib/index.js"},"main":"./lib/index.js","types":"./types/index.d.ts","files":["lib","types/index.d.ts"],"scripts":{"format":"npx prettier --loglevel silent --write ./lib ./test ./certification ./types","test":"mocha test/**/*.test.js"},"dependencies":{"jose":"^4.15.9","lru-cache":"^6.0.0","object-hash":"^2.2.0","oidc-token-hash":"^5.0.3"},"devDependencies":{"@types/node":"^16.18.106","@types/passport":"^1.0.16","base64url":"^3.0.1","chai":"^4.5.0","mocha":"^10.7.3","nock":"^13.5.5","prettier":"^2.8.8","readable-mock-req":"^0.2.2","sinon":"^9.2.4","timekeeper":"^2.3.1"},"standard-version":{"scripts":{"postchangelog":"sed -i \'\' -e \'s/### \\\\[/## [/g\' CHANGELOG.md"},"types":[{"type":"feat","section":"Features"},{"type":"fix","section":"Fixes"},{"type":"chore","hidden":true},{"type":"docs","hidden":true},{"type":"style","hidden":true},{"type":"refactor","section":"Refactor","hidden":false},{"type":"perf","section":"Performance","hidden":false},{"type":"test","hidden":true}]}}'))},69695,(e,t,r)=>{},83125,e=>{"use strict";e.s(["handler",()=>N,"patchFetch",()=>A,"routeModule",()=>b,"serverHooks",()=>j,"workAsyncStorage",()=>C,"workUnitAsyncStorage",()=>q],83125);var t=e.i(6137),r=e.i(11365),n=e.i(9638),s=e.i(15243),a=e.i(66378),i=e.i(92101),o=e.i(50012),p=e.i(62885),l=e.i(31409),d=e.i(78448),u=e.i(28015),c=e.i(72721),x=e.i(75714),h=e.i(12634),m=e.i(93695);e.i(74732);var y=e.i(66662);e.s(["GET",()=>w],58007);var v=e.i(2835),f=e.i(58356),R=e.i(43382),g=e.i(31279);async function w(e){try{let t=await (0,f.getServerSession)(R.authOptions);if(!t||"STUDENT"!==t.user.role)return v.NextResponse.json({error:"Unauthorized"},{status:401});let r=await g.prisma.student.findUnique({where:{userId:t.user.id}});if(!r)return v.NextResponse.json({error:"Student not found"},{status:404});let{searchParams:n}=new URL(e.url),s=n.get("termId"),a={studentId:r.id};s&&"all"!==s&&(a.termId=s);let i=await g.prisma.reportCard.findMany({where:a,include:{term:!0},orderBy:{generatedAt:"desc"}});return v.NextResponse.json(i)}catch(e){return console.error("Error fetching student report cards:",e),v.NextResponse.json({error:"Internal server error"},{status:500})}}var E=e.i(58007);let b=new t.AppRouteRouteModule({definition:{kind:r.RouteKind.APP_ROUTE,page:"/api/student/reports/route",pathname:"/api/student/reports",filename:"route",bundlePath:""},distDir:".next",relativeProjectDir:"",resolvedPagePath:"[project]/school-management-system/src/app/api/student/reports/route.ts",nextConfigOutput:"",userland:E}),{workAsyncStorage:C,workUnitAsyncStorage:q,serverHooks:j}=b;function A(){return(0,n.patchFetch)({workAsyncStorage:C,workUnitAsyncStorage:q})}async function N(e,t,n){var v;let f="/api/student/reports/route";f=f.replace(/\/index$/,"")||"/";let R=await b.prepare(e,t,{srcPage:f,multiZoneDraftMode:!1});if(!R)return t.statusCode=400,t.end("Bad Request"),null==n.waitUntil||n.waitUntil.call(n,Promise.resolve()),null;let{buildId:g,params:w,nextConfig:E,isDraftMode:C,prerenderManifest:q,routerServerContext:j,isOnDemandRevalidate:A,revalidateOnlyGenerated:N,resolvedPathname:k}=R,T=(0,i.normalizeAppPath)(f),O=!!(q.dynamicRoutes[T]||q.routes[k]);if(O&&!C){let e=!!q.routes[k],t=q.dynamicRoutes[T];if(t&&!1===t.fallback&&!e)throw new m.NoFallbackError}let P=null;!O||b.isDev||C||(P="/index"===(P=k)?"/":P);let S=!0===b.isDev||!O,I=O&&!S,U=e.method||"GET",_=(0,a.getTracer)(),H=_.getActiveScopeSpan(),M={params:w,prerenderManifest:q,renderOpts:{experimental:{cacheComponents:!!E.experimental.cacheComponents,authInterrupts:!!E.experimental.authInterrupts},supportsDynamicResponse:S,incrementalCache:(0,s.getRequestMeta)(e,"incrementalCache"),cacheLifeProfiles:null==(v=E.experimental)?void 0:v.cacheLife,isRevalidate:I,waitUntil:n.waitUntil,onClose:e=>{t.on("close",e)},onAfterTaskError:void 0,onInstrumentationRequestError:(t,r,n)=>b.onRequestError(e,t,n,j)},sharedContext:{buildId:g}},D=new o.NodeNextRequest(e),F=new o.NodeNextResponse(t),$=p.NextRequestAdapter.fromNodeNextRequest(D,(0,p.signalFromNodeResponse)(t));try{let i=async r=>b.handle($,M).finally(()=>{if(!r)return;r.setAttributes({"http.status_code":t.statusCode,"next.rsc":!1});let n=_.getRootSpanAttributes();if(!n)return;if(n.get("next.span_type")!==l.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${n.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let s=n.get("next.route");if(s){let e=`${U} ${s}`;r.setAttributes({"next.route":s,"http.route":s,"next.span_name":e}),r.updateName(e)}else r.updateName(`${U} ${e.url}`)}),o=async a=>{var o,p;let l=async({previousCacheEntry:r})=>{try{if(!(0,s.getRequestMeta)(e,"minimalMode")&&A&&N&&!r)return t.statusCode=404,t.setHeader("x-nextjs-cache","REVALIDATED"),t.end("This page could not be found"),null;let o=await i(a);e.fetchMetrics=M.renderOpts.fetchMetrics;let p=M.renderOpts.pendingWaitUntil;p&&n.waitUntil&&(n.waitUntil(p),p=void 0);let l=M.renderOpts.collectedTags;if(!O)return await (0,u.sendResponse)(D,F,o,M.renderOpts.pendingWaitUntil),null;{let e=await o.blob(),t=(0,c.toNodeOutgoingHttpHeaders)(o.headers);l&&(t[h.NEXT_CACHE_TAGS_HEADER]=l),!t["content-type"]&&e.type&&(t["content-type"]=e.type);let r=void 0!==M.renderOpts.collectedRevalidate&&!(M.renderOpts.collectedRevalidate>=h.INFINITE_CACHE)&&M.renderOpts.collectedRevalidate,n=void 0===M.renderOpts.collectedExpire||M.renderOpts.collectedExpire>=h.INFINITE_CACHE?void 0:M.renderOpts.collectedExpire;return{value:{kind:y.CachedRouteKind.APP_ROUTE,status:o.status,body:Buffer.from(await e.arrayBuffer()),headers:t},cacheControl:{revalidate:r,expire:n}}}}catch(t){throw(null==r?void 0:r.isStale)&&await b.onRequestError(e,t,{routerKind:"App Router",routePath:f,routeType:"route",revalidateReason:(0,d.getRevalidateReason)({isRevalidate:I,isOnDemandRevalidate:A})},j),t}},m=await b.handleResponse({req:e,nextConfig:E,cacheKey:P,routeKind:r.RouteKind.APP_ROUTE,isFallback:!1,prerenderManifest:q,isRoutePPREnabled:!1,isOnDemandRevalidate:A,revalidateOnlyGenerated:N,responseGenerator:l,waitUntil:n.waitUntil});if(!O)return null;if((null==m||null==(o=m.value)?void 0:o.kind)!==y.CachedRouteKind.APP_ROUTE)throw Object.defineProperty(Error(`Invariant: app-route received invalid cache entry ${null==m||null==(p=m.value)?void 0:p.kind}`),"__NEXT_ERROR_CODE",{value:"E701",enumerable:!1,configurable:!0});(0,s.getRequestMeta)(e,"minimalMode")||t.setHeader("x-nextjs-cache",A?"REVALIDATED":m.isMiss?"MISS":m.isStale?"STALE":"HIT"),C&&t.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");let v=(0,c.fromNodeOutgoingHttpHeaders)(m.value.headers);return(0,s.getRequestMeta)(e,"minimalMode")&&O||v.delete(h.NEXT_CACHE_TAGS_HEADER),!m.cacheControl||t.getHeader("Cache-Control")||v.get("Cache-Control")||v.set("Cache-Control",(0,x.getCacheControlHeader)(m.cacheControl)),await (0,u.sendResponse)(D,F,new Response(m.value.body,{headers:v,status:m.value.status||200})),null};H?await o(H):await _.withPropagatedContext(e.headers,()=>_.trace(l.BaseServerSpan.handleRequest,{spanName:`${U} ${e.url}`,kind:a.SpanKind.SERVER,attributes:{"http.method":U,"http.target":e.url}},o))}catch(t){if(H||t instanceof m.NoFallbackError||await b.onRequestError(e,t,{routerKind:"App Router",routePath:T,routeType:"route",revalidateReason:(0,d.getRevalidateReason)({isRevalidate:I,isOnDemandRevalidate:A})}),O)throw t;return await (0,u.sendResponse)(D,F,new Response(null,{status:500})),null}}}];

//# sourceMappingURL=%5Broot-of-the-server%5D__561616de._.js.map