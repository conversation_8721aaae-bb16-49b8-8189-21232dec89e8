module.exports=[18622,(e,t,r)=>{t.exports=e.x("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js"))},56704,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/work-async-storage.external.js",()=>require("next/dist/server/app-render/work-async-storage.external.js"))},32319,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/work-unit-async-storage.external.js",()=>require("next/dist/server/app-render/work-unit-async-storage.external.js"))},20635,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/action-async-storage.external.js",()=>require("next/dist/server/app-render/action-async-storage.external.js"))},24725,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/after-task-async-storage.external.js",()=>require("next/dist/server/app-render/after-task-async-storage.external.js"))},29173,(e,t,r)=>{t.exports=e.x("@prisma/client",()=>require("@prisma/client"))},70406,(e,t,r)=>{t.exports=e.x("next/dist/compiled/@opentelemetry/api",()=>require("next/dist/compiled/@opentelemetry/api"))},93695,(e,t,r)=>{t.exports=e.x("next/dist/shared/lib/no-fallback-error.external.js",()=>require("next/dist/shared/lib/no-fallback-error.external.js"))},61724,(e,t,r)=>{t.exports=e.x("next/dist/compiled/next-server/app-route-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-route-turbo.runtime.prod.js"))},54799,(e,t,r)=>{t.exports=e.x("crypto",()=>require("crypto"))},64643,(e,t,r)=>{},51934,e=>{"use strict";e.s(["handler",()=>I,"patchFetch",()=>P,"routeModule",()=>b,"serverHooks",()=>q,"workAsyncStorage",()=>A,"workUnitAsyncStorage",()=>j],51934);var t=e.i(6137),r=e.i(11365),a=e.i(9638),n=e.i(15243),s=e.i(66378),i=e.i(92101),o=e.i(50012),l=e.i(62885),d=e.i(31409),u=e.i(78448),p=e.i(28015),c=e.i(72721),m=e.i(75714),x=e.i(12634),h=e.i(93695);e.i(74732);var g=e.i(66662);e.s(["GET",()=>E,"POST",()=>C],35123);var f=e.i(2835),R=e.i(31279),v=e.i(47504),w=e.i(24638);let N=v.z.object({firstName:v.z.string().min(1,"First name is required"),lastName:v.z.string().min(1,"Last name is required"),email:v.z.string().email("Invalid email address"),dateOfBirth:v.z.string().regex(/^\d{4}-\d{2}-\d{2}$/,"Date must be in YYYY-MM-DD format"),gender:v.z.enum(["MALE","FEMALE","OTHER"]),phoneNumber:v.z.string().optional(),address:v.z.string().optional(),classId:v.z.string().min(1,"Class ID is required"),parentName:v.z.string().optional(),parentPhone:v.z.string().optional(),rollNumber:v.z.string().optional()});async function E(e){try{let{searchParams:t}=new URL(e.url),r=parseInt(t.get("page")||"1"),a=parseInt(t.get("limit")||"10"),n=t.get("search")||"",s=t.get("classId")||"",i=t.get("gender")||"",o=await R.prisma.student.findMany({include:{user:!0,currentClass:!0,currentSection:!0},orderBy:{createdAt:"desc"}}),l=o;n&&(l=o.filter(e=>e.user?.firstName?.toLowerCase().includes(n.toLowerCase())||e.user?.lastName?.toLowerCase().includes(n.toLowerCase())||e.user?.email?.toLowerCase().includes(n.toLowerCase()))),s&&(l=l.filter(e=>e.currentClassId===parseInt(s))),i&&(l=l.filter(e=>e.gender===i));let d=l.length,u=Math.ceil(d/a),p=(r-1)*a,c=l.slice(p,p+a);return f.NextResponse.json({students:c,pagination:{page:r,limit:a,totalCount:d,totalPages:u,hasNextPage:r<u,hasPrevPage:r>1}})}catch(e){return console.error("Error fetching students:",e),f.NextResponse.json({error:"Internal server error"},{status:500})}}async function C(e){try{let t=await e.json(),r=N.parse(t);if(await R.prisma.user.findUnique({where:{email:r.email}}))return f.NextResponse.json({error:"A user with this email already exists"},{status:400});let[a,n]=r.classId.split("-");if(!await R.prisma.class.findUnique({where:{id:a}}))return f.NextResponse.json({error:"Class not found"},{status:400});if(n&&!await R.prisma.section.findUnique({where:{id:n}}))return f.NextResponse.json({error:"Section not found"},{status:400});let s=await w.default.hash("Student@12345",12),i=await R.prisma.user.create({data:{email:r.email,hashedPassword:s,role:"STUDENT",firstName:r.firstName,lastName:r.lastName,phone:r.phoneNumber}}),o=await R.prisma.student.create({data:{userId:i.id,admissionNo:`STU${Date.now()}`,dob:new Date(r.dateOfBirth),gender:r.gender,address:r.address,guardianName:r.parentName||"Guardian",guardianPhone:r.parentPhone||"",currentClassId:a,currentSectionId:n||null,rollNumber:r.rollNumber},include:{user:!0,currentClass:!0,currentSection:!0}});return f.NextResponse.json({message:"Student created successfully",student:o},{status:201})}catch(e){if(e instanceof v.z.ZodError)return f.NextResponse.json({error:"Validation error",details:e.format()},{status:400});return console.error("Error creating student:",e),f.NextResponse.json({error:"Internal server error"},{status:500})}}var y=e.i(35123);let b=new t.AppRouteRouteModule({definition:{kind:r.RouteKind.APP_ROUTE,page:"/api/admin/students/route",pathname:"/api/admin/students",filename:"route",bundlePath:""},distDir:".next",relativeProjectDir:"",resolvedPagePath:"[project]/school-management-system/src/app/api/admin/students/route.ts",nextConfigOutput:"",userland:y}),{workAsyncStorage:A,workUnitAsyncStorage:j,serverHooks:q}=b;function P(){return(0,a.patchFetch)({workAsyncStorage:A,workUnitAsyncStorage:j})}async function I(e,t,a){var f;let R="/api/admin/students/route";R=R.replace(/\/index$/,"")||"/";let v=await b.prepare(e,t,{srcPage:R,multiZoneDraftMode:!1});if(!v)return t.statusCode=400,t.end("Bad Request"),null==a.waitUntil||a.waitUntil.call(a,Promise.resolve()),null;let{buildId:w,params:N,nextConfig:E,isDraftMode:C,prerenderManifest:y,routerServerContext:A,isOnDemandRevalidate:j,revalidateOnlyGenerated:q,resolvedPathname:P}=v,I=(0,i.normalizeAppPath)(R),T=!!(y.dynamicRoutes[I]||y.routes[P]);if(T&&!C){let e=!!y.routes[P],t=y.dynamicRoutes[I];if(t&&!1===t.fallback&&!e)throw new h.NoFallbackError}let S=null;!T||b.isDev||C||(S="/index"===(S=P)?"/":S);let O=!0===b.isDev||!T,U=T&&!O,k=e.method||"GET",M=(0,s.getTracer)(),_=M.getActiveScopeSpan(),D={params:N,prerenderManifest:y,renderOpts:{experimental:{cacheComponents:!!E.experimental.cacheComponents,authInterrupts:!!E.experimental.authInterrupts},supportsDynamicResponse:O,incrementalCache:(0,n.getRequestMeta)(e,"incrementalCache"),cacheLifeProfiles:null==(f=E.experimental)?void 0:f.cacheLife,isRevalidate:U,waitUntil:a.waitUntil,onClose:e=>{t.on("close",e)},onAfterTaskError:void 0,onInstrumentationRequestError:(t,r,a)=>b.onRequestError(e,t,a,A)},sharedContext:{buildId:w}},H=new o.NodeNextRequest(e),L=new o.NodeNextResponse(t),z=l.NextRequestAdapter.fromNodeNextRequest(H,(0,l.signalFromNodeResponse)(t));try{let i=async r=>b.handle(z,D).finally(()=>{if(!r)return;r.setAttributes({"http.status_code":t.statusCode,"next.rsc":!1});let a=M.getRootSpanAttributes();if(!a)return;if(a.get("next.span_type")!==d.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${a.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let n=a.get("next.route");if(n){let e=`${k} ${n}`;r.setAttributes({"next.route":n,"http.route":n,"next.span_name":e}),r.updateName(e)}else r.updateName(`${k} ${e.url}`)}),o=async s=>{var o,l;let d=async({previousCacheEntry:r})=>{try{if(!(0,n.getRequestMeta)(e,"minimalMode")&&j&&q&&!r)return t.statusCode=404,t.setHeader("x-nextjs-cache","REVALIDATED"),t.end("This page could not be found"),null;let o=await i(s);e.fetchMetrics=D.renderOpts.fetchMetrics;let l=D.renderOpts.pendingWaitUntil;l&&a.waitUntil&&(a.waitUntil(l),l=void 0);let d=D.renderOpts.collectedTags;if(!T)return await (0,p.sendResponse)(H,L,o,D.renderOpts.pendingWaitUntil),null;{let e=await o.blob(),t=(0,c.toNodeOutgoingHttpHeaders)(o.headers);d&&(t[x.NEXT_CACHE_TAGS_HEADER]=d),!t["content-type"]&&e.type&&(t["content-type"]=e.type);let r=void 0!==D.renderOpts.collectedRevalidate&&!(D.renderOpts.collectedRevalidate>=x.INFINITE_CACHE)&&D.renderOpts.collectedRevalidate,a=void 0===D.renderOpts.collectedExpire||D.renderOpts.collectedExpire>=x.INFINITE_CACHE?void 0:D.renderOpts.collectedExpire;return{value:{kind:g.CachedRouteKind.APP_ROUTE,status:o.status,body:Buffer.from(await e.arrayBuffer()),headers:t},cacheControl:{revalidate:r,expire:a}}}}catch(t){throw(null==r?void 0:r.isStale)&&await b.onRequestError(e,t,{routerKind:"App Router",routePath:R,routeType:"route",revalidateReason:(0,u.getRevalidateReason)({isRevalidate:U,isOnDemandRevalidate:j})},A),t}},h=await b.handleResponse({req:e,nextConfig:E,cacheKey:S,routeKind:r.RouteKind.APP_ROUTE,isFallback:!1,prerenderManifest:y,isRoutePPREnabled:!1,isOnDemandRevalidate:j,revalidateOnlyGenerated:q,responseGenerator:d,waitUntil:a.waitUntil});if(!T)return null;if((null==h||null==(o=h.value)?void 0:o.kind)!==g.CachedRouteKind.APP_ROUTE)throw Object.defineProperty(Error(`Invariant: app-route received invalid cache entry ${null==h||null==(l=h.value)?void 0:l.kind}`),"__NEXT_ERROR_CODE",{value:"E701",enumerable:!1,configurable:!0});(0,n.getRequestMeta)(e,"minimalMode")||t.setHeader("x-nextjs-cache",j?"REVALIDATED":h.isMiss?"MISS":h.isStale?"STALE":"HIT"),C&&t.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");let f=(0,c.fromNodeOutgoingHttpHeaders)(h.value.headers);return(0,n.getRequestMeta)(e,"minimalMode")&&T||f.delete(x.NEXT_CACHE_TAGS_HEADER),!h.cacheControl||t.getHeader("Cache-Control")||f.get("Cache-Control")||f.set("Cache-Control",(0,m.getCacheControlHeader)(h.cacheControl)),await (0,p.sendResponse)(H,L,new Response(h.value.body,{headers:f,status:h.value.status||200})),null};_?await o(_):await M.withPropagatedContext(e.headers,()=>M.trace(d.BaseServerSpan.handleRequest,{spanName:`${k} ${e.url}`,kind:s.SpanKind.SERVER,attributes:{"http.method":k,"http.target":e.url}},o))}catch(t){if(_||t instanceof h.NoFallbackError||await b.onRequestError(e,t,{routerKind:"App Router",routePath:I,routeType:"route",revalidateReason:(0,u.getRevalidateReason)({isRevalidate:U,isOnDemandRevalidate:j})}),T)throw t;return await (0,p.sendResponse)(H,L,new Response(null,{status:500})),null}}}];

//# sourceMappingURL=%5Broot-of-the-server%5D__9e49a448._.js.map