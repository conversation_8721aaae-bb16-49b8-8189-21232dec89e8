module.exports=[18622,(e,t,r)=>{t.exports=e.x("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js"))},56704,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/work-async-storage.external.js",()=>require("next/dist/server/app-render/work-async-storage.external.js"))},32319,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/work-unit-async-storage.external.js",()=>require("next/dist/server/app-render/work-unit-async-storage.external.js"))},20635,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/action-async-storage.external.js",()=>require("next/dist/server/app-render/action-async-storage.external.js"))},24725,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/after-task-async-storage.external.js",()=>require("next/dist/server/app-render/after-task-async-storage.external.js"))},24361,(e,t,r)=>{t.exports=e.x("util",()=>require("util"))},29173,(e,t,r)=>{t.exports=e.x("@prisma/client",()=>require("@prisma/client"))},70406,(e,t,r)=>{t.exports=e.x("next/dist/compiled/@opentelemetry/api",()=>require("next/dist/compiled/@opentelemetry/api"))},93695,(e,t,r)=>{t.exports=e.x("next/dist/shared/lib/no-fallback-error.external.js",()=>require("next/dist/shared/lib/no-fallback-error.external.js"))},61724,(e,t,r)=>{t.exports=e.x("next/dist/compiled/next-server/app-route-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-route-turbo.runtime.prod.js"))},54799,(e,t,r)=>{t.exports=e.x("crypto",()=>require("crypto"))},92509,(e,t,r)=>{t.exports=e.x("url",()=>require("url"))},21517,(e,t,r)=>{t.exports=e.x("http",()=>require("http"))},49719,(e,t,r)=>{t.exports=e.x("assert",()=>require("assert"))},45706,(e,t,r)=>{t.exports=e.x("querystring",()=>require("querystring"))},874,(e,t,r)=>{t.exports=e.x("buffer",()=>require("buffer"))},6461,(e,t,r)=>{t.exports=e.x("zlib",()=>require("zlib"))},24836,(e,t,r)=>{t.exports=e.x("https",()=>require("https"))},27699,(e,t,r)=>{t.exports=e.x("events",()=>require("events"))},12756,e=>{e.v(JSON.parse('{"name":"openid-client","version":"5.7.1","description":"OpenID Connect Relying Party (RP, Client) implementation for Node.js runtime, supports passportjs","keywords":["auth","authentication","basic","certified","client","connect","dynamic","electron","hybrid","identity","implicit","oauth","oauth2","oidc","openid","passport","relying party","strategy"],"homepage":"https://github.com/panva/openid-client","repository":"panva/openid-client","funding":{"url":"https://github.com/sponsors/panva"},"license":"MIT","author":"Filip Skokan <<EMAIL>>","exports":{"types":"./types/index.d.ts","import":"./lib/index.mjs","require":"./lib/index.js"},"main":"./lib/index.js","types":"./types/index.d.ts","files":["lib","types/index.d.ts"],"scripts":{"format":"npx prettier --loglevel silent --write ./lib ./test ./certification ./types","test":"mocha test/**/*.test.js"},"dependencies":{"jose":"^4.15.9","lru-cache":"^6.0.0","object-hash":"^2.2.0","oidc-token-hash":"^5.0.3"},"devDependencies":{"@types/node":"^16.18.106","@types/passport":"^1.0.16","base64url":"^3.0.1","chai":"^4.5.0","mocha":"^10.7.3","nock":"^13.5.5","prettier":"^2.8.8","readable-mock-req":"^0.2.2","sinon":"^9.2.4","timekeeper":"^2.3.1"},"standard-version":{"scripts":{"postchangelog":"sed -i \'\' -e \'s/### \\\\[/## [/g\' CHANGELOG.md"},"types":[{"type":"feat","section":"Features"},{"type":"fix","section":"Fixes"},{"type":"chore","hidden":true},{"type":"docs","hidden":true},{"type":"style","hidden":true},{"type":"refactor","section":"Refactor","hidden":false},{"type":"perf","section":"Performance","hidden":false},{"type":"test","hidden":true}]}}'))},54828,(e,t,r)=>{},644,e=>{"use strict";e.s(["handler",()=>A,"patchFetch",()=>j,"routeModule",()=>b,"serverHooks",()=>N,"workAsyncStorage",()=>q,"workUnitAsyncStorage",()=>C],644);var t=e.i(6137),r=e.i(11365),n=e.i(9638),a=e.i(15243),s=e.i(66378),i=e.i(92101),o=e.i(50012),d=e.i(62885),l=e.i(31409),p=e.i(78448),u=e.i(28015),c=e.i(72721),x=e.i(75714),h=e.i(12634),m=e.i(93695);e.i(74732);var f=e.i(66662);e.s(["GET",()=>R],94313);var g=e.i(2835),y=e.i(58356),v=e.i(43382),w=e.i(31279);async function R(e){try{let t=await (0,y.getServerSession)(v.authOptions);if(!t?.user||"STUDENT"!==t.user.role)return g.NextResponse.json({error:"Unauthorized"},{status:401});let{searchParams:r}=new URL(e.url),n=r.get("startDate"),a=r.get("endDate"),s=parseInt(r.get("page")||"1"),i=parseInt(r.get("limit")||"10"),o=(s-1)*i,d={};if("STUDENT"===t.user.role){let e=await w.prisma.student.findUnique({where:{userId:t.user.id}});if(!e)return g.NextResponse.json({error:"Student profile not found"},{status:404});d.studentId=e.id}n&&a&&(d.date={gte:new Date(n),lte:new Date(a)});let[l,p]=await Promise.all([w.prisma.attendance.findMany({where:d,skip:o,take:i,orderBy:{date:"desc"},include:{class:{select:{id:!0,name:!0,section:{select:{name:!0}}}}}}),w.prisma.attendance.count({where:d})]),u=await w.prisma.student.findUnique({where:{userId:t.user.id}});if(!u)return g.NextResponse.json({error:"Student profile not found"},{status:404});let c=await w.prisma.attendance.count({where:{studentId:u.id,...n&&a&&{date:{gte:new Date(n),lte:new Date(a)}}}}),x=await w.prisma.attendance.count({where:{studentId:u.id,status:"PRESENT",...n&&a&&{date:{gte:new Date(n),lte:new Date(a)}}}}),h=await w.prisma.attendance.count({where:{studentId:u.id,status:"ABSENT",...n&&a&&{date:{gte:new Date(n),lte:new Date(a)}}}}),m=await w.prisma.attendance.count({where:{studentId:u.id,status:"LATE",...n&&a&&{date:{gte:new Date(n),lte:new Date(a)}}}}),f=await w.prisma.attendance.count({where:{studentId:u.id,status:"HALF_DAY",...n&&a&&{date:{gte:new Date(n),lte:new Date(a)}}}}),R=Math.ceil(p/i),E=c>0?x/c*100:0;return g.NextResponse.json({attendanceRecords:l,statistics:{total:c,present:x,absent:h,late:m,halfDay:f,percentage:Math.round(100*E)/100},pagination:{page:s,limit:i,total:p,totalPages:R}})}catch(e){return console.error("Error fetching student attendance:",e),g.NextResponse.json({error:"Failed to fetch attendance"},{status:500})}}var E=e.i(94313);let b=new t.AppRouteRouteModule({definition:{kind:r.RouteKind.APP_ROUTE,page:"/api/student/attendance/route",pathname:"/api/student/attendance",filename:"route",bundlePath:""},distDir:".next",relativeProjectDir:"",resolvedPagePath:"[project]/school-management-system/src/app/api/student/attendance/route.ts",nextConfigOutput:"",userland:E}),{workAsyncStorage:q,workUnitAsyncStorage:C,serverHooks:N}=b;function j(){return(0,n.patchFetch)({workAsyncStorage:q,workUnitAsyncStorage:C})}async function A(e,t,n){var g;let y="/api/student/attendance/route";y=y.replace(/\/index$/,"")||"/";let v=await b.prepare(e,t,{srcPage:y,multiZoneDraftMode:!1});if(!v)return t.statusCode=400,t.end("Bad Request"),null==n.waitUntil||n.waitUntil.call(n,Promise.resolve()),null;let{buildId:w,params:R,nextConfig:E,isDraftMode:q,prerenderManifest:C,routerServerContext:N,isOnDemandRevalidate:j,revalidateOnlyGenerated:A,resolvedPathname:D}=v,T=(0,i.normalizeAppPath)(y),k=!!(C.dynamicRoutes[T]||C.routes[D]);if(k&&!q){let e=!!C.routes[D],t=C.dynamicRoutes[T];if(t&&!1===t.fallback&&!e)throw new m.NoFallbackError}let S=null;!k||b.isDev||q||(S="/index"===(S=D)?"/":S);let P=!0===b.isDev||!k,I=k&&!P,O=e.method||"GET",U=(0,s.getTracer)(),_=U.getActiveScopeSpan(),H={params:R,prerenderManifest:C,renderOpts:{experimental:{cacheComponents:!!E.experimental.cacheComponents,authInterrupts:!!E.experimental.authInterrupts},supportsDynamicResponse:P,incrementalCache:(0,a.getRequestMeta)(e,"incrementalCache"),cacheLifeProfiles:null==(g=E.experimental)?void 0:g.cacheLife,isRevalidate:I,waitUntil:n.waitUntil,onClose:e=>{t.on("close",e)},onAfterTaskError:void 0,onInstrumentationRequestError:(t,r,n)=>b.onRequestError(e,t,n,N)},sharedContext:{buildId:w}},M=new o.NodeNextRequest(e),F=new o.NodeNextResponse(t),L=d.NextRequestAdapter.fromNodeNextRequest(M,(0,d.signalFromNodeResponse)(t));try{let i=async r=>b.handle(L,H).finally(()=>{if(!r)return;r.setAttributes({"http.status_code":t.statusCode,"next.rsc":!1});let n=U.getRootSpanAttributes();if(!n)return;if(n.get("next.span_type")!==l.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${n.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let a=n.get("next.route");if(a){let e=`${O} ${a}`;r.setAttributes({"next.route":a,"http.route":a,"next.span_name":e}),r.updateName(e)}else r.updateName(`${O} ${e.url}`)}),o=async s=>{var o,d;let l=async({previousCacheEntry:r})=>{try{if(!(0,a.getRequestMeta)(e,"minimalMode")&&j&&A&&!r)return t.statusCode=404,t.setHeader("x-nextjs-cache","REVALIDATED"),t.end("This page could not be found"),null;let o=await i(s);e.fetchMetrics=H.renderOpts.fetchMetrics;let d=H.renderOpts.pendingWaitUntil;d&&n.waitUntil&&(n.waitUntil(d),d=void 0);let l=H.renderOpts.collectedTags;if(!k)return await (0,u.sendResponse)(M,F,o,H.renderOpts.pendingWaitUntil),null;{let e=await o.blob(),t=(0,c.toNodeOutgoingHttpHeaders)(o.headers);l&&(t[h.NEXT_CACHE_TAGS_HEADER]=l),!t["content-type"]&&e.type&&(t["content-type"]=e.type);let r=void 0!==H.renderOpts.collectedRevalidate&&!(H.renderOpts.collectedRevalidate>=h.INFINITE_CACHE)&&H.renderOpts.collectedRevalidate,n=void 0===H.renderOpts.collectedExpire||H.renderOpts.collectedExpire>=h.INFINITE_CACHE?void 0:H.renderOpts.collectedExpire;return{value:{kind:f.CachedRouteKind.APP_ROUTE,status:o.status,body:Buffer.from(await e.arrayBuffer()),headers:t},cacheControl:{revalidate:r,expire:n}}}}catch(t){throw(null==r?void 0:r.isStale)&&await b.onRequestError(e,t,{routerKind:"App Router",routePath:y,routeType:"route",revalidateReason:(0,p.getRevalidateReason)({isRevalidate:I,isOnDemandRevalidate:j})},N),t}},m=await b.handleResponse({req:e,nextConfig:E,cacheKey:S,routeKind:r.RouteKind.APP_ROUTE,isFallback:!1,prerenderManifest:C,isRoutePPREnabled:!1,isOnDemandRevalidate:j,revalidateOnlyGenerated:A,responseGenerator:l,waitUntil:n.waitUntil});if(!k)return null;if((null==m||null==(o=m.value)?void 0:o.kind)!==f.CachedRouteKind.APP_ROUTE)throw Object.defineProperty(Error(`Invariant: app-route received invalid cache entry ${null==m||null==(d=m.value)?void 0:d.kind}`),"__NEXT_ERROR_CODE",{value:"E701",enumerable:!1,configurable:!0});(0,a.getRequestMeta)(e,"minimalMode")||t.setHeader("x-nextjs-cache",j?"REVALIDATED":m.isMiss?"MISS":m.isStale?"STALE":"HIT"),q&&t.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");let g=(0,c.fromNodeOutgoingHttpHeaders)(m.value.headers);return(0,a.getRequestMeta)(e,"minimalMode")&&k||g.delete(h.NEXT_CACHE_TAGS_HEADER),!m.cacheControl||t.getHeader("Cache-Control")||g.get("Cache-Control")||g.set("Cache-Control",(0,x.getCacheControlHeader)(m.cacheControl)),await (0,u.sendResponse)(M,F,new Response(m.value.body,{headers:g,status:m.value.status||200})),null};_?await o(_):await U.withPropagatedContext(e.headers,()=>U.trace(l.BaseServerSpan.handleRequest,{spanName:`${O} ${e.url}`,kind:s.SpanKind.SERVER,attributes:{"http.method":O,"http.target":e.url}},o))}catch(t){if(_||t instanceof m.NoFallbackError||await b.onRequestError(e,t,{routerKind:"App Router",routePath:T,routeType:"route",revalidateReason:(0,p.getRevalidateReason)({isRevalidate:I,isOnDemandRevalidate:j})}),k)throw t;return await (0,u.sendResponse)(M,F,new Response(null,{status:500})),null}}}];

//# sourceMappingURL=%5Broot-of-the-server%5D__ebe04496._.js.map