module.exports=[36870,a=>{"use strict";a.s(["Primitive",()=>f,"dispatchDiscreteCustomEvent",()=>g]);var b=a.i(54159),c=a.i(81453),d=a.i(54472),e=a.i(41825),f=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((a,c)=>{let f=(0,d.createSlot)(`Primitive.${c}`),g=b.forwardRef((a,b)=>{let{asChild:d,...g}=a;return(0,e.jsx)(d?f:c,{...g,ref:b})});return g.displayName=`Primitive.${c}`,{...a,[c]:g}},{});function g(a,b){a&&c.flushSync(()=>a.dispatchEvent(b))}},56704,(a,b,c)=>{b.exports=a.x("next/dist/server/app-render/work-async-storage.external.js",()=>require("next/dist/server/app-render/work-async-storage.external.js"))},32319,(a,b,c)=>{b.exports=a.x("next/dist/server/app-render/work-unit-async-storage.external.js",()=>require("next/dist/server/app-render/work-unit-async-storage.external.js"))},20635,(a,b,c)=>{b.exports=a.x("next/dist/server/app-render/action-async-storage.external.js",()=>require("next/dist/server/app-render/action-async-storage.external.js"))},76449,(a,b,c)=>{"use strict";b.exports=a.r(59230).vendored.contexts.AppRouterContext},72108,(a,b,c)=>{"use strict";b.exports=a.r(59230).vendored.contexts.HooksClientContext},2580,(a,b,c)=>{"use strict";b.exports=a.r(59230).vendored.contexts.ServerInsertedHtml},81453,(a,b,c)=>{"use strict";b.exports=a.r(59230).vendored["react-ssr"].ReactDOM},4082,a=>{"use strict";a.s(["Card",()=>e,"CardContent",()=>i,"CardDescription",()=>h,"CardHeader",()=>f,"CardTitle",()=>g]);var b=a.i(41825),c=a.i(54159),d=a.i(18688);let e=c.forwardRef(({className:a,...c},e)=>(0,b.jsx)("div",{ref:e,className:(0,d.cn)("rounded-lg border border-gray-200 dark:border-gray-800 bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 shadow-sm",a),...c}));e.displayName="Card";let f=c.forwardRef(({className:a,...c},e)=>(0,b.jsx)("div",{ref:e,className:(0,d.cn)("flex flex-col space-y-1.5 p-6",a),...c}));f.displayName="CardHeader";let g=c.forwardRef(({className:a,...c},e)=>(0,b.jsx)("h3",{ref:e,className:(0,d.cn)("text-2xl font-semibold leading-none tracking-tight",a),...c}));g.displayName="CardTitle";let h=c.forwardRef(({className:a,...c},e)=>(0,b.jsx)("p",{ref:e,className:(0,d.cn)("text-sm text-gray-600 dark:text-gray-400",a),...c}));h.displayName="CardDescription";let i=c.forwardRef(({className:a,...c},e)=>(0,b.jsx)("div",{ref:e,className:(0,d.cn)("p-6 pt-0",a),...c}));i.displayName="CardContent",c.forwardRef(({className:a,...c},e)=>(0,b.jsx)("div",{ref:e,className:(0,d.cn)("flex items-center p-6 pt-0",a),...c})).displayName="CardFooter"},2331,98752,54472,a=>{"use strict";a.s(["Button",()=>n],2331);var b=a.i(41825),c=a.i(54159);function d(a,b){if("function"==typeof a)return a(b);null!=a&&(a.current=b)}function e(...a){return b=>{let c=!1,e=a.map(a=>{let e=d(a,b);return c||"function"!=typeof e||(c=!0),e});if(c)return()=>{for(let b=0;b<e.length;b++){let c=e[b];"function"==typeof c?c():d(a[b],null)}}}}function f(...a){return c.useCallback(e(...a),a)}function g(a){let d=function(a){let b=c.forwardRef((a,b)=>{let{children:d,...f}=a;if(c.isValidElement(d)){var g;let a,h,i=(g=d,(h=(a=Object.getOwnPropertyDescriptor(g.props,"ref")?.get)&&"isReactWarning"in a&&a.isReactWarning)?g.ref:(h=(a=Object.getOwnPropertyDescriptor(g,"ref")?.get)&&"isReactWarning"in a&&a.isReactWarning)?g.props.ref:g.props.ref||g.ref),j=function(a,b){let c={...b};for(let d in b){let e=a[d],f=b[d];/^on[A-Z]/.test(d)?e&&f?c[d]=(...a)=>{let b=f(...a);return e(...a),b}:e&&(c[d]=e):"style"===d?c[d]={...e,...f}:"className"===d&&(c[d]=[e,f].filter(Boolean).join(" "))}return{...a,...c}}(f,d.props);return d.type!==c.Fragment&&(j.ref=b?e(b,i):i),c.cloneElement(d,j)}return c.Children.count(d)>1?c.Children.only(null):null});return b.displayName=`${a}.SlotClone`,b}(a),f=c.forwardRef((a,e)=>{let{children:f,...g}=a,h=c.Children.toArray(f),i=h.find(j);if(i){let a=i.props.children,f=h.map(b=>b!==i?b:c.Children.count(a)>1?c.Children.only(null):c.isValidElement(a)?a.props.children:null);return(0,b.jsx)(d,{...g,ref:e,children:c.isValidElement(a)?c.cloneElement(a,void 0,f):null})}return(0,b.jsx)(d,{...g,ref:e,children:f})});return f.displayName=`${a}.Slot`,f}a.s(["Slot",()=>h,"createSlot",()=>g],54472),a.s(["composeRefs",()=>e,"useComposedRefs",()=>f],98752);var h=g("Slot"),i=Symbol("radix.slottable");function j(a){return c.isValidElement(a)&&"function"==typeof a.type&&"__radixId"in a.type&&a.type.__radixId===i}var k=a.i(24311),l=a.i(18688);let m=(0,k.cva)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-blue-600 text-white hover:bg-blue-700 dark:bg-blue-600 dark:hover:bg-blue-700",destructive:"bg-red-600 text-white hover:bg-red-700 dark:bg-red-600 dark:hover:bg-red-700",outline:"border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-900 hover:bg-gray-50 dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-gray-100",secondary:"bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-gray-100 hover:bg-gray-200 dark:hover:bg-gray-700",ghost:"hover:bg-gray-100 dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-gray-100",link:"text-blue-600 dark:text-blue-400 underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),n=c.forwardRef(({className:a,variant:c,size:d,asChild:e=!1,...f},g)=>(0,b.jsx)(e?h:"button",{className:(0,l.cn)(m({variant:c,size:d,className:a})),ref:g,...f}));n.displayName="Button"},59844,a=>{"use strict";a.s(["User",()=>b],59844);let b=(0,a.i(32639).default)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},32639,a=>{"use strict";a.s(["default",()=>g],32639);var b=a.i(54159);let c=a=>{let b=a.replace(/^([A-Z])|[\s-_]+(\w)/g,(a,b,c)=>c?c.toUpperCase():b.toLowerCase());return b.charAt(0).toUpperCase()+b.slice(1)},d=(...a)=>a.filter((a,b,c)=>!!a&&""!==a.trim()&&c.indexOf(a)===b).join(" ").trim();var e={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let f=(0,b.forwardRef)(({color:a="currentColor",size:c=24,strokeWidth:f=2,absoluteStrokeWidth:g,className:h="",children:i,iconNode:j,...k},l)=>(0,b.createElement)("svg",{ref:l,...e,width:c,height:c,stroke:a,strokeWidth:g?24*Number(f)/Number(c):f,className:d("lucide",h),...!i&&!(a=>{for(let b in a)if(b.startsWith("aria-")||"role"===b||"title"===b)return!0})(k)&&{"aria-hidden":"true"},...k},[...j.map(([a,c])=>(0,b.createElement)(a,c)),...Array.isArray(i)?i:[i]])),g=(a,e)=>{let g=(0,b.forwardRef)(({className:g,...h},i)=>(0,b.createElement)(f,{ref:i,iconNode:e,className:d(`lucide-${c(a).replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()}`,`lucide-${a}`,g),...h}));return g.displayName=c(a),g}},40770,a=>{"use strict";a.s(["Home",()=>b],40770);let b=(0,a.i(32639).default)("house",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]])},95788,a=>{"use strict";a.s(["adminNavigation",()=>b,"getRoleDashboardUrl",()=>e,"studentNavigation",()=>d,"teacherNavigation",()=>c]);let b=[{name:"Dashboard",href:"/admin",icon:"BarChart3"},{name:"Students",href:"/admin/students",icon:"Users"},{name:"Teachers",href:"/admin/teachers",icon:"GraduationCap"},{name:"Classes & Sections",href:"/admin/classes",icon:"BookOpen"},{name:"Subjects",href:"/admin/subjects",icon:"FileText"},{name:"Terms & Exams",href:"/admin/exams",icon:"Calendar"},{name:"Attendance",href:"/admin/attendance",icon:"ClipboardList"},{name:"Marks",href:"/admin/marks",icon:"Award"},{name:"Reports",href:"/admin/reports",icon:"Download"},{name:"Settings",href:"/admin/settings",icon:"Settings"}],c=[{name:"Dashboard",href:"/teacher",icon:"BarChart3"},{name:"My Classes",href:"/teacher/classes",icon:"BookOpen"},{name:"Attendance",href:"/teacher/attendance",icon:"ClipboardList"},{name:"Marks",href:"/teacher/marks",icon:"Award"},{name:"Students",href:"/teacher/students",icon:"Users"},{name:"Reports",href:"/teacher/reports",icon:"FileText"},{name:"Profile",href:"/teacher/profile",icon:"User"}],d=[{name:"Dashboard",href:"/student",icon:"BarChart3"},{name:"My Classes",href:"/student/classes",icon:"BookOpen"},{name:"Attendance",href:"/student/attendance",icon:"ClipboardList"},{name:"Marks",href:"/student/marks",icon:"Award"},{name:"Reports",href:"/student/reports",icon:"FileText"},{name:"Profile",href:"/student/profile",icon:"User"}];function e(a){switch(a){case"ADMIN":return"/admin";case"TEACHER":return"/teacher";case"STUDENT":return"/student";default:return"/"}}},11090,a=>{"use strict";a.s(["Label",()=>i],11090);var b=a.i(41825),c=a.i(54159),d=a.i(36870),e=c.forwardRef((a,c)=>(0,b.jsx)(d.Primitive.label,{...a,ref:c,onMouseDown:b=>{b.target.closest("button, input, select, textarea")||(a.onMouseDown?.(b),!b.defaultPrevented&&b.detail>1&&b.preventDefault())}}));e.displayName="Label";var f=a.i(24311),g=a.i(18688);let h=(0,f.cva)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),i=c.forwardRef(({className:a,...c},d)=>(0,b.jsx)(e,{ref:d,className:(0,g.cn)(h(),a),...c}));i.displayName=e.displayName},66710,a=>{"use strict";a.s(["TrendingUp",()=>b],66710);let b=(0,a.i(32639).default)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},29683,a=>{"use strict";a.s(["CheckCircle",()=>b],29683);let b=(0,a.i(32639).default)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},8653,a=>{"use strict";a.s(["XCircle",()=>b],8653);let b=(0,a.i(32639).default)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},77622,a=>{"use strict";a.s(["Filter",()=>b],77622);let b=(0,a.i(32639).default)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]])},79301,a=>{"use strict";a.s(["default",()=>q]);var b=a.i(41825),c=a.i(54159),d=a.i(4082),e=a.i(2331),f=a.i(11090),g=a.i(62821),h=a.i(32541),i=a.i(66710),j=a.i(78402),k=a.i(14401),l=a.i(77622),m=a.i(37906),n=a.i(29683),o=a.i(8653),p=a.i(95788);function q(){let[a,q]=(0,c.useState)([]),[r,s]=(0,c.useState)("all"),[t,u]=(0,c.useState)("all"),[v,w]=(0,c.useState)("all");(0,c.useEffect)(()=>{q([{id:"1",studentName:"John Doe",admissionNo:"STU001",examName:"Unit Test 1",subjectName:"Mathematics",className:"Grade 8",sectionName:"A",obtainedMarks:45,maxMarks:50,percentage:90,grade:"A+",gradedBy:"Mrs. Smith",date:"2024-10-15"},{id:"2",studentName:"Jane Smith",admissionNo:"STU002",examName:"Unit Test 1",subjectName:"Mathematics",className:"Grade 8",sectionName:"A",obtainedMarks:38,maxMarks:50,percentage:76,grade:"B+",gradedBy:"Mrs. Smith",date:"2024-10-15"},{id:"3",studentName:"Mike Johnson",admissionNo:"STU003",examName:"Mid Term Exam",subjectName:"English",className:"Grade 8",sectionName:"A",obtainedMarks:85,maxMarks:100,percentage:85,grade:"A",gradedBy:"Mr. Brown",date:"2024-11-20"}])},[]);let x={total:a.length,average:a.length>0?Math.round(a.reduce((a,b)=>a+b.percentage,0)/a.length):0,highest:Math.max(...a.map(a=>a.percentage)),lowest:Math.min(...a.map(a=>a.percentage)),passed:a.filter(a=>a.percentage>=40).length,failed:a.filter(a=>a.percentage<40).length};return(0,b.jsx)(g.default,{title:"Marks Management",navigation:p.adminNavigation,children:(0,b.jsxs)("div",{className:"space-y-6",children:[(0,b.jsxs)("div",{className:"flex justify-between items-center",children:[(0,b.jsxs)("div",{children:[(0,b.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Marks Management"}),(0,b.jsx)("p",{className:"text-gray-600",children:"Monitor and manage student examination marks"})]}),(0,b.jsxs)("div",{className:"flex space-x-2",children:[(0,b.jsxs)(e.Button,{variant:"outline",children:[(0,b.jsx)(k.Download,{className:"w-4 h-4 mr-2"}),"Export Marks"]}),(0,b.jsxs)(e.Button,{children:[(0,b.jsx)(m.Plus,{className:"w-4 h-4 mr-2"}),"Add Marks"]})]})]}),(0,b.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-6",children:[(0,b.jsxs)(d.Card,{children:[(0,b.jsxs)(d.CardHeader,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,b.jsx)(d.CardTitle,{className:"text-sm font-medium",children:"Total Records"}),(0,b.jsx)(j.FileText,{className:"h-4 w-4 text-muted-foreground"})]}),(0,b.jsx)(d.CardContent,{children:(0,b.jsx)("div",{className:"text-2xl font-bold",children:x.total})})]},"total-marks-records"),(0,b.jsxs)(d.Card,{children:[(0,b.jsxs)(d.CardHeader,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,b.jsx)(d.CardTitle,{className:"text-sm font-medium",children:"Average Score"}),(0,b.jsx)(i.TrendingUp,{className:"h-4 w-4 text-blue-600"})]}),(0,b.jsx)(d.CardContent,{children:(0,b.jsxs)("div",{className:"text-2xl font-bold text-blue-600",children:[x.average,"%"]})})]},"average-marks-score"),(0,b.jsxs)(d.Card,{children:[(0,b.jsxs)(d.CardHeader,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,b.jsx)(d.CardTitle,{className:"text-sm font-medium",children:"Highest Score"}),(0,b.jsx)(h.Award,{className:"h-4 w-4 text-green-600"})]}),(0,b.jsx)(d.CardContent,{children:(0,b.jsxs)("div",{className:"text-2xl font-bold text-green-600",children:[x.highest,"%"]})})]},"highest-marks-score"),(0,b.jsxs)(d.Card,{children:[(0,b.jsxs)(d.CardHeader,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,b.jsx)(d.CardTitle,{className:"text-sm font-medium",children:"Lowest Score"}),(0,b.jsx)(i.TrendingUp,{className:"h-4 w-4 text-red-600"})]}),(0,b.jsx)(d.CardContent,{children:(0,b.jsxs)("div",{className:"text-2xl font-bold text-red-600",children:[x.lowest,"%"]})})]},"lowest-marks-score"),(0,b.jsxs)(d.Card,{children:[(0,b.jsxs)(d.CardHeader,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,b.jsx)(d.CardTitle,{className:"text-sm font-medium",children:"Passed"}),(0,b.jsx)(n.CheckCircle,{className:"h-4 w-4 text-green-600"})]}),(0,b.jsx)(d.CardContent,{children:(0,b.jsx)("div",{className:"text-2xl font-bold text-green-600",children:x.passed})})]},"passed-students"),(0,b.jsxs)(d.Card,{children:[(0,b.jsxs)(d.CardHeader,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,b.jsx)(d.CardTitle,{className:"text-sm font-medium",children:"Failed"}),(0,b.jsx)(o.XCircle,{className:"h-4 w-4 text-red-600"})]}),(0,b.jsx)(d.CardContent,{children:(0,b.jsx)("div",{className:"text-2xl font-bold text-red-600",children:x.failed})})]},"failed-students")]}),(0,b.jsxs)(d.Card,{children:[(0,b.jsx)(d.CardHeader,{children:(0,b.jsxs)(d.CardTitle,{className:"flex items-center",children:[(0,b.jsx)(l.Filter,{className:"w-5 h-5 mr-2"}),"Filters"]})}),(0,b.jsx)(d.CardContent,{children:(0,b.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,b.jsxs)("div",{children:[(0,b.jsx)(f.Label,{htmlFor:"exam",children:"Exam"}),(0,b.jsxs)("select",{id:"exam",value:r,onChange:a=>s(a.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,b.jsx)("option",{value:"all",children:"All Exams"}),(0,b.jsx)("option",{value:"unit1",children:"Unit Test 1"}),(0,b.jsx)("option",{value:"midterm",children:"Mid Term Exam"}),(0,b.jsx)("option",{value:"final",children:"Final Exam"})]})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)(f.Label,{htmlFor:"subject",children:"Subject"}),(0,b.jsxs)("select",{id:"subject",value:t,onChange:a=>u(a.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,b.jsx)("option",{value:"all",children:"All Subjects"}),(0,b.jsx)("option",{value:"math",children:"Mathematics"}),(0,b.jsx)("option",{value:"english",children:"English"}),(0,b.jsx)("option",{value:"science",children:"Science"})]})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)(f.Label,{htmlFor:"class",children:"Class"}),(0,b.jsxs)("select",{id:"class",value:v,onChange:a=>w(a.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,b.jsx)("option",{value:"all",children:"All Classes"}),(0,b.jsx)("option",{value:"grade8",children:"Grade 8"}),(0,b.jsx)("option",{value:"grade9",children:"Grade 9"}),(0,b.jsx)("option",{value:"grade10",children:"Grade 10"})]})]})]})})]}),(0,b.jsxs)(d.Card,{children:[(0,b.jsxs)(d.CardHeader,{children:[(0,b.jsx)(d.CardTitle,{children:"Marks Records"}),(0,b.jsx)(d.CardDescription,{children:"Examination marks and grades for all students"})]}),(0,b.jsx)(d.CardContent,{children:(0,b.jsx)("div",{className:"overflow-x-auto",children:(0,b.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,b.jsx)("thead",{className:"bg-gray-50",children:(0,b.jsxs)("tr",{children:[(0,b.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Student"}),(0,b.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Exam"}),(0,b.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Subject"}),(0,b.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Class"}),(0,b.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Marks"}),(0,b.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Percentage"}),(0,b.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Grade"}),(0,b.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Graded By"}),(0,b.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,b.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:a.map(a=>(0,b.jsxs)("tr",{children:[(0,b.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,b.jsxs)("div",{className:"flex items-center",children:[(0,b.jsx)("div",{className:"flex-shrink-0 h-10 w-10",children:(0,b.jsx)("div",{className:"h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center",children:(0,b.jsx)("span",{className:"text-sm font-medium text-gray-700",children:a.studentName.split(" ").map(a=>a[0]).join("")})})}),(0,b.jsxs)("div",{className:"ml-4",children:[(0,b.jsx)("div",{className:"text-sm font-medium text-gray-900",children:a.studentName}),(0,b.jsx)("div",{className:"text-sm text-gray-500",children:a.admissionNo})]})]})}),(0,b.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:a.examName}),(0,b.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:a.subjectName}),(0,b.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:[a.className," - ",a.sectionName]}),(0,b.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:[a.obtainedMarks,"/",a.maxMarks]}),(0,b.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:[a.percentage,"%"]}),(0,b.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,b.jsx)("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${(a=>{switch(a){case"A+":case"A":return"bg-green-100 text-green-800";case"B+":case"B":return"bg-blue-100 text-blue-800";case"C+":case"C":return"bg-yellow-100 text-yellow-800";case"D":return"bg-orange-100 text-orange-800";case"F":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}})(a.grade)}`,children:a.grade})}),(0,b.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:a.gradedBy}),(0,b.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:(0,b.jsxs)("div",{className:"flex space-x-2",children:[(0,b.jsx)(e.Button,{variant:"outline",size:"sm",children:"Edit"}),(0,b.jsx)(e.Button,{variant:"outline",size:"sm",children:"View"})]})})]},a.id))})]})})})]})]})})}}];

//# sourceMappingURL=%5Broot-of-the-server%5D__fa3c87e6._.js.map