module.exports=[18622,(e,t,r)=>{t.exports=e.x("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js"))},56704,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/work-async-storage.external.js",()=>require("next/dist/server/app-render/work-async-storage.external.js"))},32319,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/work-unit-async-storage.external.js",()=>require("next/dist/server/app-render/work-unit-async-storage.external.js"))},20635,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/action-async-storage.external.js",()=>require("next/dist/server/app-render/action-async-storage.external.js"))},24725,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/after-task-async-storage.external.js",()=>require("next/dist/server/app-render/after-task-async-storage.external.js"))},24361,(e,t,r)=>{t.exports=e.x("util",()=>require("util"))},29173,(e,t,r)=>{t.exports=e.x("@prisma/client",()=>require("@prisma/client"))},70406,(e,t,r)=>{t.exports=e.x("next/dist/compiled/@opentelemetry/api",()=>require("next/dist/compiled/@opentelemetry/api"))},93695,(e,t,r)=>{t.exports=e.x("next/dist/shared/lib/no-fallback-error.external.js",()=>require("next/dist/shared/lib/no-fallback-error.external.js"))},61724,(e,t,r)=>{t.exports=e.x("next/dist/compiled/next-server/app-route-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-route-turbo.runtime.prod.js"))},54799,(e,t,r)=>{t.exports=e.x("crypto",()=>require("crypto"))},92509,(e,t,r)=>{t.exports=e.x("url",()=>require("url"))},21517,(e,t,r)=>{t.exports=e.x("http",()=>require("http"))},49719,(e,t,r)=>{t.exports=e.x("assert",()=>require("assert"))},45706,(e,t,r)=>{t.exports=e.x("querystring",()=>require("querystring"))},874,(e,t,r)=>{t.exports=e.x("buffer",()=>require("buffer"))},6461,(e,t,r)=>{t.exports=e.x("zlib",()=>require("zlib"))},24836,(e,t,r)=>{t.exports=e.x("https",()=>require("https"))},27699,(e,t,r)=>{t.exports=e.x("events",()=>require("events"))},12756,e=>{e.v(JSON.parse('{"name":"openid-client","version":"5.7.1","description":"OpenID Connect Relying Party (RP, Client) implementation for Node.js runtime, supports passportjs","keywords":["auth","authentication","basic","certified","client","connect","dynamic","electron","hybrid","identity","implicit","oauth","oauth2","oidc","openid","passport","relying party","strategy"],"homepage":"https://github.com/panva/openid-client","repository":"panva/openid-client","funding":{"url":"https://github.com/sponsors/panva"},"license":"MIT","author":"Filip Skokan <<EMAIL>>","exports":{"types":"./types/index.d.ts","import":"./lib/index.mjs","require":"./lib/index.js"},"main":"./lib/index.js","types":"./types/index.d.ts","files":["lib","types/index.d.ts"],"scripts":{"format":"npx prettier --loglevel silent --write ./lib ./test ./certification ./types","test":"mocha test/**/*.test.js"},"dependencies":{"jose":"^4.15.9","lru-cache":"^6.0.0","object-hash":"^2.2.0","oidc-token-hash":"^5.0.3"},"devDependencies":{"@types/node":"^16.18.106","@types/passport":"^1.0.16","base64url":"^3.0.1","chai":"^4.5.0","mocha":"^10.7.3","nock":"^13.5.5","prettier":"^2.8.8","readable-mock-req":"^0.2.2","sinon":"^9.2.4","timekeeper":"^2.3.1"},"standard-version":{"scripts":{"postchangelog":"sed -i \'\' -e \'s/### \\\\[/## [/g\' CHANGELOG.md"},"types":[{"type":"feat","section":"Features"},{"type":"fix","section":"Fixes"},{"type":"chore","hidden":true},{"type":"docs","hidden":true},{"type":"style","hidden":true},{"type":"refactor","section":"Refactor","hidden":false},{"type":"perf","section":"Performance","hidden":false},{"type":"test","hidden":true}]}}'))},16797,(e,t,r)=>{},44285,e=>{"use strict";e.s(["handler",()=>A,"patchFetch",()=>j,"routeModule",()=>E,"serverHooks",()=>k,"workAsyncStorage",()=>C,"workUnitAsyncStorage",()=>q],44285);var t=e.i(6137),r=e.i(11365),a=e.i(9638),n=e.i(15243),s=e.i(66378),i=e.i(92101),o=e.i(50012),d=e.i(62885),p=e.i(31409),l=e.i(78448),u=e.i(28015),c=e.i(72721),x=e.i(75714),h=e.i(12634),m=e.i(93695);e.i(74732);var g=e.i(66662);e.s(["GET",()=>w],78575);var v=e.i(2835),y=e.i(58356),R=e.i(43382),f=e.i(31279);async function w(e){try{let e=await (0,y.getServerSession)(R.authOptions);if(!e||"ADMIN"!==e.user.role)return v.NextResponse.json({error:"Unauthorized"},{status:401});let[t,r,a,n,s,i,o,d]=await Promise.all([f.prisma.student.count(),f.prisma.teacher.count(),f.prisma.class.count(),f.prisma.subject.count(),f.prisma.attendance.aggregate({where:{date:{gte:new Date(Date.now()-2592e6)}},_count:{id:!0}}).then(async e=>{let t=await f.prisma.attendance.count({where:{date:{gte:new Date(Date.now()-2592e6)},status:"PRESENT"}});return{total:e._count.id,present:t,rate:e._count.id>0?t/e._count.id*100:0}}),f.prisma.mark.aggregate({_avg:{obtainedMarks:!0},_count:{id:!0}}),f.prisma.student.count({where:{attendances:{some:{date:{gte:new Date(Date.now()-2592e6)}}}}}),f.prisma.teacher.count({where:{attendances:{some:{date:{gte:new Date(Date.now()-2592e6)}}}}})]),p={totalStudents:t,totalTeachers:r,totalClasses:a,totalSubjects:n,activeStudents:o,activeTeachers:d,attendanceRate:Math.round(10*s.rate)/10,averageMarks:i._avg.obtainedMarks?Math.round(10*i._avg.obtainedMarks)/10:0,totalMarksRecords:i._count.id,totalAttendanceRecords:s.total};return v.NextResponse.json(p)}catch(e){return console.error("Error fetching admin dashboard stats:",e),v.NextResponse.json({error:"Internal server error"},{status:500})}}var b=e.i(78575);let E=new t.AppRouteRouteModule({definition:{kind:r.RouteKind.APP_ROUTE,page:"/api/admin/dashboard/stats/route",pathname:"/api/admin/dashboard/stats",filename:"route",bundlePath:""},distDir:".next",relativeProjectDir:"",resolvedPagePath:"[project]/school-management-system/src/app/api/admin/dashboard/stats/route.ts",nextConfigOutput:"",userland:b}),{workAsyncStorage:C,workUnitAsyncStorage:q,serverHooks:k}=E;function j(){return(0,a.patchFetch)({workAsyncStorage:C,workUnitAsyncStorage:q})}async function A(e,t,a){var v;let y="/api/admin/dashboard/stats/route";y=y.replace(/\/index$/,"")||"/";let R=await E.prepare(e,t,{srcPage:y,multiZoneDraftMode:!1});if(!R)return t.statusCode=400,t.end("Bad Request"),null==a.waitUntil||a.waitUntil.call(a,Promise.resolve()),null;let{buildId:f,params:w,nextConfig:b,isDraftMode:C,prerenderManifest:q,routerServerContext:k,isOnDemandRevalidate:j,revalidateOnlyGenerated:A,resolvedPathname:N}=R,_=(0,i.normalizeAppPath)(y),P=!!(q.dynamicRoutes[_]||q.routes[N]);if(P&&!C){let e=!!q.routes[N],t=q.dynamicRoutes[_];if(t&&!1===t.fallback&&!e)throw new m.NoFallbackError}let O=null;!P||E.isDev||C||(O="/index"===(O=N)?"/":O);let T=!0===E.isDev||!P,D=P&&!T,M=e.method||"GET",S=(0,s.getTracer)(),H=S.getActiveScopeSpan(),I={params:w,prerenderManifest:q,renderOpts:{experimental:{cacheComponents:!!b.experimental.cacheComponents,authInterrupts:!!b.experimental.authInterrupts},supportsDynamicResponse:T,incrementalCache:(0,n.getRequestMeta)(e,"incrementalCache"),cacheLifeProfiles:null==(v=b.experimental)?void 0:v.cacheLife,isRevalidate:D,waitUntil:a.waitUntil,onClose:e=>{t.on("close",e)},onAfterTaskError:void 0,onInstrumentationRequestError:(t,r,a)=>E.onRequestError(e,t,a,k)},sharedContext:{buildId:f}},U=new o.NodeNextRequest(e),F=new o.NodeNextResponse(t),$=d.NextRequestAdapter.fromNodeNextRequest(U,(0,d.signalFromNodeResponse)(t));try{let i=async r=>E.handle($,I).finally(()=>{if(!r)return;r.setAttributes({"http.status_code":t.statusCode,"next.rsc":!1});let a=S.getRootSpanAttributes();if(!a)return;if(a.get("next.span_type")!==p.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${a.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let n=a.get("next.route");if(n){let e=`${M} ${n}`;r.setAttributes({"next.route":n,"http.route":n,"next.span_name":e}),r.updateName(e)}else r.updateName(`${M} ${e.url}`)}),o=async s=>{var o,d;let p=async({previousCacheEntry:r})=>{try{if(!(0,n.getRequestMeta)(e,"minimalMode")&&j&&A&&!r)return t.statusCode=404,t.setHeader("x-nextjs-cache","REVALIDATED"),t.end("This page could not be found"),null;let o=await i(s);e.fetchMetrics=I.renderOpts.fetchMetrics;let d=I.renderOpts.pendingWaitUntil;d&&a.waitUntil&&(a.waitUntil(d),d=void 0);let p=I.renderOpts.collectedTags;if(!P)return await (0,u.sendResponse)(U,F,o,I.renderOpts.pendingWaitUntil),null;{let e=await o.blob(),t=(0,c.toNodeOutgoingHttpHeaders)(o.headers);p&&(t[h.NEXT_CACHE_TAGS_HEADER]=p),!t["content-type"]&&e.type&&(t["content-type"]=e.type);let r=void 0!==I.renderOpts.collectedRevalidate&&!(I.renderOpts.collectedRevalidate>=h.INFINITE_CACHE)&&I.renderOpts.collectedRevalidate,a=void 0===I.renderOpts.collectedExpire||I.renderOpts.collectedExpire>=h.INFINITE_CACHE?void 0:I.renderOpts.collectedExpire;return{value:{kind:g.CachedRouteKind.APP_ROUTE,status:o.status,body:Buffer.from(await e.arrayBuffer()),headers:t},cacheControl:{revalidate:r,expire:a}}}}catch(t){throw(null==r?void 0:r.isStale)&&await E.onRequestError(e,t,{routerKind:"App Router",routePath:y,routeType:"route",revalidateReason:(0,l.getRevalidateReason)({isRevalidate:D,isOnDemandRevalidate:j})},k),t}},m=await E.handleResponse({req:e,nextConfig:b,cacheKey:O,routeKind:r.RouteKind.APP_ROUTE,isFallback:!1,prerenderManifest:q,isRoutePPREnabled:!1,isOnDemandRevalidate:j,revalidateOnlyGenerated:A,responseGenerator:p,waitUntil:a.waitUntil});if(!P)return null;if((null==m||null==(o=m.value)?void 0:o.kind)!==g.CachedRouteKind.APP_ROUTE)throw Object.defineProperty(Error(`Invariant: app-route received invalid cache entry ${null==m||null==(d=m.value)?void 0:d.kind}`),"__NEXT_ERROR_CODE",{value:"E701",enumerable:!1,configurable:!0});(0,n.getRequestMeta)(e,"minimalMode")||t.setHeader("x-nextjs-cache",j?"REVALIDATED":m.isMiss?"MISS":m.isStale?"STALE":"HIT"),C&&t.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");let v=(0,c.fromNodeOutgoingHttpHeaders)(m.value.headers);return(0,n.getRequestMeta)(e,"minimalMode")&&P||v.delete(h.NEXT_CACHE_TAGS_HEADER),!m.cacheControl||t.getHeader("Cache-Control")||v.get("Cache-Control")||v.set("Cache-Control",(0,x.getCacheControlHeader)(m.cacheControl)),await (0,u.sendResponse)(U,F,new Response(m.value.body,{headers:v,status:m.value.status||200})),null};H?await o(H):await S.withPropagatedContext(e.headers,()=>S.trace(p.BaseServerSpan.handleRequest,{spanName:`${M} ${e.url}`,kind:s.SpanKind.SERVER,attributes:{"http.method":M,"http.target":e.url}},o))}catch(t){if(H||t instanceof m.NoFallbackError||await E.onRequestError(e,t,{routerKind:"App Router",routePath:_,routeType:"route",revalidateReason:(0,l.getRevalidateReason)({isRevalidate:D,isOnDemandRevalidate:j})}),P)throw t;return await (0,u.sendResponse)(U,F,new Response(null,{status:500})),null}}}];

//# sourceMappingURL=%5Broot-of-the-server%5D__7dff73b8._.js.map