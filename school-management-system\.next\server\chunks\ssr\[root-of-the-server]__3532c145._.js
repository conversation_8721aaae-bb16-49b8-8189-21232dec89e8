module.exports=[36870,a=>{"use strict";a.s(["Primitive",()=>f,"dispatchDiscreteCustomEvent",()=>g]);var b=a.i(54159),c=a.i(81453),d=a.i(54472),e=a.i(41825),f=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((a,c)=>{let f=(0,d.createSlot)(`Primitive.${c}`),g=b.forwardRef((a,b)=>{let{asChild:d,...g}=a;return(0,e.jsx)(d?f:c,{...g,ref:b})});return g.displayName=`Primitive.${c}`,{...a,[c]:g}},{});function g(a,b){a&&c.flushSync(()=>a.dispatchEvent(b))}},56704,(a,b,c)=>{b.exports=a.x("next/dist/server/app-render/work-async-storage.external.js",()=>require("next/dist/server/app-render/work-async-storage.external.js"))},32319,(a,b,c)=>{b.exports=a.x("next/dist/server/app-render/work-unit-async-storage.external.js",()=>require("next/dist/server/app-render/work-unit-async-storage.external.js"))},20635,(a,b,c)=>{b.exports=a.x("next/dist/server/app-render/action-async-storage.external.js",()=>require("next/dist/server/app-render/action-async-storage.external.js"))},76449,(a,b,c)=>{"use strict";b.exports=a.r(59230).vendored.contexts.AppRouterContext},72108,(a,b,c)=>{"use strict";b.exports=a.r(59230).vendored.contexts.HooksClientContext},2580,(a,b,c)=>{"use strict";b.exports=a.r(59230).vendored.contexts.ServerInsertedHtml},81453,(a,b,c)=>{"use strict";b.exports=a.r(59230).vendored["react-ssr"].ReactDOM},4082,a=>{"use strict";a.s(["Card",()=>e,"CardContent",()=>i,"CardDescription",()=>h,"CardHeader",()=>f,"CardTitle",()=>g]);var b=a.i(41825),c=a.i(54159),d=a.i(18688);let e=c.forwardRef(({className:a,...c},e)=>(0,b.jsx)("div",{ref:e,className:(0,d.cn)("rounded-lg border border-gray-200 dark:border-gray-800 bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 shadow-sm",a),...c}));e.displayName="Card";let f=c.forwardRef(({className:a,...c},e)=>(0,b.jsx)("div",{ref:e,className:(0,d.cn)("flex flex-col space-y-1.5 p-6",a),...c}));f.displayName="CardHeader";let g=c.forwardRef(({className:a,...c},e)=>(0,b.jsx)("h3",{ref:e,className:(0,d.cn)("text-2xl font-semibold leading-none tracking-tight",a),...c}));g.displayName="CardTitle";let h=c.forwardRef(({className:a,...c},e)=>(0,b.jsx)("p",{ref:e,className:(0,d.cn)("text-sm text-gray-600 dark:text-gray-400",a),...c}));h.displayName="CardDescription";let i=c.forwardRef(({className:a,...c},e)=>(0,b.jsx)("div",{ref:e,className:(0,d.cn)("p-6 pt-0",a),...c}));i.displayName="CardContent",c.forwardRef(({className:a,...c},e)=>(0,b.jsx)("div",{ref:e,className:(0,d.cn)("flex items-center p-6 pt-0",a),...c})).displayName="CardFooter"},2331,98752,54472,a=>{"use strict";a.s(["Button",()=>n],2331);var b=a.i(41825),c=a.i(54159);function d(a,b){if("function"==typeof a)return a(b);null!=a&&(a.current=b)}function e(...a){return b=>{let c=!1,e=a.map(a=>{let e=d(a,b);return c||"function"!=typeof e||(c=!0),e});if(c)return()=>{for(let b=0;b<e.length;b++){let c=e[b];"function"==typeof c?c():d(a[b],null)}}}}function f(...a){return c.useCallback(e(...a),a)}function g(a){let d=function(a){let b=c.forwardRef((a,b)=>{let{children:d,...f}=a;if(c.isValidElement(d)){var g;let a,h,i=(g=d,(h=(a=Object.getOwnPropertyDescriptor(g.props,"ref")?.get)&&"isReactWarning"in a&&a.isReactWarning)?g.ref:(h=(a=Object.getOwnPropertyDescriptor(g,"ref")?.get)&&"isReactWarning"in a&&a.isReactWarning)?g.props.ref:g.props.ref||g.ref),j=function(a,b){let c={...b};for(let d in b){let e=a[d],f=b[d];/^on[A-Z]/.test(d)?e&&f?c[d]=(...a)=>{let b=f(...a);return e(...a),b}:e&&(c[d]=e):"style"===d?c[d]={...e,...f}:"className"===d&&(c[d]=[e,f].filter(Boolean).join(" "))}return{...a,...c}}(f,d.props);return d.type!==c.Fragment&&(j.ref=b?e(b,i):i),c.cloneElement(d,j)}return c.Children.count(d)>1?c.Children.only(null):null});return b.displayName=`${a}.SlotClone`,b}(a),f=c.forwardRef((a,e)=>{let{children:f,...g}=a,h=c.Children.toArray(f),i=h.find(j);if(i){let a=i.props.children,f=h.map(b=>b!==i?b:c.Children.count(a)>1?c.Children.only(null):c.isValidElement(a)?a.props.children:null);return(0,b.jsx)(d,{...g,ref:e,children:c.isValidElement(a)?c.cloneElement(a,void 0,f):null})}return(0,b.jsx)(d,{...g,ref:e,children:f})});return f.displayName=`${a}.Slot`,f}a.s(["Slot",()=>h,"createSlot",()=>g],54472),a.s(["composeRefs",()=>e,"useComposedRefs",()=>f],98752);var h=g("Slot"),i=Symbol("radix.slottable");function j(a){return c.isValidElement(a)&&"function"==typeof a.type&&"__radixId"in a.type&&a.type.__radixId===i}var k=a.i(24311),l=a.i(18688);let m=(0,k.cva)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-blue-600 text-white hover:bg-blue-700 dark:bg-blue-600 dark:hover:bg-blue-700",destructive:"bg-red-600 text-white hover:bg-red-700 dark:bg-red-600 dark:hover:bg-red-700",outline:"border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-900 hover:bg-gray-50 dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-gray-100",secondary:"bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-gray-100 hover:bg-gray-200 dark:hover:bg-gray-700",ghost:"hover:bg-gray-100 dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-gray-100",link:"text-blue-600 dark:text-blue-400 underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),n=c.forwardRef(({className:a,variant:c,size:d,asChild:e=!1,...f},g)=>(0,b.jsx)(e?h:"button",{className:(0,l.cn)(m({variant:c,size:d,className:a})),ref:g,...f}));n.displayName="Button"},40770,a=>{"use strict";a.s(["Home",()=>b],40770);let b=(0,a.i(32639).default)("house",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]])},32639,a=>{"use strict";a.s(["default",()=>g],32639);var b=a.i(54159);let c=a=>{let b=a.replace(/^([A-Z])|[\s-_]+(\w)/g,(a,b,c)=>c?c.toUpperCase():b.toLowerCase());return b.charAt(0).toUpperCase()+b.slice(1)},d=(...a)=>a.filter((a,b,c)=>!!a&&""!==a.trim()&&c.indexOf(a)===b).join(" ").trim();var e={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let f=(0,b.forwardRef)(({color:a="currentColor",size:c=24,strokeWidth:f=2,absoluteStrokeWidth:g,className:h="",children:i,iconNode:j,...k},l)=>(0,b.createElement)("svg",{ref:l,...e,width:c,height:c,stroke:a,strokeWidth:g?24*Number(f)/Number(c):f,className:d("lucide",h),...!i&&!(a=>{for(let b in a)if(b.startsWith("aria-")||"role"===b||"title"===b)return!0})(k)&&{"aria-hidden":"true"},...k},[...j.map(([a,c])=>(0,b.createElement)(a,c)),...Array.isArray(i)?i:[i]])),g=(a,e)=>{let g=(0,b.forwardRef)(({className:g,...h},i)=>(0,b.createElement)(f,{ref:i,iconNode:e,className:d(`lucide-${c(a).replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()}`,`lucide-${a}`,g),...h}));return g.displayName=c(a),g}},59844,a=>{"use strict";a.s(["User",()=>b],59844);let b=(0,a.i(32639).default)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},95788,a=>{"use strict";a.s(["adminNavigation",()=>b,"getRoleDashboardUrl",()=>e,"studentNavigation",()=>d,"teacherNavigation",()=>c]);let b=[{name:"Dashboard",href:"/admin",icon:"BarChart3"},{name:"Students",href:"/admin/students",icon:"Users"},{name:"Teachers",href:"/admin/teachers",icon:"GraduationCap"},{name:"Classes & Sections",href:"/admin/classes",icon:"BookOpen"},{name:"Subjects",href:"/admin/subjects",icon:"FileText"},{name:"Terms & Exams",href:"/admin/exams",icon:"Calendar"},{name:"Attendance",href:"/admin/attendance",icon:"ClipboardList"},{name:"Marks",href:"/admin/marks",icon:"Award"},{name:"Reports",href:"/admin/reports",icon:"Download"},{name:"Settings",href:"/admin/settings",icon:"Settings"}],c=[{name:"Dashboard",href:"/teacher",icon:"BarChart3"},{name:"My Classes",href:"/teacher/classes",icon:"BookOpen"},{name:"Attendance",href:"/teacher/attendance",icon:"ClipboardList"},{name:"Marks",href:"/teacher/marks",icon:"Award"},{name:"Students",href:"/teacher/students",icon:"Users"},{name:"Reports",href:"/teacher/reports",icon:"FileText"},{name:"Profile",href:"/teacher/profile",icon:"User"}],d=[{name:"Dashboard",href:"/student",icon:"BarChart3"},{name:"My Classes",href:"/student/classes",icon:"BookOpen"},{name:"Attendance",href:"/student/attendance",icon:"ClipboardList"},{name:"Marks",href:"/student/marks",icon:"Award"},{name:"Reports",href:"/student/reports",icon:"FileText"},{name:"Profile",href:"/student/profile",icon:"User"}];function e(a){switch(a){case"ADMIN":return"/admin";case"TEACHER":return"/teacher";case"STUDENT":return"/student";default:return"/"}}},5492,a=>{"use strict";a.s(["Input",()=>e]);var b=a.i(41825),c=a.i(54159),d=a.i(18688);let e=c.forwardRef(({className:a,type:c,...e},f)=>(0,b.jsx)("input",{type:c,className:(0,d.cn)("flex h-10 w-full rounded-md border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-900 px-3 py-2 text-sm text-gray-900 dark:text-gray-100 file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-500 dark:placeholder:text-gray-400 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",a),ref:f,...e}));e.displayName="Input"},11090,a=>{"use strict";a.s(["Label",()=>i],11090);var b=a.i(41825),c=a.i(54159),d=a.i(36870),e=c.forwardRef((a,c)=>(0,b.jsx)(d.Primitive.label,{...a,ref:c,onMouseDown:b=>{b.target.closest("button, input, select, textarea")||(a.onMouseDown?.(b),!b.defaultPrevented&&b.detail>1&&b.preventDefault())}}));e.displayName="Label";var f=a.i(24311),g=a.i(18688);let h=(0,f.cva)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),i=c.forwardRef(({className:a,...c},d)=>(0,b.jsx)(e,{ref:d,className:(0,g.cn)(h(),a),...c}));i.displayName=e.displayName},26006,a=>{"use strict";a.s(["Save",()=>b],26006);let b=(0,a.i(32639).default)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},29683,a=>{"use strict";a.s(["CheckCircle",()=>b],29683);let b=(0,a.i(32639).default)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},43001,a=>{"use strict";a.s(["AlertCircle",()=>b],43001);let b=(0,a.i(32639).default)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])}];

//# sourceMappingURL=%5Broot-of-the-server%5D__3532c145._.js.map