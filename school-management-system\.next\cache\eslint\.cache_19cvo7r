[{"C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(auth)\\login\\page.tsx": "1", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\admin\\attendance\\page.tsx": "2", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\admin\\classes\\new\\page.tsx": "3", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\admin\\classes\\page.tsx": "4", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\admin\\exams\\page.tsx": "5", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\admin\\marks\\page.tsx": "6", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\admin\\page.tsx": "7", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\admin\\reports\\page.tsx": "8", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\admin\\settings\\page.tsx": "9", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\admin\\students\\bulk\\page.tsx": "10", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\admin\\students\\new\\page.tsx": "11", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\admin\\students\\page.tsx": "12", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\admin\\students\\[id]\\edit\\page.tsx": "13", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\admin\\students\\[id]\\page.tsx": "14", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\admin\\subjects\\page.tsx": "15", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\admin\\teachers\\new\\page.tsx": "16", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\admin\\teachers\\page.tsx": "17", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\admin\\teachers\\[id]\\edit\\page.tsx": "18", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\admin\\teachers\\[id]\\page.tsx": "19", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\student\\attendance\\page.tsx": "20", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\student\\marks\\page.tsx": "21", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\student\\page.tsx": "22", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\student\\reports\\page.tsx": "23", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\teacher\\attendance\\mark\\page.tsx": "24", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\teacher\\attendance\\page.tsx": "25", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\teacher\\marks\\page.tsx": "26", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\teacher\\marks\\[examId]\\page.tsx": "27", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\teacher\\marks\\[examId]\\view\\page.tsx": "28", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\teacher\\page.tsx": "29", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\admin\\attendance\\route.ts": "30", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\admin\\classes\\route.ts": "31", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\admin\\classes\\[id]\\route.ts": "32", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\admin\\dashboard\\stats\\route.ts": "33", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\admin\\exams\\route.ts": "34", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\admin\\marks\\route.ts": "35", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\admin\\reports\\route.ts": "36", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\admin\\sections\\route.ts": "37", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\admin\\settings\\route.ts": "38", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\admin\\students\\bulk\\route.ts": "39", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\admin\\students\\route.ts": "40", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\admin\\students\\[id]\\route.ts": "41", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\admin\\subjects\\route.ts": "42", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\admin\\teachers\\route.ts": "43", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\admin\\teachers\\[id]\\route.ts": "44", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\auth\\[...nextauth]\\route.ts": "45", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\student\\attendance\\route.ts": "46", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\student\\dashboard\\stats\\route.ts": "47", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\student\\marks\\route.ts": "48", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\student\\reports\\route.ts": "49", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\teacher\\attendance\\route.ts": "50", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\teacher\\dashboard\\stats\\route.ts": "51", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\teacher\\exams\\route.ts": "52", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\teacher\\exams\\[examId]\\students\\route.ts": "53", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\teacher\\marks\\route.ts": "54", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\layout.tsx": "55", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\page.tsx": "56", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\test-login\\page.tsx": "57", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\unauthorized\\page.tsx": "58", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\attendance\\attendance-form.tsx": "59", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\auth\\logout-button.tsx": "60", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\auth\\protected-route.tsx": "61", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\classes\\class-form.tsx": "62", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\classes\\class-table.tsx": "63", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\layout\\dashboard-layout.tsx": "64", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\marks\\grade-calculator.tsx": "65", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\marks\\marks-entry-form.tsx": "66", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\marks\\marks-table.tsx": "67", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\providers\\session-provider.tsx": "68", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\providers\\theme-provider.tsx": "69", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\students\\bulk-import.tsx": "70", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\students\\student-form.tsx": "71", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\students\\student-table.tsx": "72", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\teachers\\teacher-form.tsx": "73", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\teachers\\teacher-table.tsx": "74", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\ui\\alert.tsx": "75", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\ui\\badge.tsx": "76", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\ui\\button.tsx": "77", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\ui\\card.tsx": "78", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\ui\\dropdown-menu.tsx": "79", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\ui\\input.tsx": "80", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\ui\\label.tsx": "81", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\ui\\responsive-container.tsx": "82", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\ui\\simple-theme-toggle.tsx": "83", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\ui\\theme-toggle.tsx": "84", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\hooks\\use-login.ts": "85", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\lib\\auth-utils.ts": "86", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\lib\\auth.ts": "87", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\lib\\db.ts": "88", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\lib\\grading.ts": "89", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\lib\\marks-validation.ts": "90", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\lib\\navigation.ts": "91", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\lib\\rbac.ts": "92", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\lib\\utils.ts": "93", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\middleware.ts": "94", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\types\\next-auth.d.ts": "95", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\admin\\students\\template\\route.ts": "96", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\students\\StudentImport.tsx": "97", "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\ui\\progress.tsx": "98"}, {"size": 6234, "mtime": 1756888610598, "results": "99", "hashOfConfig": "100"}, {"size": 13827, "mtime": 1756885850874, "results": "101", "hashOfConfig": "100"}, {"size": 633, "mtime": 1756885899712, "results": "102", "hashOfConfig": "100"}, {"size": 5154, "mtime": 1756977624206, "results": "103", "hashOfConfig": "100"}, {"size": 9702, "mtime": 1756885931424, "results": "104", "hashOfConfig": "100"}, {"size": 14659, "mtime": 1756899227679, "results": "105", "hashOfConfig": "100"}, {"size": 10882, "mtime": 1756977537696, "results": "106", "hashOfConfig": "100"}, {"size": 23444, "mtime": 1756899204270, "results": "107", "hashOfConfig": "100"}, {"size": 28112, "mtime": 1756977669560, "results": "108", "hashOfConfig": "100"}, {"size": 962, "mtime": 1756977775419, "results": "109", "hashOfConfig": "100"}, {"size": 1430, "mtime": 1756885266272, "results": "110", "hashOfConfig": "100"}, {"size": 4710, "mtime": 1756977702381, "results": "111", "hashOfConfig": "100"}, {"size": 2727, "mtime": 1756890831207, "results": "112", "hashOfConfig": "100"}, {"size": 13822, "mtime": 1756890820333, "results": "113", "hashOfConfig": "100"}, {"size": 8887, "mtime": 1756977718206, "results": "114", "hashOfConfig": "100"}, {"size": 1139, "mtime": 1756885273906, "results": "115", "hashOfConfig": "100"}, {"size": 4945, "mtime": 1756977733724, "results": "116", "hashOfConfig": "100"}, {"size": 3239, "mtime": 1756890791506, "results": "117", "hashOfConfig": "100"}, {"size": 9418, "mtime": 1756890885919, "results": "118", "hashOfConfig": "100"}, {"size": 8053, "mtime": 1756887545945, "results": "119", "hashOfConfig": "100"}, {"size": 18371, "mtime": 1756894954105, "results": "120", "hashOfConfig": "100"}, {"size": 13277, "mtime": 1756977583232, "results": "121", "hashOfConfig": "100"}, {"size": 11164, "mtime": 1756894988580, "results": "122", "hashOfConfig": "100"}, {"size": 398, "mtime": 1756798332445, "results": "123", "hashOfConfig": "100"}, {"size": 9863, "mtime": 1756798332445, "results": "124", "hashOfConfig": "100"}, {"size": 9273, "mtime": 1756894860221, "results": "125", "hashOfConfig": "100"}, {"size": 6607, "mtime": 1756894882482, "results": "126", "hashOfConfig": "100"}, {"size": 8910, "mtime": 1756977514091, "results": "127", "hashOfConfig": "100"}, {"size": 11718, "mtime": 1756977561398, "results": "128", "hashOfConfig": "100"}, {"size": 2352, "mtime": 1756977919212, "results": "129", "hashOfConfig": "100"}, {"size": 7806, "mtime": 1756892297872, "results": "130", "hashOfConfig": "100"}, {"size": 4632, "mtime": 1756978362774, "results": "131", "hashOfConfig": "100"}, {"size": 3234, "mtime": 1756898086295, "results": "132", "hashOfConfig": "100"}, {"size": 2536, "mtime": 1756814576844, "results": "133", "hashOfConfig": "100"}, {"size": 2467, "mtime": 1756814471224, "results": "134", "hashOfConfig": "100"}, {"size": 5236, "mtime": 1756814485233, "results": "135", "hashOfConfig": "100"}, {"size": 5515, "mtime": 1756799849243, "results": "136", "hashOfConfig": "100"}, {"size": 8654, "mtime": 1756977902800, "results": "137", "hashOfConfig": "100"}, {"size": 11572, "mtime": 1756977864635, "results": "138", "hashOfConfig": "100"}, {"size": 6863, "mtime": 1756891698086, "results": "139", "hashOfConfig": "100"}, {"size": 8063, "mtime": 1756885638017, "results": "140", "hashOfConfig": "100"}, {"size": 6338, "mtime": 1756816248462, "results": "141", "hashOfConfig": "100"}, {"size": 8295, "mtime": 1756891390912, "results": "142", "hashOfConfig": "100"}, {"size": 5832, "mtime": 1756891014057, "results": "143", "hashOfConfig": "100"}, {"size": 163, "mtime": 1756792184475, "results": "144", "hashOfConfig": "100"}, {"size": 4752, "mtime": 1756977937384, "results": "145", "hashOfConfig": "100"}, {"size": 4085, "mtime": 1756896137803, "results": "146", "hashOfConfig": "100"}, {"size": 1652, "mtime": 1756814576851, "results": "147", "hashOfConfig": "100"}, {"size": 1368, "mtime": 1756814576858, "results": "148", "hashOfConfig": "100"}, {"size": 8368, "mtime": 1756977961751, "results": "149", "hashOfConfig": "100"}, {"size": 4821, "mtime": 1756896118959, "results": "150", "hashOfConfig": "100"}, {"size": 1666, "mtime": 1756893659821, "results": "151", "hashOfConfig": "100"}, {"size": 2220, "mtime": 1756978079845, "results": "152", "hashOfConfig": "100"}, {"size": 7312, "mtime": 1756894402060, "results": "153", "hashOfConfig": "100"}, {"size": 945, "mtime": 1756886916185, "results": "154", "hashOfConfig": "100"}, {"size": 3287, "mtime": 1756886934351, "results": "155", "hashOfConfig": "100"}, {"size": 4644, "mtime": 1756889607556, "results": "156", "hashOfConfig": "100"}, {"size": 1790, "mtime": 1756977609270, "results": "157", "hashOfConfig": "100"}, {"size": 9653, "mtime": 1756890967660, "results": "158", "hashOfConfig": "100"}, {"size": 2569, "mtime": 1756888490440, "results": "159", "hashOfConfig": "100"}, {"size": 4796, "mtime": 1756888515488, "results": "160", "hashOfConfig": "100"}, {"size": 8108, "mtime": 1756798104289, "results": "161", "hashOfConfig": "100"}, {"size": 8908, "mtime": 1756798123208, "results": "162", "hashOfConfig": "100"}, {"size": 6869, "mtime": 1756892563597, "results": "163", "hashOfConfig": "100"}, {"size": 10055, "mtime": 1756894133538, "results": "164", "hashOfConfig": "100"}, {"size": 14550, "mtime": 1756894523296, "results": "165", "hashOfConfig": "100"}, {"size": 13419, "mtime": 1756894096579, "results": "166", "hashOfConfig": "100"}, {"size": 424, "mtime": 1756882431724, "results": "167", "hashOfConfig": "100"}, {"size": 3253, "mtime": 1756881867586, "results": "168", "hashOfConfig": "100"}, {"size": 11015, "mtime": 1756976543352, "results": "169", "hashOfConfig": "100"}, {"size": 12859, "mtime": 1756887183493, "results": "170", "hashOfConfig": "100"}, {"size": 17377, "mtime": 1756978262384, "results": "171", "hashOfConfig": "100"}, {"size": 10412, "mtime": 1756797937081, "results": "172", "hashOfConfig": "100"}, {"size": 14392, "mtime": 1756890917447, "results": "173", "hashOfConfig": "100"}, {"size": 1783, "mtime": 1756880256590, "results": "174", "hashOfConfig": "100"}, {"size": 1300, "mtime": 1756880299321, "results": "175", "hashOfConfig": "100"}, {"size": 2085, "mtime": 1756880229145, "results": "176", "hashOfConfig": "100"}, {"size": 2032, "mtime": 1756880929472, "results": "177", "hashOfConfig": "100"}, {"size": 7302, "mtime": 1756879926941, "results": "178", "hashOfConfig": "100"}, {"size": 1007, "mtime": 1756977639787, "results": "179", "hashOfConfig": "100"}, {"size": 732, "mtime": 1756792184477, "results": "180", "hashOfConfig": "100"}, {"size": 2835, "mtime": 1756886907469, "results": "181", "hashOfConfig": "100"}, {"size": 1583, "mtime": 1756882485393, "results": "182", "hashOfConfig": "100"}, {"size": 1695, "mtime": 1756882497465, "results": "183", "hashOfConfig": "100"}, {"size": 5287, "mtime": 1756888536272, "results": "184", "hashOfConfig": "100"}, {"size": 5716, "mtime": 1756977996396, "results": "185", "hashOfConfig": "100"}, {"size": 2144, "mtime": 1756889548014, "results": "186", "hashOfConfig": "100"}, {"size": 288, "mtime": 1756791879185, "results": "187", "hashOfConfig": "100"}, {"size": 4030, "mtime": 1756799139956, "results": "188", "hashOfConfig": "100"}, {"size": 5462, "mtime": 1756895773964, "results": "189", "hashOfConfig": "100"}, {"size": 2316, "mtime": 1756898452977, "results": "190", "hashOfConfig": "100"}, {"size": 3183, "mtime": 1756812578167, "results": "191", "hashOfConfig": "100"}, {"size": 172, "mtime": 1756792184477, "results": "192", "hashOfConfig": "100"}, {"size": 1935, "mtime": 1756891638445, "results": "193", "hashOfConfig": "100"}, {"size": 559, "mtime": 1756798682449, "results": "194", "hashOfConfig": "100"}, {"size": 2121, "mtime": 1756976398242, "results": "195", "hashOfConfig": "100"}, {"size": 9303, "mtime": 1756976436031, "results": "196", "hashOfConfig": "100"}, {"size": 791, "mtime": 1756976447445, "results": "197", "hashOfConfig": "100"}, {"filePath": "198", "messages": "199", "suppressedMessages": "200", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1ydjnhk", {"filePath": "201", "messages": "202", "suppressedMessages": "203", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "204", "messages": "205", "suppressedMessages": "206", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "207", "messages": "208", "suppressedMessages": "209", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "210", "messages": "211", "suppressedMessages": "212", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "213", "messages": "214", "suppressedMessages": "215", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "216", "messages": "217", "suppressedMessages": "218", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "219", "messages": "220", "suppressedMessages": "221", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "222", "messages": "223", "suppressedMessages": "224", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "225", "messages": "226", "suppressedMessages": "227", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "228", "messages": "229", "suppressedMessages": "230", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "231", "messages": "232", "suppressedMessages": "233", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "234", "messages": "235", "suppressedMessages": "236", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "237", "messages": "238", "suppressedMessages": "239", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "240", "messages": "241", "suppressedMessages": "242", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "243", "messages": "244", "suppressedMessages": "245", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "246", "messages": "247", "suppressedMessages": "248", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "249", "messages": "250", "suppressedMessages": "251", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "252", "messages": "253", "suppressedMessages": "254", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "255", "messages": "256", "suppressedMessages": "257", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "258", "messages": "259", "suppressedMessages": "260", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "261", "messages": "262", "suppressedMessages": "263", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "264", "messages": "265", "suppressedMessages": "266", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "267", "messages": "268", "suppressedMessages": "269", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "270", "messages": "271", "suppressedMessages": "272", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "273", "messages": "274", "suppressedMessages": "275", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "276", "messages": "277", "suppressedMessages": "278", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "279", "messages": "280", "suppressedMessages": "281", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "282", "messages": "283", "suppressedMessages": "284", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "285", "messages": "286", "suppressedMessages": "287", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "288", "messages": "289", "suppressedMessages": "290", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "291", "messages": "292", "suppressedMessages": "293", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "294", "messages": "295", "suppressedMessages": "296", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "297", "messages": "298", "suppressedMessages": "299", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "300", "messages": "301", "suppressedMessages": "302", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "303", "messages": "304", "suppressedMessages": "305", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "306", "messages": "307", "suppressedMessages": "308", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "309", "messages": "310", "suppressedMessages": "311", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "312", "messages": "313", "suppressedMessages": "314", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "315", "messages": "316", "suppressedMessages": "317", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "318", "messages": "319", "suppressedMessages": "320", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "321", "messages": "322", "suppressedMessages": "323", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "324", "messages": "325", "suppressedMessages": "326", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "327", "messages": "328", "suppressedMessages": "329", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "330", "messages": "331", "suppressedMessages": "332", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "333", "messages": "334", "suppressedMessages": "335", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "336", "messages": "337", "suppressedMessages": "338", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "339", "messages": "340", "suppressedMessages": "341", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "342", "messages": "343", "suppressedMessages": "344", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "345", "messages": "346", "suppressedMessages": "347", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "348", "messages": "349", "suppressedMessages": "350", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "351", "messages": "352", "suppressedMessages": "353", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "354", "messages": "355", "suppressedMessages": "356", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "357", "messages": "358", "suppressedMessages": "359", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "360", "messages": "361", "suppressedMessages": "362", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "363", "messages": "364", "suppressedMessages": "365", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "366", "messages": "367", "suppressedMessages": "368", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "369", "messages": "370", "suppressedMessages": "371", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "372", "messages": "373", "suppressedMessages": "374", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "375", "messages": "376", "suppressedMessages": "377", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "378", "messages": "379", "suppressedMessages": "380", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "381", "messages": "382", "suppressedMessages": "383", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "384", "messages": "385", "suppressedMessages": "386", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "387", "messages": "388", "suppressedMessages": "389", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "390", "messages": "391", "suppressedMessages": "392", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "393", "messages": "394", "suppressedMessages": "395", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "396", "messages": "397", "suppressedMessages": "398", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "399", "messages": "400", "suppressedMessages": "401", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "402", "messages": "403", "suppressedMessages": "404", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "405", "messages": "406", "suppressedMessages": "407", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "408", "messages": "409", "suppressedMessages": "410", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "411", "messages": "412", "suppressedMessages": "413", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "414", "messages": "415", "suppressedMessages": "416", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "417", "messages": "418", "suppressedMessages": "419", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "420", "messages": "421", "suppressedMessages": "422", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "423", "messages": "424", "suppressedMessages": "425", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "426", "messages": "427", "suppressedMessages": "428", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "429", "messages": "430", "suppressedMessages": "431", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "432", "messages": "433", "suppressedMessages": "434", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "435", "messages": "436", "suppressedMessages": "437", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "438", "messages": "439", "suppressedMessages": "440", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "441", "messages": "442", "suppressedMessages": "443", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "444", "messages": "445", "suppressedMessages": "446", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "447", "messages": "448", "suppressedMessages": "449", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "450", "messages": "451", "suppressedMessages": "452", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "453", "messages": "454", "suppressedMessages": "455", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "456", "messages": "457", "suppressedMessages": "458", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "459", "messages": "460", "suppressedMessages": "461", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "462", "messages": "463", "suppressedMessages": "464", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "465", "messages": "466", "suppressedMessages": "467", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "468", "messages": "469", "suppressedMessages": "470", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "471", "messages": "472", "suppressedMessages": "473", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "474", "messages": "475", "suppressedMessages": "476", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "477", "messages": "478", "suppressedMessages": "479", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "480", "messages": "481", "suppressedMessages": "482", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "483", "messages": "484", "suppressedMessages": "485", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "486", "messages": "487", "suppressedMessages": "488", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "489", "messages": "490", "suppressedMessages": "491", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(auth)\\login\\page.tsx", [], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\admin\\attendance\\page.tsx", ["492"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\admin\\classes\\new\\page.tsx", [], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\admin\\classes\\page.tsx", ["493", "494", "495", "496", "497", "498", "499", "500", "501", "502", "503"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\admin\\exams\\page.tsx", ["504", "505", "506", "507", "508", "509"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\admin\\marks\\page.tsx", ["510", "511", "512", "513"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\admin\\page.tsx", ["514", "515", "516"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\admin\\reports\\page.tsx", ["517", "518"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\admin\\settings\\page.tsx", ["519", "520", "521", "522", "523", "524"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\admin\\students\\bulk\\page.tsx", ["525", "526"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\admin\\students\\new\\page.tsx", ["527", "528", "529", "530"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\admin\\students\\page.tsx", ["531", "532", "533", "534", "535"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\admin\\students\\[id]\\edit\\page.tsx", ["536", "537", "538", "539", "540", "541"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\admin\\students\\[id]\\page.tsx", ["542", "543", "544", "545", "546", "547", "548", "549"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\admin\\subjects\\page.tsx", ["550"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\admin\\teachers\\new\\page.tsx", ["551", "552", "553", "554"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\admin\\teachers\\page.tsx", ["555", "556", "557", "558", "559", "560", "561", "562", "563", "564", "565"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\admin\\teachers\\[id]\\edit\\page.tsx", ["566", "567"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\admin\\teachers\\[id]\\page.tsx", ["568", "569"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\student\\attendance\\page.tsx", ["570"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\student\\marks\\page.tsx", ["571", "572", "573"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\student\\page.tsx", ["574", "575", "576", "577", "578"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\student\\reports\\page.tsx", ["579", "580"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\teacher\\attendance\\mark\\page.tsx", [], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\teacher\\attendance\\page.tsx", ["581", "582"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\teacher\\marks\\page.tsx", ["583", "584", "585", "586"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\teacher\\marks\\[examId]\\page.tsx", ["587", "588", "589", "590", "591", "592", "593", "594", "595", "596"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\teacher\\marks\\[examId]\\view\\page.tsx", ["597", "598", "599", "600"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\(dash)\\teacher\\page.tsx", ["601"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\admin\\attendance\\route.ts", ["602", "603"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\admin\\classes\\route.ts", ["604", "605", "606", "607"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\admin\\classes\\[id]\\route.ts", ["608", "609", "610"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\admin\\dashboard\\stats\\route.ts", ["611"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\admin\\exams\\route.ts", [], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\admin\\marks\\route.ts", ["612"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\admin\\reports\\route.ts", ["613"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\admin\\sections\\route.ts", ["614"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\admin\\settings\\route.ts", [], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\admin\\students\\bulk\\route.ts", [], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\admin\\students\\route.ts", ["615", "616", "617"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\admin\\students\\[id]\\route.ts", ["618", "619", "620", "621", "622", "623"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\admin\\subjects\\route.ts", ["624", "625"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\admin\\teachers\\route.ts", ["626"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\admin\\teachers\\[id]\\route.ts", [], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\auth\\[...nextauth]\\route.ts", [], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\student\\attendance\\route.ts", ["627"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\student\\dashboard\\stats\\route.ts", ["628"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\student\\marks\\route.ts", ["629"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\student\\reports\\route.ts", ["630"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\teacher\\attendance\\route.ts", [], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\teacher\\dashboard\\stats\\route.ts", ["631"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\teacher\\exams\\route.ts", ["632"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\teacher\\exams\\[examId]\\students\\route.ts", [], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\teacher\\marks\\route.ts", ["633"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\layout.tsx", [], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\page.tsx", [], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\test-login\\page.tsx", ["634"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\unauthorized\\page.tsx", [], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\attendance\\attendance-form.tsx", ["635", "636"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\auth\\logout-button.tsx", [], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\auth\\protected-route.tsx", ["637", "638", "639"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\classes\\class-form.tsx", ["640", "641"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\classes\\class-table.tsx", ["642"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\layout\\dashboard-layout.tsx", [], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\marks\\grade-calculator.tsx", [], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\marks\\marks-entry-form.tsx", ["643", "644"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\marks\\marks-table.tsx", ["645"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\providers\\session-provider.tsx", [], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\providers\\theme-provider.tsx", [], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\students\\bulk-import.tsx", [], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\students\\student-form.tsx", [], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\students\\student-table.tsx", ["646"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\teachers\\teacher-form.tsx", ["647", "648", "649", "650"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\teachers\\teacher-table.tsx", ["651", "652", "653", "654", "655"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\ui\\alert.tsx", [], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\ui\\badge.tsx", [], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\ui\\button.tsx", [], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\ui\\card.tsx", [], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\ui\\dropdown-menu.tsx", [], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\ui\\input.tsx", ["656"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\ui\\label.tsx", [], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\ui\\responsive-container.tsx", [], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\ui\\simple-theme-toggle.tsx", ["657", "658"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\ui\\theme-toggle.tsx", ["659"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\hooks\\use-login.ts", [], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\lib\\auth-utils.ts", [], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\lib\\auth.ts", [], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\lib\\db.ts", [], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\lib\\grading.ts", ["660", "661"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\lib\\marks-validation.ts", ["662"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\lib\\navigation.ts", [], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\lib\\rbac.ts", ["663", "664", "665", "666"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\lib\\utils.ts", [], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\middleware.ts", [], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\types\\next-auth.d.ts", ["667"], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\app\\api\\admin\\students\\template\\route.ts", [], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\students\\StudentImport.tsx", [], [], "C:\\xampp\\htdocs\\Advance School\\school-management-system\\src\\components\\ui\\progress.tsx", [], [], {"ruleId": "668", "severity": 1, "message": "669", "line": 10, "column": 3, "nodeType": null, "messageId": "670", "endLine": 10, "endColumn": 11}, {"ruleId": "668", "severity": 1, "message": "671", "line": 12, "column": 3, "nodeType": null, "messageId": "670", "endLine": 12, "endColumn": 8}, {"ruleId": "668", "severity": 1, "message": "672", "line": 13, "column": 3, "nodeType": null, "messageId": "670", "endLine": 13, "endColumn": 16}, {"ruleId": "668", "severity": 1, "message": "673", "line": 14, "column": 3, "nodeType": null, "messageId": "670", "endLine": 14, "endColumn": 11}, {"ruleId": "668", "severity": 1, "message": "674", "line": 15, "column": 3, "nodeType": null, "messageId": "670", "endLine": 15, "endColumn": 11}, {"ruleId": "668", "severity": 1, "message": "669", "line": 16, "column": 3, "nodeType": null, "messageId": "670", "endLine": 16, "endColumn": 11}, {"ruleId": "668", "severity": 1, "message": "675", "line": 17, "column": 3, "nodeType": null, "messageId": "670", "endLine": 17, "endColumn": 12}, {"ruleId": "668", "severity": 1, "message": "676", "line": 18, "column": 3, "nodeType": null, "messageId": "670", "endLine": 18, "endColumn": 11}, {"ruleId": "668", "severity": 1, "message": "677", "line": 19, "column": 3, "nodeType": null, "messageId": "670", "endLine": 19, "endColumn": 11}, {"ruleId": "668", "severity": 1, "message": "678", "line": 20, "column": 3, "nodeType": null, "messageId": "670", "endLine": 20, "endColumn": 16}, {"ruleId": "668", "severity": 1, "message": "679", "line": 21, "column": 3, "nodeType": null, "messageId": "670", "endLine": 21, "endColumn": 8}, {"ruleId": "680", "severity": 1, "message": "681", "line": 86, "column": 6, "nodeType": "682", "endLine": 86, "endColumn": 49, "suggestions": "683"}, {"ruleId": "668", "severity": 1, "message": "684", "line": 6, "column": 10, "nodeType": null, "messageId": "670", "endLine": 6, "endColumn": 15}, {"ruleId": "668", "severity": 1, "message": "685", "line": 7, "column": 10, "nodeType": null, "messageId": "670", "endLine": 7, "endColumn": 15}, {"ruleId": "668", "severity": 1, "message": "686", "line": 15, "column": 3, "nodeType": null, "messageId": "670", "endLine": 15, "endColumn": 8}, {"ruleId": "668", "severity": 1, "message": "679", "line": 16, "column": 3, "nodeType": null, "messageId": "670", "endLine": 16, "endColumn": 8}, {"ruleId": "668", "severity": 1, "message": "687", "line": 43, "column": 10, "nodeType": null, "messageId": "670", "endLine": 43, "endColumn": 21}, {"ruleId": "668", "severity": 1, "message": "688", "line": 44, "column": 10, "nodeType": null, "messageId": "670", "endLine": 44, "endColumn": 21}, {"ruleId": "668", "severity": 1, "message": "684", "line": 6, "column": 10, "nodeType": null, "messageId": "670", "endLine": 6, "endColumn": 15}, {"ruleId": "668", "severity": 1, "message": "669", "line": 10, "column": 3, "nodeType": null, "messageId": "670", "endLine": 10, "endColumn": 11}, {"ruleId": "668", "severity": 1, "message": "673", "line": 12, "column": 3, "nodeType": null, "messageId": "670", "endLine": 12, "endColumn": 11}, {"ruleId": "668", "severity": 1, "message": "671", "line": 13, "column": 3, "nodeType": null, "messageId": "670", "endLine": 13, "endColumn": 8}, {"ruleId": "668", "severity": 1, "message": "689", "line": 6, "column": 29, "nodeType": null, "messageId": "670", "endLine": 6, "endColumn": 44}, {"ruleId": "668", "severity": 1, "message": "669", "line": 14, "column": 3, "nodeType": null, "messageId": "670", "endLine": 14, "endColumn": 11}, {"ruleId": "668", "severity": 1, "message": "676", "line": 16, "column": 3, "nodeType": null, "messageId": "670", "endLine": 16, "endColumn": 11}, {"ruleId": "668", "severity": 1, "message": "684", "line": 6, "column": 10, "nodeType": null, "messageId": "670", "endLine": 6, "endColumn": 15}, {"ruleId": "668", "severity": 1, "message": "669", "line": 10, "column": 3, "nodeType": null, "messageId": "670", "endLine": 10, "endColumn": 11}, {"ruleId": "668", "severity": 1, "message": "676", "line": 11, "column": 3, "nodeType": null, "messageId": "670", "endLine": 11, "endColumn": 11}, {"ruleId": "668", "severity": 1, "message": "671", "line": 13, "column": 3, "nodeType": null, "messageId": "670", "endLine": 13, "endColumn": 8}, {"ruleId": "668", "severity": 1, "message": "690", "line": 163, "column": 14, "nodeType": null, "messageId": "670", "endLine": 163, "endColumn": 19}, {"ruleId": "668", "severity": 1, "message": "690", "line": 184, "column": 14, "nodeType": null, "messageId": "670", "endLine": 184, "endColumn": 19}, {"ruleId": "668", "severity": 1, "message": "690", "line": 205, "column": 14, "nodeType": null, "messageId": "670", "endLine": 205, "endColumn": 19}, {"ruleId": "668", "severity": 1, "message": "690", "line": 226, "column": 14, "nodeType": null, "messageId": "670", "endLine": 226, "endColumn": 19}, {"ruleId": "668", "severity": 1, "message": "671", "line": 7, "column": 10, "nodeType": null, "messageId": "670", "endLine": 7, "endColumn": 15}, {"ruleId": "668", "severity": 1, "message": "691", "line": 7, "column": 17, "nodeType": null, "messageId": "670", "endLine": 7, "endColumn": 23}, {"ruleId": "668", "severity": 1, "message": "692", "line": 1, "column": 10, "nodeType": null, "messageId": "670", "endLine": 1, "endColumn": 26}, {"ruleId": "668", "severity": 1, "message": "693", "line": 2, "column": 10, "nodeType": null, "messageId": "670", "endLine": 2, "endColumn": 18}, {"ruleId": "668", "severity": 1, "message": "694", "line": 3, "column": 10, "nodeType": null, "messageId": "670", "endLine": 3, "endColumn": 21}, {"ruleId": "668", "severity": 1, "message": "695", "line": 5, "column": 10, "nodeType": null, "messageId": "670", "endLine": 5, "endColumn": 23}, {"ruleId": "668", "severity": 1, "message": "692", "line": 2, "column": 10, "nodeType": null, "messageId": "670", "endLine": 2, "endColumn": 26}, {"ruleId": "668", "severity": 1, "message": "693", "line": 3, "column": 10, "nodeType": null, "messageId": "670", "endLine": 3, "endColumn": 18}, {"ruleId": "668", "severity": 1, "message": "694", "line": 4, "column": 10, "nodeType": null, "messageId": "670", "endLine": 4, "endColumn": 21}, {"ruleId": "668", "severity": 1, "message": "695", "line": 6, "column": 10, "nodeType": null, "messageId": "670", "endLine": 6, "endColumn": 23}, {"ruleId": "668", "severity": 1, "message": "696", "line": 12, "column": 24, "nodeType": null, "messageId": "670", "endLine": 12, "endColumn": 32}, {"ruleId": "668", "severity": 1, "message": "692", "line": 1, "column": 10, "nodeType": null, "messageId": "670", "endLine": 1, "endColumn": 26}, {"ruleId": "668", "severity": 1, "message": "693", "line": 2, "column": 10, "nodeType": null, "messageId": "670", "endLine": 2, "endColumn": 18}, {"ruleId": "668", "severity": 1, "message": "694", "line": 3, "column": 10, "nodeType": null, "messageId": "670", "endLine": 3, "endColumn": 21}, {"ruleId": "668", "severity": 1, "message": "695", "line": 5, "column": 10, "nodeType": null, "messageId": "670", "endLine": 5, "endColumn": 23}, {"ruleId": "668", "severity": 1, "message": "671", "line": 9, "column": 10, "nodeType": null, "messageId": "670", "endLine": 9, "endColumn": 15}, {"ruleId": "668", "severity": 1, "message": "697", "line": 9, "column": 17, "nodeType": null, "messageId": "670", "endLine": 9, "endColumn": 21}, {"ruleId": "668", "severity": 1, "message": "692", "line": 1, "column": 10, "nodeType": null, "messageId": "670", "endLine": 1, "endColumn": 26}, {"ruleId": "668", "severity": 1, "message": "693", "line": 2, "column": 10, "nodeType": null, "messageId": "670", "endLine": 2, "endColumn": 18}, {"ruleId": "668", "severity": 1, "message": "694", "line": 3, "column": 10, "nodeType": null, "messageId": "670", "endLine": 3, "endColumn": 21}, {"ruleId": "668", "severity": 1, "message": "695", "line": 5, "column": 10, "nodeType": null, "messageId": "670", "endLine": 5, "endColumn": 23}, {"ruleId": "668", "severity": 1, "message": "689", "line": 8, "column": 29, "nodeType": null, "messageId": "670", "endLine": 8, "endColumn": 44}, {"ruleId": "668", "severity": 1, "message": "698", "line": 13, "column": 3, "nodeType": null, "messageId": "670", "endLine": 13, "endColumn": 7}, {"ruleId": "668", "severity": 1, "message": "669", "line": 15, "column": 3, "nodeType": null, "messageId": "670", "endLine": 15, "endColumn": 11}, {"ruleId": "668", "severity": 1, "message": "699", "line": 16, "column": 3, "nodeType": null, "messageId": "670", "endLine": 16, "endColumn": 9}, {"ruleId": "680", "severity": 1, "message": "700", "line": 66, "column": 6, "nodeType": "682", "endLine": 66, "endColumn": 35, "suggestions": "701"}, {"ruleId": "668", "severity": 1, "message": "692", "line": 1, "column": 10, "nodeType": null, "messageId": "670", "endLine": 1, "endColumn": 26}, {"ruleId": "668", "severity": 1, "message": "693", "line": 2, "column": 10, "nodeType": null, "messageId": "670", "endLine": 2, "endColumn": 18}, {"ruleId": "668", "severity": 1, "message": "694", "line": 3, "column": 10, "nodeType": null, "messageId": "670", "endLine": 3, "endColumn": 21}, {"ruleId": "668", "severity": 1, "message": "695", "line": 4, "column": 10, "nodeType": null, "messageId": "670", "endLine": 4, "endColumn": 23}, {"ruleId": "668", "severity": 1, "message": "671", "line": 12, "column": 3, "nodeType": null, "messageId": "670", "endLine": 12, "endColumn": 8}, {"ruleId": "668", "severity": 1, "message": "672", "line": 13, "column": 3, "nodeType": null, "messageId": "670", "endLine": 13, "endColumn": 16}, {"ruleId": "668", "severity": 1, "message": "673", "line": 14, "column": 3, "nodeType": null, "messageId": "670", "endLine": 14, "endColumn": 11}, {"ruleId": "668", "severity": 1, "message": "674", "line": 15, "column": 3, "nodeType": null, "messageId": "670", "endLine": 15, "endColumn": 11}, {"ruleId": "668", "severity": 1, "message": "669", "line": 16, "column": 3, "nodeType": null, "messageId": "670", "endLine": 16, "endColumn": 11}, {"ruleId": "668", "severity": 1, "message": "675", "line": 17, "column": 3, "nodeType": null, "messageId": "670", "endLine": 17, "endColumn": 12}, {"ruleId": "668", "severity": 1, "message": "676", "line": 18, "column": 3, "nodeType": null, "messageId": "670", "endLine": 18, "endColumn": 11}, {"ruleId": "668", "severity": 1, "message": "677", "line": 19, "column": 3, "nodeType": null, "messageId": "670", "endLine": 19, "endColumn": 11}, {"ruleId": "668", "severity": 1, "message": "678", "line": 20, "column": 3, "nodeType": null, "messageId": "670", "endLine": 20, "endColumn": 16}, {"ruleId": "668", "severity": 1, "message": "679", "line": 21, "column": 3, "nodeType": null, "messageId": "670", "endLine": 21, "endColumn": 8}, {"ruleId": "680", "severity": 1, "message": "702", "line": 95, "column": 6, "nodeType": "682", "endLine": 95, "endColumn": 49, "suggestions": "703"}, {"ruleId": "668", "severity": 1, "message": "704", "line": 54, "column": 16, "nodeType": null, "messageId": "670", "endLine": 54, "endColumn": 19}, {"ruleId": "705", "severity": 1, "message": "706", "line": 74, "column": 21, "nodeType": "707", "messageId": "708", "endLine": 74, "endColumn": 24, "suggestions": "709"}, {"ruleId": "668", "severity": 1, "message": "704", "line": 57, "column": 16, "nodeType": null, "messageId": "670", "endLine": 57, "endColumn": 19}, {"ruleId": "705", "severity": 1, "message": "706", "line": 77, "column": 21, "nodeType": "707", "messageId": "708", "endLine": 77, "endColumn": 24, "suggestions": "710"}, {"ruleId": "705", "severity": 1, "message": "706", "line": 56, "column": 21, "nodeType": "707", "messageId": "708", "endLine": 56, "endColumn": 24, "suggestions": "711"}, {"ruleId": "668", "severity": 1, "message": "669", "line": 10, "column": 3, "nodeType": null, "messageId": "670", "endLine": 10, "endColumn": 11}, {"ruleId": "668", "severity": 1, "message": "712", "line": 45, "column": 17, "nodeType": null, "messageId": "670", "endLine": 45, "endColumn": 24}, {"ruleId": "680", "severity": 1, "message": "713", "line": 56, "column": 6, "nodeType": "682", "endLine": 56, "endColumn": 37, "suggestions": "714"}, {"ruleId": "668", "severity": 1, "message": "689", "line": 5, "column": 29, "nodeType": null, "messageId": "670", "endLine": 5, "endColumn": 44}, {"ruleId": "668", "severity": 1, "message": "715", "line": 10, "column": 3, "nodeType": null, "messageId": "670", "endLine": 10, "endColumn": 7}, {"ruleId": "668", "severity": 1, "message": "672", "line": 11, "column": 3, "nodeType": null, "messageId": "670", "endLine": 11, "endColumn": 16}, {"ruleId": "668", "severity": 1, "message": "716", "line": 12, "column": 3, "nodeType": null, "messageId": "670", "endLine": 12, "endColumn": 9}, {"ruleId": "668", "severity": 1, "message": "671", "line": 19, "column": 3, "nodeType": null, "messageId": "670", "endLine": 19, "endColumn": 8}, {"ruleId": "668", "severity": 1, "message": "669", "line": 10, "column": 3, "nodeType": null, "messageId": "670", "endLine": 10, "endColumn": 11}, {"ruleId": "668", "severity": 1, "message": "712", "line": 35, "column": 17, "nodeType": null, "messageId": "670", "endLine": 35, "endColumn": 24}, {"ruleId": "705", "severity": 1, "message": "706", "line": 63, "column": 19, "nodeType": "707", "messageId": "708", "endLine": 63, "endColumn": 22, "suggestions": "717"}, {"ruleId": "680", "severity": 1, "message": "718", "line": 72, "column": 6, "nodeType": "682", "endLine": 72, "endColumn": 52, "suggestions": "719"}, {"ruleId": "668", "severity": 1, "message": "669", "line": 10, "column": 3, "nodeType": null, "messageId": "670", "endLine": 10, "endColumn": 11}, {"ruleId": "668", "severity": 1, "message": "720", "line": 14, "column": 3, "nodeType": null, "messageId": "670", "endLine": 14, "endColumn": 7}, {"ruleId": "668", "severity": 1, "message": "712", "line": 47, "column": 17, "nodeType": null, "messageId": "670", "endLine": 47, "endColumn": 24}, {"ruleId": "680", "severity": 1, "message": "721", "line": 55, "column": 6, "nodeType": "682", "endLine": 55, "endColumn": 37, "suggestions": "722"}, {"ruleId": "668", "severity": 1, "message": "689", "line": 6, "column": 29, "nodeType": null, "messageId": "670", "endLine": 6, "endColumn": 44}, {"ruleId": "668", "severity": 1, "message": "669", "line": 12, "column": 3, "nodeType": null, "messageId": "670", "endLine": 12, "endColumn": 11}, {"ruleId": "668", "severity": 1, "message": "679", "line": 13, "column": 3, "nodeType": null, "messageId": "670", "endLine": 13, "endColumn": 8}, {"ruleId": "668", "severity": 1, "message": "671", "line": 15, "column": 3, "nodeType": null, "messageId": "670", "endLine": 15, "endColumn": 8}, {"ruleId": "668", "severity": 1, "message": "723", "line": 67, "column": 9, "nodeType": null, "messageId": "670", "endLine": 67, "endColumn": 15}, {"ruleId": "668", "severity": 1, "message": "712", "line": 68, "column": 17, "nodeType": null, "messageId": "670", "endLine": 68, "endColumn": 24}, {"ruleId": "680", "severity": 1, "message": "724", "line": 77, "column": 6, "nodeType": "682", "endLine": 77, "endColumn": 14, "suggestions": "725"}, {"ruleId": "705", "severity": 1, "message": "706", "line": 113, "column": 56, "nodeType": "707", "messageId": "708", "endLine": 113, "endColumn": 59, "suggestions": "726"}, {"ruleId": "705", "severity": 1, "message": "706", "line": 114, "column": 54, "nodeType": "707", "messageId": "708", "endLine": 114, "endColumn": 57, "suggestions": "727"}, {"ruleId": "705", "severity": 1, "message": "706", "line": 121, "column": 81, "nodeType": "707", "messageId": "708", "endLine": 121, "endColumn": 84, "suggestions": "728"}, {"ruleId": "668", "severity": 1, "message": "689", "line": 6, "column": 29, "nodeType": null, "messageId": "670", "endLine": 6, "endColumn": 44}, {"ruleId": "668", "severity": 1, "message": "669", "line": 12, "column": 3, "nodeType": null, "messageId": "670", "endLine": 12, "endColumn": 11}, {"ruleId": "668", "severity": 1, "message": "712", "line": 73, "column": 17, "nodeType": null, "messageId": "670", "endLine": 73, "endColumn": 24}, {"ruleId": "680", "severity": 1, "message": "724", "line": 81, "column": 6, "nodeType": "682", "endLine": 81, "endColumn": 14, "suggestions": "729"}, {"ruleId": "668", "severity": 1, "message": "715", "line": 15, "column": 3, "nodeType": null, "messageId": "670", "endLine": 15, "endColumn": 7}, {"ruleId": "668", "severity": 1, "message": "692", "line": 2, "column": 10, "nodeType": null, "messageId": "670", "endLine": 2, "endColumn": 26}, {"ruleId": "668", "severity": 1, "message": "694", "line": 3, "column": 10, "nodeType": null, "messageId": "670", "endLine": 3, "endColumn": 21}, {"ruleId": "668", "severity": 1, "message": "692", "line": 2, "column": 10, "nodeType": null, "messageId": "670", "endLine": 2, "endColumn": 26}, {"ruleId": "668", "severity": 1, "message": "694", "line": 3, "column": 10, "nodeType": null, "messageId": "670", "endLine": 3, "endColumn": 21}, {"ruleId": "668", "severity": 1, "message": "695", "line": 5, "column": 10, "nodeType": null, "messageId": "670", "endLine": 5, "endColumn": 23}, {"ruleId": "668", "severity": 1, "message": "730", "line": 30, "column": 11, "nodeType": null, "messageId": "670", "endLine": 30, "endColumn": 19}, {"ruleId": "668", "severity": 1, "message": "692", "line": 2, "column": 10, "nodeType": null, "messageId": "670", "endLine": 2, "endColumn": 26}, {"ruleId": "668", "severity": 1, "message": "694", "line": 3, "column": 10, "nodeType": null, "messageId": "670", "endLine": 3, "endColumn": 21}, {"ruleId": "668", "severity": 1, "message": "695", "line": 5, "column": 10, "nodeType": null, "messageId": "670", "endLine": 5, "endColumn": 23}, {"ruleId": "668", "severity": 1, "message": "731", "line": 6, "column": 27, "nodeType": null, "messageId": "670", "endLine": 6, "endColumn": 34}, {"ruleId": "705", "severity": 1, "message": "706", "line": 19, "column": 18, "nodeType": "707", "messageId": "708", "endLine": 19, "endColumn": 21, "suggestions": "732"}, {"ruleId": "705", "severity": 1, "message": "706", "line": 19, "column": 18, "nodeType": "707", "messageId": "708", "endLine": 19, "endColumn": 21, "suggestions": "733"}, {"ruleId": "705", "severity": 1, "message": "706", "line": 32, "column": 18, "nodeType": "707", "messageId": "708", "endLine": 32, "endColumn": 21, "suggestions": "734"}, {"ruleId": "668", "severity": 1, "message": "692", "line": 2, "column": 10, "nodeType": null, "messageId": "670", "endLine": 2, "endColumn": 26}, {"ruleId": "668", "severity": 1, "message": "694", "line": 3, "column": 10, "nodeType": null, "messageId": "670", "endLine": 3, "endColumn": 21}, {"ruleId": "668", "severity": 1, "message": "695", "line": 6, "column": 10, "nodeType": null, "messageId": "670", "endLine": 6, "endColumn": 23}, {"ruleId": "668", "severity": 1, "message": "692", "line": 2, "column": 10, "nodeType": null, "messageId": "670", "endLine": 2, "endColumn": 26}, {"ruleId": "668", "severity": 1, "message": "694", "line": 3, "column": 10, "nodeType": null, "messageId": "670", "endLine": 3, "endColumn": 21}, {"ruleId": "668", "severity": 1, "message": "695", "line": 6, "column": 10, "nodeType": null, "messageId": "670", "endLine": 6, "endColumn": 23}, {"ruleId": "705", "severity": 1, "message": "706", "line": 155, "column": 27, "nodeType": "707", "messageId": "708", "endLine": 155, "endColumn": 30, "suggestions": "735"}, {"ruleId": "705", "severity": 1, "message": "706", "line": 162, "column": 30, "nodeType": "707", "messageId": "708", "endLine": 162, "endColumn": 33, "suggestions": "736"}, {"ruleId": "668", "severity": 1, "message": "737", "line": 172, "column": 11, "nodeType": null, "messageId": "670", "endLine": 172, "endColumn": 22}, {"ruleId": "668", "severity": 1, "message": "695", "line": 5, "column": 10, "nodeType": null, "messageId": "670", "endLine": 5, "endColumn": 23}, {"ruleId": "705", "severity": 1, "message": "706", "line": 32, "column": 18, "nodeType": "707", "messageId": "708", "endLine": 32, "endColumn": 21, "suggestions": "738"}, {"ruleId": "668", "severity": 1, "message": "730", "line": 38, "column": 11, "nodeType": null, "messageId": "670", "endLine": 38, "endColumn": 19}, {"ruleId": "668", "severity": 1, "message": "695", "line": 5, "column": 10, "nodeType": null, "messageId": "670", "endLine": 5, "endColumn": 23}, {"ruleId": "668", "severity": 1, "message": "731", "line": 6, "column": 27, "nodeType": null, "messageId": "670", "endLine": 6, "endColumn": 34}, {"ruleId": "705", "severity": 1, "message": "706", "line": 29, "column": 18, "nodeType": "707", "messageId": "708", "endLine": 29, "endColumn": 21, "suggestions": "739"}, {"ruleId": "705", "severity": 1, "message": "706", "line": 28, "column": 18, "nodeType": "707", "messageId": "708", "endLine": 28, "endColumn": 21, "suggestions": "740"}, {"ruleId": "668", "severity": 1, "message": "731", "line": 6, "column": 27, "nodeType": null, "messageId": "670", "endLine": 6, "endColumn": 34}, {"ruleId": "705", "severity": 1, "message": "706", "line": 29, "column": 18, "nodeType": "707", "messageId": "708", "endLine": 29, "endColumn": 21, "suggestions": "741"}, {"ruleId": "705", "severity": 1, "message": "706", "line": 36, "column": 18, "nodeType": "707", "messageId": "708", "endLine": 36, "endColumn": 21, "suggestions": "742"}, {"ruleId": "705", "severity": 1, "message": "706", "line": 16, "column": 40, "nodeType": "707", "messageId": "708", "endLine": 16, "endColumn": 43, "suggestions": "743"}, {"ruleId": "668", "severity": 1, "message": "744", "line": 10, "column": 10, "nodeType": null, "messageId": "670", "endLine": 10, "endColumn": 15}, {"ruleId": "705", "severity": 1, "message": "706", "line": 114, "column": 19, "nodeType": "707", "messageId": "708", "endLine": 114, "endColumn": 22, "suggestions": "745"}, {"ruleId": "668", "severity": 1, "message": "746", "line": 7, "column": 10, "nodeType": null, "messageId": "670", "endLine": 7, "endColumn": 19}, {"ruleId": "705", "severity": 1, "message": "706", "line": 103, "column": 19, "nodeType": "707", "messageId": "708", "endLine": 103, "endColumn": 22, "suggestions": "747"}, {"ruleId": "705", "severity": 1, "message": "706", "line": 153, "column": 75, "nodeType": "707", "messageId": "708", "endLine": 153, "endColumn": 78, "suggestions": "748"}, {"ruleId": "705", "severity": 1, "message": "706", "line": 12, "column": 15, "nodeType": "707", "messageId": "708", "endLine": 12, "endColumn": 18, "suggestions": "749"}, {"ruleId": "705", "severity": 1, "message": "706", "line": 127, "column": 19, "nodeType": "707", "messageId": "708", "endLine": 127, "endColumn": 22, "suggestions": "750"}, {"ruleId": "705", "severity": 1, "message": "706", "line": 86, "column": 21, "nodeType": "707", "messageId": "708", "endLine": 86, "endColumn": 24, "suggestions": "751"}, {"ruleId": "668", "severity": 1, "message": "752", "line": 8, "column": 29, "nodeType": null, "messageId": "670", "endLine": 8, "endColumn": 51}, {"ruleId": "668", "severity": 1, "message": "753", "line": 141, "column": 17, "nodeType": null, "messageId": "670", "endLine": 141, "endColumn": 18}, {"ruleId": "668", "severity": 1, "message": "754", "line": 9, "column": 3, "nodeType": null, "messageId": "670", "endLine": 9, "endColumn": 6}, {"ruleId": "668", "severity": 1, "message": "755", "line": 3, "column": 20, "nodeType": null, "messageId": "670", "endLine": 3, "endColumn": 29}, {"ruleId": "668", "severity": 1, "message": "744", "line": 10, "column": 10, "nodeType": null, "messageId": "670", "endLine": 10, "endColumn": 15}, {"ruleId": "705", "severity": 1, "message": "706", "line": 13, "column": 13, "nodeType": "707", "messageId": "708", "endLine": 13, "endColumn": 16, "suggestions": "756"}, {"ruleId": "705", "severity": 1, "message": "706", "line": 22, "column": 50, "nodeType": "707", "messageId": "708", "endLine": 22, "endColumn": 53, "suggestions": "757"}, {"ruleId": "705", "severity": 1, "message": "706", "line": 104, "column": 19, "nodeType": "707", "messageId": "708", "endLine": 104, "endColumn": 22, "suggestions": "758"}, {"ruleId": "668", "severity": 1, "message": "755", "line": 3, "column": 20, "nodeType": null, "messageId": "670", "endLine": 3, "endColumn": 29}, {"ruleId": "668", "severity": 1, "message": "759", "line": 9, "column": 10, "nodeType": null, "messageId": "670", "endLine": 9, "endColumn": 15}, {"ruleId": "668", "severity": 1, "message": "760", "line": 9, "column": 17, "nodeType": null, "messageId": "670", "endLine": 9, "endColumn": 33}, {"ruleId": "705", "severity": 1, "message": "706", "line": 96, "column": 21, "nodeType": "707", "messageId": "708", "endLine": 96, "endColumn": 24, "suggestions": "761"}, {"ruleId": "668", "severity": 1, "message": "762", "line": 103, "column": 9, "nodeType": null, "messageId": "670", "endLine": 103, "endColumn": 19}, {"ruleId": "763", "severity": 1, "message": "764", "line": 4, "column": 18, "nodeType": "765", "messageId": "766", "endLine": 4, "endColumn": 28, "suggestions": "767"}, {"ruleId": "668", "severity": 1, "message": "755", "line": 3, "column": 10, "nodeType": null, "messageId": "670", "endLine": 3, "endColumn": 19}, {"ruleId": "668", "severity": 1, "message": "768", "line": 3, "column": 21, "nodeType": null, "messageId": "670", "endLine": 3, "endColumn": 29}, {"ruleId": "668", "severity": 1, "message": "769", "line": 14, "column": 11, "nodeType": null, "messageId": "670", "endLine": 14, "endColumn": 16}, {"ruleId": "668", "severity": 1, "message": "770", "line": 47, "column": 48, "nodeType": null, "messageId": "670", "endLine": 47, "endColumn": 54}, {"ruleId": "668", "severity": 1, "message": "770", "line": 73, "column": 3, "nodeType": null, "messageId": "670", "endLine": 73, "endColumn": 9}, {"ruleId": "668", "severity": 1, "message": "771", "line": 120, "column": 28, "nodeType": null, "messageId": "670", "endLine": 120, "endColumn": 33}, {"ruleId": "668", "severity": 1, "message": "772", "line": 70, "column": 67, "nodeType": null, "messageId": "670", "endLine": 70, "endColumn": 76}, {"ruleId": "668", "severity": 1, "message": "773", "line": 70, "column": 87, "nodeType": null, "messageId": "670", "endLine": 70, "endColumn": 101}, {"ruleId": "668", "severity": 1, "message": "772", "line": 83, "column": 65, "nodeType": null, "messageId": "670", "endLine": 83, "endColumn": 74}, {"ruleId": "668", "severity": 1, "message": "774", "line": 83, "column": 85, "nodeType": null, "messageId": "670", "endLine": 83, "endColumn": 92}, {"ruleId": "668", "severity": 1, "message": "775", "line": 1, "column": 8, "nodeType": null, "messageId": "670", "endLine": 1, "endColumn": 16}, "@typescript-eslint/no-unused-vars", "'Calendar' is defined but never used.", "unusedVar", "'Users' is defined but never used.", "'GraduationCap' is defined but never used.", "'BookOpen' is defined but never used.", "'FileText' is defined but never used.", "'BarChart3' is defined but never used.", "'Settings' is defined but never used.", "'UserPlus' is defined but never used.", "'ClipboardList' is defined but never used.", "'Award' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchClasses'. Either include it or remove the dependency array.", "ArrayExpression", ["776"], "'Input' is defined but never used.", "'Label' is defined but never used.", "'Clock' is defined but never used.", "'showAddTerm' is assigned a value but never used.", "'showAddExam' is assigned a value but never used.", "'CardDescription' is defined but never used.", "'error' is defined but never used.", "'Upload' is defined but never used.", "'getServerSession' is defined but never used.", "'redirect' is defined but never used.", "'authOptions' is defined but never used.", "'hasPermission' is defined but never used.", "'Download' is defined but never used.", "'Edit' is defined but never used.", "'Mail' is defined but never used.", "'MapPin' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchSubjects'. Either include it or remove the dependency array.", ["777"], "React Hook useEffect has a missing dependency: 'fetchTeachers'. Either include it or remove the dependency array.", ["778"], "'err' is defined but never used.", "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["779", "780"], ["781", "782"], ["783", "784"], "'session' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchMarks'. Either include it or remove the dependency array.", ["785"], "'Bell' is defined but never used.", "'Target' is defined but never used.", ["786", "787"], "React Hook useEffect has a missing dependency: 'fetchAttendance'. Either include it or remove the dependency array.", ["788"], "'Plus' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchExams'. Either include it or remove the dependency array.", ["789"], "'router' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchExamData'. Either include it or remove the dependency array.", ["790"], ["791", "792"], ["793", "794"], ["795", "796"], ["797"], "'isActive' is assigned a value but never used.", "'request' is defined but never used.", ["798", "799"], ["800", "801"], ["802", "803"], ["804", "805"], ["806", "807"], "'updatedUser' is assigned a value but never used.", ["808", "809"], ["810", "811"], ["812", "813"], ["814", "815"], ["816", "817"], ["818", "819"], "'Badge' is defined but never used.", ["820", "821"], "'checkAuth' is defined but never used.", ["822", "823"], ["824", "825"], ["826", "827"], ["828", "829"], ["830", "831"], "'formatValidationErrors' is defined but never used.", "'_' is defined but never used.", "'Eye' is defined but never used.", "'useEffect' is defined but never used.", ["832", "833"], ["834", "835"], ["836", "837"], "'Alert' is defined but never used.", "'AlertDescription' is defined but never used.", ["838", "839"], "'formatDate' is assigned a value but never used.", "@typescript-eslint/no-empty-object-type", "An interface declaring no members is equivalent to its supertype.", "Identifier", "noEmptyInterfaceWithSuper", ["840"], "'useState' is defined but never used.", "'theme' is assigned a value but never used.", "'config' is assigned a value but never used.", "'index' is defined but never used.", "'teacherId' is defined but never used.", "'studentClassId' is defined but never used.", "'classId' is defined but never used.", "'NextAuth' is defined but never used.", {"desc": "841", "fix": "842"}, {"desc": "843", "fix": "844"}, {"desc": "845", "fix": "846"}, {"messageId": "847", "fix": "848", "desc": "849"}, {"messageId": "850", "fix": "851", "desc": "852"}, {"messageId": "847", "fix": "853", "desc": "849"}, {"messageId": "850", "fix": "854", "desc": "852"}, {"messageId": "847", "fix": "855", "desc": "849"}, {"messageId": "850", "fix": "856", "desc": "852"}, {"desc": "857", "fix": "858"}, {"messageId": "847", "fix": "859", "desc": "849"}, {"messageId": "850", "fix": "860", "desc": "852"}, {"desc": "861", "fix": "862"}, {"desc": "863", "fix": "864"}, {"desc": "865", "fix": "866"}, {"messageId": "847", "fix": "867", "desc": "849"}, {"messageId": "850", "fix": "868", "desc": "852"}, {"messageId": "847", "fix": "869", "desc": "849"}, {"messageId": "850", "fix": "870", "desc": "852"}, {"messageId": "847", "fix": "871", "desc": "849"}, {"messageId": "850", "fix": "872", "desc": "852"}, {"desc": "865", "fix": "873"}, {"messageId": "847", "fix": "874", "desc": "849"}, {"messageId": "850", "fix": "875", "desc": "852"}, {"messageId": "847", "fix": "876", "desc": "849"}, {"messageId": "850", "fix": "877", "desc": "852"}, {"messageId": "847", "fix": "878", "desc": "849"}, {"messageId": "850", "fix": "879", "desc": "852"}, {"messageId": "847", "fix": "880", "desc": "849"}, {"messageId": "850", "fix": "881", "desc": "852"}, {"messageId": "847", "fix": "882", "desc": "849"}, {"messageId": "850", "fix": "883", "desc": "852"}, {"messageId": "847", "fix": "884", "desc": "849"}, {"messageId": "850", "fix": "885", "desc": "852"}, {"messageId": "847", "fix": "886", "desc": "849"}, {"messageId": "850", "fix": "887", "desc": "852"}, {"messageId": "847", "fix": "888", "desc": "849"}, {"messageId": "850", "fix": "889", "desc": "852"}, {"messageId": "847", "fix": "890", "desc": "849"}, {"messageId": "850", "fix": "891", "desc": "852"}, {"messageId": "847", "fix": "892", "desc": "849"}, {"messageId": "850", "fix": "893", "desc": "852"}, {"messageId": "847", "fix": "894", "desc": "849"}, {"messageId": "850", "fix": "895", "desc": "852"}, {"messageId": "847", "fix": "896", "desc": "849"}, {"messageId": "850", "fix": "897", "desc": "852"}, {"messageId": "847", "fix": "898", "desc": "849"}, {"messageId": "850", "fix": "899", "desc": "852"}, {"messageId": "847", "fix": "900", "desc": "849"}, {"messageId": "850", "fix": "901", "desc": "852"}, {"messageId": "847", "fix": "902", "desc": "849"}, {"messageId": "850", "fix": "903", "desc": "852"}, {"messageId": "847", "fix": "904", "desc": "849"}, {"messageId": "850", "fix": "905", "desc": "852"}, {"messageId": "847", "fix": "906", "desc": "849"}, {"messageId": "850", "fix": "907", "desc": "852"}, {"messageId": "847", "fix": "908", "desc": "849"}, {"messageId": "850", "fix": "909", "desc": "852"}, {"messageId": "847", "fix": "910", "desc": "849"}, {"messageId": "850", "fix": "911", "desc": "852"}, {"messageId": "847", "fix": "912", "desc": "849"}, {"messageId": "850", "fix": "913", "desc": "852"}, {"messageId": "847", "fix": "914", "desc": "849"}, {"messageId": "850", "fix": "915", "desc": "852"}, {"messageId": "916", "fix": "917", "desc": "918"}, "Update the dependencies array to be: [pagination.page, searchTerm, filterActive, fetchClasses]", {"range": "919", "text": "920"}, "Update the dependencies array to be: [fetchSubjects, pagination.page, searchTerm]", {"range": "921", "text": "922"}, "Update the dependencies array to be: [pagination.page, searchTerm, filterActive, fetchTeachers]", {"range": "923", "text": "924"}, "suggestUnknown", {"range": "925", "text": "926"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "927", "text": "928"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "929", "text": "926"}, {"range": "930", "text": "928"}, {"range": "931", "text": "926"}, {"range": "932", "text": "928"}, "Update the dependencies array to be: [selectedTerm, selectedSubject, fetchMarks]", {"range": "933", "text": "934"}, {"range": "935", "text": "926"}, {"range": "936", "text": "928"}, "Update the dependencies array to be: [fetchAttendance, pagination.page, selectedClass, selectedDate]", {"range": "937", "text": "938"}, "Update the dependencies array to be: [selectedTerm, selectedSubject, fetchExams]", {"range": "939", "text": "940"}, "Update the dependencies array to be: [examId, fetchExamData]", {"range": "941", "text": "942"}, {"range": "943", "text": "926"}, {"range": "944", "text": "928"}, {"range": "945", "text": "926"}, {"range": "946", "text": "928"}, {"range": "947", "text": "926"}, {"range": "948", "text": "928"}, {"range": "949", "text": "942"}, {"range": "950", "text": "926"}, {"range": "951", "text": "928"}, {"range": "952", "text": "926"}, {"range": "953", "text": "928"}, {"range": "954", "text": "926"}, {"range": "955", "text": "928"}, {"range": "956", "text": "926"}, {"range": "957", "text": "928"}, {"range": "958", "text": "926"}, {"range": "959", "text": "928"}, {"range": "960", "text": "926"}, {"range": "961", "text": "928"}, {"range": "962", "text": "926"}, {"range": "963", "text": "928"}, {"range": "964", "text": "926"}, {"range": "965", "text": "928"}, {"range": "966", "text": "926"}, {"range": "967", "text": "928"}, {"range": "968", "text": "926"}, {"range": "969", "text": "928"}, {"range": "970", "text": "926"}, {"range": "971", "text": "928"}, {"range": "972", "text": "926"}, {"range": "973", "text": "928"}, {"range": "974", "text": "926"}, {"range": "975", "text": "928"}, {"range": "976", "text": "926"}, {"range": "977", "text": "928"}, {"range": "978", "text": "926"}, {"range": "979", "text": "928"}, {"range": "980", "text": "926"}, {"range": "981", "text": "928"}, {"range": "982", "text": "926"}, {"range": "983", "text": "928"}, {"range": "984", "text": "926"}, {"range": "985", "text": "928"}, {"range": "986", "text": "926"}, {"range": "987", "text": "928"}, {"range": "988", "text": "926"}, {"range": "989", "text": "928"}, {"range": "990", "text": "926"}, {"range": "991", "text": "928"}, "replaceEmptyInterfaceWithSuper", {"range": "992", "text": "993"}, "Replace empty interface with a type alias.", [2175, 2218], "[pagination.page, searchTerm, filterActive, fetchClasses]", [1810, 1839], "[fetchSubjects, pagination.page, searchTerm]", [2356, 2399], "[pagination.page, searchTerm, filterActive, fetchTeachers]", [1915, 1918], "unknown", [1915, 1918], "never", [2028, 2031], [2028, 2031], [1420, 1423], [1420, 1423], [1366, 1397], "[selectedTerm, selectedSubject, fetchMarks]", [1913, 1916], [1913, 1916], [2059, 2105], "[fetchAttendance, pagination.page, selectedClass, selectedDate]", [1137, 1168], "[selectedTerm, selectedSubject, fetchExams]", [1610, 1618], "[examId, fetchExamData]", [2596, 2599], [2596, 2599], [2675, 2678], [2675, 2678], [3030, 3033], [3030, 3033], [1633, 1641], [663, 666], [663, 666], [657, 660], [657, 660], [1174, 1177], [1174, 1177], [4765, 4768], [4765, 4768], [5169, 5172], [5169, 5172], [1172, 1175], [1172, 1175], [900, 903], [900, 903], [847, 850], [847, 850], [872, 875], [872, 875], [1053, 1056], [1053, 1056], [569, 572], [569, 572], [3342, 3345], [3342, 3345], [2649, 2652], [2649, 2652], [3844, 3847], [3844, 3847], [447, 450], [447, 450], [3509, 3512], [3509, 3512], [2126, 2129], [2126, 2129], [495, 498], [495, 498], [830, 833], [830, 833], [3217, 3220], [3217, 3220], [2298, 2301], [2298, 2301], [75, 240], "type InputProps = React.InputHTMLAttributes<HTMLInputElement>"]