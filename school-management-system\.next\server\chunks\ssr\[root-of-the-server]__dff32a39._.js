module.exports=[93695,(a,b,c)=>{b.exports=a.x("next/dist/shared/lib/no-fallback-error.external.js",()=>require("next/dist/shared/lib/no-fallback-error.external.js"))},61146,a=>{"use strict";function b(a){return a.isOnDemandRevalidate?"on-demand":a.isRevalidate?"stale":void 0}a.s(["getRevalidateReason",()=>b])},34057,a=>{"use strict";a.s(["NodeNextRequest",()=>k,"NodeNextResponse",()=>l],34057);var b,c=a.i(45535);class d extends Error{constructor(){super("Headers cannot be modified. Read more: https://nextjs.org/docs/app/api-reference/functions/headers")}static callable(){throw new d}}class e extends Headers{constructor(a){super(),this.headers=new Proxy(a,{get(b,d,e){if("symbol"==typeof d)return c.ReflectAdapter.get(b,d,e);let f=d.toLowerCase(),g=Object.keys(a).find(a=>a.toLowerCase()===f);if(void 0!==g)return c.ReflectAdapter.get(b,g,e)},set(b,d,e,f){if("symbol"==typeof d)return c.ReflectAdapter.set(b,d,e,f);let g=d.toLowerCase(),h=Object.keys(a).find(a=>a.toLowerCase()===g);return c.ReflectAdapter.set(b,h??d,e,f)},has(b,d){if("symbol"==typeof d)return c.ReflectAdapter.has(b,d);let e=d.toLowerCase(),f=Object.keys(a).find(a=>a.toLowerCase()===e);return void 0!==f&&c.ReflectAdapter.has(b,f)},deleteProperty(b,d){if("symbol"==typeof d)return c.ReflectAdapter.deleteProperty(b,d);let e=d.toLowerCase(),f=Object.keys(a).find(a=>a.toLowerCase()===e);return void 0===f||c.ReflectAdapter.deleteProperty(b,f)}})}static seal(a){return new Proxy(a,{get(a,b,e){switch(b){case"append":case"delete":case"set":return d.callable;default:return c.ReflectAdapter.get(a,b,e)}}})}merge(a){return Array.isArray(a)?a.join(", "):a}static from(a){return a instanceof Headers?a:new e(a)}append(a,b){let c=this.headers[a];"string"==typeof c?this.headers[a]=[c,b]:Array.isArray(c)?c.push(b):this.headers[a]=b}delete(a){delete this.headers[a]}get(a){let b=this.headers[a];return void 0!==b?this.merge(b):null}has(a){return void 0!==this.headers[a]}set(a,b){this.headers[a]=b}forEach(a,b){for(let[c,d]of this.entries())a.call(b,d,c,this)}*entries(){for(let a of Object.keys(this.headers)){let b=a.toLowerCase(),c=this.get(b);yield[b,c]}}*keys(){for(let a of Object.keys(this.headers)){let b=a.toLowerCase();yield b}}*values(){for(let a of Object.keys(this.headers)){let b=this.get(a);yield b}}[Symbol.iterator](){return this.entries()}}a.i(20463),a.i(741),a.i(98468),Symbol("__next_preview_data");let f=Symbol("__prerender_bypass");var g=a.i(26974),h=a.i(60358);class i{constructor(a,b,c){this.method=a,this.url=b,this.body=c}get cookies(){var b;return this._cookies?this._cookies:this._cookies=(b=this.headers,function(){let{cookie:c}=b;if(!c)return{};let{parse:d}=a.r(62360);return d(Array.isArray(c)?c.join("; "):c)})()}}class j{constructor(a){this.destination=a}redirect(a,b){return this.setHeader("Location",a),this.statusCode=b,b===h.RedirectStatusCode.PermanentRedirect&&this.setHeader("Refresh",`0;url=${a}`),this}}class k extends i{static #a=b=g.NEXT_REQUEST_META;constructor(a){var c;super(a.method.toUpperCase(),a.url,a),this._req=a,this.headers=this._req.headers,this.fetchMetrics=null==(c=this._req)?void 0:c.fetchMetrics,this[b]=this._req[g.NEXT_REQUEST_META]||{},this.streaming=!1}get originalRequest(){return this._req[g.NEXT_REQUEST_META]=this[g.NEXT_REQUEST_META],this._req.url=this.url,this._req.cookies=this.cookies,this._req}set originalRequest(a){this._req=a}stream(){if(this.streaming)throw Object.defineProperty(Error("Invariant: NodeNextRequest.stream() can only be called once"),"__NEXT_ERROR_CODE",{value:"E467",enumerable:!1,configurable:!0});return this.streaming=!0,new ReadableStream({start:a=>{this._req.on("data",b=>{a.enqueue(new Uint8Array(b))}),this._req.on("end",()=>{a.close()}),this._req.on("error",b=>{a.error(b)})}})}}class l extends j{get originalResponse(){return f in this&&(this._res[f]=this[f]),this._res}constructor(a){super(a),this._res=a,this.textBody=void 0}get sent(){return this._res.finished||this._res.headersSent}get statusCode(){return this._res.statusCode}set statusCode(a){this._res.statusCode=a}get statusMessage(){return this._res.statusMessage}set statusMessage(a){this._res.statusMessage=a}setHeader(a,b){return this._res.setHeader(a,b),this}removeHeader(a){return this._res.removeHeader(a),this}getHeaderValues(a){let b=this._res.getHeader(a);if(void 0!==b)return(Array.isArray(b)?b:[b]).map(a=>a.toString())}hasHeader(a){return this._res.hasHeader(a)}getHeader(a){let b=this.getHeaderValues(a);return Array.isArray(b)?b.join(","):void 0}getHeaders(){return this._res.getHeaders()}appendHeader(a,b){let c=this.getHeaderValues(a)??[];return c.includes(b)||this._res.setHeader(a,[...c,b]),this}body(a){return this.textBody=a,this}send(){this._res.end(this.textBody)}onClose(a){this.originalResponse.on("close",a)}}},91795,a=>{"use strict";a.s(["normalizeAppPath",()=>c],91795);var b=a.i(2105);function c(a){var c;return(c=a.split("/").reduce((a,c,d,e)=>!c||(0,b.isGroupSegment)(c)||"@"===c[0]||("page"===c||"route"===c)&&d===e.length-1?a:a+"/"+c,"")).startsWith("/")?c:"/"+c}},24478,a=>{"use strict";a.s(["getCacheControlHeader",()=>c]);var b=a.i(20463);function c({revalidate:a,expire:c}){let d="number"==typeof a&&void 0!==c&&a<c?`, stale-while-revalidate=${c-a}`:"";return 0===a?"private, no-cache, no-store, max-age=0, must-revalidate":"number"==typeof a?`s-maxage=${a}${d}`:`s-maxage=${b.CACHE_ONE_YEAR}${d}`}},3727,a=>{a.n(a.i(14188))},29173,(a,b,c)=>{b.exports=a.x("@prisma/client",()=>require("@prisma/client"))},46112,a=>{"use strict";a.s(["default",()=>b]);let b=(0,a.i(58730).registerClientReference)(function(){throw Error("Attempted to call the default export of [project]/school-management-system/src/components/layout/dashboard-layout.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/school-management-system/src/components/layout/dashboard-layout.tsx <module evaluation>","default")},36631,a=>{"use strict";a.s(["default",()=>b]);let b=(0,a.i(58730).registerClientReference)(function(){throw Error("Attempted to call the default export of [project]/school-management-system/src/components/layout/dashboard-layout.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/school-management-system/src/components/layout/dashboard-layout.tsx","default")},75031,a=>{"use strict";a.i(46112);var b=a.i(36631);a.n(b)},80975,a=>{"use strict";a.s(["adminNavigation",()=>b]);let b=[{name:"Dashboard",href:"/admin",icon:"BarChart3"},{name:"Students",href:"/admin/students",icon:"Users"},{name:"Teachers",href:"/admin/teachers",icon:"GraduationCap"},{name:"Classes & Sections",href:"/admin/classes",icon:"BookOpen"},{name:"Subjects",href:"/admin/subjects",icon:"FileText"},{name:"Terms & Exams",href:"/admin/exams",icon:"Calendar"},{name:"Attendance",href:"/admin/attendance",icon:"ClipboardList"},{name:"Marks",href:"/admin/marks",icon:"Award"},{name:"Reports",href:"/admin/reports",icon:"Download"},{name:"Settings",href:"/admin/settings",icon:"Settings"}]},97854,a=>{"use strict";a.s(["prisma",()=>c]);var b=a.i(29173);let c=globalThis.prisma??new b.PrismaClient},88678,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"RedirectStatusCode",{enumerable:!0,get:function(){return d}});var d=function(a){return a[a.SeeOther=303]="SeeOther",a[a.TemporaryRedirect=307]="TemporaryRedirect",a[a.PermanentRedirect=308]="PermanentRedirect",a}({});("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},89439,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{REDIRECT_ERROR_CODE:function(){return e},RedirectType:function(){return f},isRedirectError:function(){return g}});let d=a.r(88678),e="NEXT_REDIRECT";var f=function(a){return a.push="push",a.replace="replace",a}({});function g(a){if("object"!=typeof a||null===a||!("digest"in a)||"string"!=typeof a.digest)return!1;let b=a.digest.split(";"),[c,f]=b,g=b.slice(2,-2).join(";"),h=Number(b.at(-2));return c===e&&("replace"===f||"push"===f)&&"string"==typeof g&&!isNaN(h)&&h in d.RedirectStatusCode}("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},50369,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{getRedirectError:function(){return g},getRedirectStatusCodeFromError:function(){return l},getRedirectTypeFromError:function(){return k},getURLFromRedirectError:function(){return j},permanentRedirect:function(){return i},redirect:function(){return h}});let d=a.r(88678),e=a.r(89439),f=a.r(20635).actionAsyncStorage;function g(a,b,c){void 0===c&&(c=d.RedirectStatusCode.TemporaryRedirect);let f=Object.defineProperty(Error(e.REDIRECT_ERROR_CODE),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return f.digest=e.REDIRECT_ERROR_CODE+";"+b+";"+a+";"+c+";",f}function h(a,b){var c;throw null!=b||(b=(null==f||null==(c=f.getStore())?void 0:c.isAction)?e.RedirectType.push:e.RedirectType.replace),g(a,b,d.RedirectStatusCode.TemporaryRedirect)}function i(a,b){throw void 0===b&&(b=e.RedirectType.replace),g(a,b,d.RedirectStatusCode.PermanentRedirect)}function j(a){return(0,e.isRedirectError)(a)?a.digest.split(";").slice(2,-2).join(";"):null}function k(a){if(!(0,e.isRedirectError)(a))throw Object.defineProperty(Error("Not a redirect error"),"__NEXT_ERROR_CODE",{value:"E260",enumerable:!1,configurable:!0});return a.digest.split(";",2)[1]}function l(a){if(!(0,e.isRedirectError)(a))throw Object.defineProperty(Error("Not a redirect error"),"__NEXT_ERROR_CODE",{value:"E260",enumerable:!1,configurable:!0});return Number(a.digest.split(";").at(-2))}("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},53562,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{HTTPAccessErrorStatus:function(){return d},HTTP_ERROR_FALLBACK_ERROR_CODE:function(){return f},getAccessFallbackErrorTypeByStatus:function(){return i},getAccessFallbackHTTPStatus:function(){return h},isHTTPAccessFallbackError:function(){return g}});let d={NOT_FOUND:404,FORBIDDEN:403,UNAUTHORIZED:401},e=new Set(Object.values(d)),f="NEXT_HTTP_ERROR_FALLBACK";function g(a){if("object"!=typeof a||null===a||!("digest"in a)||"string"!=typeof a.digest)return!1;let[b,c]=a.digest.split(";");return b===f&&e.has(Number(c))}function h(a){return Number(a.digest.split(";")[1])}function i(a){switch(a){case 401:return"unauthorized";case 403:return"forbidden";case 404:return"not-found";default:return}}("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},44082,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"notFound",{enumerable:!0,get:function(){return e}});let d=""+a.r(53562).HTTP_ERROR_FALLBACK_ERROR_CODE+";404";function e(){let a=Object.defineProperty(Error(d),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});throw a.digest=d,a}("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},34717,(a,b,c)=>{"use strict";function d(){throw Object.defineProperty(Error("`forbidden()` is experimental and only allowed to be enabled when `experimental.authInterrupts` is enabled."),"__NEXT_ERROR_CODE",{value:"E488",enumerable:!1,configurable:!0})}Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"forbidden",{enumerable:!0,get:function(){return d}}),a.r(53562).HTTP_ERROR_FALLBACK_ERROR_CODE,("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},55736,(a,b,c)=>{"use strict";function d(){throw Object.defineProperty(Error("`unauthorized()` is experimental and only allowed to be used when `experimental.authInterrupts` is enabled."),"__NEXT_ERROR_CODE",{value:"E411",enumerable:!1,configurable:!0})}Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"unauthorized",{enumerable:!0,get:function(){return d}}),a.r(53562).HTTP_ERROR_FALLBACK_ERROR_CODE,("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},33310,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"isPostpone",{enumerable:!0,get:function(){return e}});let d=Symbol.for("react.postpone");function e(a){return"object"==typeof a&&null!==a&&a.$$typeof===d}},17271,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"isNextRouterError",{enumerable:!0,get:function(){return f}});let d=a.r(53562),e=a.r(89439);function f(a){return(0,e.isRedirectError)(a)||(0,d.isHTTPAccessFallbackError)(a)}("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},2815,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"unstable_rethrow",{enumerable:!0,get:function(){return function a(b){if((0,g.isNextRouterError)(b)||(0,f.isBailoutToCSRError)(b)||(0,i.isDynamicServerError)(b)||(0,h.isDynamicPostpone)(b)||(0,e.isPostpone)(b)||(0,d.isHangingPromiseRejectionError)(b))throw b;b instanceof Error&&"cause"in b&&a(b.cause)}}});let d=a.r(73955),e=a.r(33310),f=a.r(14240),g=a.r(17271),h=a.r(68627),i=a.r(67126);("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},40201,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"unstable_rethrow",{enumerable:!0,get:function(){return d}});let d=a.r(2815).unstable_rethrow;("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},96123,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{ReadonlyURLSearchParams:function(){return k},RedirectType:function(){return e.RedirectType},forbidden:function(){return g.forbidden},notFound:function(){return f.notFound},permanentRedirect:function(){return d.permanentRedirect},redirect:function(){return d.redirect},unauthorized:function(){return h.unauthorized},unstable_isUnrecognizedActionError:function(){return l},unstable_rethrow:function(){return i.unstable_rethrow}});let d=a.r(50369),e=a.r(89439),f=a.r(44082),g=a.r(34717),h=a.r(55736),i=a.r(40201);class j extends Error{constructor(){super("Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams")}}class k extends URLSearchParams{append(){throw new j}delete(){throw new j}set(){throw new j}sort(){throw new j}}function l(){throw Object.defineProperty(Error("`unstable_isUnrecognizedActionError` can only be used on the client."),"__NEXT_ERROR_CODE",{value:"E776",enumerable:!1,configurable:!0})}("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},99614,a=>{"use strict";a.s([]),a.i(96123)},67836,(a,b,c)=>{},92082,a=>{"use strict";a.s(["default",()=>x],92082);var b=a.i(18042);a.i(99614);var c=a.i(96123),d=a.i(97854),e=a.i(75031),f=a.i(80975),g=a.i(37532),h=a.i(8265),i=a.i(52535),j=a.i(79668);let k=(0,i.cva)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-blue-600 text-white hover:bg-blue-700 dark:bg-blue-600 dark:hover:bg-blue-700",secondary:"border-transparent bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-gray-100 hover:bg-gray-200 dark:hover:bg-gray-700",destructive:"border-transparent bg-red-600 text-white hover:bg-red-700 dark:bg-red-600 dark:hover:bg-red-700",outline:"text-gray-900 dark:text-gray-100 border-gray-300 dark:border-gray-700"}},defaultVariants:{variant:"default"}});function l({className:a,variant:c,...d}){return(0,b.jsx)("div",{className:(0,j.cn)(k({variant:c}),a),...d})}var m=a.i(51251);let n=(0,m.default)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]]),o=(0,m.default)("phone",[["path",{d:"M13.832 16.568a1 1 0 0 0 1.213-.303l.355-.465A2 2 0 0 1 17 15h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2A18 18 0 0 1 2 4a2 2 0 0 1 2-2h3a2 2 0 0 1 2 2v3a2 2 0 0 1-.8 1.6l-.468.351a1 1 0 0 0-.292 1.233 14 14 0 0 0 6.392 6.384",key:"9njp5v"}]]),p=(0,m.default)("book-open",[["path",{d:"M12 7v14",key:"1akyts"}],["path",{d:"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z",key:"ruj8y"}]]),q=(0,m.default)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]]),r=(0,m.default)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]]),s=(0,m.default)("graduation-cap",[["path",{d:"M21.42 10.922a1 1 0 0 0-.019-1.838L12.83 5.18a2 2 0 0 0-1.66 0L2.6 9.08a1 1 0 0 0 0 1.832l8.57 3.908a2 2 0 0 0 1.66 0z",key:"j76jl0"}],["path",{d:"M22 10v6",key:"1lu8f3"}],["path",{d:"M6 12.5V16a6 3 0 0 0 12 0v-3.5",key:"1r8lef"}]]),t=(0,m.default)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]]),u=(0,m.default)("clock",[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]]),v=(0,m.default)("award",[["path",{d:"m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526",key:"1yiouv"}],["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}]]);var w=a.i(46711);async function x({params:a}){let{id:i}=await a,j=await d.prisma.student.findUnique({where:{id:i},include:{user:!0,currentClass:{include:{sections:!0}},currentSection:!0,enrollments:{include:{class:!0,section:!0}},attendances:{take:10,orderBy:{date:"desc"}},marks:{include:{exam:{include:{subject:!0}}},take:20,orderBy:{createdAt:"desc"}}}});j||(0,c.notFound)();let k=a=>new Date(a).toLocaleDateString(),m=j.attendances.reduce((a,b)=>(a[b.status]=(a[b.status]||0)+1,a),{}),x=j.attendances.length,y=m.PRESENT||0,z=x>0?Math.round(y/x*100):0;return(0,b.jsx)(e.default,{title:"Student Details",navigation:f.adminNavigation,children:(0,b.jsxs)("div",{className:"space-y-6",children:[(0,b.jsxs)("div",{className:"flex items-center justify-between",children:[(0,b.jsxs)("div",{className:"flex items-center gap-4",children:[(0,b.jsx)(w.default,{href:"/admin/students",children:(0,b.jsxs)(h.Button,{variant:"ghost",size:"sm",children:[(0,b.jsx)(r,{className:"w-4 h-4 mr-2"}),"Back to Students"]})}),(0,b.jsxs)("div",{children:[(0,b.jsxs)("h1",{className:"text-3xl font-bold tracking-tight",children:[j.user.firstName," ",j.user.lastName]}),(0,b.jsxs)("p",{className:"text-muted-foreground",children:["Student ID: ",j.id]})]})]}),(0,b.jsx)(w.default,{href:`/admin/students/${j.id}/edit`,children:(0,b.jsxs)(h.Button,{children:[(0,b.jsx)(q,{className:"w-4 h-4 mr-2"}),"Edit Student"]})})]}),(0,b.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,b.jsxs)("div",{className:"lg:col-span-2 space-y-6",children:[(0,b.jsxs)(g.Card,{children:[(0,b.jsx)(g.CardHeader,{children:(0,b.jsxs)(g.CardTitle,{className:"flex items-center gap-2",children:[(0,b.jsx)(n,{className:"w-5 h-5"}),"Personal Information"]})}),(0,b.jsx)(g.CardContent,{className:"space-y-4",children:(0,b.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,b.jsxs)("div",{children:[(0,b.jsx)("label",{className:"text-sm font-medium text-gray-600",children:"Full Name"}),(0,b.jsxs)("p",{className:"text-lg font-medium",children:[j.user.firstName," ",j.user.lastName]})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)("label",{className:"text-sm font-medium text-gray-600",children:"Email"}),(0,b.jsx)("p",{className:"text-lg font-medium",children:j.user.email})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)("label",{className:"text-sm font-medium text-gray-600",children:"Date of Birth"}),(0,b.jsxs)("p",{className:"text-lg font-medium",children:[k(j.dob)," (",(a=>{let b=new Date,c=new Date(a),d=b.getFullYear()-c.getFullYear(),e=b.getMonth()-c.getMonth();return(e<0||0===e&&b.getDate()<c.getDate())&&d--,d})(j.dob)," years old)"]})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)("label",{className:"text-sm font-medium text-gray-600",children:"Gender"}),(0,b.jsx)("p",{className:"text-lg font-medium",children:(a=>{switch(a){case"MALE":return"Male";case"FEMALE":return"Female";case"OTHER":return"Other";default:return a}})(j.gender)})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)("label",{className:"text-sm font-medium text-gray-600",children:"Phone Number"}),(0,b.jsx)("p",{className:"text-lg font-medium",children:j.user.phone||"Not provided"})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)("label",{className:"text-sm font-medium text-gray-600",children:"Address"}),(0,b.jsx)("p",{className:"text-lg font-medium",children:j.address||"Not provided"})]})]})})]}),(0,b.jsxs)(g.Card,{children:[(0,b.jsx)(g.CardHeader,{children:(0,b.jsxs)(g.CardTitle,{className:"flex items-center gap-2",children:[(0,b.jsx)(s,{className:"w-5 h-5"}),"Academic Information"]})}),(0,b.jsxs)(g.CardContent,{className:"space-y-4",children:[(0,b.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,b.jsxs)("div",{children:[(0,b.jsx)("label",{className:"text-sm font-medium text-gray-600",children:"Current Class"}),(0,b.jsxs)("p",{className:"flex items-center gap-2",children:[(0,b.jsx)(p,{className:"w-4 h-4"}),j.currentClass?`${j.currentClass.name} - ${j.currentSection?.name||"N/A"}`:"Not assigned"]})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)("label",{className:"text-sm font-medium text-gray-600",children:"Enrolled Subjects"}),(0,b.jsxs)("p",{className:"flex items-center gap-2",children:[(0,b.jsx)(t,{className:"w-4 h-4"}),j.enrollments.length," subjects"]})]})]}),j.enrollments.length>0&&(0,b.jsxs)("div",{children:[(0,b.jsx)("label",{className:"text-sm font-medium text-gray-600",children:"Enrollment History"}),(0,b.jsx)("div",{className:"flex flex-wrap gap-2 mt-2",children:j.enrollments.map(a=>(0,b.jsxs)(l,{variant:"secondary",children:[a.class.name," - ",a.section.name]},a.id))})]})]})]}),j.marks.length>0&&(0,b.jsxs)(g.Card,{children:[(0,b.jsx)(g.CardHeader,{children:(0,b.jsxs)(g.CardTitle,{className:"flex items-center gap-2",children:[(0,b.jsx)(v,{className:"w-5 h-5"}),"Recent Marks"]})}),(0,b.jsx)(g.CardContent,{children:(0,b.jsx)("div",{className:"space-y-3",children:j.marks.slice(0,5).map(a=>(0,b.jsxs)("div",{className:"flex items-center justify-between p-3 border rounded-lg",children:[(0,b.jsxs)("div",{children:[(0,b.jsx)("p",{className:"font-medium",children:a.exam.subject.name}),(0,b.jsx)("p",{className:"text-sm text-gray-600",children:a.exam.name})]}),(0,b.jsxs)("div",{className:"text-right",children:[(0,b.jsxs)("p",{className:"font-bold text-lg",children:[a.obtainedMarks,"/",a.exam.maxMarks]}),(0,b.jsxs)("p",{className:"text-sm text-gray-600",children:[Math.round(a.obtainedMarks/a.exam.maxMarks*100),"%"]})]})]},a.id))})})]})]}),(0,b.jsxs)("div",{className:"space-y-6",children:[(0,b.jsxs)(g.Card,{children:[(0,b.jsx)(g.CardHeader,{children:(0,b.jsx)(g.CardTitle,{className:"text-lg",children:"Guardian Information"})}),(0,b.jsxs)(g.CardContent,{className:"space-y-2",children:[(0,b.jsxs)("div",{children:[(0,b.jsx)("label",{className:"text-sm font-medium text-gray-600",children:"Guardian Name"}),(0,b.jsx)("p",{children:j.guardianName})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)("label",{className:"text-sm font-medium text-gray-600",children:"Guardian Phone"}),(0,b.jsxs)("p",{className:"flex items-center gap-2",children:[(0,b.jsx)(o,{className:"w-4 h-4"}),j.guardianPhone]})]})]})]}),j.attendances.length>0&&(0,b.jsxs)(g.Card,{children:[(0,b.jsx)(g.CardHeader,{children:(0,b.jsx)(g.CardTitle,{className:"text-lg",children:"Attendance Summary"})}),(0,b.jsxs)(g.CardContent,{className:"space-y-4",children:[(0,b.jsxs)("div",{className:"text-center",children:[(0,b.jsxs)("div",{className:"text-3xl font-bold text-blue-600",children:[z,"%"]}),(0,b.jsx)("div",{className:"text-sm text-gray-600",children:"Attendance Rate"})]}),(0,b.jsxs)("div",{className:"space-y-2",children:[(0,b.jsxs)("div",{className:"flex justify-between",children:[(0,b.jsx)("span",{className:"text-sm",children:"Present"}),(0,b.jsx)("span",{className:"font-medium",children:y})]}),(0,b.jsxs)("div",{className:"flex justify-between",children:[(0,b.jsx)("span",{className:"text-sm",children:"Absent"}),(0,b.jsx)("span",{className:"font-medium",children:m.ABSENT||0})]}),(0,b.jsxs)("div",{className:"flex justify-between",children:[(0,b.jsx)("span",{className:"text-sm",children:"Late"}),(0,b.jsx)("span",{className:"font-medium",children:m.LATE||0})]}),(0,b.jsxs)("div",{className:"flex justify-between",children:[(0,b.jsx)("span",{className:"text-sm",children:"Total Days"}),(0,b.jsx)("span",{className:"font-medium",children:x})]})]})]})]}),j.attendances.length>0&&(0,b.jsxs)(g.Card,{children:[(0,b.jsx)(g.CardHeader,{children:(0,b.jsx)(g.CardTitle,{className:"text-lg",children:"Recent Attendance"})}),(0,b.jsx)(g.CardContent,{children:(0,b.jsx)("div",{className:"space-y-2",children:j.attendances.slice(0,5).map(a=>{let c=(a=>{switch(a){case"PRESENT":return{label:"Present",color:"bg-green-100 text-green-800"};case"ABSENT":return{label:"Absent",color:"bg-red-100 text-red-800"};case"LATE":return{label:"Late",color:"bg-yellow-100 text-yellow-800"};case"HALF_DAY":return{label:"Half Day",color:"bg-orange-100 text-orange-800"};default:return{label:a,color:"bg-gray-100 text-gray-800"}}})(a.status);return(0,b.jsxs)("div",{className:"flex items-center justify-between",children:[(0,b.jsxs)("div",{className:"flex items-center gap-2",children:[(0,b.jsx)(u,{className:"w-4 h-4 text-gray-400"}),(0,b.jsx)("span",{className:"text-sm",children:k(a.date)})]}),(0,b.jsx)(l,{className:c.color,children:c.label})]},a.id)})})})]})]})]})]})})}}];

//# sourceMappingURL=%5Broot-of-the-server%5D__dff32a39._.js.map