module.exports=[18622,(e,t,r)=>{t.exports=e.x("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js"))},56704,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/work-async-storage.external.js",()=>require("next/dist/server/app-render/work-async-storage.external.js"))},32319,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/work-unit-async-storage.external.js",()=>require("next/dist/server/app-render/work-unit-async-storage.external.js"))},20635,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/action-async-storage.external.js",()=>require("next/dist/server/app-render/action-async-storage.external.js"))},24725,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/after-task-async-storage.external.js",()=>require("next/dist/server/app-render/after-task-async-storage.external.js"))},24361,(e,t,r)=>{t.exports=e.x("util",()=>require("util"))},29173,(e,t,r)=>{t.exports=e.x("@prisma/client",()=>require("@prisma/client"))},70406,(e,t,r)=>{t.exports=e.x("next/dist/compiled/@opentelemetry/api",()=>require("next/dist/compiled/@opentelemetry/api"))},93695,(e,t,r)=>{t.exports=e.x("next/dist/shared/lib/no-fallback-error.external.js",()=>require("next/dist/shared/lib/no-fallback-error.external.js"))},61724,(e,t,r)=>{t.exports=e.x("next/dist/compiled/next-server/app-route-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-route-turbo.runtime.prod.js"))},54799,(e,t,r)=>{t.exports=e.x("crypto",()=>require("crypto"))},92509,(e,t,r)=>{t.exports=e.x("url",()=>require("url"))},21517,(e,t,r)=>{t.exports=e.x("http",()=>require("http"))},49719,(e,t,r)=>{t.exports=e.x("assert",()=>require("assert"))},45706,(e,t,r)=>{t.exports=e.x("querystring",()=>require("querystring"))},874,(e,t,r)=>{t.exports=e.x("buffer",()=>require("buffer"))},6461,(e,t,r)=>{t.exports=e.x("zlib",()=>require("zlib"))},24836,(e,t,r)=>{t.exports=e.x("https",()=>require("https"))},27699,(e,t,r)=>{t.exports=e.x("events",()=>require("events"))},12756,e=>{e.v(JSON.parse('{"name":"openid-client","version":"5.7.1","description":"OpenID Connect Relying Party (RP, Client) implementation for Node.js runtime, supports passportjs","keywords":["auth","authentication","basic","certified","client","connect","dynamic","electron","hybrid","identity","implicit","oauth","oauth2","oidc","openid","passport","relying party","strategy"],"homepage":"https://github.com/panva/openid-client","repository":"panva/openid-client","funding":{"url":"https://github.com/sponsors/panva"},"license":"MIT","author":"Filip Skokan <<EMAIL>>","exports":{"types":"./types/index.d.ts","import":"./lib/index.mjs","require":"./lib/index.js"},"main":"./lib/index.js","types":"./types/index.d.ts","files":["lib","types/index.d.ts"],"scripts":{"format":"npx prettier --loglevel silent --write ./lib ./test ./certification ./types","test":"mocha test/**/*.test.js"},"dependencies":{"jose":"^4.15.9","lru-cache":"^6.0.0","object-hash":"^2.2.0","oidc-token-hash":"^5.0.3"},"devDependencies":{"@types/node":"^16.18.106","@types/passport":"^1.0.16","base64url":"^3.0.1","chai":"^4.5.0","mocha":"^10.7.3","nock":"^13.5.5","prettier":"^2.8.8","readable-mock-req":"^0.2.2","sinon":"^9.2.4","timekeeper":"^2.3.1"},"standard-version":{"scripts":{"postchangelog":"sed -i \'\' -e \'s/### \\\\[/## [/g\' CHANGELOG.md"},"types":[{"type":"feat","section":"Features"},{"type":"fix","section":"Fixes"},{"type":"chore","hidden":true},{"type":"docs","hidden":true},{"type":"style","hidden":true},{"type":"refactor","section":"Refactor","hidden":false},{"type":"perf","section":"Performance","hidden":false},{"type":"test","hidden":true}]}}'))},84599,(e,t,r)=>{},74022,e=>{"use strict";e.s(["handler",()=>_,"patchFetch",()=>k,"routeModule",()=>E,"serverHooks",()=>j,"workAsyncStorage",()=>C,"workUnitAsyncStorage",()=>q],74022);var t=e.i(6137),r=e.i(11365),a=e.i(9638),n=e.i(15243),s=e.i(66378),i=e.i(92101),o=e.i(50012),d=e.i(62885),l=e.i(31409),p=e.i(78448),u=e.i(28015),c=e.i(72721),h=e.i(75714),x=e.i(12634),m=e.i(93695);e.i(74732);var g=e.i(66662);e.s(["GET",()=>f],86367);var y=e.i(2835),v=e.i(58356),w=e.i(43382),R=e.i(31279);async function f(e){try{let e=await (0,v.getServerSession)(w.authOptions);if(!e||"TEACHER"!==e.user.role)return y.NextResponse.json({error:"Unauthorized"},{status:401});let t=await R.prisma.teacher.findUnique({where:{userId:e.user.id}});if(!t)return y.NextResponse.json({error:"Teacher not found"},{status:404});let r=await R.prisma.subject.findMany({where:{teacherId:t.id},include:{class:{include:{sections:!0,_count:{select:{students:!0}}}},exams:{where:{date:{gte:new Date}},orderBy:{date:"asc"},take:5}}}),a=new Set(r.map(e=>e.class.id)).size,n=r.reduce((e,t)=>e+t.class._count.students,0),s=r.reduce((e,t)=>e+t.exams.length,0),i=[...new Set(r.map(e=>e.class.id))],[o,d]=await Promise.all([i.length>0?R.prisma.attendance.aggregate({where:{classId:{in:i},date:{gte:new Date(Date.now()-2592e6)}},_count:{id:!0}}).then(async e=>{let t=await R.prisma.attendance.count({where:{classId:{in:i},date:{gte:new Date(Date.now()-2592e6)},status:"PRESENT"}});return{total:e._count.id,present:t,rate:e._count.id>0?t/e._count.id*100:0}}):{total:0,present:0,rate:0},R.prisma.mark.aggregate({where:{gradedByTeacherId:t.id},_avg:{obtainedMarks:!0},_count:{id:!0}})]),l=await Promise.all(r.map(async e=>{let t=await R.prisma.attendance.aggregate({where:{classId:e.class.id,date:{gte:new Date(Date.now()-6048e5)}},_count:{id:!0}}).then(async t=>{let r=await R.prisma.attendance.count({where:{classId:e.class.id,date:{gte:new Date(Date.now()-6048e5)},status:"PRESENT"}});return t._count.id>0?r/t._count.id*100:0});return{id:e.class.id,name:e.class.name,subject:e.name,students:e.class._count.students,attendance:Math.round(10*t)/10,nextExam:e.exams[0]?{name:e.exams[0].name,date:e.exams[0].date}:null}})),p={totalClasses:a,totalStudents:n,averageAttendance:Math.round(10*o.rate)/10,averageMarks:d._avg.obtainedMarks?Math.round(10*d._avg.obtainedMarks)/10:0,upcomingExams:s,totalMarksGraded:d._count.id,assignedClasses:l};return y.NextResponse.json(p)}catch(e){return console.error("Error fetching teacher dashboard stats:",e),y.NextResponse.json({error:"Internal server error"},{status:500})}}var b=e.i(86367);let E=new t.AppRouteRouteModule({definition:{kind:r.RouteKind.APP_ROUTE,page:"/api/teacher/dashboard/stats/route",pathname:"/api/teacher/dashboard/stats",filename:"route",bundlePath:""},distDir:".next",relativeProjectDir:"",resolvedPagePath:"[project]/school-management-system/src/app/api/teacher/dashboard/stats/route.ts",nextConfigOutput:"",userland:b}),{workAsyncStorage:C,workUnitAsyncStorage:q,serverHooks:j}=E;function k(){return(0,a.patchFetch)({workAsyncStorage:C,workUnitAsyncStorage:q})}async function _(e,t,a){var y;let v="/api/teacher/dashboard/stats/route";v=v.replace(/\/index$/,"")||"/";let w=await E.prepare(e,t,{srcPage:v,multiZoneDraftMode:!1});if(!w)return t.statusCode=400,t.end("Bad Request"),null==a.waitUntil||a.waitUntil.call(a,Promise.resolve()),null;let{buildId:R,params:f,nextConfig:b,isDraftMode:C,prerenderManifest:q,routerServerContext:j,isOnDemandRevalidate:k,revalidateOnlyGenerated:_,resolvedPathname:A}=w,N=(0,i.normalizeAppPath)(v),P=!!(q.dynamicRoutes[N]||q.routes[A]);if(P&&!C){let e=!!q.routes[A],t=q.dynamicRoutes[N];if(t&&!1===t.fallback&&!e)throw new m.NoFallbackError}let T=null;!P||E.isDev||C||(T="/index"===(T=A)?"/":T);let S=!0===E.isDev||!P,O=P&&!S,I=e.method||"GET",M=(0,s.getTracer)(),D=M.getActiveScopeSpan(),H={params:f,prerenderManifest:q,renderOpts:{experimental:{cacheComponents:!!b.experimental.cacheComponents,authInterrupts:!!b.experimental.authInterrupts},supportsDynamicResponse:S,incrementalCache:(0,n.getRequestMeta)(e,"incrementalCache"),cacheLifeProfiles:null==(y=b.experimental)?void 0:y.cacheLife,isRevalidate:O,waitUntil:a.waitUntil,onClose:e=>{t.on("close",e)},onAfterTaskError:void 0,onInstrumentationRequestError:(t,r,a)=>E.onRequestError(e,t,a,j)},sharedContext:{buildId:R}},U=new o.NodeNextRequest(e),F=new o.NodeNextResponse(t),$=d.NextRequestAdapter.fromNodeNextRequest(U,(0,d.signalFromNodeResponse)(t));try{let i=async r=>E.handle($,H).finally(()=>{if(!r)return;r.setAttributes({"http.status_code":t.statusCode,"next.rsc":!1});let a=M.getRootSpanAttributes();if(!a)return;if(a.get("next.span_type")!==l.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${a.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let n=a.get("next.route");if(n){let e=`${I} ${n}`;r.setAttributes({"next.route":n,"http.route":n,"next.span_name":e}),r.updateName(e)}else r.updateName(`${I} ${e.url}`)}),o=async s=>{var o,d;let l=async({previousCacheEntry:r})=>{try{if(!(0,n.getRequestMeta)(e,"minimalMode")&&k&&_&&!r)return t.statusCode=404,t.setHeader("x-nextjs-cache","REVALIDATED"),t.end("This page could not be found"),null;let o=await i(s);e.fetchMetrics=H.renderOpts.fetchMetrics;let d=H.renderOpts.pendingWaitUntil;d&&a.waitUntil&&(a.waitUntil(d),d=void 0);let l=H.renderOpts.collectedTags;if(!P)return await (0,u.sendResponse)(U,F,o,H.renderOpts.pendingWaitUntil),null;{let e=await o.blob(),t=(0,c.toNodeOutgoingHttpHeaders)(o.headers);l&&(t[x.NEXT_CACHE_TAGS_HEADER]=l),!t["content-type"]&&e.type&&(t["content-type"]=e.type);let r=void 0!==H.renderOpts.collectedRevalidate&&!(H.renderOpts.collectedRevalidate>=x.INFINITE_CACHE)&&H.renderOpts.collectedRevalidate,a=void 0===H.renderOpts.collectedExpire||H.renderOpts.collectedExpire>=x.INFINITE_CACHE?void 0:H.renderOpts.collectedExpire;return{value:{kind:g.CachedRouteKind.APP_ROUTE,status:o.status,body:Buffer.from(await e.arrayBuffer()),headers:t},cacheControl:{revalidate:r,expire:a}}}}catch(t){throw(null==r?void 0:r.isStale)&&await E.onRequestError(e,t,{routerKind:"App Router",routePath:v,routeType:"route",revalidateReason:(0,p.getRevalidateReason)({isRevalidate:O,isOnDemandRevalidate:k})},j),t}},m=await E.handleResponse({req:e,nextConfig:b,cacheKey:T,routeKind:r.RouteKind.APP_ROUTE,isFallback:!1,prerenderManifest:q,isRoutePPREnabled:!1,isOnDemandRevalidate:k,revalidateOnlyGenerated:_,responseGenerator:l,waitUntil:a.waitUntil});if(!P)return null;if((null==m||null==(o=m.value)?void 0:o.kind)!==g.CachedRouteKind.APP_ROUTE)throw Object.defineProperty(Error(`Invariant: app-route received invalid cache entry ${null==m||null==(d=m.value)?void 0:d.kind}`),"__NEXT_ERROR_CODE",{value:"E701",enumerable:!1,configurable:!0});(0,n.getRequestMeta)(e,"minimalMode")||t.setHeader("x-nextjs-cache",k?"REVALIDATED":m.isMiss?"MISS":m.isStale?"STALE":"HIT"),C&&t.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");let y=(0,c.fromNodeOutgoingHttpHeaders)(m.value.headers);return(0,n.getRequestMeta)(e,"minimalMode")&&P||y.delete(x.NEXT_CACHE_TAGS_HEADER),!m.cacheControl||t.getHeader("Cache-Control")||y.get("Cache-Control")||y.set("Cache-Control",(0,h.getCacheControlHeader)(m.cacheControl)),await (0,u.sendResponse)(U,F,new Response(m.value.body,{headers:y,status:m.value.status||200})),null};D?await o(D):await M.withPropagatedContext(e.headers,()=>M.trace(l.BaseServerSpan.handleRequest,{spanName:`${I} ${e.url}`,kind:s.SpanKind.SERVER,attributes:{"http.method":I,"http.target":e.url}},o))}catch(t){if(D||t instanceof m.NoFallbackError||await E.onRequestError(e,t,{routerKind:"App Router",routePath:N,routeType:"route",revalidateReason:(0,p.getRevalidateReason)({isRevalidate:O,isOnDemandRevalidate:k})}),P)throw t;return await (0,u.sendResponse)(U,F,new Response(null,{status:500})),null}}}];

//# sourceMappingURL=%5Broot-of-the-server%5D__e9602816._.js.map