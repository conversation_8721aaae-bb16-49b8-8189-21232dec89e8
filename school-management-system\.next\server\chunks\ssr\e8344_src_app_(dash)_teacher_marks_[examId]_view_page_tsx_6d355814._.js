module.exports=[37187,a=>{"use strict";a.s(["default",()=>t],37187);var b=a.i(41825),c=a.i(54159),d=a.i(52963),e=a.i(25384),f=a.i(4082),g=a.i(2331),h=a.i(62821),i=a.i(95788),j=a.i(91662),k=a.i(29683),l=a.i(43001),m=a.i(57012);function n({exam:a,students:c,onEdit:d,showActions:e=!0}){let h=a=>a>=90?"bg-green-100 text-green-800":a>=80?"bg-blue-100 text-blue-800":a>=70?"bg-yellow-100 text-yellow-800":a>=60?"bg-orange-100 text-orange-800":"bg-red-100 text-red-800",i=a=>a>=90?"A+":a>=80?"A":a>=70?"B+":a>=60?"B":a>=50?"C+":a>=40?"C":a>=30?"D":"F",n=c.filter(a=>a.hasMarks),o=c.filter(a=>!a.hasMarks),p={totalStudents:c.length,gradedStudents:n.length,pendingStudents:o.length,averageMarks:n.length>0?Math.round(n.reduce((a,b)=>a+(b.currentMark?.obtainedMarks||0),0)/n.length*100)/100:0,averagePercentage:n.length>0?Math.round(n.reduce((b,c)=>b+(c.currentMark?.obtainedMarks||0)/a.maxMarks*100,0)/n.length*100)/100:0};return(0,b.jsxs)(f.Card,{children:[(0,b.jsxs)(f.CardHeader,{children:[(0,b.jsxs)(f.CardTitle,{className:"flex items-center justify-between",children:[(0,b.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,b.jsx)(j.Users,{className:"w-5 h-5"}),(0,b.jsx)("span",{children:"Student Marks"})]}),e&&d&&(0,b.jsxs)(g.Button,{variant:"outline",onClick:()=>d(a.id),children:[(0,b.jsx)(m.Edit,{className:"w-4 h-4 mr-2"}),"Edit Marks"]})]}),(0,b.jsxs)(f.CardDescription,{children:["Marks for ",a.name," - ",a.subject.name," (",a.subject.class.name,")"]}),(0,b.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-5 gap-4 text-sm",children:[(0,b.jsxs)("div",{className:"text-center p-2 bg-gray-50 rounded",children:[(0,b.jsx)("div",{className:"font-semibold text-gray-900",children:p.totalStudents}),(0,b.jsx)("div",{className:"text-gray-600",children:"Total"})]}),(0,b.jsxs)("div",{className:"text-center p-2 bg-green-50 rounded",children:[(0,b.jsx)("div",{className:"font-semibold text-green-800",children:p.gradedStudents}),(0,b.jsx)("div",{className:"text-green-600",children:"Graded"})]}),(0,b.jsxs)("div",{className:"text-center p-2 bg-orange-50 rounded",children:[(0,b.jsx)("div",{className:"font-semibold text-orange-800",children:p.pendingStudents}),(0,b.jsx)("div",{className:"text-orange-600",children:"Pending"})]}),(0,b.jsxs)("div",{className:"text-center p-2 bg-blue-50 rounded",children:[(0,b.jsx)("div",{className:"font-semibold text-blue-800",children:p.averageMarks}),(0,b.jsx)("div",{className:"text-blue-600",children:"Avg Marks"})]}),(0,b.jsxs)("div",{className:"text-center p-2 bg-blue-50 rounded",children:[(0,b.jsxs)("div",{className:"font-semibold text-blue-800",children:[p.averagePercentage,"%"]}),(0,b.jsx)("div",{className:"text-blue-600",children:"Avg %"})]})]})]}),(0,b.jsx)(f.CardContent,{children:0===c.length?(0,b.jsxs)("div",{className:"text-center py-8",children:[(0,b.jsx)(j.Users,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,b.jsx)("p",{className:"text-gray-600",children:"No students found for this exam"})]}):(0,b.jsxs)("div",{className:"space-y-4",children:[(0,b.jsx)("div",{className:"hidden lg:block overflow-x-auto",children:(0,b.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,b.jsx)("thead",{className:"bg-gray-50",children:(0,b.jsxs)("tr",{children:[(0,b.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Student"}),(0,b.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Roll No."}),(0,b.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Section"}),(0,b.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Marks"}),(0,b.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Percentage"}),(0,b.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Grade"}),(0,b.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Remarks"}),(0,b.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"})]})}),(0,b.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:c.map(c=>{let d=c.currentMark?Math.round(c.currentMark.obtainedMarks/a.maxMarks*1e4)/100:0,e=c.currentMark?i(d):"-";return(0,b.jsxs)("tr",{children:[(0,b.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,b.jsxs)("div",{children:[(0,b.jsxs)("div",{className:"text-sm font-medium text-gray-900",children:[c.firstName," ",c.lastName]}),(0,b.jsx)("div",{className:"text-sm text-gray-500",children:c.admissionNo})]})}),(0,b.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:c.rollNumber||"-"}),(0,b.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:c.sectionName||"-"}),(0,b.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:c.currentMark?`${c.currentMark.obtainedMarks}/${a.maxMarks}`:"-"}),(0,b.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:c.currentMark?`${d}%`:"-"}),(0,b.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:c.currentMark?(0,b.jsx)("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${h(d)}`,children:e}):(0,b.jsx)("span",{className:"text-gray-400",children:"-"})}),(0,b.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:c.currentMark?.remarks||"-"}),(0,b.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:c.hasMarks?(0,b.jsxs)("span",{className:"inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800",children:[(0,b.jsx)(k.CheckCircle,{className:"w-3 h-3 mr-1"}),"Graded"]}):(0,b.jsxs)("span",{className:"inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800",children:[(0,b.jsx)(l.AlertCircle,{className:"w-3 h-3 mr-1"}),"Pending"]})})]},c.id)})})]})}),(0,b.jsx)("div",{className:"lg:hidden space-y-4",children:c.map(c=>{let d=c.currentMark?Math.round(c.currentMark.obtainedMarks/a.maxMarks*1e4)/100:0,e=c.currentMark?i(d):"-";return(0,b.jsx)(f.Card,{className:"p-4",children:(0,b.jsxs)("div",{className:"space-y-3",children:[(0,b.jsxs)("div",{className:"flex items-start justify-between",children:[(0,b.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,b.jsxs)("h3",{className:"text-lg font-medium text-gray-900 truncate",children:[c.firstName," ",c.lastName]}),(0,b.jsxs)("p",{className:"text-sm text-gray-500",children:[c.admissionNo," • Roll: ",c.rollNumber||"-"," • Section: ",c.sectionName||"-"]})]}),(0,b.jsx)("div",{className:"ml-4",children:c.hasMarks?(0,b.jsxs)("span",{className:"inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800",children:[(0,b.jsx)(k.CheckCircle,{className:"w-3 h-3 mr-1"}),"Graded"]}):(0,b.jsxs)("span",{className:"inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800",children:[(0,b.jsx)(l.AlertCircle,{className:"w-3 h-3 mr-1"}),"Pending"]})})]}),c.currentMark?(0,b.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[(0,b.jsxs)("div",{children:[(0,b.jsx)("span",{className:"font-medium text-gray-700",children:"Marks:"}),(0,b.jsxs)("p",{className:"text-gray-600",children:[c.currentMark.obtainedMarks,"/",a.maxMarks]})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)("span",{className:"font-medium text-gray-700",children:"Percentage:"}),(0,b.jsxs)("p",{className:"text-gray-600 font-semibold",children:[d,"%"]})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)("span",{className:"font-medium text-gray-700",children:"Grade:"}),(0,b.jsx)("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${h(d)}`,children:e})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)("span",{className:"font-medium text-gray-700",children:"Remarks:"}),(0,b.jsx)("p",{className:"text-gray-600",children:c.currentMark.remarks||"-"})]})]}):(0,b.jsxs)("div",{className:"text-center py-4 text-gray-500",children:[(0,b.jsx)(l.AlertCircle,{className:"h-8 w-8 mx-auto mb-2"}),(0,b.jsx)("p",{children:"No marks entered yet"})]})]})},c.id)})})]})})]})}var o=a.i(32541),p=a.i(92761),q=a.i(55371),r=a.i(14401),s=a.i(39260);function t(){let a=(0,d.useParams)(),t=(0,d.useRouter)(),{data:u}=(0,e.useSession)(),v=a.examId,[w,x]=(0,c.useState)(null),[y,z]=(0,c.useState)(!0);(0,c.useEffect)(()=>{A()},[v]);let A=async()=>{try{z(!0);let a=await fetch(`/api/teacher/exams/${v}/students`);if(a.ok){let b=await a.json();x(b)}else console.error("Failed to fetch exam data")}catch(a){console.error("Error fetching exam data:",a)}finally{z(!1)}};if(y)return(0,b.jsx)(h.default,{title:"View Marks",navigation:i.teacherNavigation,children:(0,b.jsxs)("div",{className:"text-center py-8",children:[(0,b.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"}),(0,b.jsx)("p",{className:"mt-2 text-gray-600",children:"Loading exam data..."})]})});if(!w)return(0,b.jsx)(h.default,{title:"View Marks",navigation:i.teacherNavigation,children:(0,b.jsxs)("div",{className:"text-center py-8",children:[(0,b.jsx)(l.AlertCircle,{className:"h-12 w-12 text-red-400 mx-auto mb-4"}),(0,b.jsx)("p",{className:"text-gray-600",children:"Exam not found"}),(0,b.jsx)(s.default,{href:"/teacher/marks",children:(0,b.jsxs)(g.Button,{className:"mt-4",children:[(0,b.jsx)(q.ArrowLeft,{className:"w-4 h-4 mr-2"}),"Back to Marks"]})})]})});let B=w.students.filter(a=>a.hasMarks),C=w.students.filter(a=>!a.hasMarks),D={totalStudents:w.students.length,gradedStudents:B.length,pendingStudents:C.length,averageMarks:B.length>0?Math.round(B.reduce((a,b)=>a+(b.currentMark?.obtainedMarks||0),0)/B.length*100)/100:0,averagePercentage:B.length>0?Math.round(B.reduce((a,b)=>a+(b.currentMark?.obtainedMarks||0)/w.exam.maxMarks*100,0)/B.length*100)/100:0};return(0,b.jsx)(h.default,{title:"View Marks",navigation:i.teacherNavigation,children:(0,b.jsxs)("div",{className:"space-y-6",children:[(0,b.jsxs)("div",{className:"flex flex-col space-y-4 sm:flex-row sm:justify-between sm:items-center sm:space-y-0",children:[(0,b.jsxs)("div",{children:[(0,b.jsx)("div",{className:"flex items-center space-x-2 mb-2",children:(0,b.jsx)(s.default,{href:"/teacher/marks",children:(0,b.jsxs)(g.Button,{variant:"outline",size:"sm",children:[(0,b.jsx)(q.ArrowLeft,{className:"w-4 h-4 mr-1"}),"Back"]})})}),(0,b.jsx)("h1",{className:"text-xl sm:text-2xl font-bold text-gray-900",children:"View Marks"}),(0,b.jsxs)("p",{className:"text-sm sm:text-base text-gray-600",children:[w.exam.name," - ",w.exam.subject.name," (",w.exam.subject.class.name,")"]})]}),(0,b.jsxs)("div",{className:"flex space-x-2",children:[(0,b.jsx)(s.default,{href:`/teacher/marks/${v}`,children:(0,b.jsxs)(g.Button,{variant:"outline",children:[(0,b.jsx)(m.Edit,{className:"w-4 h-4 mr-2"}),"Edit Marks"]})}),(0,b.jsxs)(g.Button,{variant:"outline",children:[(0,b.jsx)(r.Download,{className:"w-4 h-4 mr-2"}),"Export"]})]})]}),(0,b.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6",children:[(0,b.jsxs)(f.Card,{children:[(0,b.jsxs)(f.CardHeader,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,b.jsx)(f.CardTitle,{className:"text-sm font-medium",children:"Total Students"}),(0,b.jsx)(j.Users,{className:"h-4 w-4 text-muted-foreground"})]}),(0,b.jsx)(f.CardContent,{children:(0,b.jsx)("div",{className:"text-2xl font-bold",children:D.totalStudents})})]}),(0,b.jsxs)(f.Card,{children:[(0,b.jsxs)(f.CardHeader,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,b.jsx)(f.CardTitle,{className:"text-sm font-medium",children:"Graded"}),(0,b.jsx)(k.CheckCircle,{className:"h-4 w-4 text-green-600"})]}),(0,b.jsx)(f.CardContent,{children:(0,b.jsx)("div",{className:"text-2xl font-bold text-green-600",children:D.gradedStudents})})]}),(0,b.jsxs)(f.Card,{children:[(0,b.jsxs)(f.CardHeader,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,b.jsx)(f.CardTitle,{className:"text-sm font-medium",children:"Pending"}),(0,b.jsx)(l.AlertCircle,{className:"h-4 w-4 text-orange-600"})]}),(0,b.jsx)(f.CardContent,{children:(0,b.jsx)("div",{className:"text-2xl font-bold text-orange-600",children:D.pendingStudents})})]}),(0,b.jsxs)(f.Card,{children:[(0,b.jsxs)(f.CardHeader,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,b.jsx)(f.CardTitle,{className:"text-sm font-medium",children:"Average Marks"}),(0,b.jsx)(o.Award,{className:"h-4 w-4 text-blue-600"})]}),(0,b.jsx)(f.CardContent,{children:(0,b.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:D.averageMarks})})]}),(0,b.jsxs)(f.Card,{children:[(0,b.jsxs)(f.CardHeader,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,b.jsx)(f.CardTitle,{className:"text-sm font-medium",children:"Average %"}),(0,b.jsx)(o.Award,{className:"h-4 w-4 text-blue-600"})]}),(0,b.jsx)(f.CardContent,{children:(0,b.jsxs)("div",{className:"text-2xl font-bold text-blue-600",children:[D.averagePercentage,"%"]})})]})]}),(0,b.jsxs)(f.Card,{children:[(0,b.jsx)(f.CardHeader,{children:(0,b.jsxs)(f.CardTitle,{className:"flex items-center space-x-2",children:[(0,b.jsx)(p.BookOpen,{className:"w-5 h-5"}),(0,b.jsx)("span",{children:"Exam Details"})]})}),(0,b.jsx)(f.CardContent,{children:(0,b.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4 text-sm",children:[(0,b.jsxs)("div",{children:[(0,b.jsx)("span",{className:"font-medium text-gray-700",children:"Subject:"}),(0,b.jsx)("p",{className:"text-gray-600",children:w.exam.subject.name})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)("span",{className:"font-medium text-gray-700",children:"Class:"}),(0,b.jsx)("p",{className:"text-gray-600",children:w.exam.subject.class.name})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)("span",{className:"font-medium text-gray-700",children:"Max Marks:"}),(0,b.jsx)("p",{className:"text-gray-600",children:w.exam.maxMarks})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)("span",{className:"font-medium text-gray-700",children:"Date:"}),(0,b.jsx)("p",{className:"text-gray-600",children:new Date(w.exam.date).toLocaleDateString()})]})]})})]}),(0,b.jsx)(n,{exam:w.exam,students:w.students,onEdit:a=>{t.push(`/teacher/marks/${a}`)},showActions:!0})]})})}}];

//# sourceMappingURL=e8344_src_app_%28dash%29_teacher_marks_%5BexamId%5D_view_page_tsx_6d355814._.js.map