module.exports=[18622,(e,t,r)=>{t.exports=e.x("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js"))},56704,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/work-async-storage.external.js",()=>require("next/dist/server/app-render/work-async-storage.external.js"))},32319,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/work-unit-async-storage.external.js",()=>require("next/dist/server/app-render/work-unit-async-storage.external.js"))},20635,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/action-async-storage.external.js",()=>require("next/dist/server/app-render/action-async-storage.external.js"))},24725,(e,t,r)=>{t.exports=e.x("next/dist/server/app-render/after-task-async-storage.external.js",()=>require("next/dist/server/app-render/after-task-async-storage.external.js"))},29173,(e,t,r)=>{t.exports=e.x("@prisma/client",()=>require("@prisma/client"))},70406,(e,t,r)=>{t.exports=e.x("next/dist/compiled/@opentelemetry/api",()=>require("next/dist/compiled/@opentelemetry/api"))},93695,(e,t,r)=>{t.exports=e.x("next/dist/shared/lib/no-fallback-error.external.js",()=>require("next/dist/shared/lib/no-fallback-error.external.js"))},61724,(e,t,r)=>{t.exports=e.x("next/dist/compiled/next-server/app-route-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-route-turbo.runtime.prod.js"))},25057,(e,t,r)=>{},15020,e=>{"use strict";e.s(["handler",()=>T,"patchFetch",()=>q,"routeModule",()=>j,"serverHooks",()=>I,"workAsyncStorage",()=>b,"workUnitAsyncStorage",()=>A],15020);var t=e.i(6137),r=e.i(11365),a=e.i(9638),s=e.i(15243),n=e.i(66378),i=e.i(92101),o=e.i(50012),l=e.i(62885),d=e.i(31409),c=e.i(78448),u=e.i(28015),p=e.i(72721),m=e.i(75714),x=e.i(12634),h=e.i(93695);e.i(74732);var f=e.i(66662);e.s(["GET",()=>y,"POST",()=>E,"PUT",()=>C],32616);var R=e.i(2835),v=e.i(31279),w=e.i(47504);let g=w.z.object({name:w.z.string().min(1,"Class name is required"),sectionId:w.z.number().min(1,"Section is required"),teacherId:w.z.number().optional(),capacity:w.z.number().min(1,"Capacity must be at least 1").optional(),academicYear:w.z.string().optional(),isActive:w.z.boolean().default(!0)});async function y(e){try{let{searchParams:t}=new URL(e.url),r=parseInt(t.get("page")||"1"),a=parseInt(t.get("limit")||"10"),s=t.get("search")||"";t.get("isActive");let n=(await v.prisma.class.findMany({include:{sections:!0,_count:{select:{students:!0}}},orderBy:{name:"asc"}})).flatMap(e=>e.sections.map(t=>({id:parseInt(t.id),name:`${e.name} - Section ${t.name}`,capacity:30,academicYear:"2024-2025",isActive:!0,section:{id:parseInt(t.id),name:t.name},teacher:null,_count:{students:Math.floor(25*Math.random())+5}}))),i=n;s&&(i=n.filter(e=>e.name.toLowerCase().includes(s.toLowerCase())));let o=i.length,l=Math.ceil(o/a),d=(r-1)*a,c=i.slice(d,d+a);return R.NextResponse.json({classes:c,pagination:{page:r,limit:a,total:o,totalPages:l}})}catch(e){return console.error("Error fetching classes:",e),R.NextResponse.json({error:"Failed to fetch classes"},{status:500})}}async function E(e){try{let t=await e.json(),r=g.parse(t);if(!await v.prisma.section.findUnique({where:{id:r.sectionId}}))return R.NextResponse.json({error:"Section not found"},{status:400});if(await v.prisma.class.findFirst({where:{name:r.name,sectionId:r.sectionId}}))return R.NextResponse.json({error:"Class with this name already exists in the selected section"},{status:400});if(r.teacherId&&!await v.prisma.teacher.findUnique({where:{id:r.teacherId}}))return R.NextResponse.json({error:"Teacher not found"},{status:400});let a=await v.prisma.class.create({data:r,include:{section:!0,teacher:{select:{id:!0,firstName:!0,lastName:!0,email:!0}}}});return R.NextResponse.json({message:"Class created successfully",class:a},{status:201})}catch(e){if(e instanceof w.z.ZodError)return R.NextResponse.json({error:"Validation error",details:e.errors},{status:400});return console.error("Error creating class:",e),R.NextResponse.json({error:"Failed to create class"},{status:500})}}async function C(e){try{let{id:t,...r}=await e.json();if(!t)return R.NextResponse.json({error:"Class ID is required"},{status:400});let a=g.partial().parse(r);if(!await v.prisma.class.findUnique({where:{id:parseInt(t)}}))return R.NextResponse.json({error:"Class not found"},{status:404});if(a.sectionId&&!await v.prisma.section.findUnique({where:{id:a.sectionId}}))return R.NextResponse.json({error:"Section not found"},{status:400});if(a.teacherId&&!await v.prisma.teacher.findUnique({where:{id:a.teacherId}}))return R.NextResponse.json({error:"Teacher not found"},{status:400});let s=await v.prisma.class.update({where:{id:parseInt(t)},data:a,include:{section:!0,teacher:{select:{id:!0,firstName:!0,lastName:!0,email:!0}}}});return R.NextResponse.json({message:"Class updated successfully",class:s})}catch(e){if(e instanceof w.z.ZodError)return R.NextResponse.json({error:"Validation error",details:e.errors},{status:400});return console.error("Error updating class:",e),R.NextResponse.json({error:"Failed to update class"},{status:500})}}var N=e.i(32616);let j=new t.AppRouteRouteModule({definition:{kind:r.RouteKind.APP_ROUTE,page:"/api/admin/classes/route",pathname:"/api/admin/classes",filename:"route",bundlePath:""},distDir:".next",relativeProjectDir:"",resolvedPagePath:"[project]/school-management-system/src/app/api/admin/classes/route.ts",nextConfigOutput:"",userland:N}),{workAsyncStorage:b,workUnitAsyncStorage:A,serverHooks:I}=j;function q(){return(0,a.patchFetch)({workAsyncStorage:b,workUnitAsyncStorage:A})}async function T(e,t,a){var R;let v="/api/admin/classes/route";v=v.replace(/\/index$/,"")||"/";let w=await j.prepare(e,t,{srcPage:v,multiZoneDraftMode:!1});if(!w)return t.statusCode=400,t.end("Bad Request"),null==a.waitUntil||a.waitUntil.call(a,Promise.resolve()),null;let{buildId:g,params:y,nextConfig:E,isDraftMode:C,prerenderManifest:N,routerServerContext:b,isOnDemandRevalidate:A,revalidateOnlyGenerated:I,resolvedPathname:q}=w,T=(0,i.normalizeAppPath)(v),P=!!(N.dynamicRoutes[T]||N.routes[q]);if(P&&!C){let e=!!N.routes[q],t=N.dynamicRoutes[T];if(t&&!1===t.fallback&&!e)throw new h.NoFallbackError}let S=null;!P||j.isDev||C||(S="/index"===(S=q)?"/":S);let U=!0===j.isDev||!P,O=P&&!U,_=e.method||"GET",k=(0,n.getTracer)(),M=k.getActiveScopeSpan(),H={params:y,prerenderManifest:N,renderOpts:{experimental:{cacheComponents:!!E.experimental.cacheComponents,authInterrupts:!!E.experimental.authInterrupts},supportsDynamicResponse:U,incrementalCache:(0,s.getRequestMeta)(e,"incrementalCache"),cacheLifeProfiles:null==(R=E.experimental)?void 0:R.cacheLife,isRevalidate:O,waitUntil:a.waitUntil,onClose:e=>{t.on("close",e)},onAfterTaskError:void 0,onInstrumentationRequestError:(t,r,a)=>j.onRequestError(e,t,a,b)},sharedContext:{buildId:g}},D=new o.NodeNextRequest(e),F=new o.NodeNextResponse(t),$=l.NextRequestAdapter.fromNodeNextRequest(D,(0,l.signalFromNodeResponse)(t));try{let i=async r=>j.handle($,H).finally(()=>{if(!r)return;r.setAttributes({"http.status_code":t.statusCode,"next.rsc":!1});let a=k.getRootSpanAttributes();if(!a)return;if(a.get("next.span_type")!==d.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${a.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let s=a.get("next.route");if(s){let e=`${_} ${s}`;r.setAttributes({"next.route":s,"http.route":s,"next.span_name":e}),r.updateName(e)}else r.updateName(`${_} ${e.url}`)}),o=async n=>{var o,l;let d=async({previousCacheEntry:r})=>{try{if(!(0,s.getRequestMeta)(e,"minimalMode")&&A&&I&&!r)return t.statusCode=404,t.setHeader("x-nextjs-cache","REVALIDATED"),t.end("This page could not be found"),null;let o=await i(n);e.fetchMetrics=H.renderOpts.fetchMetrics;let l=H.renderOpts.pendingWaitUntil;l&&a.waitUntil&&(a.waitUntil(l),l=void 0);let d=H.renderOpts.collectedTags;if(!P)return await (0,u.sendResponse)(D,F,o,H.renderOpts.pendingWaitUntil),null;{let e=await o.blob(),t=(0,p.toNodeOutgoingHttpHeaders)(o.headers);d&&(t[x.NEXT_CACHE_TAGS_HEADER]=d),!t["content-type"]&&e.type&&(t["content-type"]=e.type);let r=void 0!==H.renderOpts.collectedRevalidate&&!(H.renderOpts.collectedRevalidate>=x.INFINITE_CACHE)&&H.renderOpts.collectedRevalidate,a=void 0===H.renderOpts.collectedExpire||H.renderOpts.collectedExpire>=x.INFINITE_CACHE?void 0:H.renderOpts.collectedExpire;return{value:{kind:f.CachedRouteKind.APP_ROUTE,status:o.status,body:Buffer.from(await e.arrayBuffer()),headers:t},cacheControl:{revalidate:r,expire:a}}}}catch(t){throw(null==r?void 0:r.isStale)&&await j.onRequestError(e,t,{routerKind:"App Router",routePath:v,routeType:"route",revalidateReason:(0,c.getRevalidateReason)({isRevalidate:O,isOnDemandRevalidate:A})},b),t}},h=await j.handleResponse({req:e,nextConfig:E,cacheKey:S,routeKind:r.RouteKind.APP_ROUTE,isFallback:!1,prerenderManifest:N,isRoutePPREnabled:!1,isOnDemandRevalidate:A,revalidateOnlyGenerated:I,responseGenerator:d,waitUntil:a.waitUntil});if(!P)return null;if((null==h||null==(o=h.value)?void 0:o.kind)!==f.CachedRouteKind.APP_ROUTE)throw Object.defineProperty(Error(`Invariant: app-route received invalid cache entry ${null==h||null==(l=h.value)?void 0:l.kind}`),"__NEXT_ERROR_CODE",{value:"E701",enumerable:!1,configurable:!0});(0,s.getRequestMeta)(e,"minimalMode")||t.setHeader("x-nextjs-cache",A?"REVALIDATED":h.isMiss?"MISS":h.isStale?"STALE":"HIT"),C&&t.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");let R=(0,p.fromNodeOutgoingHttpHeaders)(h.value.headers);return(0,s.getRequestMeta)(e,"minimalMode")&&P||R.delete(x.NEXT_CACHE_TAGS_HEADER),!h.cacheControl||t.getHeader("Cache-Control")||R.get("Cache-Control")||R.set("Cache-Control",(0,m.getCacheControlHeader)(h.cacheControl)),await (0,u.sendResponse)(D,F,new Response(h.value.body,{headers:R,status:h.value.status||200})),null};M?await o(M):await k.withPropagatedContext(e.headers,()=>k.trace(d.BaseServerSpan.handleRequest,{spanName:`${_} ${e.url}`,kind:n.SpanKind.SERVER,attributes:{"http.method":_,"http.target":e.url}},o))}catch(t){if(M||t instanceof h.NoFallbackError||await j.onRequestError(e,t,{routerKind:"App Router",routePath:T,routeType:"route",revalidateReason:(0,c.getRevalidateReason)({isRevalidate:O,isOnDemandRevalidate:A})}),P)throw t;return await (0,u.sendResponse)(D,F,new Response(null,{status:500})),null}}}];

//# sourceMappingURL=%5Broot-of-the-server%5D__b4d7398c._.js.map